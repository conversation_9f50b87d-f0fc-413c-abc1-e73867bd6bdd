<?xml version="1.0" encoding="UTF-8"?>
<serviceCatalog
        xmlns="http://service.sankuai.com/1.0.0"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://service.sankuai.com/1.0.0
            http://pixel.sankuai.com/repository/releases/com/meituan/apidoc/servicecatalog/1.0.0/servicecatalog-1.0.0.xsd
http://service.sankuai.com/1.0.0 http://pixel.sankuai.com/repository/releases/com/meituan/apidoc/servicecatalog/1.0.0/servicecatalog-1.0.0.xsd">
    <!-- Service description -->
    <serviceDescs>
        <serviceDesc>
            <appkey>com.sankuai.waimai.e.customerweb</appkey>
            <name>customerweb</name>
            <description>客户、kp、合同、结算操作的服务</description>
            <scenarios>先富系统里面的客户服务</scenarios>
            <interfaceDescs>
                <interfaceDesc>
                    <type>restful</type>
                    <class>com.sankuai.meituan.waimai.web.controller.WmContractController</class>
                </interfaceDesc>

                <interfaceDesc>
                    <type>restful</type>
                    <class>com.sankuai.meituan.waimai.web.controller.WmCustomerCallbackController</class>
                </interfaceDesc>

                <interfaceDesc>
                    <type>restful</type>
                    <class>com.sankuai.meituan.waimai.web.controller.WmCustomerCommonController</class>
                </interfaceDesc>

                <interfaceDesc>
                    <type>restful</type>
                    <class>com.sankuai.meituan.waimai.web.controller.WmCustomerController</class>
                </interfaceDesc>

                <interfaceDesc>
                    <type>restful</type>
                    <class>com.sankuai.meituan.waimai.web.controller.WmCustomerKpController</class>
                </interfaceDesc>

                <interfaceDesc>
                    <type>restful</type>
                    <class>com.sankuai.meituan.waimai.web.controller.WmCustomerOplogController</class>
                </interfaceDesc>

                <interfaceDesc>
                    <type>restful</type>
                    <class>com.sankuai.meituan.waimai.web.controller.WmCustomerPoiController</class>
                </interfaceDesc>

                <interfaceDesc>
                    <type>restful</type>
                    <class>com.sankuai.meituan.waimai.web.controller.WmCustomerVersionController</class>
                </interfaceDesc>

                <interfaceDesc>
                    <type>restful</type>
                    <class>com.sankuai.meituan.waimai.web.controller.base.UiComponentController</class>
                </interfaceDesc>

                <interfaceDesc>
                    <type>restful</type>
                    <class>com.sankuai.meituan.waimai.web.controller.base.SettleUiComponentController</class>
                </interfaceDesc>

                <interfaceDesc>
                    <type>restful</type>
                    <class>com.sankuai.meituan.waimai.web.controller.settle.WmSettleCleanController</class>
                </interfaceDesc>

                <interfaceDesc>
                    <type>restful</type>
                    <class>com.sankuai.meituan.waimai.web.controller.settle.WmSettleCommonController</class>
                </interfaceDesc>

                <interfaceDesc>
                    <type>restful</type>
                    <class>com.sankuai.meituan.waimai.web.controller.settle.WmSettleController</class>
                </interfaceDesc>

                <interfaceDesc>
                    <type>restful</type>
                    <class>com.sankuai.meituan.waimai.web.controller.settle.WmSettleWashController</class>
                </interfaceDesc>

                <interfaceDesc>
                    <type>restful</type>
                    <class>com.sankuai.meituan.waimai.web.controller.task.WmCustomerTaskController</class>
                </interfaceDesc>
            </interfaceDescs>
        </serviceDesc>
    </serviceDescs>

</serviceCatalog>
package com.sankuai.meituan.waimai.econtract.server.dao;

import com.sankuai.meituan.waimai.econtract.server.bo.query.EcontractRecordQueryBo;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractRecordEntity;
import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

@Component
public interface EcontractRecordEntityMapper {

    int deleteByPrimaryKey(Integer id);

    int deleteById(@Param("id") Integer id);

    int insert(EcontractRecordEntity record);

    int insertSelective(EcontractRecordEntity record);

    List<Integer> selectFinishIdsByUtime(@Param("startId") Integer startId,
                                         @Param("utime") Date utime,
                                         @Param("pageSize") Integer pageSize);

    EcontractRecordEntity selectBaseByPrimaryKey(Integer id);

    EcontractRecordEntity selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(EcontractRecordEntity record);

    int updateNotFailRecordByPrimaryKeySelective(EcontractRecordEntity record);

    int updateByPrimaryKeySelectiveNoSaveUrl(EcontractRecordEntity record);

    int updateOptimisticLock(EcontractRecordEntity record);

    int updateByPrimaryKey(EcontractRecordEntity record);

    EcontractRecordEntity queryRecordByRecordKey(@Param(value = "recordKey") String recordKey);

    List<EcontractRecordEntity> queryRecordListByRecordKeyList(@Param(value = "recordKeyList") List<String> recordKeyList);

    List<EcontractRecordEntity> queryEcontractRecordByPage(EcontractRecordQueryBo queryBo);

    Integer queryEcontractRecordByPageCount(EcontractRecordQueryBo queryBo);

    Integer queryLastIndex();

    int updateSaveUrlById(@Param(value = "id") Integer Id,
                          @Param(value = "saveUrl") String saveUrl,
                          @Param(value = "version") Integer version,
                          @Param(value = "utime") Date utime);

    /**
     * 翻页查询签约热数据-升序
     * @param lastId
     * @param size
     * @return
     */
    List<EcontractRecordEntity> queryDeleteEntityListWithLabel4Clean(@Param("lastId") long lastId, @Param("lastDate") Date lastDate, @Param("size") int size);

    /**
     * 翻页查询签约热数据-降序
     * @param lastId
     * @param size
     * @return
     */
    List<EcontractRecordEntity> queryHotEntityListWithLabel4Encryption(@Param("lastId") long lastId, @Param("size") int size);

    void batchUpdateOriginalRecordByIds(@Param("ids") List<Integer> ids);

    /**
     * 根据批次号获取签约record记录
     * @param batchId
     * @return
     */
    List<EcontractRecordEntity> queryEcontractRecordByBatchId(@Param("batchId")Integer batchId);

    /**
     * 根据批次号获取签约recordKey
     * @param batchId 批次ID
     * @return recordKey的list
     */
    List<String> queryRecordKeyListByBatchId(@Param("batchId")Integer batchId);

    List<EcontractRecordEntity> queryEcontractRecordByBatchIdAsc(@Param("batchId") Integer batchId);

    /**
     * 根据批次号列表获取签约record记录
     *
     * @param batchIds
     * @return
     */
    List<EcontractRecordEntity> queryEcontractRecordByBatchIds(@Param("batchIds") List<Integer> batchIds);

    void batchUpdateBatchOpIdByRecordKeyList(@Param("recordKeyList") List<String> recordKeyList,
                                             @Param("batchOpId") Long batchOpId);

    int updateUpstreamStatusByRecordKey(@Param("recordKey") String recordKey,
                                        @Param("upstreamStatus") Integer upstreamStatus);

    Integer queryRecordBatchIdByRecordKey(@Param("recordKey") String recordKey);

    List<EcontractRecordEntity> queryRecordList(@Param("recordKeyList") List<String> recordKeyList);

}

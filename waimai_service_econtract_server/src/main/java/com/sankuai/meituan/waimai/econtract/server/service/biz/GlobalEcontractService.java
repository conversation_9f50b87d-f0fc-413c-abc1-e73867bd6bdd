package com.sankuai.meituan.waimai.econtract.server.service.biz;

import com.sankuai.meituan.waimai.econtract.server.bo.task.EcontractContext;
import com.sankuai.meituan.waimai.econtrct.client.domain.global.GlobalContractInfoRequestDTO;
import com.sankuai.meituan.waimai.econtrct.client.domain.global.GlobalContractInfoResponseDTO;
import com.sankuai.meituan.waimai.econtrct.client.domain.global.GlobalEcontractGenerateRequestDTO;
import com.sankuai.meituan.waimai.econtrct.client.dto.SignContext;
import com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException;
import org.springframework.stereotype.Service;

/**
 * @description:
 * @author: liuyunjie05
 * @create: 2024/1/3 15:29
 */
@Service
public interface GlobalEcontractService {

    Long generateGlobalEcontractId(GlobalEcontractGenerateRequestDTO requestDTO) throws EcontractException;

    SignContext extractSignContext(EcontractContext context);

    GlobalContractInfoResponseDTO queryGlobalContractInfo(String taskType);
}

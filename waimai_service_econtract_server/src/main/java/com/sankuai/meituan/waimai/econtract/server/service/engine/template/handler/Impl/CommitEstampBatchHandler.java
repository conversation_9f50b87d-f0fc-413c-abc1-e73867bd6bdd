package com.sankuai.meituan.waimai.econtract.server.service.engine.template.handler.Impl;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.econtract.server.bo.task.EcontractContext;
import com.sankuai.meituan.waimai.econtract.server.bo.task.TaskContext;
import com.sankuai.meituan.waimai.econtract.server.bo.task.TaskNodeBo;
import com.sankuai.meituan.waimai.econtract.server.config.MccConfig;
import com.sankuai.meituan.waimai.econtract.server.constants.ContextConstant;
import com.sankuai.meituan.waimai.econtract.server.constants.EcontractRecordConstant;
import com.sankuai.meituan.waimai.econtract.server.constants.TaskConstant;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractRecordEntity;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractTaskEntity;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractRecordService;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractTaskService;
import com.sankuai.meituan.waimai.econtract.server.service.engine.task.TaskManager;
import com.sankuai.meituan.waimai.econtract.server.service.engine.template.handler.TaskHandler;
import com.sankuai.meituan.waimai.econtract.server.service.engine.template.processor.Impl.AbstractProcessor;
import com.sankuai.meituan.waimai.econtract.server.service.mq.TaskMsg;
import com.sankuai.meituan.waimai.econtract.server.utils.TimeUtils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

@Service
public class CommitEstampBatchHandler extends AbstractProcessor implements TaskHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(CommitEstampHandler.class);

    @Resource
    private EcontractTaskService econtractTaskService;

    @Resource
    private EcontractRecordService econtractRecordService;

    @Resource
    private TaskManager taskManager;

    @Override
    public void handleTask(EcontractContext context) {
        LOGGER.info("econtract context record : {} commit estamp : {} ", context.getEcontractRecordEntity().getRecordKey(),context.getCurrentTaskNode().getTaskName());
        TaskNodeBo currentTaskNode = context.getCurrentTaskNode();

        //更新当前子节点
        //TODO 更新callbackTask
        EcontractTaskEntity taskEntity = econtractTaskService.selectByPrimaryKey(context.getExecuteTaskId());
        taskEntity.setTaskState(TaskConstant.TASK_SUCCESS);
        taskEntity.setUtime(TimeUtils.getUTime());
        econtractTaskService.updateByPrimaryKeySelective(taskEntity);

        if (!executeTaskFinish(currentTaskNode)) {
            executeTaskSuccess(context, taskEntity);
            return;
        }
        stageTaskSuccess(context, taskEntity);
    }

    @Override
    public void handleTaskWithNoTransactionalMessage(EcontractContext context) {
        handleTask(context);
    }

    @Transactional(rollbackFor = Exception.class)
    public void executeTaskSuccess(EcontractContext context, EcontractTaskEntity executeTask) {
        LOGGER.info("executeTaskSuccess");
        TaskNodeBo currentTaskNode = context.getCurrentTaskNode();

        //更新record信息
        EcontractRecordEntity recordEntity = econtractRecordService.queryRecordByRecordKey(context.getEcontractRecordEntity().getRecordKey());
        recordEntity.setUtime(TimeUtils.getUTime());
        econtractRecordService.updateByPrimaryKeySelectiveNoSaveUrl(recordEntity);

        if (currentTaskNode.isNeedNotify()) {
            TaskContext taskContext = createNotifierTaskContext(context, recordEntity.getEcontractStage(),TaskConstant.TASK_SUCCESS);
            doNotifier(context, taskContext, executeTask.getTaskType());
        }
    }

//    @Transactional(rollbackFor = Exception.class)
    public void stageTaskSuccess(EcontractContext context, EcontractTaskEntity executeTask) {
        LOGGER.info("stageTaskSuccess");
        TaskNodeBo currentTaskNode = context.getCurrentTaskNode();
        TaskNodeBo callbackTaskNode = currentTaskNode.getCallBackTask();
        TaskNodeBo nextTaskNode = currentTaskNode.getNextTask();

        //1 更新父节点
        EcontractTaskEntity currentTaskEntity = econtractTaskService.selectByPrimaryKey(currentTaskNode.getTaskId());
        EcontractTaskEntity parentTaskEntity = econtractTaskService.selectByPrimaryKey(currentTaskEntity.getParentTaskId());
        parentTaskEntity.setTaskState(TaskConstant.TASK_SUCCESS);
        parentTaskEntity.setUtime(TimeUtils.getUTime());
        econtractTaskService.updateByPrimaryKeySelective(parentTaskEntity);

        //2 更新Record信息
        //2.1 更新TaskNode为Success
        currentTaskNode.setTaskState(TaskConstant.TASK_SUCCESS);
        callbackTaskNode.setTaskState(TaskConstant.TASK_SUCCESS);
        //2.2更新record信息
        EcontractRecordEntity recordEntity = econtractRecordService.queryRecordByRecordKey(context.getEcontractRecordEntity().getRecordKey());
        recordEntity.setUtime(TimeUtils.getUTime());
        recordEntity.setEcontractState(EcontractRecordConstant.RUNNING);
        context.setCurrentTaskNode(nextTaskNode);
        context.setContextState(ContextConstant.CONTEXT_TO_EXECUTE);
        recordEntity.setEcontractRecordContext(JSON.toJSONString(context));
        econtractRecordService.updateByPrimaryKeySelectiveNoSaveUrl(recordEntity);

        if (currentTaskNode.isNeedNotify()) {
            TaskContext taskContext = createNotifierTaskContext(context,recordEntity.getEcontractStage(),TaskConstant.TASK_SUCCESS);
            doNotifier(context, taskContext, executeTask.getTaskType());
        }

        boolean isMafka = taskManager.mafkaMigrateSwitch();
        TaskMsg taskMsg = handleMessage(context,isMafka);
        taskManager.commitTask(taskMsg,isMafka);
    }
}

package com.sankuai.meituan.waimai.econtract.server.service.engine.template.handler.Impl;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.econtract.server.bo.task.EcontractContext;
import com.sankuai.meituan.waimai.econtract.server.bo.task.TaskContext;
import com.sankuai.meituan.waimai.econtract.server.bo.task.TaskNodeBo;
import com.sankuai.meituan.waimai.econtract.server.config.MccConfig;
import com.sankuai.meituan.waimai.econtract.server.constants.ContextConstant;
import com.sankuai.meituan.waimai.econtract.server.constants.EcontractRecordConstant;
import com.sankuai.meituan.waimai.econtract.server.constants.TaskConstant;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractRecordEntity;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractTaskEntity;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractRecordService;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractTaskService;
import com.sankuai.meituan.waimai.econtract.server.service.engine.task.TaskManager;
import com.sankuai.meituan.waimai.econtract.server.service.engine.template.handler.TaskHandler;
import com.sankuai.meituan.waimai.econtract.server.service.engine.template.processor.Impl.AbstractProcessor;
import com.sankuai.meituan.waimai.econtract.server.service.mq.TaskMsg;
import com.sankuai.meituan.waimai.econtract.server.utils.ContextUtils;
import com.sankuai.meituan.waimai.econtract.server.utils.TimeUtils;

import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR> Hou
 * @date 2017/11/26
 * @time 下午12:46
 */
@Service
public class TurnCreatePdfHandler extends AbstractProcessor implements TaskHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(TurnCreatePdfHandler.class);

    @Autowired
    private EcontractRecordService econtractRecordService;
    @Autowired
    private EcontractTaskService econtractTaskService;
    @Autowired
    private TaskManager taskManager;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleTask(EcontractContext context) {
        LOGGER.info("econtract context record : {} turn create PDF", context.getEcontractRecordEntity().getRecordKey());
        EcontractRecordEntity recordEntity = econtractRecordService.queryRecordByRecordKey(context.getEcontractRecordEntity().getRecordKey());
        TaskNodeBo currentTaskNode = context.getCurrentTaskNode();
        currentTaskNode.setTaskState(TaskConstant.TASK_RUNNIG);

        //1.修改老的task数据;
        EcontractTaskEntity currentTask = econtractTaskService.selectByPrimaryKey(currentTaskNode.getTaskId());
        currentTask.setTaskState(TaskConstant.TASK_RUNNIG);
        currentTask.setUtime(TimeUtils.getUTime());
        econtractTaskService.updateByPrimaryKeySelective(currentTask);


        //2.修改record状态;
        recordEntity.setEcontractStage(TaskConstant.CREATE_PDF);
        recordEntity.setEcontractState(EcontractRecordConstant.RUNNING);
        recordEntity.setUtime(TimeUtils.getUTime());
        context.setContextState(ContextConstant.CONTEXT_TO_EXECUTE);
        recordEntity.setEcontractRecordContext(JSON.toJSONString(context));
        econtractRecordService.updateByPrimaryKeySelective(recordEntity);


        //3.修改新的task数据
        EcontractTaskEntity taskEntity = econtractTaskService.selectByPrimaryKey(currentTaskNode.getTaskId());
        TaskContext taskContext = new TaskContext();
        taskContext.setTaskId(taskEntity.getId());
        taskContext.setStageInfoBo(ContextUtils.getStageInfoBoFromList(context, TaskConstant.CREATE_PDF));
        taskContext.setExecutorType(TaskConstant.CREATE_PDF_EXECUTOR);
        taskContext.setEcontractStage(taskEntity.getTaskType());
        taskContext.setState(TaskConstant.TASK_RUNNIG);
        if (CollectionUtils.isNotEmpty(context.getFlowList())) {
            taskContext.setTaskType(context.getFlowList().get(0));
        }
        taskEntity.setEcontractTaskContext(JSON.toJSONString(taskContext));
        econtractTaskService.updateByPrimaryKeySelective(taskEntity);
        context.setTaskContext(taskContext);

        String contextStr = JSON.toJSONString(context);
        if (context.getUseMafka() && contextStr.getBytes().length > MccConfig.MESSAGE_SIZE){
            context.setContextId(messageProducerHandler.getEntityId(contextStr));
        }
    }

    @Override
    public void handleTaskWithNoTransactionalMessage(EcontractContext context) {
        TurnCreatePdfHandler turnCreatePdfHandler = (TurnCreatePdfHandler) AopContext
                .currentProxy();
        LOGGER.info("#turnCreatePdfHandler={}", turnCreatePdfHandler);
        boolean isMafka = taskManager.mafkaMigrateSwitch();
        context.setUseMafka(isMafka);
        turnCreatePdfHandler.handleTask(context);
        //4.消息推到CommonProcessor
        TaskMsg taskMsg = handleMessage(context,isMafka);
        taskManager.submitTask(taskMsg,isMafka);
    }
}

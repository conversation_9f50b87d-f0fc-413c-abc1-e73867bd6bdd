package com.sankuai.meituan.waimai.econtract.server.service.biz.Impl;

import com.sankuai.meituan.waimai.econtract.server.service.biz.TairService;
import com.sankuai.meituan.waimai.kv.groupm.service.BaseKvService;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;


/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 16-6-23.
 */
@Service
public class TairServiceImpl implements TairService {

    private static final Logger logger = LoggerFactory.getLogger(TairServiceImpl.class);

    // 50ms超时
    private static final int TAIR_TIMEOUT = 2000;

    @Autowired(required = false)
    @Qualifier("mBaseKvService")
    private BaseKvService baseKvService;

    @Override
    public String getStrValue(String key) {
        return baseKvService.get(key);
    }

    @Override
    public Boolean setStrValue(String key, String value,int expire) {
        baseKvService.set(key, value, expire);
        return Boolean.TRUE;
    }

    @Override
    public Boolean delKey(String key) {
        baseKvService.del(key);
        return Boolean.TRUE;
    }



}

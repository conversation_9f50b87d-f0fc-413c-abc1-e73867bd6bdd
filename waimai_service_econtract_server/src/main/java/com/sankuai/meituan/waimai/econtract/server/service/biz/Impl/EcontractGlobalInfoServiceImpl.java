package com.sankuai.meituan.waimai.econtract.server.service.biz.Impl;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractGlobalInfoService;
import com.sankuai.meituan.waimai.econtract.server.template.config.dao.EcontractGlobalInfoMapper;
import com.sankuai.meituan.waimai.econtract.server.template.config.entity.EcontractGlobalInfoEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description:
 * @author: liuyunjie05
 * @create: 2023/12/4 20:04
 */
@Slf4j
@Service
public class EcontractGlobalInfoServiceImpl implements EcontractGlobalInfoService {

    @Resource
    private EcontractGlobalInfoMapper econtractGlobalInfoMapper;

    @Override
    public void batchInsert(List<EcontractGlobalInfoEntity> econtractGlobalInfoList) {
        log.info("EcontractGlobalInfoServiceImpl#batchInsert, econtractGlobalInfoList: {}", JSON.toJSONString(econtractGlobalInfoList));
        econtractGlobalInfoMapper.batchInsert(econtractGlobalInfoList);
    }

    @Override
    public void insert(EcontractGlobalInfoEntity globalInfo) {
        log.info("EcontractGlobalInfoServiceImpl#insert, globalInfo: {}", JSON.toJSONString(globalInfo));
        econtractGlobalInfoMapper.insert(globalInfo);
    }


}

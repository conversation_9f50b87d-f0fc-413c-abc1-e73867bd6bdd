package com.sankuai.meituan.waimai.econtract.server.service.biz.Impl;

import com.sankuai.meituan.waimai.econtract.server.dao.EcontractUserRelMapper;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractUserRelEntity;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractUserRelService;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class EcontractUserRelServiceImpl implements EcontractUserRelService {

    private static final Logger LOGGER = LoggerFactory.getLogger(EcontractUserRelServiceImpl.class);

    @Autowired
    private EcontractUserRelMapper econtractUserRelMapper;

    @Override
    public EcontractUserRelEntity getByUid(Integer uid) {
        return econtractUserRelMapper.selectByUid(uid);
    }

    @Override
    public void deleteByPrimaryKey(Long id) {
        econtractUserRelMapper.deleteByPrimaryKey(id.intValue());
    }

    @Override
    public void insertSelective(EcontractUserRelEntity entity) {
        econtractUserRelMapper.insertSelective(entity);
    }

    @Override
    public void insert(EcontractUserRelEntity entity) {
        econtractUserRelMapper.insert(entity);
    }

    @Override
    public EcontractUserRelEntity selectByPrimaryKey(Integer id) {
        return econtractUserRelMapper.selectByPrimaryKey(id);
    }

    @Override
    public void updateByPrimaryKeySelective(EcontractUserRelEntity entity) {
        econtractUserRelMapper.updateByPrimaryKeySelective(entity);
    }

    @Override
    public void updateByPrimaryKey(EcontractUserRelEntity entity) {
        econtractUserRelMapper.updateByPrimaryKey(entity);
    }

}

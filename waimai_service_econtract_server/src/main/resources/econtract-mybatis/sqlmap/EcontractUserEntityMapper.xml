<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.meituan.waimai.econtract.server.dao.EcontractUserEntityMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.meituan.waimai.econtract.server.entity.EcontractUserEntity">
    <id column="id" jdbcType="INTEGER" property="id"/>
    <result column="name" jdbcType="VARCHAR" property="name"/>
    <result column="token" jdbcType="VARCHAR" property="token"/>
    <result column="type" jdbcType="VARCHAR" property="type"/>
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime"/>
    <result column="valid" jdbcType="TINYINT" property="valid"/>
    <result column="description" jdbcType="VARCHAR" property="description"/>
    <result column="member_mis" jdbcType="VARCHAR" property="memberMis"/>
    <result column="utime" jdbcType="TIMESTAMP" property="utime"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, name, token, type, ctime, valid, description, member_mis, utime
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from wm_econtract_user
    where id = #{id,jdbcType=INTEGER}
  </select>

  <select id="queryEcontractUserList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from wm_econtract_user
    WHERE valid = 1
    order by id DESC
  </select>

  <select id="queryUserByToken" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from wm_econtract_user
    where token = #{token,jdbcType=VARCHAR}
    AND valid = 1
  </select>

  <select id="queryUserByName" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from wm_econtract_user
    where name = #{userName,jdbcType=VARCHAR}
    AND valid = 1
  </select>

  <select id="queryValidUserById" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from wm_econtract_user
    where id = #{id, jdbcType=INTEGER}
    and valid = 1
  </select>

  <select id="queryUserByDescription" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from wm_econtract_user
    where description = #{description, jdbcType=VARCHAR}
    AND valid = 1
  </select>

  <select id="queryAllUserByMisId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from wm_econtract_user
    where
    (member_mis = '' or member_mis like CONCAT('%', #{misId}, '%'))
    and valid = 1
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from wm_econtract_user
    where id = #{id,jdbcType=INTEGER}
  </delete>

  <update id="deleteByPrimaryKeyLogic" parameterType="java.lang.Integer">
    update wm_econtract_user
    set valid = 0
    where id = #{id,jdbcType=INTEGER}
  </update>

  <insert id="insert" parameterType="com.sankuai.meituan.waimai.econtract.server.entity.EcontractUserEntity">
    insert into wm_econtract_user (id, name, token,
                                   type, ctime, valid, description, member_mis, utime)
    values (#{id,jdbcType=INTEGER}, #{name,jdbcType=VARCHAR}, #{token,jdbcType=VARCHAR},
            #{type,jdbcType=VARCHAR}, #{ctime,jdbcType=TIMESTAMP}, #{valid,jdbcType=TINYINT}
           #{description,jdbcType=VARCHAR}, #{memberMis, jdbcType=VARCHAR}, #{utime, jdbcType=TIMESTAMP})
  </insert>

  <insert id="insertSelective" parameterType="com.sankuai.meituan.waimai.econtract.server.entity.EcontractUserEntity">
    insert into wm_econtract_user
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="token != null">
        token,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="valid != null">
        valid,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="memberMis != null">
        member_mis,
      </if>
      <if test="valid != null">
        utime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="token != null">
        #{token,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=TINYINT},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="memberMis != null">
        #{memberMis, jdbcType=VARCHAR},
      </if>
      <if test="utime != null">
        #{utime, jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective"
          parameterType="com.sankuai.meituan.waimai.econtract.server.entity.EcontractUserEntity">
    update wm_econtract_user
    <set>
      <if test="description != null">
        description = #{description, jdbcType=VARCHAR},
      </if>
      <if test="memberMis != null">
        member_mis = #{memberMis, jdbcType=VARCHAR},
      </if>
      <if test="utime != null">
        utime = #{utime, jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id, jdbcType=INTEGER}
  </update>

  <update id="updateByPrimaryKey" parameterType="com.sankuai.meituan.waimai.econtract.server.entity.EcontractUserEntity">
    update wm_econtract_user
    set name = #{name,jdbcType=VARCHAR},
      token = #{token,jdbcType=VARCHAR},
      type = #{type,jdbcType=VARCHAR},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=TINYINT}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <update id="invalidUserById">
    update wm_econtract_user
    set valid = 0,
        utime = #{utime}
    where id = #{id}
  </update>

</mapper>
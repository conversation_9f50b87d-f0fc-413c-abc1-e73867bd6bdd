package com.sankuai.meituan.waimai.econtrct.client.domain.applybiz;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;

/**
 * Author: 窦旭蒙
 * MIS: douxumeng
 * Date: 2021-11-27 22:04
 * Email: <EMAIL>
 * Desc:
 */
@ThriftStruct
public class SignAdditionInfoMeta {

    //消息类型，见SignAdditionInfoTypeEnum
    private String infoType;

    //消息位置索引
    private Integer infoIndex;

    //消息文案
    private SignAdditionInfoText infoText;


    @ThriftField(1)
    public String getInfoType() {
        return this.infoType;
    }

    @ThriftField
    public void setInfoType(String infoType) {
        this.infoType = infoType;
    }

    @ThriftField(2)
    public Integer getInfoIndex() {
        return this.infoIndex;
    }

    @ThriftField
    public void setInfoIndex(Integer infoIndex) {
        this.infoIndex = infoIndex;
    }

    @ThriftField(3)
    public SignAdditionInfoText getInfoText() {
        return this.infoText;
    }

    @ThriftField
    public void setInfoText(SignAdditionInfoText infoText) {
        this.infoText = infoText;
    }
}

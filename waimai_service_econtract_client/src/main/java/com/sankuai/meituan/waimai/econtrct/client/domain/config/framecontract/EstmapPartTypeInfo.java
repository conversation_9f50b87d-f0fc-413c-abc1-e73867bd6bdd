package com.sankuai.meituan.waimai.econtrct.client.domain.config.framecontract;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;

/**
 * @description:
 * @author: liuyunjie05
 * @create: 2024/5/18 14:32
 */
@ThriftStruct
public class EstmapPartTypeInfo {

    private Integer partTypeCode;

    private String  partTypeDesc;

    @ThriftField(1)
    public Integer getPartTypeCode() {
        return this.partTypeCode;
    }

    @ThriftField
    public void setPartTypeCode(Integer partTypeCode) {
        this.partTypeCode = partTypeCode;
    }

    @ThriftField(2)
    public String getPartTypeDesc() {
        return this.partTypeDesc;
    }

    @ThriftField
    public void setPartTypeDesc(String partTypeDesc) {
        this.partTypeDesc = partTypeDesc;
    }
}

package com.sankuai.meituan.waimai.econtrct.client.domain.api;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

@Builder
@ThriftStruct
@NoArgsConstructor
@AllArgsConstructor
public class CoopBusinessInfoBo {

    @FieldDoc(description = "业务id")
    public String businessId;

    @FieldDoc(description = "业务名称")
    public String businessName;

    @FieldDoc(description = "付款方式")
    public String autoPayPlanDetail;

    @FieldDoc(description = "确认电话")
    public String contactMobile;

    @FieldDoc(description = "联系邮箱")
    public String contactEmail;


    @ThriftField(1)
    public String getBusinessId() {
        return this.businessId;
    }

    @ThriftField
    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }

    @ThriftField(2)
    public String getBusinessName() {
        return this.businessName;
    }

    @ThriftField
    public void setBusinessName(String businessName) {
        this.businessName = businessName;
    }

    @ThriftField(3)
    public String getAutoPayPlanDetail() {
        return this.autoPayPlanDetail;
    }

    @ThriftField
    public void setAutoPayPlanDetail(String autoPayPlanDetail) {
        this.autoPayPlanDetail = autoPayPlanDetail;
    }

    @ThriftField(4)
    public String getContactMobile() {
        return this.contactMobile;
    }

    @ThriftField
    public void setContactMobile(String contactMobile) {
        this.contactMobile = contactMobile;
    }

    @ThriftField(5)
    public String getContactEmail() {
        return this.contactEmail;
    }

    @ThriftField
    public void setContactEmail(String contactEmail) {
        this.contactEmail = contactEmail;
    }
}

/**
 * Autogenerated by Thrift Compiler (0.8.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.sankuai.meituan.waimai.econtrct.client.domain.api;


public enum CAType implements org.apache.thrift.TEnum {
  PERSON(1),
  COMPANY(2);

  private final int value;

  private CAType(int value) {
    this.value = value;
  }

  /**
   * Get the integer value of this enum value, as defined in the Thrift IDL.
   */
  public int getValue() {
    return value;
  }

  /**
   * Find a the enum type by its integer value, as defined in the Thrift IDL.
   * @return null if the value is not found.
   */
  public static CAType findByValue(int value) { 
    switch (value) {
      case 1:
        return PERSON;
      case 2:
        return COMPANY;
      default:
        return null;
    }
  }
}

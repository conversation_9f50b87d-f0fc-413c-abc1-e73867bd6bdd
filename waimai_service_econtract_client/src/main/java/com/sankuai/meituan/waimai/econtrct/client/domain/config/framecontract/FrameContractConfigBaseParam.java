package com.sankuai.meituan.waimai.econtrct.client.domain.config.framecontract;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;

/**
 * @description:
 * @author: liuyunjie05
 * @create: 2024/5/17 19:02
 */
@ThriftStruct
public class FrameContractConfigBaseParam {
    @FieldDoc(description = "框架合同类型code")
    private String contractCode;

    @FieldDoc(description = "框架合同名称")
    private String contractName;

    @FieldDoc(description = "注册时间")
    private Long registerStartTime;

    @FieldDoc(description = "注册时间")
    private Long registerEndTime;

    @FieldDoc(description = "状态")
    private Integer status;

    @FieldDoc(description = "操作人uid")
    private Integer operatorId;

    @ThriftField(1)
    public String getContractCode() {
        return this.contractCode;
    }

    @ThriftField
    public void setContractCode(String contractCode) {
        this.contractCode = contractCode;
    }

    @ThriftField(2)
    public String getContractName() {
        return this.contractName;
    }

    @ThriftField
    public void setContractName(String contractName) {
        this.contractName = contractName;
    }

    @ThriftField(3)
    public Long getRegisterStartTime() {
        return this.registerStartTime;
    }

    @ThriftField
    public void setRegisterStartTime(Long registerStartTime) {
        this.registerStartTime = registerStartTime;
    }

    @ThriftField(4)
    public Long getRegisterEndTime() {
        return this.registerEndTime;
    }

    @ThriftField
    public void setRegisterEndTime(Long registerEndTime) {
        this.registerEndTime = registerEndTime;
    }

    @ThriftField(5)
    public Integer getStatus() {
        return this.status;
    }

    @ThriftField
    public void setStatus(Integer status) {
        this.status = status;
    }

    @ThriftField(6)
    public Integer getOperatorId() {
        return this.operatorId;
    }

    @ThriftField
    public void setOperatorId(Integer operatorId) {
        this.operatorId = operatorId;
    }
}

package com.sankuai.meituan.waimai.econtrct.client.domain.base;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;

@ThriftStruct
public class BoolResult {

    private boolean value;

    public BoolResult() {
    }

    public BoolResult(boolean value) {
        this.value = value;
    }

    @ThriftField(1)
    public boolean isValue() {
        return value;
    }

    @ThriftField
    public void setValue(boolean value) {
        this.value = value;
    }
}

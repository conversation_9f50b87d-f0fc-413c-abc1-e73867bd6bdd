package com.sankuai.meituan.waimai.econtract.web.utils;

import org.jpedal.PdfDecoder;
import org.jpedal.fonts.FontMappings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;

/**
 * Created by zhangyuanbo02 on 16/5/27.
 */
public class PdfConvertImageUtil {

    private static Logger LOG = LoggerFactory.getLogger(ImageUtil.class);

    private static final String IMAGE_FORM_TO_JPG = "jpg";


    public static byte[] transfer(byte[] bytes, int pageNum) throws IOException {
        LOG.info("PDF合同转IMAGE开始...pageNum={}", pageNum);
        PdfDecoder decode_pdf = new PdfDecoder(true);
        decode_pdf.scaling = 1.5F;
        FontMappings.setFontReplacements();
        byte[] outbytes = new byte[0];
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        try {
            decode_pdf.openPdfArray(bytes); //bytes is byte[] array with PDF
            BufferedImage img = decode_pdf.getPageAsImage(pageNum);
            ImageIO.write(img, "jpg", out);
            outbytes = out.toByteArray();
            LOG.info("PDF合同转IMAGE成功...pageNum={}", pageNum);
        } catch (Exception e) {
            LOG.error("PDF合同转IMAGE异常..",e);
        } finally {
            out.close();
            decode_pdf.flushObjectValues(true);
            decode_pdf.closePdfFile();
        }
        return outbytes;
    }

    public static Integer getTotalPages(byte[] bytes) {
        LOG.info("PDF合同转IMAGE，获取总页数开始...");
        PdfDecoder decode_pdf = new PdfDecoder(true);
        decode_pdf.scaling = 1.5F;
        FontMappings.setFontReplacements();
        Integer totalPages = 0;
        try {
            decode_pdf.openPdfArray(bytes); //bytes is byte[] array with PDF
            totalPages = decode_pdf.getPageCount();
            LOG.info("PDF合同转IMAGE，获取总页数成功，totalPages={}", totalPages);
        } catch (Exception e) {
            LOG.error("PDF合同转IMAGE，获取总页数异常...e={}", e);
        } finally {
            decode_pdf.flushObjectValues(true);
            decode_pdf.closePdfFile();
        }
        return totalPages;
    }
}

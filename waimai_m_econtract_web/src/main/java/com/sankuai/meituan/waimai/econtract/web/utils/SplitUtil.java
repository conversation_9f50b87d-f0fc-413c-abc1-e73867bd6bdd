package com.sankuai.meituan.waimai.econtract.web.utils;

import com.google.common.collect.Lists;

import com.mchange.lang.IntegerUtils;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

public class SplitUtil {

    private static final String DEFAULT_SPLIT = ",";

    public static List<Integer> splitToIntList(String str) {
        if (StringUtils.isBlank(str)) {
            return Lists.newArrayList();
        }

        String[] strs = StringUtils.split(str, DEFAULT_SPLIT);
        List<Integer> intList = Lists.newArrayList();
        for (String splitStr:strs) {
            Integer value = IntegerUtils.parseInt(splitStr, 0);
            if (value == null || value <= 0) {
                continue;
            }

            CollectionUtils.addIgnoreNull(intList, value);
        }
        return intList;
    }
}

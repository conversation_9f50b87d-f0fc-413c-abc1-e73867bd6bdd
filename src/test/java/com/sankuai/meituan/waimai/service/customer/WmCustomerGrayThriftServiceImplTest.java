package com.sankuai.meituan.waimai.service.customer;

import com.sankuai.meituan.waimai.customer.service.WmCustomerGrayThriftServiceImpl;
import com.sankuai.meituan.waimai.customer.service.gray.WmCustomerGrayService;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.KpBankStatementCheckResult;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;

/**
 * KP灰度接口单测
 */
@RunWith(MockitoJUnitRunner.class)
public class WmCustomerGrayThriftServiceImplTest {

    @InjectMocks
    private WmCustomerGrayThriftServiceImpl service;

    @Mock
    private WmCustomerGrayService wmCustomerGrayService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试checkKpAllowNoBankStatement方法，当wmCustomerGrayService.checkHitAllowNoBankStatementKpOperate返回true时
     */
    @Test
    public void testCheckKpAllowNoBankStatementWhenServiceReturnsTrue() throws Exception {
        // arrange
        Long customerId = 1L;
        when(wmCustomerGrayService.checkHitAllowNoBankStatementKpOperate(customerId)).thenReturn(true);

        // act
        KpBankStatementCheckResult result = service.checkKpAllowNoBankStatement(customerId);

        // assert
        assertEquals(0, result.getCode());
        assertTrue(result.getData());
    }

    /**
     * 测试checkKpAllowNoBankStatement方法，当wmCustomerGrayService.checkHitAllowNoBankStatementKpOperate返回false时
     */
    @Test
    public void testCheckKpAllowNoBankStatementWhenServiceReturnsFalse() throws Exception {
        // arrange
        Long customerId = 1L;
        when(wmCustomerGrayService.checkHitAllowNoBankStatementKpOperate(customerId)).thenReturn(false);

        // act
        KpBankStatementCheckResult result = service.checkKpAllowNoBankStatement(customerId);

        // assert
        assertEquals(0, result.getCode());
        assertFalse(result.getData());
    }

    /**
     * 测试checkKpAllowNoBankStatement方法，当wmCustomerGrayService.checkHitAllowNoBankStatementKpOperate抛出异常时
     */
    @Test
    public void testCheckKpAllowNoBankStatementWhenServiceThrowsException() throws Exception {
        // arrange
        Long customerId = 1L;
        when(wmCustomerGrayService.checkHitAllowNoBankStatementKpOperate(customerId)).thenThrow(new RuntimeException());

        // act
        KpBankStatementCheckResult result = service.checkKpAllowNoBankStatement(customerId);

        // assert
        assertEquals(1, result.getCode());
        assertEquals("系统异常", result.getMsg());
    }
}

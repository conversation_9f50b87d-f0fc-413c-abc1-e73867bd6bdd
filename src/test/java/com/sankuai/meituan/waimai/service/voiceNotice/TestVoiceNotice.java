package com.sankuai.meituan.waimai.service.voiceNotice;

import static com.amazonaws.services.s3.internal.Constants.HMAC_SHA1_ALGORITHM;

import java.io.IOException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import java.util.Map.Entry;
import java.util.TimeZone;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

import org.apache.commons.codec.binary.Base64;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import com.alibaba.fastjson.JSONObject;

public class TestVoiceNotice {

    static class VoiceDTO {
        String   tenant_id;
        String   app_uuid;
        String   app_call_uuid;
        String   dest_num;
        String   ivr_access_id;
        MediaTxt media_txt;
        String   display_num    = "";
        int      auto_call_mode = 0;

        public String getTenant_id() {
            return tenant_id;
        }

        public void setTenant_id(String tenant_id) {
            this.tenant_id = tenant_id;
        }

        public String getApp_uuid() {
            return app_uuid;
        }

        public void setApp_uuid(String app_uuid) {
            this.app_uuid = app_uuid;
        }

        public String getApp_call_uuid() {
            return app_call_uuid;
        }

        public void setApp_call_uuid(String app_call_uuid) {
            this.app_call_uuid = app_call_uuid;
        }

        public String getDest_num() {
            return dest_num;
        }

        public void setDest_num(String dest_num) {
            this.dest_num = dest_num;
        }

        public String getIvr_access_id() {
            return ivr_access_id;
        }

        public void setIvr_access_id(String ivr_access_id) {
            this.ivr_access_id = ivr_access_id;
        }

        public MediaTxt getMedia_txt() {
            return media_txt;
        }

        public void setMedia_txt(MediaTxt media_txt) {
            this.media_txt = media_txt;
        }

        public String getDisplay_num() {
            return display_num;
        }

        public void setDisplay_num(String display_num) {
            this.display_num = display_num;
        }

        public int getAuto_call_mode() {
            return auto_call_mode;
        }

        public void setAuto_call_mode(int auto_call_mode) {
            this.auto_call_mode = auto_call_mode;
        }
    }

    static class MediaTxt {

        public String getText1() {
            return text1;
        }

        public void setText1(String text1) {
            this.text1 = text1;
        }

        String text1;
    }

    public static void main(String[] args) {
        //https://km.sankuai.com/page/156736614

        //++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
        //        线下环境
        //        tenant_id：eeae2e20-0aaa-11ea-aaf1-00223e9f3d04
        //        client_id：139d700a8bf80707
        //        secret：6c5aeaef805614e25b6f8f003842e48d
        //        ivr_access_id：710412
        //        String host = "http://call.csp.test.sankuai.com";
        //++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++


        String completionUrl = "http://call.csp.test.sankuai.com/auto_call";
        String path = "/auto_call";
        String clientId = "139d700a8bf80707";
        String secret = "6c5aeaef805614e25b6f8f003842e48d";
        VoiceDTO voiceDTO = new VoiceDTO();
        voiceDTO.setTenant_id("eeae2e20-0aaa-11ea-aaf1-00223e9f3d04");
        voiceDTO.setApp_uuid("com.sankuai.waimai.poilogistics");
        voiceDTO.setApp_call_uuid(System.currentTimeMillis() + "");//业务唯一标记
        voiceDTO.setDest_num("18811598290");//拨打的手机号
        voiceDTO.setIvr_access_id("710412");
        MediaTxt txt = new MediaTxt();
        txt.setText1("这个是语音测试。");//填充语音播放内容文字
        voiceDTO.setMedia_txt(txt);
        System.out.println(JSONObject.toJSONString(voiceDTO));
        StringEntity stringEntity = new StringEntity(JSONObject.toJSONString(voiceDTO), "UTF-8");
        stringEntity.setContentEncoding("UTF-8");
        try {
            HttpPost post = new HttpPost(completionUrl);
            post.setEntity(stringEntity);
            Map<String, String> headers = genBaHeaders("post", path, clientId, secret);
            for (Entry<String, String> entry : headers.entrySet()) {
                post.addHeader(entry.getKey(), entry.getValue());
            }
            post.addHeader("Content-Type", "application/json");
            try (CloseableHttpClient httpClient = HttpClients.createDefault(); CloseableHttpResponse response = httpClient.execute(post)) {
                String result = EntityUtils.toString(response.getEntity());
                System.out.println(result);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static Map<String, String> genBaHeaders(String method, String path, String clientId, String secret) {
        Map<String, String> map = new HashMap<>();

        String date = toGMT();
        String data = method.toUpperCase() + " " + path + "\n" + date;
        String auth = genHMAC(data, secret);
        if (auth != null) {
            String authorization = "MWS " + clientId + ":" + auth;
            map.put("Date", date);
            map.put("Authorization", authorization);
            return map;
        } else {
            return null;
        }
    }

    public static String toGMT() {
        Calendar cd = Calendar.getInstance();
        SimpleDateFormat sdf = new SimpleDateFormat("EEE, dd MMM yyyy HH:mm:ss 'GMT'", Locale.US);
        sdf.setTimeZone(TimeZone.getTimeZone("GMT")); // 设置时区为GMT +8为北京时间东八区
        String str = sdf.format(cd.getTime());
        return str;
    }

    public static String genHMAC(String data, String key) {
        byte[] result = null;
        try {
            //根据给定的字节数组构造一个密钥,第二参数指定一个密钥算法的名称
            SecretKeySpec signinKey = new SecretKeySpec(key.getBytes(), HMAC_SHA1_ALGORITHM);
            //生成一个指定 Mac 算法 的 Mac 对象
            Mac mac = Mac.getInstance(HMAC_SHA1_ALGORITHM);
            //用给定密钥初始化 Mac 对象
            mac.init(signinKey);
            //完成 Mac 操作
            byte[] rawHmac = mac.doFinal(data.getBytes());
            result = Base64.encodeBase64(rawHmac);

        } catch (NoSuchAlgorithmException e) {
            System.err.println(e.getMessage());
        } catch (InvalidKeyException e) {
            System.err.println(e.getMessage());
        }
        if (null != result) {
            return new String(result);
        } else {
            return null;
        }
    }

}

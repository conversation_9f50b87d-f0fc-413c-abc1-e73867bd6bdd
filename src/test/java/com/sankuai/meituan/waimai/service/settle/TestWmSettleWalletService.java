//package com.sankuai.meituan.waimai.service.settle;
//
//import com.sankuai.meituan.waimai.BaseSpringJunit;
//import com.sankuai.meituan.waimai.BaseSpringTransactionJunit;
//import com.sankuai.meituan.waimai.BaseTest;
//import com.sankuai.meituan.waimai.thrift.customer.service.WmSettleWalletManagerThriftService;
//import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
//import org.apache.thrift.TException;
//import org.junit.Before;
//import org.junit.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//
//import java.util.List;
//
//public class TestWmSettleWalletService/*  extends BaseTest  */  extends BaseSpringTransactionJunit{
//
//    @Autowired
//    private WmSettleWalletManagerThriftService wmSettleWalletManagerThriftService;
//
////    @Before
////    public void before(){
////        this.setRemoteAppKey("com.sankuai.waimai.e.customer");
////        this.setRemoteServerPort(8444);
////        this.setEnv(ENV_DEV);
////        this.setRunMode(RUN_MODE.DEBUG_LOCAL_WITHSTART);
////        try{
////            wmSettleWalletManagerThriftService = (WmSettleWalletManagerThriftService)getObject(WmSettleWalletManagerThriftService.class);
////        }catch (Exception e){
////            e.printStackTrace();
////        }
////    }
//
//    @Test
//    public void testGetWalletIdListByWmPoiId() throws TException, WmCustomerException {
//        List<Long> list = wmSettleWalletManagerThriftService.getWalletIdListByWmPoiId(5105076);
////        List<Long> list = wmSettleWalletManagerThriftService.getWalletIdListByWmPoiId(10000484);
//        System.out.println("### list = " + list);
//    }
//}

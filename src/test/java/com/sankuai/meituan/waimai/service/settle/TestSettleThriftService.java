//package com.sankuai.meituan.waimai.service.settle;
//
//import com.alibaba.fastjson.JSONObject;
//import com.sankuai.meituan.waimai.BaseTest;
//import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettle;
//import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettleAudited;
//import com.sankuai.meituan.waimai.thrift.customer.service.WmSettleThriftService;
//import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
//import org.apache.thrift.TException;
//import org.junit.Before;
//import org.junit.Test;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
//import java.util.List;
//
//public class TestSettleThriftService extends BaseTest {
//
//    /*** 结算接口服务 */
//    private static WmSettleThriftService wmSettleThriftService;
//
//    private static Logger                logger = LoggerFactory.getLogger(TestSettleThriftService.class);
//
//    @Before
//    public void before() {
//        this.setRemoteAppKey("com.sankuai.waimai.e.customer");
//        this.setRemoteServerPort(8439);
//        this.setEnv(ENV_TEST);
//        this.setRunMode(RUN_MODE.REMOTE);
//        try {
//            wmSettleThriftService = (WmSettleThriftService) getObject(WmSettleThriftService.class);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
//
//    /**
//     * 根据客户id获取线上结算(包含门店信息)
//     */
//    @Test
//    public void testGetWmSettleAuditedByWmCustomerId() throws TException, WmCustomerException {
//
//        //setSwimlane("", "8246-dvlhy");
//
//        Integer customerId = 10014599;
//
//        List<WmSettleAudited> wmSettleAuditedList = wmSettleThriftService.getWmSettleAuditedByWmCustomerId(customerId);
//        logger.info("testGetWmSettleAuditedByWmCustomerId={}", JSONObject.toJSONString(wmSettleAuditedList, true));
//
//    }
//
//    /**
//     * 根据客户id获取线上结算(包含门店信息)
//     */
//    @Test
//    public void testGetWmSettleByWmCustomerId() throws TException, WmCustomerException {
//
//        //setSwimlane("", "8246-dvlhy");
//
//        Integer customerId = 11758628;
//
//        List<WmSettle> wmSettleList = wmSettleThriftService.getWmSettleByWmCustomerId(customerId);
//        logger.info("testGetWmSettleAuditedByWmCustomerId={}", JSONObject.toJSONString(wmSettleList, true));
//
//    }
//}

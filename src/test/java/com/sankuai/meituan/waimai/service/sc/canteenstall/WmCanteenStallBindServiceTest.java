package com.sankuai.meituan.waimai.service.sc.canteenstall;

import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.adapter.WdcRelationServiceAdapter;
import com.sankuai.meituan.waimai.customer.adapter.WmHighseasThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.adapter.WmScEmployAdaptor;
import com.sankuai.meituan.waimai.customer.dao.sc.canteenstall.WmCanteenStallBindMapper;
import com.sankuai.meituan.waimai.customer.dao.sc.canteenstall.WmCanteenStallClueMapper;
import com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallBindDO;
import com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallClueDO;
import com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallWmPoiBindBO;
import com.sankuai.meituan.waimai.customer.service.sc.canteenstall.WmCanteenStallBindService;
import com.sankuai.meituan.waimai.customer.service.sc.canteenstall.WmCanteenStallManageService;
import com.sankuai.meituan.waimai.customer.service.sc.canteenstall.check.WmCanteenStallCheckService;
import com.sankuai.meituan.waimai.customer.service.sc.canteenstall.status.WmCanteenStallClueBindService;
import com.sankuai.meituan.waimai.customer.service.sc.canteenstall.status.WmCanteenStallClueGenerateService;
import com.sankuai.meituan.waimai.customer.service.sc.canteenstall.status.WmCanteenStallPoiBindService;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import com.sankuai.meituan.waimai.poibaseinfo.thrift.domain.mq.PoiProcessEventMsgBody;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.canteenstall.CanteenStallClueBindStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.canteenstall.CanteenStallClueGenerateStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.canteenstall.CanteenStallWmPoiBindStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.canteenstall.WmCanteenStallBindSubmitDTO;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import com.sankuai.meituan.wdc.poi.flow.action.common.vo.result.ErrorEnum;
import com.sankuai.meituan.wdc.poi.flow.action.common.vo.result.FlowError;
import com.sankuai.meituan.wdc.poi.flow.action.common.vo.result.OuterPoiCreateResult;
import com.sankuai.meituan.wdc.poi.flow.action.common.vo.result.ResultEnum;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiHighSeasPoiInfo;
import org.apache.thrift.TException;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

public class WmCanteenStallBindServiceTest {
    @InjectMocks
    private WmCanteenStallBindService wmCanteenStallBindService;

    @Mock
    private WmCanteenStallBindMapper wmCanteenStallBindMapper;

    @Mock
    private WmCanteenStallClueGenerateService wmCanteenStallClueGenerateService;

    @Mock
    private WmCanteenStallClueBindService wmCanteenStallClueBindService;

    @Mock
    private WmHighseasThriftServiceAdapter wmHighseasThriftServiceAdapter;

    @Mock
    private WdcRelationServiceAdapter wdcRelationServiceAdapter;

    @Mock
    private WmCanteenStallPoiBindService wmCanteenStallPoiBindService;

    @Mock
    private WmScEmployAdaptor wmScEmployAdaptor;

    @Mock
    private WmCanteenStallClueMapper wmCanteenStallClueMapper;

    @Mock
    private WmCanteenStallCheckService wmCanteenStallCheckService;

    @Mock
    private WmCanteenStallManageService wmCanteenStallManageService;


    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 分支：1-创建成功(回写线索生成状态并分配线索跟进人)
     * 测试创建成功的情况
     */
    @Test
    public void testHandleWdcClueCreateResultSuccess() throws Throwable {
        // arrange
        OuterPoiCreateResult createResult = new OuterPoiCreateResult();
        createResult.setStatus(ResultEnum.SUCCESS);
        createResult.setExtra("1");

        // act
        wmCanteenStallBindService.handleWdcClueCreateResult(createResult);

        // assert
        verify(wmCanteenStallClueGenerateService, times(1)).generateClueSuccess(createResult);
    }

    /**
     * 分支：2-档口绑定任务的线索生成状态是否为"生成中"
     * 测试创建失败，但线索生成状态不为"生成中"的情况
     */
    @Test
    public void testHandleWdcClueCreateResultNotGenerating() throws Throwable {
        // arrange
        OuterPoiCreateResult createResult = new OuterPoiCreateResult();
        createResult.setStatus(ResultEnum.FAIL);
        createResult.setExtra("123");
        WmCanteenStallBindDO bindDO = new WmCanteenStallBindDO();
        // 非生成中状态
        bindDO.setClueGenerateStatus((int) CanteenStallClueGenerateStatusEnum.GENERATE_FAIL.getType());
        ErrorEnum errorEnum = ErrorEnum.SIMILAR_ERROR;

        when(wmCanteenStallBindMapper.selectByPrimaryKey(anyInt())).thenReturn(bindDO);

        // act
        wmCanteenStallBindService.handleWdcClueCreateResult(createResult);

        // assert
        verify(wmCanteenStallBindMapper, times(1)).selectByPrimaryKey(anyInt());
        verify(wmCanteenStallClueGenerateService, never()).generateClueFail(bindDO, errorEnum);
        verify(wmCanteenStallClueGenerateService, never()).generateClueSuccessWithSimilarClueId(createResult, bindDO);
    }

    /**
     * 分支：3-创建失败原因是否为"重复创建"
     * 测试创建失败，失败原因不是"重复创建"
     */
//    @Test
//    public void testHandleWdcClueCreateResultNotSimilarError() throws Throwable {
//        // arrange
//        OuterPoiCreateResult createResult = new OuterPoiCreateResult();
//        createResult.setStatus(ResultEnum.FAIL);
//        FlowError flowError = new FlowError();
//        flowError.setErrorEnum(ErrorEnum.ADDRESS_VERIFY_ERROR);
//        flowError.setMessage("");
//        createResult.setError(flowError);
//        createResult.setWdcSimilarId(1L);
//        createResult.setExtra("1");
//
//        WmCanteenStallBindDO bindDO = new WmCanteenStallBindDO();
//        // 线索生成状态=生成中
//        bindDO.setClueGenerateStatus((int) CanteenStallClueGenerateStatusEnum.GENERATING.getType());
//        ErrorEnum errorEnum = ErrorEnum.SIMILAR_ERROR;
//        when(wmCanteenStallBindMapper.selectByPrimaryKey(anyInt())).thenReturn(bindDO);
//
//        // act
//        wmCanteenStallBindService.handleWdcClueCreateResult(createResult);
//
//        // assert
//        verify(wmCanteenStallClueGenerateService, times(1)).generateClueFail(bindDO, errorEnum);
//    }


    /**
     * 分支：4-相似线索的认领上单状态是否为"未认领"
     * 测试创建失败，失败原因为"重复创建"，但相似线索认领上单状态不为"未认领"的情况
     */
    @Test
    public void testHandleWdcClueCreateResultSimilarErrorNotUnclaimed() throws Throwable {
        // arrange
        OuterPoiCreateResult createResult = new OuterPoiCreateResult();
        createResult.setStatus(ResultEnum.FAIL);
        FlowError flowError = new FlowError();
        flowError.setErrorEnum(ErrorEnum.SIMILAR_ERROR);
        flowError.setMessage("");
        createResult.setError(flowError);
        createResult.setWdcSimilarId(1L);
        createResult.setExtra("1");

        WmCanteenStallBindDO bindDO = new WmCanteenStallBindDO();
        // 线索生成状态=生成中
        bindDO.setClueGenerateStatus((int) CanteenStallClueGenerateStatusEnum.GENERATING.getType());

        WmPoiHighSeasPoiInfo wdcClueInfo = new WmPoiHighSeasPoiInfo();
        // 相似线索认领状态=已认领
        wdcClueInfo.setClaimStatus(1);
        ErrorEnum errorEnum = ErrorEnum.SIMILAR_ERROR;

        when(wmCanteenStallBindMapper.selectByPrimaryKey(anyInt())).thenReturn(bindDO);
        when(wmHighseasThriftServiceAdapter.getWdcClueInfoByWdcClueId(anyLong())).thenReturn(wdcClueInfo);

        // act
        wmCanteenStallBindService.handleWdcClueCreateResult(createResult);

        // assert
        verify(wmCanteenStallClueGenerateService, times(1)).generateClueFail(bindDO, errorEnum);
    }

    /**
     * 分支：5-相似线索是否已有线索绑定状态为"绑定中"/"绑定成功"的档口绑定任务
     * 测试创建失败，失败原因为"重复创建"，相似线索认领上单状态为"未认领"，但相似线索已有绑定中/绑定成功的档口绑定任务的情况
     */
    @Test
    public void testHandleWdcClueCreateResultSimilarErrorWithBindingOrBindSuccess() throws Throwable {
        // arrange
        OuterPoiCreateResult createResult = new OuterPoiCreateResult();
        createResult.setStatus(ResultEnum.FAIL);
        FlowError flowError = new FlowError();
        flowError.setErrorEnum(ErrorEnum.SIMILAR_ERROR);
        flowError.setMessage("");
        createResult.setError(flowError);
        createResult.setWdcSimilarId(1L);
        createResult.setExtra("1");

        WmCanteenStallBindDO bindDO = new WmCanteenStallBindDO();
        // 线索生成状态=生成中
        bindDO.setClueGenerateStatus((int) CanteenStallClueGenerateStatusEnum.GENERATING.getType());

        WmPoiHighSeasPoiInfo wdcClueInfo = new WmPoiHighSeasPoiInfo();
        // 相似线索认领状态=已认领
        wdcClueInfo.setClaimStatus(0);
        ErrorEnum errorEnum = ErrorEnum.SIMILAR_ERROR;

        // 模拟已有绑定中/绑定成功的档口绑定任务
        WmCanteenStallBindDO bindDOSimilar = new WmCanteenStallBindDO();

        when(wmCanteenStallBindMapper.selectByPrimaryKey(anyInt())).thenReturn(bindDO);
        when(wmHighseasThriftServiceAdapter.getWdcClueInfoByWdcClueId(anyLong())).thenReturn(wdcClueInfo);
        when(wmCanteenStallBindMapper.selectByWdcClueIdWithClueBindingOrBindSuccess(anyLong())).thenReturn(bindDOSimilar);

        // act
        wmCanteenStallBindService.handleWdcClueCreateResult(createResult);

        // assert
        verify(wmCanteenStallClueGenerateService, times(1)).generateClueFail(bindDO, errorEnum);
    }


    /**
     * 分支：6-相似线索是否已有和该食堂的档口绑定任务
     * 测试创建失败，失败原因为"重复创建"，相似线索认领上单状态为"未认领"，相似线索没有绑定中/绑定成功的档口绑定任务，但已有和该食堂的档口绑定任务情况
     */
//    @Test
//    public void testHandleWdcClueCreateResultSimilarErrorWithBindWithCanteen() throws Throwable {
//        // arrange
//        OuterPoiCreateResult createResult = new OuterPoiCreateResult();
//        createResult.setStatus(ResultEnum.FAIL);
//        FlowError flowError = new FlowError();
//        flowError.setErrorEnum(ErrorEnum.SIMILAR_ERROR);
//        flowError.setMessage("");
//        createResult.setError(flowError);
//        createResult.setWdcSimilarId(1L);
//        createResult.setExtra("1");
//
//        WmCanteenStallBindDO bindDO = new WmCanteenStallBindDO();
//        // 线索生成状态=生成中
//        bindDO.setClueGenerateStatus((int) CanteenStallClueGenerateStatusEnum.GENERATING.getType());
//
//        WmPoiHighSeasPoiInfo wdcClueInfo = new WmPoiHighSeasPoiInfo();
//        // 相似线索认领状态=已认领
//        wdcClueInfo.setClaimStatus(0);
//        WmCanteenStallBindDO bindDOWithCanteenPrimaryId = new WmCanteenStallBindDO();
//        ErrorEnum errorEnum = ErrorEnum.SIMILAR_ERROR;
//
//        when(wmCanteenStallBindMapper.selectByPrimaryKey(anyInt())).thenReturn(bindDO);
//        when(wmHighseasThriftServiceAdapter.getWdcClueInfoByWdcClueId(anyLong())).thenReturn(wdcClueInfo);
//        when(wmCanteenStallBindMapper.selectByWdcClueIdWithClueBindingOrBindSuccess(anyLong())).thenReturn(null);
//        when(wmCanteenStallBindMapper.selectByWdcClueIdAndCanteenPrimaryId(anyLong(),anyInt())).thenReturn(bindDOWithCanteenPrimaryId);
//
//        // act
//        wmCanteenStallBindService.handleWdcClueCreateResult(createResult);
//
//        // assert
//        verify(wmCanteenStallClueGenerateService, times(1)).generateClueFail(bindDO, errorEnum);
//    }


    /**
     * 分支：7-回写相似线索ID+线索生成状态并分配线索跟进人 & 8-线索绑定状态为"绑定中"则触发线索绑定流程
     * 测试创建失败，失败原因为"重复创建"，相似线索认领上单状态为"未认领"，相似线索没有绑定中/绑定成功的档口绑定任务，和该食堂无档口绑定任务情况
     */
    @Test
    public void testHandleWdcClueCreateResultSimilarErrorWithGenerateSuccess() throws Throwable {
        // arrange
        OuterPoiCreateResult createResult = new OuterPoiCreateResult();
        createResult.setStatus(ResultEnum.FAIL);
        FlowError flowError = new FlowError();
        flowError.setErrorEnum(ErrorEnum.SIMILAR_ERROR);
        flowError.setMessage("");
        createResult.setError(flowError);
        createResult.setWdcSimilarId(1L);
        createResult.setExtra("1");

        WmCanteenStallBindDO bindDO = new WmCanteenStallBindDO();
        // 线索生成状态=生成中
        bindDO.setClueGenerateStatus((int) CanteenStallClueGenerateStatusEnum.GENERATING.getType());
        bindDO.setClueBindStatus((int) CanteenStallClueBindStatusEnum.BINDING.getType());

        WmPoiHighSeasPoiInfo wdcClueInfo = new WmPoiHighSeasPoiInfo();
        // 相似线索认领状态=已认领
        wdcClueInfo.setClaimStatus(0);
        ErrorEnum errorEnum = ErrorEnum.SIMILAR_ERROR;

        when(wmCanteenStallBindMapper.selectByPrimaryKey(anyInt())).thenReturn(bindDO);
        when(wmHighseasThriftServiceAdapter.getWdcClueInfoByWdcClueId(anyLong())).thenReturn(wdcClueInfo);
        when(wmCanteenStallBindMapper.selectByWdcClueIdWithClueBindingOrBindSuccess(anyLong())).thenReturn(null);
        when(wmCanteenStallBindMapper.selectByWdcClueIdAndCanteenPrimaryId(anyLong(),anyInt())).thenReturn(null);

        // act
        wmCanteenStallBindService.handleWdcClueCreateResult(createResult);

        // assert
        verify(wmCanteenStallClueGenerateService, never()).generateClueFail(bindDO, errorEnum);
        verify(wmCanteenStallClueGenerateService, times(1)).generateClueSuccessWithSimilarClueId(createResult, bindDO);
        verify(wmCanteenStallClueBindService, times(1)).bindWdcClue(bindDO);
    }

    /**
     * 测试handlePoiCreateResult方法，当wdcClueId为null时抛出WmSchCantException异常
     */
    @Test(expected = WmSchCantException.class)
    public void testHandlePoiCreateResult_WhenWdcClueIdIsNull() throws Throwable {
        // arrange
        PoiProcessEventMsgBody msgBody = new PoiProcessEventMsgBody();
        msgBody.setWmPoiId(1L);
        when(wdcRelationServiceAdapter.getWdcClueIdByWmPoiId(anyLong())).thenReturn(null);

        // act
        wmCanteenStallBindService.handlePoiCreateResult(msgBody);

        // assert is handled by the expected exception
    }

    /**
     * 测试handlePoiCreateResult方法，当bindDO为null时不进行任何操作
     */
    @Test
    public void testHandlePoiCreateResult_WhenBindDOIsNull() throws Throwable {
        // arrange
        PoiProcessEventMsgBody msgBody = new PoiProcessEventMsgBody();
        msgBody.setWmPoiId(1L);
        when(wdcRelationServiceAdapter.getWdcClueIdByWmPoiId(anyLong())).thenReturn(1L);
        when(wmCanteenStallBindMapper.selectByWdcClueIdWithClueBindSuccess(anyLong())).thenReturn(null);

        // act
        wmCanteenStallBindService.handlePoiCreateResult(msgBody);

        // assert
        verify(wmCanteenStallPoiBindService, never()).bindCanteenPoiByPoiCreate(any(WmCanteenStallWmPoiBindBO.class));
    }

    /**
     * 测试handlePoiCreateResult方法，正常情况下调用bindCanteenPoiByPoiCreate方法
     */
    @Test
    public void testHandlePoiCreateResult_Normal() throws Throwable {
        // arrange
        PoiProcessEventMsgBody msgBody = new PoiProcessEventMsgBody();
        msgBody.setWmPoiId(1L);
        msgBody.setOperateUid(1L);
        WmCanteenStallBindDO bindDO = new WmCanteenStallBindDO();
        when(wdcRelationServiceAdapter.getWdcClueIdByWmPoiId(anyLong())).thenReturn(1L);
        when(wmCanteenStallBindMapper.selectByWdcClueIdWithClueBindSuccess(anyLong())).thenReturn(bindDO);

        WmEmploy wmEmploy = new WmEmploy();
        wmEmploy.setName("TestName");
        wmEmploy.setMisId("TestMisId");
        when(wmScEmployAdaptor.getWmEmployByUid(anyInt())).thenReturn(wmEmploy);

        // act
        wmCanteenStallBindService.handlePoiCreateResult(msgBody);

        // assert
        verify(wmCanteenStallPoiBindService, times(1)).bindCanteenPoiByPoiCreate(any(WmCanteenStallWmPoiBindBO.class));
    }

    /**
     * 测试管理ID为null时抛出异常
     */
    @Test(expected = WmSchCantException.class)
    public void testCreateStallBindListByManageId_ManageIdIsNull() throws WmSchCantException, TException {
        // arrange
        Integer manageId = null;
        Integer userId = 1;
        String userName = "testUser";

        // act
        wmCanteenStallBindService.createStallBindListByManageId(manageId, userId, userName);

        // assert
        // Expected exception
    }

    /**
     * 测试管理ID小于等于0时抛出异常
     */
    @Test(expected = WmSchCantException.class)
    public void testCreateStallBindListByManageId_ManageIdIsInvalid() throws WmSchCantException, TException {
        // arrange
        Integer manageId = 0;
        Integer userId = 1;
        String userName = "testUser";

        // act
        wmCanteenStallBindService.createStallBindListByManageId(manageId, userId, userName);

        // assert
        // Expected exception
    }

    /**
     * 测试正常情况
     */
    @Test
    public void testCreateStallBindListByManageId_Normal() throws WmSchCantException, TException {
        // arrange
        Integer manageId = 1;
        Integer userId = 1;
        String userName = "testUser";
        List<WmCanteenStallClueDO> clueDOList = new ArrayList<>();
        clueDOList.add(new WmCanteenStallClueDO());

        when(wmCanteenStallClueMapper.selectByManageId(manageId)).thenReturn(clueDOList);
        doNothing().when(wmCanteenStallCheckService).checkSubmitByBatchCreateClue(manageId);
        doNothing().when(wmCanteenStallManageService).updateStallManageSubmitStatusToSubmitted(manageId);

        // act
        wmCanteenStallBindService.createStallBindListByManageId(manageId, userId, userName);

        // assert
        verify(wmCanteenStallClueMapper, times(1)).selectByManageId(manageId);
        verify(wmCanteenStallCheckService, times(1)).checkSubmitByBatchCreateClue(manageId);
        verify(wmCanteenStallManageService, times(1)).updateStallManageSubmitStatusToSubmitted(manageId);
    }


    /**
     * 测试存在档口绑定任务时更新线索生成状态为"生成成功"
     */
    @Test
    public void testUpsertStallBindWithClueGenerateSuccessByWmPoiBind_ExistingBind() throws WmSchCantException {
        // arrange
        WmCanteenStallBindSubmitDTO submitDTO = new WmCanteenStallBindSubmitDTO();
        submitDTO.setCanteenPrimaryId(1);
        submitDTO.setUserId(100);
        Long wdcClueId = 123L;
        WmCanteenStallBindDO existingBindDO = new WmCanteenStallBindDO();
        when(wmCanteenStallBindMapper.selectByWdcClueIdAndCanteenPrimaryId(wdcClueId, submitDTO.getCanteenPrimaryId())).thenReturn(existingBindDO);
        when(wmCanteenStallClueGenerateService.updateClueGenerateStatusToSuccess(existingBindDO, submitDTO, "外卖门店绑定")).thenReturn(existingBindDO);

        // act
        WmCanteenStallBindDO result = wmCanteenStallBindService.upsertStallBindWithClueGenerateSuccessByWmPoiBind(wdcClueId, submitDTO);

        // assert
        verify(wmCanteenStallBindMapper, times(1)).selectByWdcClueIdAndCanteenPrimaryId(wdcClueId, submitDTO.getCanteenPrimaryId());
        verify(wmCanteenStallClueGenerateService, times(1)).updateClueGenerateStatusToSuccess(existingBindDO, submitDTO, "外卖门店绑定");
        assertSame(existingBindDO, result);
    }

    /**
     * 测试场景：食堂取消合作，食堂关联的档口绑定任务为空
     */
    @Test
    public void testCancelCooperation_BindDOListIsEmpty() throws WmSchCantException {
        // arrange
        Integer canteenPrimaryId = 1;
        Integer userId = 2;
        String userName = "user";
        String operationSource = "食堂取消合作";
        when(wmCanteenStallBindMapper.selectByCanteenPrimaryId(canteenPrimaryId)).thenReturn(Collections.emptyList());

        // act
        wmCanteenStallBindService.cancelCooperation(canteenPrimaryId, userId, userName, operationSource);

        // assert
        verify(wmCanteenStallBindMapper, times(1)).selectByCanteenPrimaryId(canteenPrimaryId);
        verifyNoMoreInteractions(wmCanteenStallClueBindService);
        verifyNoMoreInteractions(wmCanteenStallPoiBindService);
    }


    /**
     * 测试场景：食堂取消合作，存在绑定中的线索，但无绑定成功的外卖门店
     */
    @Test
    public void testCancelCooperation_BindInProgressNoPoiBindSuccess() throws WmSchCantException {
        // arrange
        Integer canteenPrimaryId = 1;
        Integer userId = 2;
        String userName = "user";
        String operationSource = "食堂取消合作";
        WmCanteenStallBindDO bindDO = new WmCanteenStallBindDO();
        bindDO.setClueBindStatus((int) CanteenStallClueBindStatusEnum.BINDING.getType());
        bindDO.setWmPoiBindStatus((int) CanteenStallWmPoiBindStatusEnum.BIND_FAIL.getType());
        bindDO.setWdcClueId(1L);
        List<WmCanteenStallBindDO> bindDOList = Collections.singletonList(bindDO);
        when(wmCanteenStallBindMapper.selectByCanteenPrimaryId(canteenPrimaryId)).thenReturn(bindDOList);

        // act
        wmCanteenStallBindService.cancelCooperation(canteenPrimaryId, userId, userName, operationSource);

        // assert
        verify(wmCanteenStallBindMapper, times(1)).selectByCanteenPrimaryId(canteenPrimaryId);
        verify(wmCanteenStallClueBindService, times(1)).unbindWdcClue(bindDO, userId, userName, operationSource);
        verifyNoMoreInteractions(wmCanteenStallPoiBindService);
    }


    /**
     * 测试场景：食堂取消合作，存在绑定成功的外卖门店，但无绑定中或绑定成功的线索
     */
    @Test
    public void testCancelCooperation_PoiBindSuccessNoClueBindInProgressOrSuccess() throws WmSchCantException {
        // arrange
        Integer canteenPrimaryId = 1;
        Integer userId = 2;
        String userName = "user";
        String operationSource = "食堂取消合作";
        WmCanteenStallBindDO bindDO = new WmCanteenStallBindDO();
        bindDO.setClueBindStatus((int) CanteenStallClueBindStatusEnum.BIND_FAIL.getType());
        bindDO.setWmPoiBindStatus((int) CanteenStallWmPoiBindStatusEnum.BIND_SUCCESS.getType());
        bindDO.setWmPoiId(1L);
        List<WmCanteenStallBindDO> bindDOList = Collections.singletonList(bindDO);
        when(wmCanteenStallBindMapper.selectByCanteenPrimaryId(canteenPrimaryId)).thenReturn(bindDOList);

        // act
        wmCanteenStallBindService.cancelCooperation(canteenPrimaryId, userId, userName, operationSource);

        // assert
        verify(wmCanteenStallBindMapper, times(1)).selectByCanteenPrimaryId(canteenPrimaryId);
        verifyNoMoreInteractions(wmCanteenStallClueBindService);
        verify(wmCanteenStallPoiBindService, times(1)).unbindCanteenPoi(any(WmCanteenStallWmPoiBindBO.class));
    }

    /**
     * 测试bindId为空的情况
     */
    @Test(expected = WmSchCantException.class)
    public void testUnbindClueAndWmPoiByBindId_WithNullBindId() throws Throwable {
        // arrange
        Integer bindId = null;
        Integer userId = 1;
        String userName = "testUser";

        // act
        wmCanteenStallBindService.unbindClueAndWmPoiByBindId(bindId, userId, userName);

        // assert is handled by the expected exception
    }

    /**
     * 测试bindId小于等于0的情况
     */
    @Test(expected = WmSchCantException.class)
    public void testUnbindClueAndWmPoiByBindId_WithInvalidBindId() throws Throwable {
        // arrange
        Integer bindId = 0;
        Integer userId = 1;
        String userName = "testUser";

        // act
        wmCanteenStallBindService.unbindClueAndWmPoiByBindId(bindId, userId, userName);

        // assert is handled by the expected exception
    }

    /**
     * 测试bindDO为null的情况
     */
    @Test(expected = WmSchCantException.class)
    public void testUnbindClueAndWmPoiByBindId_WithNullBindDO() throws Throwable {
        // arrange
        Integer bindId = 1;
        Integer userId = 1;
        String userName = "testUser";
        when(wmCanteenStallBindMapper.selectByPrimaryKey(bindId)).thenReturn(null);

        // act
        wmCanteenStallBindService.unbindClueAndWmPoiByBindId(bindId, userId, userName);

        // assert is handled by the expected exception
    }

    /**
     * 测试档口绑定任务未绑定，无需解绑的情况
     */
    @Test(expected = WmSchCantException.class)
    public void testUnbindClueAndWmPoiByBindId_WithBindDONotBind() throws Throwable {
        // arrange
        Integer bindId = 1;
        Integer userId = 1;
        String userName = "testUser";
        WmCanteenStallBindDO bindDO = new WmCanteenStallBindDO();
        bindDO.setClueBindStatus((int) CanteenStallClueBindStatusEnum.UNBIND.getType());
        bindDO.setWmPoiBindStatus((int) CanteenStallWmPoiBindStatusEnum.UNBIND.getType());
        when(wmCanteenStallBindMapper.selectByPrimaryKey(bindId)).thenReturn(bindDO);

        // act
        wmCanteenStallBindService.unbindClueAndWmPoiByBindId(bindId, userId, userName);

        // assert is handled by the expected exception
    }

    /**
     * 测试解绑线索和外卖门店，线索和外卖门店均为绑定成功状态
     */
    @Test
    public void testUnbindClueAndWmPoi_BothBindSuccess() throws Throwable {
        // arrange
        Integer userId = 1;
        String userName = "testUser";
        String operationSource = "testOperation";
        WmCanteenStallBindDO bindDO = new WmCanteenStallBindDO();
        bindDO.setClueBindStatus((int) CanteenStallClueBindStatusEnum.BIND_SUCCESS.getType());
        bindDO.setWdcClueId(1L);
        bindDO.setWmPoiBindStatus((int) CanteenStallWmPoiBindStatusEnum.BIND_SUCCESS.getType());
        bindDO.setWmPoiId(1L);

        // act
        wmCanteenStallBindService.unbindClueAndWmPoi(bindDO, userId, userName, operationSource);

        // assert
        verify(wmCanteenStallClueBindService, times(1)).unbindWdcClue(any(WmCanteenStallBindDO.class), eq(userId), eq(userName), eq(operationSource));
        verify(wmCanteenStallPoiBindService, times(1)).unbindCanteenPoi(any());
    }

    /**
     * 测试解绑线索和外卖门店，线索绑定成功但外卖门店未绑定
     */
    @Test
    public void testUnbindClueAndWmPoi_ClueBindSuccessPoiUnBind() throws Throwable {
        // arrange
        WmCanteenStallBindDO bindDO = new WmCanteenStallBindDO();
        bindDO.setClueBindStatus((int) CanteenStallClueBindStatusEnum.BIND_SUCCESS.getType());
        bindDO.setWdcClueId(1L);
        bindDO.setWmPoiBindStatus((int) CanteenStallWmPoiBindStatusEnum.UNBIND.getType());
        bindDO.setWmPoiId(1L);
        Integer userId = 1;
        String userName = "testUser";
        String operationSource = "testOperation";

        // act
        wmCanteenStallBindService.unbindClueAndWmPoi(bindDO, userId, userName, operationSource);

        // assert
        verify(wmCanteenStallClueBindService, times(1)).unbindWdcClue(any(WmCanteenStallBindDO.class), eq(userId), eq(userName), eq(operationSource));
        verify(wmCanteenStallPoiBindService, never()).unbindCanteenPoi(any());
    }


    /**
     * 测试解绑线索和外卖门店，线索未绑定但外卖门店绑定成功
     */
    @Test
    public void testUnbindClueAndWmPoi_ClueNotBoundPoiBindSuccess() throws Throwable {
        // arrange
        WmCanteenStallBindDO bindDO = new WmCanteenStallBindDO();
        bindDO.setClueBindStatus((int) CanteenStallClueBindStatusEnum.UNBIND.getType());
        bindDO.setWdcClueId(1L);
        bindDO.setWmPoiBindStatus((int) CanteenStallWmPoiBindStatusEnum.BIND_SUCCESS.getType());
        bindDO.setWmPoiId(1L);
        Integer userId = 1;
        String userName = "testUser";
        String operationSource = "testOperation";

        // act
        wmCanteenStallBindService.unbindClueAndWmPoi(bindDO, userId, userName, operationSource);

        // assert
        verify(wmCanteenStallClueBindService, never()).unbindWdcClue(any(WmCanteenStallBindDO.class), anyInt(), anyString(), anyString());
        verify(wmCanteenStallPoiBindService, times(1)).unbindCanteenPoi(any());
    }

    /**
     * 测试解绑线索和外卖门店，线索和外卖门店均未绑定
     */
    @Test
    public void testUnbindClueAndWmPoi_BothUnBind() throws Throwable {
        // arrange
        WmCanteenStallBindDO bindDO = new WmCanteenStallBindDO();
        bindDO.setClueBindStatus((int) CanteenStallClueBindStatusEnum.UNBIND.getType());
        bindDO.setWdcClueId(1L);
        bindDO.setWmPoiBindStatus((int) CanteenStallWmPoiBindStatusEnum.UNBIND.getType());
        bindDO.setWmPoiId(1L);
        Integer userId = 1;
        String userName = "testUser";
        String operationSource = "testOperation";

        // act
        wmCanteenStallBindService.unbindClueAndWmPoi(bindDO, userId, userName, operationSource);

        // assert
        verify(wmCanteenStallClueBindService, never()).unbindWdcClue(any(WmCanteenStallBindDO.class), anyInt(), anyString(), anyString());
        verify(wmCanteenStallPoiBindService, never()).unbindCanteenPoi(any());
    }

    /**
     * 测试传入空的档口绑定任务列表
     */
    @Test
    public void testGetStallBindListWithClueNotInSchoolArea_EmptyBindDOList() throws Throwable {
        // arrange
        List<WmCanteenStallBindDO> bindDOList = new ArrayList<>();
        List<String> schoolAreaList = Arrays.asList("area1", "area2");

        // act
        List<WmCanteenStallBindDO> result = wmCanteenStallBindService.getStallBindListWithClueNotInSchoolArea(bindDOList, schoolAreaList);

        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(wmHighseasThriftServiceAdapter, never()).getWdcClueListByWdcCludIdList(Arrays.asList(1L, 2L));
    }

    /**
     * 测试线索坐标不在学校范围内的档口绑定任务，正常情况
     */
    @Test
    public void testGetStallBindListWithClueNotInSchoolArea_Normal() throws Throwable {
        // arrange
        List<WmCanteenStallBindDO> bindDOList = Arrays.asList(
                WmCanteenStallBindDO.builder().wdcClueId(1L).build(),
                WmCanteenStallBindDO.builder().wdcClueId(2L).build()
        );

        int poiLatitude = 39731053;
        int poiLongitude = 116171295;

        String area1 = "[{\"x\":39731301,\"y\":116167209},{\"x\":39739058,\"y\":116167085},{\"x\":39739173,\"y\":116170825},{\"x\":39738925,\"y\":116172097},{\"x\":39738634,\"y\":116172355},{\"x\":39731265,\"y\":116172370}]";
        String area2 = "[{\"x\":39736231,\"y\":116172715},{\"x\":39736223,\"y\":116174954},{\"x\":39734014,\"y\":116174943},{\"x\":39734031,\"y\":116172705}]";
        String area3 = "[{\"x\":39728305,\"y\":116172381},{\"x\":39725795,\"y\":116172502},{\"x\":39725810,\"y\":116166934},{\"x\":39728274,\"y\":116167108},{\"x\":39730811,\"y\":116166962},{\"x\":39730854,\"y\":116169725},{\"x\":39730815,\"y\":116172488}]";
        List<String> schoolAreaList = Lists.newArrayList(area1, area2, area3);
        List<WmPoiHighSeasPoiInfo> poiInfoList = Arrays.asList(
                new WmPoiHighSeasPoiInfo(1L, "point1", 0, "", 0, 0, "", 0L, "", poiLatitude, poiLongitude, 0, 0, 0, 0, 0, null, 0, null, 0),
                new WmPoiHighSeasPoiInfo(2L, "point2", 0, "", 0, 0, "", 0L, "", poiLatitude, poiLongitude, 0, 0, 0, 0, 0, null, 0, null, 0)
        );
        when(wmHighseasThriftServiceAdapter.getWdcClueListByWdcCludIdList(Lists.newArrayList(1L, 2L))).thenReturn(poiInfoList);

        // act
        List<WmCanteenStallBindDO> result = wmCanteenStallBindService.getStallBindListWithClueNotInSchoolArea(bindDOList, schoolAreaList);

        // assert
        assertNotNull(result);
        assertEquals(2, result.size());
        verify(wmHighseasThriftServiceAdapter, times(1)).getWdcClueListByWdcCludIdList(Lists.newArrayList(1L, 2L));
    }

    /**
     * 测试线索坐标全部在学校范围内的情况
     */
    @Test
    public void testGetStallBindListWithClueNotInSchoolArea_AllInSchoolArea() throws Throwable {
        // arrange
        List<WmCanteenStallBindDO> bindDOList = Arrays.asList(
                WmCanteenStallBindDO.builder().wdcClueId(1L).build(),
                WmCanteenStallBindDO.builder().wdcClueId(2L).build()
        );

        int poiLatitude = 39730512;
        int poiLongitude = 116172156;

        String area1 = "[{\"x\":39731301,\"y\":116167209},{\"x\":39739058,\"y\":116167085},{\"x\":39739173,\"y\":116170825},{\"x\":39738925,\"y\":116172097},{\"x\":39738634,\"y\":116172355},{\"x\":39731265,\"y\":116172370}]";
        String area2 = "[{\"x\":39736231,\"y\":116172715},{\"x\":39736223,\"y\":116174954},{\"x\":39734014,\"y\":116174943},{\"x\":39734031,\"y\":116172705}]";
        String area3 = "[{\"x\":39728305,\"y\":116172381},{\"x\":39725795,\"y\":116172502},{\"x\":39725810,\"y\":116166934},{\"x\":39728274,\"y\":116167108},{\"x\":39730811,\"y\":116166962},{\"x\":39730854,\"y\":116169725},{\"x\":39730815,\"y\":116172488}]";
        List<String> schoolAreaList = Lists.newArrayList(area1, area2, area3);
        List<WmPoiHighSeasPoiInfo> poiInfoList = Arrays.asList(
                new WmPoiHighSeasPoiInfo(1L, "point1", 0, "", 0, 0, "", 0L, "", poiLatitude, poiLongitude, 0, 0, 0, 0, 0, null, 0, null, 0),
                new WmPoiHighSeasPoiInfo(2L, "point2", 0, "", 0, 0, "", 0L, "", poiLatitude, poiLongitude, 0, 0, 0, 0, 0, null, 0, null, 0)
        );
        when(wmHighseasThriftServiceAdapter.getWdcClueListByWdcCludIdList(Lists.newArrayList(1L, 2L))).thenReturn(poiInfoList);

        // act
        List<WmCanteenStallBindDO> result = wmCanteenStallBindService.getStallBindListWithClueNotInSchoolArea(bindDOList, schoolAreaList);

        // assert
        assertEquals(0, result.size());
        verify(wmHighseasThriftServiceAdapter, times(1)).getWdcClueListByWdcCludIdList(Lists.newArrayList(1L, 2L));
    }




}

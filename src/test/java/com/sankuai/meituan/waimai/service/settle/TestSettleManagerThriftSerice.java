//package com.sankuai.meituan.waimai.service.settle;
//
//import com.alibaba.fastjson.JSON;
//import com.sankuai.meituan.waimai.BaseTest;
//import com.sankuai.meituan.waimai.thrift.customer.domain.contract.ContractVersionPageData;
//import com.sankuai.meituan.waimai.thrift.customer.service.WmSettleManagerThriftService;
//import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
//import org.apache.thrift.TException;
//import org.junit.Before;
//import org.junit.Test;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
//public class TestSettleManagerThriftSerice extends BaseTest {
//
//    private static WmSettleManagerThriftService wmSettleManagerThriftService;
//
//    private static Logger logger = LoggerFactory.getLogger(TestSettleManagerThriftSerice.class);
//
//    @Before
//    public void before(){
//        this.setRemoteAppKey("com.sankuai.waimai.e.customer");
//        this.setRemoteServerPort(8440);
//        this.setEnv(ENV_DEV);
//        this.setRunMode(RUN_MODE.REMOTE);
//        try{
//            wmSettleManagerThriftService = (WmSettleManagerThriftService)getObject(WmSettleManagerThriftService.class);
//        }catch (Exception e){
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void testGetSettleVersions() throws TException, WmCustomerException {
//        ContractVersionPageData versions = wmSettleManagerThriftService.getSettleVersions(100529, 1, 10, 2246505, "朱家琨");
//        System.out.println("###testGetSettleVersions = " + JSON.toJSONString(versions));
//    }
//}
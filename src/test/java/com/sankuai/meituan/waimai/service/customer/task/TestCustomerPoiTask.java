/*
package com.sankuai.meituan.waimai.service.customer.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.util.StringUtils;
import com.google.common.base.Function;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sankuai.meituan.auth.util.CollectionUtils;
import com.sankuai.meituan.waimai.BaseTest;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerPoiDBMapper;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerTaskMapper;
import com.sankuai.meituan.waimai.customer.ddd.adapter.CustomerTypeRelationUtil;
import com.sankuai.meituan.waimai.customer.ddd.adapter.MtCustomerThriftServiceAdapterImpl;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiDB;
import com.sankuai.meituan.waimai.s3cloud.util.MtS3CloudFileUtil;
import com.sankuai.meituan.waimai.service.customer.task.domain.CustomerBindPoiRequest;
import com.sankuai.meituan.waimai.thrift.customer.constant.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.ModuleDetailStatus;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerCommonThriftService;
import com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerPoiThriftService;
import com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerThriftService;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.util.DateUtil;
import com.sankuai.nibcus.inf.customer.client.dto.AttachmentDTO;
import com.sankuai.nibcus.inf.customer.client.dto.ContextDTO;
import com.sankuai.nibcus.inf.customer.client.dto.CustomerDTO;
import com.sankuai.nibcus.inf.customer.client.dto.QualificationDTO;
import com.sankuai.nibcus.inf.customer.client.enums.*;
import com.sankuai.nibcus.inf.customer.client.request.*;
import com.sankuai.nibcus.inf.customer.client.response.*;
import com.sankuai.nibcus.inf.customer.client.service.CustomerThriftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.junit.Before;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;

@Slf4j
public class TestCustomerPoiTask extends BaseTest {

    @Autowired
    private WmCustomerPoiThriftService wmCustomerPoiThriftService;

    @Autowired
    private WmCustomerCommonThriftService wmCustomerCommonThriftService;

    @Autowired
    private WmCustomerThriftService wmCustomerThriftService;

    @Autowired
    CustomerThriftService customerThriftService;

    @Autowired
    private WmCustomerPoiDBMapper wmCustomerPoiDBMapper;

    //资质有效期长期有效的毫秒数
    private long validate_date_forever = 4070880000000l;

    private  String BUCKET = "wm_imgstore_intern";

    private  int CUSTOMER_STATUS_UNEFFECTIVE = 0; //客户未生效
    private  int CUSTOMER_STATUS_EFFECTIVE = 1;   //客户生效

    private  int CUSTOMER_QUA_STATUS_UNEFFECTIVE = 0; //客户未生效
    private  int CUSTOMER_QUA_STATUS_EFFECTIVE = 1;   //客户生效
    private long CUSTOMER_OPERATOR_ID = 2287281l;    //同步到客户平台的操作人id
    private int REQUEST_MT_CUSTOMER_RETRY_TIMES = 3;

    private  String URL_PREFIX = "/";
    private  String URL_HTTP_CONSTANT = "http";

    */
/**
     * 批量更新客户门店关系
     *//*

    @Test
    public void testBatchUpdateCustomerPoiRel() {
        List<WmCustomerPoiDB> list = new ArrayList<>();
        WmCustomerPoiDB wmCustomerPoiDB = new WmCustomerPoiDB();
        wmCustomerPoiDB.setCustomerId(12084802);
        wmCustomerPoiDB.setIsUnbinding(1);
        wmCustomerPoiDB.setSwitchTaskId(0L);
        wmCustomerPoiDB.setBizTaskId(101);
        wmCustomerPoiDB.setWmPoiId(1103654L);

        WmCustomerPoiDB wmCustomerPoiDB2 = new WmCustomerPoiDB();
        wmCustomerPoiDB2.setCustomerId(12084802);
        wmCustomerPoiDB2.setIsUnbinding(1);
        wmCustomerPoiDB2.setSwitchTaskId(0L);
        wmCustomerPoiDB2.setBizTaskId(102);
        wmCustomerPoiDB2.setWmPoiId(1124360L);

        list.add(wmCustomerPoiDB);
        list.add(wmCustomerPoiDB2);
        wmCustomerPoiDBMapper.batchUpdateCustomerPoiRelationByList(list);
    }

    @Test
    public void testCustomerBindPoiWithCheckVersion() {
        CustomerBindPoiRequest customerBindPoiRequest = new CustomerBindPoiRequest();
        try {

            Set<Long> wmPoiIdSet = Sets.newHashSet();
            wmPoiIdSet.add(11991788L);
            wmPoiIdSet.add(12837L);
            wmPoiIdSet.add(82772L);
            customerBindPoiRequest.setOpUId(5801826);
            customerBindPoiRequest.setOpUName("ggg");
            customerBindPoiRequest.setCheckVersion(true);
            customerBindPoiRequest.setCustomerId(17277);
            customerBindPoiRequest.setRemark("");
            customerBindPoiRequest.setWmPoiIds(wmPoiIdSet);
            customerBindPoiRequest.setOpSource(1);
            if (!checkParams(customerBindPoiRequest)) {
                log.warn("testCustomerBindPoiWithCheckVersion,参数不合法");
                return;
            }

            wmCustomerPoiThriftService.customerBindPoiWithRemark(customerBindPoiRequest.getCustomerId(),
                    customerBindPoiRequest.getWmPoiIds(),
                    customerBindPoiRequest.getRemark(),
                    customerBindPoiRequest.getOpUId(),
                    customerBindPoiRequest.getOpUName(),
                    customerBindPoiRequest.isCheckVersion());
        } catch (WmCustomerException e) {
            log.error("testCustomerBindPoiWithCheckVersion,发生业务异常,customerBindPoiRequest={}",
                    JSON.toJSONString(customerBindPoiRequest), e);
        } catch (Exception e) {
            log.error("testCustomerBindPoiWithCheckVersion,发生异常,customerBindPoiRequest={}",
                    JSON.toJSONString(customerBindPoiRequest), e);
        }
    }


    @Test
    public void testCustomerBindPoiWithSource() {
        CustomerBindPoiRequest customerBindPoiRequest = new CustomerBindPoiRequest();
        try {

            Set<Long> wmPoiIdSet = Sets.newHashSet();
            wmPoiIdSet.add(11991788L);
            wmPoiIdSet.add(12837L);
            wmPoiIdSet.add(82772L);
            customerBindPoiRequest.setOpUId(5801826);
            customerBindPoiRequest.setOpUName("ggg");
            customerBindPoiRequest.setCheckVersion(true);
            customerBindPoiRequest.setCustomerId(17277);
            customerBindPoiRequest.setRemark("");
            customerBindPoiRequest.setWmPoiIds(wmPoiIdSet);
            customerBindPoiRequest.setOpSource(1);
            if (!checkParams(customerBindPoiRequest)) {
                log.warn("testCustomerBindPoiWithSource,参数不合法");
                return;
            }

            wmCustomerPoiThriftService.customerBindPoiWithSource(customerBindPoiRequest.getCustomerId(),
                    customerBindPoiRequest.getWmPoiIds(),
                    customerBindPoiRequest.getOpUId(),
                    customerBindPoiRequest.getOpUName(),
                    customerBindPoiRequest.getOpSource());
        } catch (WmCustomerException e) {
            log.error("testCustomerBindPoiWithSource,发生业务异常,customerBindPoiRequest={}",
                    JSON.toJSONString(customerBindPoiRequest), e);
        } catch (Exception e) {
            log.error("testCustomerBindPoiWithSource,发生异常,customerBindPoiRequest={}",
                    JSON.toJSONString(customerBindPoiRequest), e);
        }
    }

    @Test
    public void testCustomerBindPoi() {
        CustomerBindPoiRequest customerBindPoiRequest = new CustomerBindPoiRequest();
        try {

            Set<Long> wmPoiIdSet = Sets.newHashSet();
            wmPoiIdSet.add(11991788L);
            wmPoiIdSet.add(12837L);
            wmPoiIdSet.add(82772L);
            customerBindPoiRequest.setOpUId(5801826);
            customerBindPoiRequest.setOpUName("ggg");
            customerBindPoiRequest.setCheckVersion(true);
            customerBindPoiRequest.setCustomerId(17277);
            customerBindPoiRequest.setRemark("");
            customerBindPoiRequest.setWmPoiIds(wmPoiIdSet);
            customerBindPoiRequest.setOpSource(1);
            if (!checkParams(customerBindPoiRequest)) {
                log.warn("testCustomerBindPoi,参数不合法");
                return;
            }

            wmCustomerPoiThriftService.customerBindPoi(customerBindPoiRequest.getCustomerId(),
                    customerBindPoiRequest.getWmPoiIds(),
                    customerBindPoiRequest.getOpUId(),
                    customerBindPoiRequest.getOpUName());
        } catch (WmCustomerException e) {
            log.error("testCustomerBindPoi,发生业务异常,customerBindPoiRequest={}",
                    JSON.toJSONString(customerBindPoiRequest), e);
        } catch (Exception e) {
            log.error("testCustomerBindPoi,发生异常,customerBindPoiRequest={}",
                    JSON.toJSONString(customerBindPoiRequest), e);
        }
    }

    @Test
    public void testCustomerUnBindPoi() {
        CustomerBindPoiRequest customerBindPoiRequest = new CustomerBindPoiRequest();
        try {

            Set<Long> wmPoiIdSet = Sets.newHashSet();
            wmPoiIdSet.add(11991788L);
            wmPoiIdSet.add(12837L);
            wmPoiIdSet.add(82772L);
            customerBindPoiRequest.setOpUId(5801826);
            customerBindPoiRequest.setOpUName("ggg");
            customerBindPoiRequest.setCheckVersion(true);
            customerBindPoiRequest.setCustomerId(17277);
            customerBindPoiRequest.setRemark("");
            customerBindPoiRequest.setWmPoiIds(wmPoiIdSet);
            customerBindPoiRequest.setOpSource(1);
            if (!checkParams(customerBindPoiRequest)) {
                log.warn("参数不合法");
                return;
            }

            wmCustomerPoiThriftService.customerUnBindPoi(customerBindPoiRequest.getCustomerId(),
                    customerBindPoiRequest.getWmPoiIds(),
                    customerBindPoiRequest.getOpUId(),
                    customerBindPoiRequest.getOpUName());
        } catch (WmCustomerException e) {
            log.error("testCustomerUnBindPoi,发生业务异常,customerBindPoiRequest={}",
                    JSON.toJSONString(customerBindPoiRequest), e);
        } catch (Exception e) {
            log.error("testCustomerUnBindPoi,发生异常,customerBindPoiRequest={}",
                    JSON.toJSONString(customerBindPoiRequest), e);
        }
    }


    */
/**
     * 校验条件
     *
     * @param customerBindPoiRequest
     * @return
     *//*

    private boolean checkParams(CustomerBindPoiRequest customerBindPoiRequest) {

        if (customerBindPoiRequest == null) {
            return false;
        }
        if (customerBindPoiRequest.getCustomerId() <= 0
                || customerBindPoiRequest.getWmPoiIds() == null
                || customerBindPoiRequest.getWmPoiIds().size() <= 0) {
            return false;
        }

        return true;
    }

}
*/

package com.sankuai.meituan.waimai.service.adaptor;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSON;
import com.sankuai.sjst.ecom.epassport.service.client.thrift.model.ValidateTokenReq;
import com.sankuai.sjst.ecom.epassport.service.client.thrift.model.ValidateTokenResp;
import com.sankuai.sjst.ecom.epassport.service.client.thrift.model.common.Status;
import com.sankuai.sjst.ecom.epassport.service.client.thrift.service.BizAccountInfoThriftService;
import org.apache.thrift.TException;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class BizAccountInfoThriftServiceAdapterValidateTokenTest {

    @Mock
    private BizAccountInfoThriftService.Iface bizAccountInfoThriftService;

    @InjectMocks
    private BizAccountInfoThriftServiceAdapter bizAccountInfoThriftServiceAdapter;

    /**
     * Test normal case with valid token and successful response
     */
    @Test
    public void testValidateTokenNormalCase() throws TException {
        // arrange
        String token = "valid_token";
        ValidateTokenResp expectedResp = new ValidateTokenResp();
        Status status = new Status(200);
        expectedResp.setStatus(status);
        when(bizAccountInfoThriftService.validateToken(any(ValidateTokenReq.class))).thenReturn(expectedResp);
        // act
        ValidateTokenResp actualResp = bizAccountInfoThriftServiceAdapter.validateToken(token);
        // assert
        assertNotNull(actualResp);
        assertEquals(200, actualResp.getStatus().getCode());
        verify(bizAccountInfoThriftService).validateToken(any(ValidateTokenReq.class));
    }

    /**
     * Test case when thrift service throws TException
     */
    @Test(expected = TException.class)
    public void testValidateTokenThrowsTException() throws TException {
        // arrange
        String token = "valid_token";
        when(bizAccountInfoThriftService.validateToken(any(ValidateTokenReq.class))).thenThrow(new TException("Thrift exception"));
        // act
        bizAccountInfoThriftServiceAdapter.validateToken(token);
        // assert - exception expected
    }

    /**
     * Test case with empty token
     */
    @Test
    public void testValidateTokenEmptyToken() throws TException {
        // arrange
        String token = "";
        ValidateTokenResp expectedResp = new ValidateTokenResp();
        Status status = new Status(200);
        expectedResp.setStatus(status);
        when(bizAccountInfoThriftService.validateToken(any(ValidateTokenReq.class))).thenReturn(expectedResp);
        // act
        ValidateTokenResp actualResp = bizAccountInfoThriftServiceAdapter.validateToken(token);
        // assert
        assertNotNull(actualResp);
        assertEquals(200, actualResp.getStatus().getCode());
        verify(bizAccountInfoThriftService).validateToken(any(ValidateTokenReq.class));
    }

    /**
     * Test case with null token
     */
    @Test
    public void testValidateTokenNullToken() throws TException {
        // arrange
        ValidateTokenResp expectedResp = new ValidateTokenResp();
        Status status = new Status(200);
        expectedResp.setStatus(status);
        when(bizAccountInfoThriftService.validateToken(any(ValidateTokenReq.class))).thenReturn(expectedResp);
        // act
        ValidateTokenResp actualResp = bizAccountInfoThriftServiceAdapter.validateToken(null);
        // assert
        assertNotNull(actualResp);
        assertEquals(200, actualResp.getStatus().getCode());
        verify(bizAccountInfoThriftService).validateToken(any(ValidateTokenReq.class));
    }

    /**
     * Test case with complex response structure
     */
    @Test
    public void testValidateTokenComplexResponse() throws TException {
        // arrange
        String token = "complex_token";
        ValidateTokenResp expectedResp = new ValidateTokenResp();
        Status status = new Status(200);
        status.setMessage("OK");
        expectedResp.setStatus(status);
        expectedResp.setId(12345);
        expectedResp.setAclUserID(1);
        expectedResp.setAclBizAcctID(2);
        expectedResp.setLoginType(3);
        when(bizAccountInfoThriftService.validateToken(any(ValidateTokenReq.class))).thenReturn(expectedResp);
        // act
        ValidateTokenResp actualResp = bizAccountInfoThriftServiceAdapter.validateToken(token);
        // assert
        assertNotNull(actualResp);
        assertEquals(200, actualResp.getStatus().getCode());
        assertEquals("OK", actualResp.getStatus().getMessage());
        assertEquals(12345, actualResp.getId());
        assertEquals(1, actualResp.getAclUserID());
        assertEquals(2, actualResp.getAclBizAcctID());
        assertEquals(3, actualResp.getLoginType());
        verify(bizAccountInfoThriftService).validateToken(any(ValidateTokenReq.class));
    }
}

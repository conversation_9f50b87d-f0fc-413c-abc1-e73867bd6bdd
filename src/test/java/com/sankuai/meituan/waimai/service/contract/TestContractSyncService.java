/*
package com.sankuai.meituan.waimai.service.contract;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.meituan.util.DateUtil;
import com.sankuai.meituan.waimai.BaseSpringTransactionJunit;
import com.sankuai.meituan.waimai.c2contract.domain.WmC2ContractBasicBo;
import com.sankuai.meituan.waimai.c2contract.domain.WmC2ContractBo;
import com.sankuai.meituan.waimai.c2contract.domain.WmC2ContractSignBo;
import com.sankuai.meituan.waimai.c2contract.service.WmC2ContractAuditedThriftService;
import com.sankuai.meituan.waimai.contract.thrift.service.WmContractAuditedThriftService;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmContractService;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmContractSyncService;
import com.sankuai.meituan.waimai.thrift.customer.constant.WmTempletContractTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerContractStatus;
import com.sankuai.meituan.waimai.thrift.customer.domain.PoiGrayRollbackCheckResBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.*;
import com.sankuai.meituan.waimai.thrift.domain.WmContractAudited;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;

import java.util.List;

public class TestContractSyncService extends BaseSpringTransactionJunit {

    @Autowired
    WmContractSyncService wmContractSyncService;

    @Autowired
    WmContractAuditedThriftService.Iface wmContractAuditedThriftService;

    @Autowired
    WmC2ContractAuditedThriftService.Iface wmC2ContractAuditedThriftService;

    @Test
    public void testCheckPoiGray() throws Exception {
        PoiGrayRollbackCheckResBo checkResBo = wmContractSyncService.checkPoiCanBeGrayRollback(73502, 2050838, "");
        System.out.println("===testCheckPoiGray===" + JSON.toJSONString(checkResBo));
    }

    @Test
    @Rollback(false)
    public void testSyncC1PaperContract() throws Exception {
        WmContractAudited auditedContract = wmContractAuditedThriftService.getWmContractAuditedByWmContractId(100634);
        WmCustomerContractBo customerContractBo = genC1WmCustomerContractBo(auditedContract);
        wmContractSyncService.saveContractForSync(customerContractBo, 0, "邱润景测试");
    }

    @Test
    @Rollback(false)
    public void testSyncC1EContract() throws Exception {
        WmContractAudited auditedContract = wmContractAuditedThriftService.getWmContractAuditedByWmContractId(10014597);
        WmCustomerContractBo customerContractBo = genC1WmCustomerContractBo(auditedContract);
        wmContractSyncService.saveContractForSync(customerContractBo, 0, "邱润景测试");
    }

    @Test
    @Rollback(false)
    public void testSyncC2PaperContract() throws Exception {
        WmC2ContractBo c2ContractBo = wmC2ContractAuditedThriftService.getC2ContractAuditedByC2ContractId(90);
        WmCustomerContractBo customerContractBo = genC2WmCustomerContractBo(c2ContractBo);
        customerContractBo.getBasicBo().setStatus(CustomerContractStatus.EFFECT.getCode());
        wmContractSyncService.saveContractForSync(customerContractBo, 0, "邱润景测试");
    }

    @Test
    @Rollback(false)
    public void testSyncC2EContract() throws Exception {
        WmC2ContractBo c2ContractBo = wmC2ContractAuditedThriftService.getC2ContractAuditedByC2ContractId(90);
        WmCustomerContractBo customerContractBo = genC2WmCustomerContractBo(c2ContractBo);
        customerContractBo.getBasicBo().setStatus(CustomerContractStatus.EFFECT.getCode());
        wmContractSyncService.saveContractForSync(customerContractBo, 0, "邱润景测试");
    }

    private WmCustomerContractBo genC2WmCustomerContractBo(WmC2ContractBo wmC2ContractBo) {
        WmTempletContractBasicBo basicBo = new WmTempletContractBasicBo();
        basicBo.setType(WmTempletContractTypeEnum.C2_E.getCode());
        basicBo.setExtStr("");
        if (wmC2ContractBo.getWmC2ContractBasicBo().getType() == 1) {
            basicBo.setType(WmTempletContractTypeEnum.C2_PAPER.getCode());
            CustomerPaperContractRemarkBo customerPaperContractRemarkBo = new CustomerPaperContractRemarkBo();
            MultiFileJsonBo multiFileJsonBo = JSON.parseObject(wmC2ContractBo.getWmC2ContractScanBo().getContractScan(), MultiFileJsonBo.class);
            customerPaperContractRemarkBo.setContractScan(multiFileJsonBo);
            basicBo.setExtStr(JSON.toJSONString(customerPaperContractRemarkBo));
        }
        WmC2ContractBasicBo wmC2ContractBasicBo = wmC2ContractBo.getWmC2ContractBasicBo();
        basicBo.setParentId(wmC2ContractBasicBo.getWmContractId());
        basicBo.setContractNum(wmC2ContractBasicBo.getContractNum());
        DateUtil.date2Unixtime(DateUtil.string2DateDay(wmC2ContractBasicBo.getContractValidDateText()));
        basicBo.setDueDate(DateUtil.date2Unixtime(DateUtil.string2DateDay(wmC2ContractBasicBo.getContractValidDateText())));
        basicBo.setEffectiveDate(wmC2ContractBasicBo.getContractEffectTime());
        // 签约人信息
        WmC2ContractSignBo wmC2ContractSignBo = wmC2ContractBo.getWmC2ContractSignBo();
        WmTempletContractSignBo signBoA = new WmTempletContractSignBo();
        signBoA.setSignId(wmC2ContractBasicBo.getWmContractId());
        signBoA.setSignType("A");
        signBoA.setSignName(wmC2ContractSignBo.getPartyA());
        signBoA.setSignPeople(wmC2ContractSignBo.getPartyAPeople());
        signBoA.setSignPhone(wmC2ContractSignBo.getPartyAPhone());
        signBoA.setSignTime(wmC2ContractSignBo.getPartyATime());
        WmTempletContractSignBo signBoB = new WmTempletContractSignBo();
        signBoB.setSignType("B");
        signBoB.setSignId(wmC2ContractSignBo.getPartyBId());
        signBoB.setSignName(wmC2ContractSignBo.getPartyB());
        signBoB.setSignPeople(wmC2ContractSignBo.getPartyBPeople());
        signBoB.setSignPhone(wmC2ContractSignBo.getPartyBPhone());
        signBoB.setSignTime(wmC2ContractSignBo.getPartyBTime());
        List<WmTempletContractSignBo> signBoList = Lists.newArrayList();
        signBoList.add(signBoA);
        signBoList.add(signBoB);
        WmCustomerContractBo wmCustomerContractBo = new WmCustomerContractBo();
        wmCustomerContractBo.setBasicBo(basicBo);
        wmCustomerContractBo.setSignBoList(signBoList);
        return wmCustomerContractBo;
    }

    private WmCustomerContractBo genC1WmCustomerContractBo(WmContractAudited wmContractAudited) {
        WmTempletContractBasicBo basicBo = new WmTempletContractBasicBo();
        basicBo.setParentId(wmContractAudited.getContract_id());
        basicBo.setType(WmTempletContractTypeEnum.C1_E.getCode());
        basicBo.setExtStr("");
        if (wmContractAudited.getType() == 1) {
            basicBo.setType(WmTempletContractTypeEnum.C1_PAPER.getCode());
            CustomerPaperContractRemarkBo customerPaperContractRemarkBo = new CustomerPaperContractRemarkBo();
            MultiFileJsonBo multiFileJsonBo = new MultiFileJsonBo();
            multiFileJsonBo.getList().add(new MultiFileJsonBo.CustomerFile("", wmContractAudited.getContract_scan()));
            customerPaperContractRemarkBo.setContractScan(multiFileJsonBo);
            basicBo.setExtStr(JSON.toJSONString(customerPaperContractRemarkBo));
        }
        basicBo.setContractNum(wmContractAudited.getContract_number());
        basicBo.setStatus(CustomerContractStatus.EFFECT.getCode());
        basicBo.setDueDate(wmContractAudited.getContract_valid_date());
        basicBo.setEffectiveDate(wmContractAudited.getContract_effect_time());
        // 签约人信息
        WmTempletContractSignBo signBoA = new WmTempletContractSignBo();
        signBoA.setSignId(wmContractAudited.getContract_id());
        signBoA.setSignType("A");
        signBoA.setSignName(wmContractAudited.getParty_a());
        signBoA.setSignPeople(wmContractAudited.getParty_a_people());
        signBoA.setSignPhone(wmContractAudited.getParty_a_phone());
        signBoA.setSignTime(DateUtil.secondsToString(wmContractAudited.getParty_a_time()));
        WmTempletContractSignBo signBoB = new WmTempletContractSignBo();
        signBoB.setSignType("B");
        signBoB.setSignName(wmContractAudited.getParty_b());
        signBoB.setSignPeople(wmContractAudited.getParty_b_people());
        signBoB.setSignPhone(wmContractAudited.getParty_b_phone());
        signBoB.setSignTime(DateUtil.secondsToString(wmContractAudited.getParty_b_time()));
        List<WmTempletContractSignBo> signBoList = Lists.newArrayList();
        signBoList.add(signBoA);
        signBoList.add(signBoB);
        WmCustomerContractBo wmCustomerContractBo = new WmCustomerContractBo();
        wmCustomerContractBo.setBasicBo(basicBo);
        wmCustomerContractBo.setSignBoList(signBoList);
        return wmCustomerContractBo;
    }

    @Test
    @Rollback(false)
    public void 测试新创建客户附件() throws Exception {
        WmCustomerContractScanBo contractScanBo = new WmCustomerContractScanBo();
        contractScanBo.setCustomerId(10014925);
        contractScanBo.setOtherScan(Lists.newArrayList("/qb/download/mos/p3_imageUploadMTCloud_201811261415094157445e9c8d046e7fb0f6d8e38a13a8.jpeg",
                "/qb/download/mos/p3_imageUploadMTCloud_201811261415094157445e9c8d046e7fb0f6d8e38a13a8.jpeg"));
        contractScanBo.setExclusiveScan(Lists.newArrayList("/qb/download/mos/p3_imageUploadMTCloud_201811261415094157445e9c8d046e7fb0f6d8e38a13a8.jpeg",
                "/qb/download/mos/p3_imageUploadMTCloud_201811261415094157445e9c8d046e7fb0f6d8e38a13a8.jpeg"));
        contractScanBo.setDeliveryScan(Lists.newArrayList("/qb/download/mos/p3_imageUploadMTCloud_201811261415094157445e9c8d046e7fb0f6d8e38a13a8.jpeg",
                "/qb/download/mos/p3_imageUploadMTCloud_201811261415094157445e9c8d046e7fb0f6d8e38a13a8.jpeg"));
        wmContractSyncService.syncContractScan(contractScanBo, 0, "邱润景");
    }

    @Test
    @Rollback(false)
    public void 测试客户_优惠申请书有线下没线上附件() throws Exception {
        WmCustomerContractScanBo contractScanBo = new WmCustomerContractScanBo();
        contractScanBo.setCustomerId(10014925);
        contractScanBo.setOtherScan(Lists.newArrayList("/customer/download/cus/mos/contract_file_28c9bebe2461d3f2cb504d5df29bf239_1543561990.jpg",
                "/qb/download/mos/p3_imageUploadMTCloud_201811261415094157445e9c8d046e7fb0f6d8e38a13a8.jpeg"));
        contractScanBo.setExclusiveScan(Lists.newArrayList("/customer/download/cus/mos/contract_file_28c9bebe2461d3f2cb504d5df29bf239_1543561990.jpg",
                "/customer/download/cus/mos/contract_file_28c9bebe2461d3f2cb504d5df29bf239_1543561990.jpg"));
        contractScanBo.setDeliveryScan(Lists.newArrayList("/customer/download/cus/mos/contract_file_28c9bebe2461d3f2cb504d5df29bf239_1543561990.jpg",
                "/customer/download/cus/mos/contract_file_28c9bebe2461d3f2cb504d5df29bf239_1543561990.jpg"));
        wmContractSyncService.syncContractScan(contractScanBo, 0, "邱润景");
    }

    @Test
    @Rollback(false)
    public void 测试客户_优惠申请书附件超出50张() throws Exception {
        WmCustomerContractScanBo contractScanBo = new WmCustomerContractScanBo();
        contractScanBo.setCustomerId(10014925);
        contractScanBo.setOtherScan(Lists.newArrayList("/customer/download/cus/mos/contract_file_28c9bebe2461d3f2cb504d5df29bf239_1543561990.jpg",
                "/qb/download/mos/p3_imageUploadMTCloud_201811261415094157445e9c8d046e7fb0f6d8e38a13a8.jpeg"));
        List<String> exclusiveScan = Lists.newArrayList();
        for (int i = 0 ; i < 55; i ++) {
            exclusiveScan.add("/customer/download/cus/mos/contract_file_28c9bebe2461d3f2cb504d5df29bf239_1543561990.jpg");
        }
        contractScanBo.setExclusiveScan(exclusiveScan);
        contractScanBo.setDeliveryScan(Lists.newArrayList("/customer/download/cus/mos/contract_file_28c9bebe2461d3f2cb504d5df29bf239_1543561990.jpg",
                "/customer/download/cus/mos/contract_file_28c9bebe2461d3f2cb504d5df29bf239_1543561990.jpg"));
        wmContractSyncService.syncContractScan(contractScanBo, 0, "邱润景");
    }
}
*/

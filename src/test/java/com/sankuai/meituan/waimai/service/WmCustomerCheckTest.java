package com.sankuai.meituan.waimai.service;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.netflix.config.ConcurrentCompositeConfiguration;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.auth.vo.User;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.config.MccConfig;
import com.sankuai.meituan.waimai.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerBasicBo;
import com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerThriftService;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.vo.customer.CustomerInfo;
import org.apache.thrift.TException;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class WmCustomerCheckTest {

    @InjectMocks
    private WmCustomerCheck wmCustomerCheck;

    @Mock
    private WmCustomerThriftService wmCustomerThriftService;

    private static boolean configInitialized = false;

    private User originalUser;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        // Initialize ConfigUtilAdapter if not already initialized
        if (!configInitialized) {
            ConcurrentCompositeConfiguration config = new ConcurrentCompositeConfiguration();
            config.addProperty("offline_customer_qua_edit_auth", "false");
            config.addProperty("check.user.have.qua.edit.auth.switch", "true");
            ConfigUtilAdapter.addConfiguration(config);
            ConfigUtilAdapter.init();
            configInitialized = true;
        }
        // Store original user
        originalUser = UserUtils.getUser();
    }

    @After
    public void tearDown() {
        // Restore original user
        if (originalUser != null) {
            UserUtils.bind(originalUser);
        } else {
            UserUtils.unbindUser();
        }
    }

    /**
     * 测试开关未开启时，方法直接返回
     */
    @Test
    public void testCheckCustomerQuaEditAuthSwitchOff() throws Throwable {
        // arrange
        CustomerInfo customerInfo = new CustomerInfo();
        customerInfo.setId(1);
        customerInfo.setCustomerNumber("12345");
        WmCustomerBasicBo wmCustomerBasicBo = new WmCustomerBasicBo();
        wmCustomerBasicBo.setCustomerNumber("12345");
        when(wmCustomerThriftService.getCustomerById(1)).thenReturn(wmCustomerBasicBo);
        // act
        wmCustomerCheck.checkCustomerQuaEditAuth(customerInfo);
        // assert
        verify(wmCustomerThriftService).getCustomerById(1);
    }

    /**
     * 测试开关开启，但获取客户信息失败，抛出 WmCustomerException
     */
    @Test(expected = WmCustomerException.class)
    public void testCheckCustomerQuaEditAuthGetCustomerInfoFailed() throws Throwable {
        // arrange
        CustomerInfo customerInfo = new CustomerInfo();
        customerInfo.setId(1);
        customerInfo.setCustomerNumber("12345");
        when(wmCustomerThriftService.getCustomerById(1)).thenThrow(new TException("System error"));
        // act
        wmCustomerCheck.checkCustomerQuaEditAuth(customerInfo);
        // assert
        // Expected WmCustomerException
    }

    /**
     * 测试开关开启，获取客户信息成功，且资质编号未修改，方法正常返回
     */
    @Test
    public void testCheckCustomerQuaEditAuthNoChange() throws Throwable {
        // arrange
        CustomerInfo customerInfo = new CustomerInfo();
        customerInfo.setId(1);
        customerInfo.setCustomerNumber("12345");
        WmCustomerBasicBo wmCustomerBasicBo = new WmCustomerBasicBo();
        wmCustomerBasicBo.setCustomerNumber("12345");
        when(wmCustomerThriftService.getCustomerById(1)).thenReturn(wmCustomerBasicBo);
        // act
        wmCustomerCheck.checkCustomerQuaEditAuth(customerInfo);
        // assert
        verify(wmCustomerThriftService).getCustomerById(1);
    }

    /**
     * 测试开关开启，获取客户信息成功，且资质编号修改，但用户无权限，抛出 WmCustomerException
     */
    @Test(expected = WmCustomerException.class)
    public void testCheckCustomerQuaEditAuthChangeNoPermission() throws Throwable {
        // arrange
        CustomerInfo customerInfo = new CustomerInfo();
        customerInfo.setId(1);
        customerInfo.setCustomerNumber("67890");
        WmCustomerBasicBo wmCustomerBasicBo = new WmCustomerBasicBo();
        wmCustomerBasicBo.setCustomerNumber("12345");
        wmCustomerBasicBo.setCustomerRealType(1);
        when(wmCustomerThriftService.getCustomerById(1)).thenReturn(wmCustomerBasicBo);
        // Set a test user directly
        User testUser = new User();
        UserUtils.bind(testUser);
        // act
        wmCustomerCheck.checkCustomerQuaEditAuth(customerInfo);
        // assert
        // Expected WmCustomerException
    }
}

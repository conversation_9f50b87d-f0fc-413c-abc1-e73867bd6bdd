package com.sankuai.meituan.waimai.service.contract;

import com.meituan.waimai.agent.otter.enterprise.exception.WmEnterpriseThriftException;
import com.meituan.waimai.agent.otter.enterprise.response.agentinfo.AgentInfoTo;
import com.meituan.waimai.agent.otter.enterprise.service.agentinfo.WmAgentInfoService;
import com.sankuai.meituan.waimai.service.adaptor.WmAgentInfoServiceAdapter;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import static org.mockito.Mockito.*;
import static org.junit.Assert.*;

/**
 * @description:
 * @author: liuyunjie05
 * @create: 2025/2/27 11:21
 */
@RunWith(MockitoJUnitRunner.class)
public class WmAgentInfoServiceAdapterTest {

    @Mock
    private WmAgentInfoService wmAgentInfoService;

    @InjectMocks
    private WmAgentInfoServiceAdapter wmAgentInfoServiceAdapter;

    /**
     * 测试getById方法，当传入有效的agentId时，应返回非null的AgentInfoTo对象
     */
    @Test
    public void testGetByIdWithValidAgentId() throws Throwable {

        when(wmAgentInfoService.getById(2)).thenReturn(new AgentInfoTo());
        // act
        AgentInfoTo result = wmAgentInfoServiceAdapter.getById(2);

        // assert
        assertNotNull(result);
        verify(wmAgentInfoService, times(1)).getById(anyInt());
    }

    /**
     * 测试getById方法，当传入无效的agentId时，应捕获WmEnterpriseThriftException并返回null
     */
    @Test
    public void testGetByIdWithInvalidAgentIdCatchWmEnterpriseThriftException() throws Throwable {
        // arrange
        Integer agentIdCausingUnknownException = 2;
        // act
        when(wmAgentInfoService.getById(agentIdCausingUnknownException)).thenThrow(new WmEnterpriseThriftException());
        AgentInfoTo result = wmAgentInfoServiceAdapter.getById(agentIdCausingUnknownException);

        // assert
        assertNull(result);
        verify(wmAgentInfoService, times(1)).getById(agentIdCausingUnknownException);
    }

    /**
     * 测试getById方法，当传入的agentId导致未知异常时，应返回null
     */
    @Test
    public void testGetByIdWithAgentIdCauseUnknownException() throws Throwable {
        // arrange
        Integer agentIdCausingUnknownException = 2;
        when(wmAgentInfoService.getById(agentIdCausingUnknownException)).thenThrow(new RuntimeException());

        // act
        AgentInfoTo result = wmAgentInfoServiceAdapter.getById(agentIdCausingUnknownException);

        // assert
        assertNull(result);
        verify(wmAgentInfoService, times(1)).getById(agentIdCausingUnknownException);
    }

}

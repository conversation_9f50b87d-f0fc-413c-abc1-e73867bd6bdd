//package com.sankuai.meituan.waimai.service;
//
//import com.alibaba.fastjson.JSONObject;
//import com.google.common.collect.Lists;
//import com.sankuai.meituan.waimai.BaseTest;
//import com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerBrandThriftService;
//import org.junit.Before;
//import org.junit.Test;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
//import java.util.List;
//
//public class TestWmCustomerBrandService extends BaseTest{
//    private static WmCustomerBrandThriftService wmCustomerBrandThriftService;
//    private static Logger logger = LoggerFactory.getLogger(TestWmCustomerBrandService.class);
//
//    @Before
//    public void before(){
//        this.setRemoteAppKey("com.sankuai.waimai.e.customer");
//        this.setRemoteServerPort(8433);
//        this.setEnv(ENV_DEV);
//        this.setRunMode(BaseTest.RUN_MODE.DEBUG_LOCAL_WITHSTART);
//        try{
//            wmCustomerBrandThriftService = (WmCustomerBrandThriftService)getObject(WmCustomerBrandThriftService.class);
//        }catch (Exception e){
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void testBatchSave(){
//        try {
//            int customerId = 1;
//            List<Integer> list = Lists.newArrayList(1,2,3);
//            wmCustomerBrandThriftService.batchSaveCustomerBrandRel(customerId,list,2146436,"郑旭04");
//        }catch (Exception e){
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void testBatchDelete(){
//        try {
//            int customerId = 1;
//            List<Integer> list = Lists.newArrayList(1,2);
//            wmCustomerBrandThriftService.deleteCustomerBrandRel(customerId,list,2146436,"郑旭04");
//        }catch (Exception e){
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void testGetList(){
//        try {
//            int customerId = 1;
//            List<Integer> list = wmCustomerBrandThriftService.selectBrandIdListByCustomerId(customerId);
//            logger.info(JSONObject.toJSONString(list));
//        }catch (Exception e){
//            e.printStackTrace();
//        }
//    }
//}

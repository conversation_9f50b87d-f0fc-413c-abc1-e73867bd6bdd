/*
package com.sankuai.meituan.waimai.service.customer.poirelfow;

import com.sankuai.meituan.waimai.BaseTest;
import com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.rule.bind.DanDianDirectBindRule;
import org.jeasy.rules.api.*;
import org.jeasy.rules.core.DefaultRulesEngine;
import org.junit.Test;

public class CustomerPoiBindFlowTest extends BaseTest {

    */
/**
     * 测试注解形式的直接绑定规则
     *//*

    @Test
    public void testDirectBindFlow() {

        RulesEngineParameters parameters = new RulesEngineParameters().skipOnFirstAppliedRule(true);
        RulesEngine rulesEngine = new DefaultRulesEngine(parameters);

        Rules rules = new Rules();
        rules.register(new DanDianDirectBindRule());
        Facts facts = new Facts();
        facts.put("customerRealType", 1);
        rulesEngine.fire(rules, facts);
    }
}
*/

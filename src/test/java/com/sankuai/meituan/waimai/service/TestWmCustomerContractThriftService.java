//package com.sankuai.meituan.waimai.service;
//
//import com.alibaba.fastjson.JSON;
//import com.google.common.collect.Lists;
//import com.meituan.mtrace.Tracer;
//import com.sankuai.meituan.waimai.BaseTest;
//import com.sankuai.meituan.waimai.thrift.customer.constant.WmTempletContractTypeEnum;
//import com.sankuai.meituan.waimai.thrift.customer.constant.SignSubjectEnum;
//import com.sankuai.meituan.waimai.thrift.customer.domain.OpResultBo;
//import com.sankuai.meituan.waimai.thrift.customer.domain.base.LongResult;
//import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractBasicBo;
//import com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerContractThriftService;
//import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
//import org.apache.thrift.TException;
//import org.junit.Before;
//import org.junit.Test;
//import org.springframework.util.CollectionUtils;
//
//import java.util.Collection;
//import java.util.List;
//
//public class TestWmCustomerContractThriftService extends BaseTest {
//
//    private WmCustomerContractThriftService customerContractThriftService;
//
//    @Before
//    public void before() {
//        this.setRemoteAppKey("com.sankuai.waimai.e.customer");
//        this.setRemoteServerPort(8431);
//        this.setEnv(ENV_TEST);
//        this.setRunMode(RUN_MODE.REMOTE);
//        try {
//            customerContractThriftService = (WmCustomerContractThriftService) getObject(WmCustomerContractThriftService.class);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void testGetC1ContractStartSignTime() throws TException, WmCustomerException {
//        Tracer.setSwimlane("9776-cuaur");
//        LongResult result = customerContractThriftService.getC1ContractStartSignTime(628210L);
//        System.out.println("testGetC1ContractStartSignTime = " + JSON.toJSONString(result));
//    }
//
//    @Test
//    public void testGetAuditedContractBasicListByPoiIdAndType() throws TException, WmCustomerException {
//        Tracer.setSwimlane("9776-cuaur");
//        List<WmTempletContractBasicBo> wmTempletContractBasicBoList = customerContractThriftService.getAuditedContractBasicListByPoiIdAndType(632961L, Lists.newArrayList(WmTempletContractTypeEnum.POI_PROMOTION_SERVICE_E.getCode()), 0, "系统");
//        System.out.println(JSON.toJSONString(wmTempletContractBasicBoList));
//        System.out.println(!CollectionUtils.isEmpty(wmTempletContractBasicBoList));
//    }
//
//    @Test
//    public void testSaveAndSignContract() throws TException, WmCustomerException {
//        Tracer.setSwimlane("9776-cuaur");
//        OpResultBo opResultBo = customerContractThriftService.saveAndSignContract(632961L, WmTempletContractTypeEnum.POI_PROMOTION_SERVICE_E.getCode(), SignSubjectEnum.SH_SANKUAI.getCode(), 0, "系统");
//        System.out.println(JSON.toJSONString(opResultBo));
//    }
//}
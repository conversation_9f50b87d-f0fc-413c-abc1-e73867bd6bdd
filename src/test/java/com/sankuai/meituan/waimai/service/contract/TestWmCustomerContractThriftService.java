/*
package com.sankuai.meituan.waimai.service.contract;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.meituan.mtrace.Tracer;
import com.sankuai.meituan.waimai.BaseTest;
import com.sankuai.meituan.waimai.thrift.customer.domain.OpCustomerResultBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractUpdateBo;
import com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerContractThriftService;
import com.sankuai.meituan.waimai.thrift.customer.service.external.WmCustomerContractExternalThriftService;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.apache.thrift.TException;
import org.junit.Before;
import org.junit.Test;

import java.util.List;

public class TestWmCustomerContractThriftService extends BaseTest {

    private WmCustomerContractExternalThriftService wmCustomerContractExternalThriftService;

    @Before
    public void before() {
        this.setRemoteAppKey("com.sankuai.waimai.e.customer");
        this.setRemoteServerPort(8448);
        this.setEnv(ENV_TEST);
        this.setRunMode(RUN_MODE.REMOTE);
        try {
            wmCustomerContractExternalThriftService = (WmCustomerContractExternalThriftService) getObject(WmCustomerContractExternalThriftService.class);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testBatchUpdate() throws TException, WmCustomerException {

        // 流程中
        WmCustomerContractUpdateBo wmCustomerContractUpdateBo = new WmCustomerContractUpdateBo();
        wmCustomerContractUpdateBo.setCustomerId(1024978547L);
        wmCustomerContractUpdateBo.setContractId(973L);
        wmCustomerContractUpdateBo.setDueDate(1604366000L);

        // 非对应客户合同
        WmCustomerContractUpdateBo wmCustomerContractUpdateBo5 = new WmCustomerContractUpdateBo();
        wmCustomerContractUpdateBo5.setCustomerId(1024078547L);
        wmCustomerContractUpdateBo5.setContractId(983L);
        wmCustomerContractUpdateBo5.setDueDate(1603366000L);

        // 纸质
        WmCustomerContractUpdateBo wmCustomerContractUpdateBo1 = new WmCustomerContractUpdateBo();
        wmCustomerContractUpdateBo1.setCustomerId(1025073711L);
        wmCustomerContractUpdateBo1.setContractId(34424L);
        wmCustomerContractUpdateBo1.setDueDate(1603399000L);

        // 重复
        WmCustomerContractUpdateBo wmCustomerContractUpdateBo6 = new WmCustomerContractUpdateBo();
        wmCustomerContractUpdateBo6.setCustomerId(1025073711L);
        wmCustomerContractUpdateBo6.setContractId(34424L);
        wmCustomerContractUpdateBo6.setDueDate(1603399000L);

        // 不存在
        WmCustomerContractUpdateBo wmCustomerContractUpdateBo2 = new WmCustomerContractUpdateBo();
        wmCustomerContractUpdateBo2.setCustomerId(1025072956L);
        wmCustomerContractUpdateBo2.setContractId(94424L);
        wmCustomerContractUpdateBo2.setDueDate(1603399000L);

        // 非闪购客户
        WmCustomerContractUpdateBo wmCustomerContractUpdateBo3 = new WmCustomerContractUpdateBo();
        wmCustomerContractUpdateBo3.setCustomerId(1025073710L);
        wmCustomerContractUpdateBo3.setContractId(34423L);
        wmCustomerContractUpdateBo3.setDueDate(1603399000L);

        // 提交客户合同数量过多,本次仅提交成功xxx个
        WmCustomerContractUpdateBo wmCustomerContractUpdateBo4 = new WmCustomerContractUpdateBo();
        wmCustomerContractUpdateBo4.setCustomerId(1025073711L);
        wmCustomerContractUpdateBo4.setContractId(34424L);
        wmCustomerContractUpdateBo4.setDueDate(1603399000L);

        List<WmCustomerContractUpdateBo> wmCustomerContractUpdateBoList = Lists.newArrayList(wmCustomerContractUpdateBo, wmCustomerContractUpdateBo5, wmCustomerContractUpdateBo1, wmCustomerContractUpdateBo6, wmCustomerContractUpdateBo2, wmCustomerContractUpdateBo3, wmCustomerContractUpdateBo4);
        OpCustomerResultBo opCustomerResultBo = wmCustomerContractExternalThriftService.batchUpdateCustomerContractDueDateForShanGou(wmCustomerContractUpdateBoList, true, 2246505, "zhujiakun");
        System.out.println("### testBatchUpdate ### = " + JSON.toJSON(opCustomerResultBo));
    }
}
*/

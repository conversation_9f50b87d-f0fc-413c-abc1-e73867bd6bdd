//package com.sankuai.meituan.waimai.service.sign;
//
//import com.sankuai.meituan.waimai.BaseSpringJunit;
//import com.sankuai.meituan.waimai.customer.constant.WmEcontractConstant;
//import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpAuditService;
//import com.sankuai.meituan.waimai.customer.service.sign.WmEcontractSignManagerBzService;
//import com.sankuai.meituan.waimai.customer.service.sign.callback.handler.WmEcontractCallbackFailHandler;
//import com.sankuai.meituan.waimai.customer.service.sign.callback.handler.WmEcontractCallbackSuccessHandler;
//import com.sankuai.meituan.waimai.econtrct.client.constants.TaskConstant;
//import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskStateEnum;
//import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractNotifyBo;
//
//import org.junit.Test;
//
//import javax.annotation.Resource;
//
//public class TestWmEcontractCallbackService extends BaseSpringJunit {
//
//    @Resource
//    private WmCustomerKpAuditService wmCustomerKpAuditService;
//
//    @Resource
//    private WmEcontractCallbackSuccessHandler wmEcontractCallbackSuccessHandler;
//
//    @Resource
//    private WmEcontractCallbackFailHandler wmEcontractCallbackFailHandler;
//
//    @Resource
//    private WmEcontractSignManagerBzService wmEcontractSignManagerBzService;
//
//    @Test
//    public void callbackKP() throws Exception {
//        wmCustomerKpAuditService.signerKpAuthCallback(78, EcontractTaskStateEnum.SUCCESS);
//    }
//
//    @Test
//    public void callbackSuccess() throws Exception {
//        EcontractNotifyBo notifyBo = new EcontractNotifyBo.Builder()
//            .recordKey("EC_c1_f4106253-bd01-44")
//            .bizKey("")
//            .executeName("c1contract")
//            .state(TaskConstant.TASK_SUCCESS)
//            .code(0)
//            .msg("")
//            .stageName(WmEcontractConstant.EFFECT)
//            .build();
//        wmEcontractSignManagerBzService.handleCallBack(notifyBo);
//    }
//
//    @Test
//    public void callbackFail() throws Exception {
//        EcontractNotifyBo notifyBo = new EcontractNotifyBo.Builder()
//            .recordKey("EC_c1_f4106253-bd01-44")
//            .bizKey("")
//            .executeName("c1contract")
//            .state(TaskConstant.TASK_FAIL)
//            .code(0)
//            .msg("")
//            .stageName(WmEcontractConstant.NOT_SIGN)
//            .build();
//        wmEcontractSignManagerBzService.handleCallBack(notifyBo);
//    }
//
//    @Test
//    public void callbackC1ContractFail() throws Exception {
//        EcontractNotifyBo notifyBo = new EcontractNotifyBo.Builder()
//            .recordKey("EC_c1_f4106253-bd01-44")
//            .bizKey("")
//            .executeName("c1contract")
//            .state(TaskConstant.TASK_FAIL)
//            .code(0)
//            .msg("")
//            .stageName(WmEcontractConstant.EFFECT)
//            .build();
//        wmEcontractSignManagerBzService.handleCallBack(notifyBo);
//    }
//
//
//}

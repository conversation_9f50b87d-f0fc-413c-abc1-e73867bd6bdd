//package com.sankuai.meituan.waimai.service;
//
//import com.sankuai.meituan.waimai.customer.dao.WmCustomerDBMapper;
//import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerCommonService;
//import com.sankuai.meituan.waimai.customer.util.MasterSlaveHelper;
//import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerRealTypeEnum;
//import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
//import org.apache.thrift.TException;
//import org.junit.Test;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//
//import com.alibaba.fastjson.JSON;
//import com.google.common.collect.Sets;
//import com.sankuai.meituan.waimai.BaseSpringJunit;
//import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
//import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
//import com.sankuai.meituan.waimai.customer.util.trans.WmCustomerTransUtil;
//import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerSignMode;
//import com.sankuai.meituan.waimai.thrift.customer.domain.customer.ValidateResultBo;
//import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerBasicBo;
//import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerFormBo;
//import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerPageDate;
//
//public class TestWmCustomerService extends BaseSpringJunit {
//
//    private static final Logger LOGGER = LoggerFactory.getLogger(TestWmCustomerService.class);
//
//    @Autowired
//    private WmCustomerService   wmCustomerService;
//
//    @Autowired
//    private WmCustomerCommonService customerCommonService;
//
//    @Autowired
//    private WmCustomerDBMapper wmCustomerDBMapper;
//
//    @Test
//    public void testGetCustomerList() {
//        WmCustomerFormBo wmCustomerFormBo = new WmCustomerFormBo();
//        wmCustomerFormBo.setWmPoiId(10000210);
//        wmCustomerFormBo.setCustomerId(10014484);
//        //        wmCustomerFormBo.setCustomerId(10014489);
//        wmCustomerFormBo.setPageNo(1);
//        wmCustomerFormBo.setPageSize(10);
//        WmCustomerPageDate pageDate = null;
//        try {
//            pageDate = wmCustomerService.getCustomerList(wmCustomerFormBo);
//        } catch (WmCustomerException e) {
//            e.printStackTrace();
//        } catch (TException e) {
//            e.printStackTrace();
//        }
//        System.out.println(JSON.toJSONString(pageDate.getList()));
//    }
//
//    @Test
//    public void testUpdateCustomer_兼容旧版本客户端不传signMode情况() throws Exception {
//        ValidateResultBo validateResultBo = new ValidateResultBo();
//        WmCustomerDB wmCustomerDB = wmCustomerService.selectCustomerById(10014515);
//        WmCustomerBasicBo wmCustomerBasicBo = WmCustomerTransUtil.customerDBToBasicBo(wmCustomerDB);
//        wmCustomerBasicBo.setSignMode(null);
//        wmCustomerBasicBo.setLegalPerson(wmCustomerDB.getLegalPerson() + "修改了");
//        wmCustomerService.saveOrUpdateCustomer(wmCustomerBasicBo, true, 2251342, "李四",1);
////        wmCustomerService.saveOrUpdateCustomer(wmCustomerBasicBo, true, 2251342, "李四");
////        wmCustomerService.saveOrUpdateCustomer(wmCustomerBasicBo, true, 2251342, "李四");
//        wmCustomerService.saveOrUpdateCustomer(wmCustomerBasicBo, true, 2251342, "李四",1);
//        System.out.println("validateResultBo:" + JSON.toJSONString(validateResultBo));
//    }
//
//    @Test
//    public void testUpdateCustomer_传signMode情况() throws Exception {
//        ValidateResultBo validateResultBo = new ValidateResultBo();
//        WmCustomerDB wmCustomerDB = wmCustomerService.selectCustomerById(10014515);
//        WmCustomerBasicBo wmCustomerBasicBo = WmCustomerTransUtil.customerDBToBasicBo(wmCustomerDB);
//        wmCustomerBasicBo.setSignMode(CustomerSignMode.ELECTTRONIC.getCode());
//        wmCustomerBasicBo.setLegalPerson(wmCustomerDB.getLegalPerson() + "修改了");
//        wmCustomerService.saveOrUpdateCustomer(wmCustomerBasicBo, true, 2251342, "李四",1);
////        wmCustomerService.saveOrUpdateCustomer(wmCustomerBasicBo, true, 2251342, "李四");
////        wmCustomerService.saveOrUpdateCustomer(wmCustomerBasicBo, true, 2251342, "李四");
//        wmCustomerService.saveOrUpdateCustomer(wmCustomerBasicBo, true, 2251342, "李四",1);
//        System.out.println("validateResultBo:" + JSON.toJSONString(validateResultBo));
//    }
//
//    @Test
//    public void addUrl() throws Exception {
//        wmCustomerService.addCustomerCommonQuaAndOther(1, Sets.newHashSet("1", "2"), Sets.newHashSet("4"), 2251342, "李四");
//    }
//
//    @Test
//    public void saveMudule() throws Exception {
//        WmCustomerDB wmCustomerDB = MasterSlaveHelper
//                .doInMaster(() -> wmCustomerService.selectCustomerById(123));
//        wmCustomerDBMapper.updateCustomerRealType(123, CustomerRealTypeEnum.MEISHICHENG.getValue());
//    }
//
//    @Test
//    public void getByNumber() throws Exception {
//        String customerNum = "351023520234";
//        int customerType = 2;
//        WmCustomerBasicBo newCustomerByNumberAndType = wmCustomerService.getNewCustomerByNumberAndType(customerNum, customerType);
//        System.out.println(newCustomerByNumberAndType);
//        customerNum = "8993892839829323";
//        customerType = 1;
//        newCustomerByNumberAndType = wmCustomerService.getNewCustomerByNumberAndType(customerNum, customerType);
//        System.out.println(newCustomerByNumberAndType);
//        customerNum = "411222111122121212";
//        customerType = 2;
//        newCustomerByNumberAndType = wmCustomerService.getNewCustomerByNumberAndType(customerNum, customerType);
//        System.out.println(newCustomerByNumberAndType);
//    }
//}

package com.sankuai.meituan.waimai.web.controller.agreement;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.bizsso.thrift.CaptchaTService;
import com.sankuai.meituan.waimai.config.MccConfig;
import com.sankuai.meituan.waimai.kv.client.WmKvTairClient;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.ToSignCertifyInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.service.WmEcontractSignThriftService;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.vo.sign.SellerSigningTaskQueryParam;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Qualifier;

import javax.servlet.http.HttpServletRequest;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class WmCustomerAgreementToSignControllerTest {

    @Spy
    @InjectMocks
    private WmCustomerAgreementToSignController controller;

    @Mock
    private WmEcontractSignThriftService wmEcontractSignThriftService;

    @Mock
    private CaptchaTService.Iface captchaTService;

    @Mock
    private HttpServletRequest request;

    @Mock
    @Qualifier("m_WmKvTairClient")
    private WmKvTairClient wmKvTairClient;

    private MockedStatic<MccConfig> mccConfigMockedStatic;


    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        mccConfigMockedStatic = mockStatic(MccConfig.class);
    }

    @After
    public void tearDown() {
        mccConfigMockedStatic.close();
    }

    /**
     * wmPoiId <= 0
     */
    @Test
    public void testSendCaptchaInvalidWmPoiId() {
        JSONObject result = (JSONObject) controller.sendCaptcha(0, "1234567890", 1, request);
        // assert
        assertNotNull(result);
        assertEquals("1", result.getString("code"));
        assertEquals("入参不合法", result.getString("msg"));
    }

    /**
     * captchaType <= 0
     */
    @Test
    public void testSendCaptchaInvalidCaptchaType() {
        JSONObject result = (JSONObject) controller.sendCaptcha(1, "1234567890", 0, request);
        // assert
        assertNotNull(result);
        assertEquals("1", result.getString("code"));
        assertEquals("入参不合法", result.getString("msg"));
    }

    /**
     * kpPhoneNum 为空
     */
    @Test
    public void testSendCaptchaEmptyKpPhoneNum() {
        JSONObject result = (JSONObject) controller.sendCaptcha(1, "", 1, request);
        // assert
        assertNotNull(result);
        assertEquals("1", result.getString("code"));
        assertEquals("入参不合法", result.getString("msg"));
    }

    /**
     * certPhone 和 kpPhoneNum 不一致
     */
    @Test
    public void testSendCaptchaCertPhoneMismatch() throws Exception {
        ToSignCertifyInfoBo certifyInfoBo = new ToSignCertifyInfoBo();
        certifyInfoBo.setCertPhone("9876543210");
        when(wmEcontractSignThriftService.queryToSignCertifyInfoBoByWmPoiId(1L)).thenReturn(certifyInfoBo);

        JSONObject result = (JSONObject) controller.sendCaptcha(1, "1234567890", 1, request);
        // assert
        assertNotNull(result);
        assertEquals("2", result.getString("code"));
        assertEquals("签约人手机号与认证手机号不一致", result.getString("msg"));
    }

    /**
     * 正常场景：验证码校验通过，成功获取签约任务列表
     */
    @Test
    public void testQuerySellerSigningTask_Normal() throws Throwable {
        // arrange
        SellerSigningTaskQueryParam param = new SellerSigningTaskQueryParam();
        param.setCustomerId(123);
        param.setKpPhoneNum("1234567890");
        param.setTaskType(1);
        param.setCaptcha("validCaptcha");
        param.setWmPoiId(null);

        when(MccConfig.sellerSigningTaskVerifyCaptchaForCert()).thenReturn(true);
        doNothing().when(controller).verifySellerSigningTaskCaptchaForCert(anyInt(), anyString(), anyString(), any());

        // act
        Object result = controller.querySellerSigningTask(param);

        // assert
        assertNotNull(result);
        verify(controller, times(1)).verifySellerSigningTaskCaptchaForCert(anyInt(), anyString(), anyString(), any());
    }

    /**
     * 异常场景：验证码校验失败
     */
    @Test
    public void testQuerySellerSigningTask_CaptchaValidationFailed() throws Throwable {
        // arrange
        SellerSigningTaskQueryParam param = new SellerSigningTaskQueryParam();
        param.setCustomerId(123);
        param.setKpPhoneNum("1234567890");
        param.setTaskType(1);
        param.setCaptcha("invalidCaptcha");
        param.setWmPoiId(null);

        when(MccConfig.sellerSigningTaskVerifyCaptchaForCert()).thenReturn(true);
        doThrow(new WmCustomerException(WmContractErrorCodeConstant.AUTHORIZING, "验证码校验失败")).when(controller).verifySellerSigningTaskCaptchaForCert(anyInt(), anyString(), anyString(), any());

        // act
        Object result = controller.querySellerSigningTask(param);

        // assert
        assertNotNull(result);
        assertEquals("验证码校验失败", JSON.parseObject(result.toString()).getString("msg"));
    }

    /**
     * 异常场景：调用 queryPendingTaskList 方法时抛出异常
     */
    @Test
    public void testQuerySellerSigningTask_QueryPendingTaskListException() throws Throwable {
        // arrange
        SellerSigningTaskQueryParam param = new SellerSigningTaskQueryParam();
        param.setCustomerId(123);
        param.setKpPhoneNum("1234567890");
        param.setTaskType(1);
        param.setCaptcha("validCaptcha");
        param.setWmPoiId(null);

        when(MccConfig.sellerSigningTaskVerifyCaptchaForCert()).thenReturn(false);
        doThrow(new RuntimeException("查询任务列表异常")).when(controller).queryPendingTaskList(anyInt(), anyString(), anyInt(), any());

        // act
        Object result = controller.querySellerSigningTask(param);

        // assert
        assertNotNull(result);
        assertEquals("系统异常，请稍后重试", JSON.parseObject(result.toString()).getString("msg"));
    }

    /**
     * 异常场景：未知异常
     */
    @Test
    public void testQuerySellerSigningTask_UnknownException() throws Throwable {
        // arrange
        SellerSigningTaskQueryParam param = new SellerSigningTaskQueryParam();
        param.setCustomerId(123);
        param.setKpPhoneNum("1234567890");
        param.setTaskType(1);
        param.setCaptcha("validCaptcha");
        param.setWmPoiId(null);

        when(MccConfig.sellerSigningTaskVerifyCaptchaForCert()).thenReturn(false);
        doThrow(new RuntimeException("未知异常")).when(controller).queryPendingTaskList(anyInt(), anyString(), anyInt(), any());

        // act
        Object result = controller.querySellerSigningTask(param);

        // assert
        assertNotNull(result);
        assertEquals("系统异常，请稍后重试", JSON.parseObject(result.toString()).getString("msg"));
    }
}
package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.delivery.pdfdelete.nationalsubsidy;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.meituan.waimai.customer.constant.sign.DeliveryPdfDataTypeEnum;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateConstant;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateEnum;
import java.util.*;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class NationalSubsidySgNewQikePerformanceDeleteTest {

    private NationalSubsidySgNewQikePerformanceDelete deleteUnderTest;

    @Before
    public void setUp() {
        // Create a subclass that overrides the problematic method
        deleteUnderTest = new TestableNationalSubsidySgNewQikePerformanceDelete();
    }

    /**
     * Test scenario: pdfDataMap contains non-empty data for NATIONAL_SUBSIDY_SG_NEW_QIKE_PERFORMANCE
     * Expected: No removal operation should be performed
     */
    @Test
    public void testDelete_WhenPdfDataContainsNonEmptyList() throws Throwable {
        // arrange
        Map<String, Collection<SignTemplateEnum>> tabPdfMap = new HashMap<>();
        Map<String, List<String>> pdfDataMap = new HashMap<>();
        List<String> dataList = new ArrayList<>();
        dataList.add("data");
        pdfDataMap.put(DeliveryPdfDataTypeEnum.NATIONAL_SUBSIDY_SG_NEW_QIKE_PERFORMANCE.getName(), dataList);
        // act
        deleteUnderTest.delete(tabPdfMap, pdfDataMap);
        // assert
        TestableNationalSubsidySgNewQikePerformanceDelete testable = (TestableNationalSubsidySgNewQikePerformanceDelete) deleteUnderTest;
        assertFalse(testable.wasRemoveIfCalled());
    }

    /**
     * Test scenario: pdfDataMap contains empty list for NATIONAL_SUBSIDY_SG_NEW_QIKE_PERFORMANCE
     * Expected: Should attempt to remove the specific template
     */
    @Test
    public void testDelete_WhenPdfDataContainsEmptyList() throws Throwable {
        // arrange
        Map<String, Collection<SignTemplateEnum>> tabPdfMap = new HashMap<>();
        Collection<SignTemplateEnum> templates = new ArrayList<>();
        templates.add(SignTemplateEnum.NATIONAL_SUBSIDY_PERFORMANCE_SERVICE_SG_QIKE_V2_FEEMODE);
        templates.add(SignTemplateEnum.C1CONTRACT_INFO_V3);
        tabPdfMap.put(SignTemplateConstant.TAB_NATION_SUBSIDY_DELIVERY_PERFORMANCE_SERVICE, templates);
        Map<String, List<String>> pdfDataMap = new HashMap<>();
        pdfDataMap.put(DeliveryPdfDataTypeEnum.NATIONAL_SUBSIDY_SG_NEW_QIKE_PERFORMANCE.getName(), new ArrayList<>());
        // act
        deleteUnderTest.delete(tabPdfMap, pdfDataMap);
        // assert
        TestableNationalSubsidySgNewQikePerformanceDelete testable = (TestableNationalSubsidySgNewQikePerformanceDelete) deleteUnderTest;
        assertTrue(testable.wasRemoveIfCalled());
        assertEquals(SignTemplateEnum.NATIONAL_SUBSIDY_PERFORMANCE_SERVICE_SG_QIKE_V2_FEEMODE, testable.getTemplateToRemove());
    }

    /**
     * Test scenario: pdfDataMap does not contain NATIONAL_SUBSIDY_SG_NEW_QIKE_PERFORMANCE key
     * Expected: Should attempt to remove the specific template
     */
    @Test
    public void testDelete_WhenPdfDataMissingKey() throws Throwable {
        // arrange
        Map<String, Collection<SignTemplateEnum>> tabPdfMap = new HashMap<>();
        Collection<SignTemplateEnum> templates = new ArrayList<>();
        templates.add(SignTemplateEnum.NATIONAL_SUBSIDY_PERFORMANCE_SERVICE_SG_QIKE_V2_FEEMODE);
        tabPdfMap.put(SignTemplateConstant.TAB_NATION_SUBSIDY_DELIVERY_PERFORMANCE_SERVICE, templates);
        Map<String, List<String>> pdfDataMap = new HashMap<>();
        // act
        deleteUnderTest.delete(tabPdfMap, pdfDataMap);
        // assert
        TestableNationalSubsidySgNewQikePerformanceDelete testable = (TestableNationalSubsidySgNewQikePerformanceDelete) deleteUnderTest;
        assertTrue(testable.wasRemoveIfCalled());
    }

    /**
     * Test scenario: pdfDataMap is null
     * Expected: Should attempt to remove the specific template
     */
    @Test
    public void testDelete_WhenPdfDataMapIsNull() throws Throwable {
        // arrange
        Map<String, Collection<SignTemplateEnum>> tabPdfMap = new HashMap<>();
        Collection<SignTemplateEnum> templates = new ArrayList<>();
        templates.add(SignTemplateEnum.NATIONAL_SUBSIDY_PERFORMANCE_SERVICE_SG_QIKE_V2_FEEMODE);
        tabPdfMap.put(SignTemplateConstant.TAB_NATION_SUBSIDY_DELIVERY_PERFORMANCE_SERVICE, templates);
        // act
        deleteUnderTest.delete(tabPdfMap, null);
        // assert
        TestableNationalSubsidySgNewQikePerformanceDelete testable = (TestableNationalSubsidySgNewQikePerformanceDelete) deleteUnderTest;
        assertTrue(testable.wasRemoveIfCalled());
    }

    /**
     * Test scenario: tabPdfMap is null
     * Expected: Should not throw exception
     */
    @Test
    public void testDelete_WhenTabPdfMapIsNull() throws Throwable {
        // arrange
        Map<String, List<String>> pdfDataMap = new HashMap<>();
        pdfDataMap.put(DeliveryPdfDataTypeEnum.NATIONAL_SUBSIDY_SG_NEW_QIKE_PERFORMANCE.getName(), new ArrayList<>());
        // act & assert
        try {
            deleteUnderTest.delete(null, pdfDataMap);
        } catch (Exception e) {
            fail("Should not throw exception when tabPdfMap is null");
        }
    }

    /**
     * Test scenario: tabPdfMap does not contain TAB_NATION_SUBSIDY_DELIVERY_PERFORMANCE_SERVICE key
     * Expected: Should not attempt to remove any template
     */
    @Test
    public void testDelete_WhenTabPdfMapDoesNotContainKey() throws Throwable {
        // arrange
        Map<String, Collection<SignTemplateEnum>> tabPdfMap = new HashMap<>();
        Map<String, List<String>> pdfDataMap = new HashMap<>();
        pdfDataMap.put(DeliveryPdfDataTypeEnum.NATIONAL_SUBSIDY_SG_NEW_QIKE_PERFORMANCE.getName(), new ArrayList<>());
        // act
        deleteUnderTest.delete(tabPdfMap, pdfDataMap);
        // assert
        TestableNationalSubsidySgNewQikePerformanceDelete testable = (TestableNationalSubsidySgNewQikePerformanceDelete) deleteUnderTest;
        assertFalse(testable.wasRemoveIfCalled());
    }

    /**
     * Test scenario: tabList is empty
     * Expected: Should not attempt to remove any template
     */
    @Test
    public void testDelete_WhenTabListIsEmpty() throws Throwable {
        // arrange
        Map<String, Collection<SignTemplateEnum>> tabPdfMap = new HashMap<>();
        tabPdfMap.put(SignTemplateConstant.TAB_NATION_SUBSIDY_DELIVERY_PERFORMANCE_SERVICE, new ArrayList<>());
        Map<String, List<String>> pdfDataMap = new HashMap<>();
        pdfDataMap.put(DeliveryPdfDataTypeEnum.NATIONAL_SUBSIDY_SG_NEW_QIKE_PERFORMANCE.getName(), new ArrayList<>());
        // act
        deleteUnderTest.delete(tabPdfMap, pdfDataMap);
        // assert
        TestableNationalSubsidySgNewQikePerformanceDelete testable = (TestableNationalSubsidySgNewQikePerformanceDelete) deleteUnderTest;
        assertFalse(testable.wasRemoveIfCalled());
    }

    /**
     * A testable subclass that overrides the problematic removeIf call
     */
    private static class TestableNationalSubsidySgNewQikePerformanceDelete extends NationalSubsidySgNewQikePerformanceDelete {

        private boolean removeIfCalled = false;

        private SignTemplateEnum templateToRemove = null;

        @Override
        public void delete(Map<String, Collection<SignTemplateEnum>> tabPdfMap, Map<String, List<String>> pdfDataMap) {
            if (CollectionUtils.isEmpty(pdfDataMap == null ? null : pdfDataMap.get(DeliveryPdfDataTypeEnum.NATIONAL_SUBSIDY_SG_NEW_QIKE_PERFORMANCE.getName()))) {
                Collection<SignTemplateEnum> tabList = tabPdfMap == null ? null : tabPdfMap.get(SignTemplateConstant.TAB_NATION_SUBSIDY_DELIVERY_PERFORMANCE_SERVICE);
                if (CollectionUtils.isNotEmpty(tabList)) {
                    // Instead of calling removeIf, we just record that it would have been called
                    removeIfCalled = true;
                    templateToRemove = SignTemplateEnum.NATIONAL_SUBSIDY_PERFORMANCE_SERVICE_SG_QIKE_V2_FEEMODE;
                }
            }
        }

        public boolean wasRemoveIfCalled() {
            return removeIfCalled;
        }

        public SignTemplateEnum getTemplateToRemove() {
            return templateToRemove;
        }
    }
}

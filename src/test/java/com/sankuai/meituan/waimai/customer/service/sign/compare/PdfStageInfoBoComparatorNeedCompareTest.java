package com.sankuai.meituan.waimai.customer.service.sign.compare;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class PdfStageInfoBoComparatorNeedCompareTest {

    private PdfStageInfoBoComparator comparator;

    private Method needCompareMethod;

    @Before
    public void setUp() throws Exception {
        comparator = new PdfStageInfoBoComparator();
        needCompareMethod = PdfStageInfoBoComparator.class.getDeclaredMethod("needCompare", String.class);
        needCompareMethod.setAccessible(true);
    }

    /**
     * 测试key为null的情况，应返回true
     */
    @Test
    public void testNeedCompareWhenKeyIsNull() throws Throwable {
        try (MockedStatic<MccConfig> mocked = mockStatic(MccConfig.class)) {
            mocked.when(MccConfig::getPhfCompareKeyWhiteList).thenReturn(Collections.emptyList());
            boolean result = (boolean) needCompareMethod.invoke(comparator, new Object[] { null });
            assertTrue("When key is null, should return true", result);
        }
    }

    /**
     * 测试key不为空但白名单为空的情况，应返回true
     */
    @Test
    public void testNeedCompareWhenKeyNotNullButWhiteListEmpty() throws Throwable {
        try (MockedStatic<MccConfig> mocked = mockStatic(MccConfig.class)) {
            mocked.when(MccConfig::getPhfCompareKeyWhiteList).thenReturn(Collections.emptyList());
            boolean result = (boolean) needCompareMethod.invoke(comparator, "testKey");
            assertTrue("When whitelist is empty, should return true", result);
        }
    }

    /**
     * 测试key不为空且白名单不为空但key不包含任何白名单项的情况，应返回true
     */
    @Test
    public void testNeedCompareWhenKeyNotInWhiteList() throws Throwable {
        try (MockedStatic<MccConfig> mocked = mockStatic(MccConfig.class)) {
            List<String> whiteList = Arrays.asList("white1", "white2");
            mocked.when(MccConfig::getPhfCompareKeyWhiteList).thenReturn(whiteList);
            boolean result = (boolean) needCompareMethod.invoke(comparator, "testKey");
            assertTrue("When key doesn't contain any whitelist item, should return true", result);
        }
    }

    /**
     * 测试key不为空且白名单不为空且key包含白名单项的情况，应返回false
     */
    @Test
    public void testNeedCompareWhenKeyContainsWhiteListItem() throws Throwable {
        try (MockedStatic<MccConfig> mocked = mockStatic(MccConfig.class)) {
            List<String> whiteList = Arrays.asList("test", "white2");
            mocked.when(MccConfig::getPhfCompareKeyWhiteList).thenReturn(whiteList);
            boolean result = (boolean) needCompareMethod.invoke(comparator, "testKey");
            assertFalse("When key contains whitelist item, should return false", result);
        }
    }

    /**
     * 测试白名单项为空字符串的情况，应返回true
     */
    @Test
    public void testNeedCompareWhenWhiteListContainsEmptyString() throws Throwable {
        try (MockedStatic<MccConfig> mocked = mockStatic(MccConfig.class)) {
            List<String> whiteList = Arrays.asList("", "white2");
            mocked.when(MccConfig::getPhfCompareKeyWhiteList).thenReturn(whiteList);
            boolean result = (boolean) needCompareMethod.invoke(comparator, "testKey");
            assertTrue("When whitelist contains empty string, should return true", result);
        }
    }

    /**
     * 测试白名单为null的情况，应返回true
     */
    @Test
    public void testNeedCompareWhenWhiteListIsNull() throws Throwable {
        try (MockedStatic<MccConfig> mocked = mockStatic(MccConfig.class)) {
            mocked.when(MccConfig::getPhfCompareKeyWhiteList).thenReturn(null);
            boolean result = (boolean) needCompareMethod.invoke(comparator, "testKey");
            assertTrue("When whitelist is null, should return true", result);
        }
    }
}
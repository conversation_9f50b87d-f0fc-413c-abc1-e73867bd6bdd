package com.sankuai.meituan.waimai.customer.service.sign.compare.phf;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.EcontractBatchBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.PhfTransferContext;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.util.ArrayList;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.*;
import org.slf4j.Logger;

@RunWith(MockitoJUnitRunner.class)
public class WmEcontractPhfJhsCompareServiceCompareTest {

    @Spy
    @InjectMocks
    private WmEcontractPhfJhsCompareService wmEcontractPhfJhsCompareService;

    @Mock
    private EcontractBatchBo currentData;

    @Mock
    private EcontractBatchContextBo batchContextBo;

    @Mock
    private Logger log;

    private List<PhfTransferContext> phfTransferContextList;

    @Before
    public void setUp() {
        phfTransferContextList = new ArrayList<>();
        PhfTransferContext context = new PhfTransferContext("phfContractId", "recordKey", 123L);
        phfTransferContextList.add(context);
        when(batchContextBo.getPhfTransferContextList()).thenReturn(phfTransferContextList);
        // Mock the logger with specific argument matchers
    }

    /**
     * Test case for normal execution with valid data
     */
    @Test
    public void testCompareWithValidData() throws Throwable {
        // arrange
        doNothing().when(wmEcontractPhfJhsCompareService).compareSinglePoiPdf(any(), any());
        // act
        wmEcontractPhfJhsCompareService.compare(currentData, batchContextBo);
        // assert
        verify(batchContextBo, times(1)).getPhfTransferContextList();
        verify(wmEcontractPhfJhsCompareService, times(1)).compareSinglePoiPdf(currentData, phfTransferContextList);
    }

    /**
     * Test case for null batchContextBo
     */
    @Test(expected = NullPointerException.class)
    public void testCompareWithNullBatchContextBo() throws Throwable {
        // act
        wmEcontractPhfJhsCompareService.compare(currentData, null);
    }

    /**
     * Test case for null phfTransferContextList
     */
    @Test
    public void testCompareWithNullPhfTransferContextList() throws Throwable {
        // arrange
        when(batchContextBo.getPhfTransferContextList()).thenReturn(null);
        doNothing().when(wmEcontractPhfJhsCompareService).compareSinglePoiPdf(any(), any());
        // act
        wmEcontractPhfJhsCompareService.compare(currentData, batchContextBo);
        // assert
        verify(batchContextBo, times(1)).getPhfTransferContextList();
        verify(wmEcontractPhfJhsCompareService, times(1)).compareSinglePoiPdf(currentData, null);
    }

    /**
     * Test case for empty phfTransferContextList
     */
    @Test
    public void testCompareWithEmptyPhfTransferContextList() throws Throwable {
        // arrange
        when(batchContextBo.getPhfTransferContextList()).thenReturn(new ArrayList<>());
        doNothing().when(wmEcontractPhfJhsCompareService).compareSinglePoiPdf(any(), any());
        // act
        wmEcontractPhfJhsCompareService.compare(currentData, batchContextBo);
        // assert
        verify(batchContextBo, times(1)).getPhfTransferContextList();
        verify(wmEcontractPhfJhsCompareService, times(1)).compareSinglePoiPdf(currentData, new ArrayList<>());
    }

    /**
     * Test case for null currentData
     */
    @Test
    public void testCompareWithNullCurrentData() throws Throwable {
        // arrange
        doNothing().when(wmEcontractPhfJhsCompareService).compareSinglePoiPdf(any(), any());
        // act
        wmEcontractPhfJhsCompareService.compare(null, batchContextBo);
        // assert
        verify(batchContextBo, times(1)).getPhfTransferContextList();
        verify(wmEcontractPhfJhsCompareService, times(1)).compareSinglePoiPdf(null, phfTransferContextList);
    }

    /**
     * Test case for WmCustomerException from compareSinglePoiPdf
     */
    @Test(expected = WmCustomerException.class)
    public void testCompareWithCompareSinglePoiPdfException() throws Throwable {
        // arrange
        doThrow(new WmCustomerException(1001, "Compare failed")).when(wmEcontractPhfJhsCompareService).compareSinglePoiPdf(any(), any());
        // act
        wmEcontractPhfJhsCompareService.compare(currentData, batchContextBo);
    }
}
package com.sankuai.meituan.waimai.customer.service.sign.applyecontract.phf;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.customer.service.sign.compare.phf.WmEcontractPhfCompareService;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.EcontractBatchBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageBatchInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplySubTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.springframework.test.util.ReflectionTestUtils;

@RunWith(MockitoJUnitRunner.class)
public class AbstractWmEcontractPhfSubApplyAdapterServiceTest {

    @InjectMocks
    private AbstractWmEcontractPhfSubApplyAdapterService service = new AbstractWmEcontractPhfSubApplyAdapterService() {

        @Override
        public EcontractTaskApplySubTypeEnum getSubTypeEnum() {
            return EcontractTaskApplySubTypeEnum.PHF_VIRTUAL;
        }
    };

    private Method updateBatchContextUrlMethod;

    @Mock
    private WmEcontractPhfCompareService wmEcontractPhfCompareService;

    @Before
    public void setUp() throws Exception {
        updateBatchContextUrlMethod = AbstractWmEcontractPhfSubApplyAdapterService.class.getDeclaredMethod("updateBatchContextUrl", EcontractBatchContextBo.class, StageBatchInfoBo.class);
        updateBatchContextUrlMethod.setAccessible(true);
    }

    /**
     * Test case: When pdfContentInfoBoMap is null
     * Expected: Method returns early without modifying downLoadUrl
     */
    @Test
    public void testUpdateBatchContextUrl_WhenPdfMapIsNull() throws Throwable {
        // arrange
        EcontractBatchContextBo batchContextBo = new EcontractBatchContextBo();
        String originalUrl = "original_url";
        batchContextBo.setDownLoadUrl(originalUrl);
        StageBatchInfoBo stageBatchInfoBo = new StageBatchInfoBo();
        stageBatchInfoBo.setPdfContentInfoBoMap(null);
        // act
        updateBatchContextUrlMethod.invoke(service, batchContextBo, stageBatchInfoBo);
        // assert
        assertEquals(originalUrl, batchContextBo.getDownLoadUrl());
    }

    /**
     * Test case: When pdfContentInfoBoMap is empty
     * Expected: Method returns early without modifying downLoadUrl
     */
    @Test
    public void testUpdateBatchContextUrl_WhenPdfMapIsEmpty() throws Throwable {
        // arrange
        EcontractBatchContextBo batchContextBo = new EcontractBatchContextBo();
        String originalUrl = "original_url";
        batchContextBo.setDownLoadUrl(originalUrl);
        StageBatchInfoBo stageBatchInfoBo = new StageBatchInfoBo();
        stageBatchInfoBo.setPdfContentInfoBoMap(new HashMap<>());
        // act
        updateBatchContextUrlMethod.invoke(service, batchContextBo, stageBatchInfoBo);
        // assert
        assertEquals(originalUrl, batchContextBo.getDownLoadUrl());
    }

    /**
     * Test case: When downLoadUrl is blank and valid PDF content exists
     * Expected: Creates new JSON object with PDF URL
     */
    @Test
    public void testUpdateBatchContextUrl_WhenDownloadUrlBlank() throws Throwable {
        // arrange
        EcontractBatchContextBo batchContextBo = new EcontractBatchContextBo();
        batchContextBo.setDownLoadUrl("");
        StageBatchInfoBo stageBatchInfoBo = new StageBatchInfoBo();
        Map<String, List<PdfContentInfoBo>> pdfMap = new HashMap<>();
        List<PdfContentInfoBo> pdfList = new ArrayList<>();
        PdfContentInfoBo pdfInfo = new PdfContentInfoBo();
        pdfInfo.setPdfUrl("http://test.pdf");
        pdfList.add(pdfInfo);
        pdfMap.put("key1", pdfList);
        stageBatchInfoBo.setPdfContentInfoBoMap(pdfMap);
        // act
        updateBatchContextUrlMethod.invoke(service, batchContextBo, stageBatchInfoBo);
        // assert
        JSONObject expected = new JSONObject();
        expected.put("key1", "http://test.pdf");
        assertEquals(expected.toJSONString(), batchContextBo.getDownLoadUrl());
    }

    /**
     * Test case: When pdfContentInfoBoList is empty
     * Expected: Skips entry without modifying JSON
     */
    @Test
    public void testUpdateBatchContextUrl_WhenPdfListEmpty() throws Throwable {
        // arrange
        EcontractBatchContextBo batchContextBo = new EcontractBatchContextBo();
        batchContextBo.setDownLoadUrl("{}");
        StageBatchInfoBo stageBatchInfoBo = new StageBatchInfoBo();
        Map<String, List<PdfContentInfoBo>> pdfMap = new HashMap<>();
        pdfMap.put("key1", new ArrayList<>());
        stageBatchInfoBo.setPdfContentInfoBoMap(pdfMap);
        // act
        updateBatchContextUrlMethod.invoke(service, batchContextBo, stageBatchInfoBo);
        // assert
        assertEquals("{}", batchContextBo.getDownLoadUrl());
    }

    /**
     * Test case: When pdfContentInfoBoList has multiple items
     * Expected: Skips entry without modifying JSON
     */
    @Test
    public void testUpdateBatchContextUrl_WhenPdfListHasMultipleItems() throws Throwable {
        // arrange
        EcontractBatchContextBo batchContextBo = new EcontractBatchContextBo();
        batchContextBo.setDownLoadUrl("{}");
        StageBatchInfoBo stageBatchInfoBo = new StageBatchInfoBo();
        Map<String, List<PdfContentInfoBo>> pdfMap = new HashMap<>();
        List<PdfContentInfoBo> pdfList = new ArrayList<>();
        pdfList.add(new PdfContentInfoBo());
        pdfList.add(new PdfContentInfoBo());
        pdfMap.put("key1", pdfList);
        stageBatchInfoBo.setPdfContentInfoBoMap(pdfMap);
        // act
        updateBatchContextUrlMethod.invoke(service, batchContextBo, stageBatchInfoBo);
        // assert
        assertEquals("{}", batchContextBo.getDownLoadUrl());
    }

    /**
     * Test case: When existing downLoadUrl has content and new valid PDF content exists
     * Expected: Merges new PDF URL with existing content
     */
    @Test
    public void testUpdateBatchContextUrl_WhenMergingWithExistingContent() throws Throwable {
        // arrange
        EcontractBatchContextBo batchContextBo = new EcontractBatchContextBo();
        JSONObject existing = new JSONObject();
        existing.put("existing_key", "existing_url");
        batchContextBo.setDownLoadUrl(existing.toJSONString());
        StageBatchInfoBo stageBatchInfoBo = new StageBatchInfoBo();
        Map<String, List<PdfContentInfoBo>> pdfMap = new HashMap<>();
        List<PdfContentInfoBo> pdfList = new ArrayList<>();
        PdfContentInfoBo pdfInfo = new PdfContentInfoBo();
        pdfInfo.setPdfUrl("http://new.pdf");
        pdfList.add(pdfInfo);
        pdfMap.put("new_key", pdfList);
        stageBatchInfoBo.setPdfContentInfoBoMap(pdfMap);
        // act
        updateBatchContextUrlMethod.invoke(service, batchContextBo, stageBatchInfoBo);
        // assert
        JSONObject expected = new JSONObject();
        expected.put("existing_key", "existing_url");
        expected.put("new_key", "http://new.pdf");
        assertEquals(expected.toJSONString(), batchContextBo.getDownLoadUrl());
    }

    /**
     * Test normal execution of compareSignData
     */
    @Test
    public void testCompareSignData_NormalExecution() throws Throwable {
        // arrange
        EcontractBatchBo econtractBatchBo = new EcontractBatchBo();
        EcontractBatchContextBo batchContextBo = new EcontractBatchContextBo();
        // act
        service.compareSignData(econtractBatchBo, batchContextBo);
        // assert
        verify(wmEcontractPhfCompareService).submitCompareTask(eq(econtractBatchBo), eq(batchContextBo), eq(EcontractTaskApplySubTypeEnum.PHF_VIRTUAL));
    }

    /**
     * Test compareSignData with null EcontractBatchBo
     */
    @Test
    public void testCompareSignData_NullEcontractBatchBo() throws Throwable {
        // arrange
        EcontractBatchContextBo batchContextBo = new EcontractBatchContextBo();
        // act
        service.compareSignData(null, batchContextBo);
        // assert
        verify(wmEcontractPhfCompareService).submitCompareTask(isNull(), eq(batchContextBo), eq(EcontractTaskApplySubTypeEnum.PHF_VIRTUAL));
    }

    /**
     * Test compareSignData with null EcontractBatchContextBo
     */
    @Test
    public void testCompareSignData_NullBatchContextBo() throws Throwable {
        // arrange
        EcontractBatchBo econtractBatchBo = new EcontractBatchBo();
        // act
        service.compareSignData(econtractBatchBo, null);
        // assert
        verify(wmEcontractPhfCompareService).submitCompareTask(eq(econtractBatchBo), isNull(), eq(EcontractTaskApplySubTypeEnum.PHF_VIRTUAL));
    }

    /**
     * Test compareSignData with all null parameters
     */
    @Test
    public void testCompareSignData_AllNullParameters() throws Throwable {
        // act
        service.compareSignData(null, null);
        // assert
        verify(wmEcontractPhfCompareService).submitCompareTask(isNull(), isNull(), eq(EcontractTaskApplySubTypeEnum.PHF_VIRTUAL));
    }

    /**
     * Test compareSignData when getSubTypeEnum returns null
     */
    @Test
    public void testCompareSignData_NullSubType() throws Throwable {
        // arrange
        EcontractBatchBo econtractBatchBo = new EcontractBatchBo();
        EcontractBatchContextBo batchContextBo = new EcontractBatchContextBo();
        AbstractWmEcontractPhfSubApplyAdapterService nullSubTypeService = new AbstractWmEcontractPhfSubApplyAdapterService() {

            @Override
            public EcontractTaskApplySubTypeEnum getSubTypeEnum() {
                return null;
            }
        };
        // Use ReflectionTestUtils to set private field
        ReflectionTestUtils.setField(nullSubTypeService, "wmEcontractPhfCompareService", wmEcontractPhfCompareService);
        // act
        nullSubTypeService.compareSignData(econtractBatchBo, batchContextBo);
        // assert
        verify(wmEcontractPhfCompareService).submitCompareTask(eq(econtractBatchBo), eq(batchContextBo), isNull());
    }
}
package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.delivery.pdfsplit;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.customer.BaseStaticMockTest;
import com.sankuai.meituan.waimai.customer.bo.sign.EcontractBatchMiddleBo;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.constant.sign.DeliveryPdfDataTypeEnum;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateConstant;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateEnum;
import com.sankuai.meituan.waimai.heron.poilogistics.thrift.constants.LogisticsFeeModeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryInfoBo;
import java.util.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;

public class TechnicalServiceSg22SplitTest extends BaseStaticMockTest {

    private TechnicalServiceSg22Split technicalServiceSg22Split;

    private EcontractDeliveryInfoBo deliveryInfoBo;

    private EcontractBatchMiddleBo middleContext;

    public static String MCC_CONFIG = "{\"TECHNICAL_SERVICE_SG_20\":true,\"TECHNICAL_SERVICE_SG_22\":true,\"PERFORMANCE_SERVICE_SG_22\":true,\"PERFORMANCE_SERVICE_SG_22_NKS\":true}";

    public static Map<String, Boolean> getGrayNewPlatformTemplateEnumMap() {
        return JSONObject.parseObject(MCC_CONFIG,
                new com.alibaba.fastjson.TypeReference<Map<String, Boolean>>() {});
    }

    @Before
    public void setUp() {
        super.setUp();
        technicalServiceSg22Split = new TechnicalServiceSg22Split();
        deliveryInfoBo = mock(EcontractDeliveryInfoBo.class);
        middleContext = new EcontractBatchMiddleBo();
        // Initialize empty maps
        middleContext.setTabPdfMap(new HashMap<>());
        middleContext.setPdfDataMap(new HashMap<>());
    }

    /**
     * Test case where the fee mode is SHANGOU_2_2 and the tabPdfMap contains the required template
     */
    @Test
    public void testSplit_FeeModeShangou2_2_AndTemplateExists() throws Throwable {
        // arrange
        // SHANGOU_2_2
        when(deliveryInfoBo.getFeeMode()).thenReturn("11");
        when(deliveryInfoBo.getDeliveryTypeUUID()).thenReturn("UUID-123");
        when(deliveryInfoBo.getWmPoiId()).thenReturn("WM-POI-1");
        when(MccConfig.getGrayNewPlatformTemplateEnumMap()).thenReturn(getGrayNewPlatformTemplateEnumMap());

        Collection<SignTemplateEnum> templates = new ArrayList<>();
        templates.add(SignTemplateEnum.TECHNICAL_SERVICE_SG_22);
        middleContext.getTabPdfMap().put(SignTemplateConstant.TAB_DELIVERY, templates);
        // act
        technicalServiceSg22Split.split(deliveryInfoBo, middleContext);
        // assert
        Map<String, List<String>> resultMap = middleContext.getPdfDataMap();
        List<String> resultList = resultMap.get(DeliveryPdfDataTypeEnum.TECHNICAL_SERVICE_SG_22.getName());
        assertEquals(1, resultList.size());
        assertEquals("UUID-123", resultList.get(0));
    }

    /**
     * Test case where the fee mode is SHANGOU_2_2 but the tabPdfMap does not contain the required template
     */
    @Test
    public void testSplit_FeeModeShangou2_2_AndTemplateNotExists() throws Throwable {
        // arrange
        // SHANGOU_2_2
        when(deliveryInfoBo.getFeeMode()).thenReturn("11");
        when(deliveryInfoBo.getDeliveryTypeUUID()).thenReturn("UUID-123");
        when(MccConfig.getGrayNewPlatformTemplateEnumMap()).thenReturn(getGrayNewPlatformTemplateEnumMap());

        middleContext.getTabPdfMap().put(SignTemplateConstant.TAB_DELIVERY, new ArrayList<>());
        // act
        technicalServiceSg22Split.split(deliveryInfoBo, middleContext);
        // assert
        Map<String, List<String>> resultMap = middleContext.getPdfDataMap();
        assertTrue(resultMap.isEmpty());
    }

    /**
     * Test case where the fee mode is SHANGOU_2_2 and existing technicalServiceSg22List has data
     */
    @Test
    public void testSplit_FeeModeShangou2_2_AndExistingList() throws Throwable {
        // arrange
        // SHANGOU_2_2
        when(deliveryInfoBo.getFeeMode()).thenReturn("11");
        when(deliveryInfoBo.getDeliveryTypeUUID()).thenReturn("UUID-456");
        when(deliveryInfoBo.getWmPoiId()).thenReturn("WM-POI-2");
        when(MccConfig.getGrayNewPlatformTemplateEnumMap()).thenReturn(getGrayNewPlatformTemplateEnumMap());

        // Set up tabPdfMap
        Collection<SignTemplateEnum> templates = new ArrayList<>();
        templates.add(SignTemplateEnum.TECHNICAL_SERVICE_SG_22);
        middleContext.getTabPdfMap().put(SignTemplateConstant.TAB_DELIVERY, templates);
        // Set up existing data in pdfDataMap
        List<String> existingList = Lists.newArrayList("EXISTING-UUID");
        middleContext.getPdfDataMap().put(DeliveryPdfDataTypeEnum.TECHNICAL_SERVICE_SG_22.getName(), existingList);
        // act
        technicalServiceSg22Split.split(deliveryInfoBo, middleContext);
        // assert
        Map<String, List<String>> resultMap = middleContext.getPdfDataMap();
        List<String> resultList = resultMap.get(DeliveryPdfDataTypeEnum.TECHNICAL_SERVICE_SG_22.getName());
        assertEquals(2, resultList.size());
        assertEquals("EXISTING-UUID", resultList.get(0));
        assertEquals("UUID-456", resultList.get(1));
    }

    /**
     * Test case where the fee mode is not SHANGOU_2_2
     */
    @Test
    public void testSplit_FeeModeNotShangou2_2() throws Throwable {
        // arrange
        // WAIMAI
        when(deliveryInfoBo.getFeeMode()).thenReturn("1");
        when(deliveryInfoBo.getDeliveryTypeUUID()).thenReturn("UUID-789");
        when(MccConfig.getGrayNewPlatformTemplateEnumMap()).thenReturn(getGrayNewPlatformTemplateEnumMap());

        Collection<SignTemplateEnum> templates = new ArrayList<>();
        templates.add(SignTemplateEnum.TECHNICAL_SERVICE_SG_22);
        middleContext.getTabPdfMap().put(SignTemplateConstant.TAB_DELIVERY, templates);
        // act
        technicalServiceSg22Split.split(deliveryInfoBo, middleContext);
        // assert
        Map<String, List<String>> resultMap = middleContext.getPdfDataMap();
        assertTrue(resultMap.isEmpty());
    }

    /**
     * Test case where tabPdfMap is empty
     */
    @Test
    public void testSplit_TabPdfMapEmpty() throws Throwable {
        // arrange
        // SHANGOU_2_2
        when(deliveryInfoBo.getFeeMode()).thenReturn("11");
        when(deliveryInfoBo.getDeliveryTypeUUID()).thenReturn("UUID-123");
        when(deliveryInfoBo.getWmPoiId()).thenReturn("WM-POI-1");
        when(MccConfig.getGrayNewPlatformTemplateEnumMap()).thenReturn(getGrayNewPlatformTemplateEnumMap());

        // act
        technicalServiceSg22Split.split(deliveryInfoBo, middleContext);
        // assert
        Map<String, List<String>> resultMap = middleContext.getPdfDataMap();
        assertTrue(resultMap.isEmpty());
    }
}
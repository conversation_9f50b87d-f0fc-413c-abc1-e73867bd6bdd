package com.sankuai.meituan.waimai.customer.service.sign.compare.phf;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.PhfTransferContext;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class WmEcontractPhfFormalCompareServiceTest {

    private WmEcontractPhfFormalCompareService wmEcontractPhfFormalCompareService;

    private WmEcontractPhfFormalCompareService service = new WmEcontractPhfFormalCompareService();

    private PhfTransferContext mockPhfTransferContext = new PhfTransferContext();

    @Before
    public void setUp() {
        wmEcontractPhfFormalCompareService = new WmEcontractPhfFormalCompareService();
    }

    private boolean invokeIsOldProcess(List<PhfTransferContext> contextList) throws Exception {
        Method method = WmEcontractPhfFormalCompareService.class.getDeclaredMethod("isOldProcess", List.class);
        method.setAccessible(true);
        return (boolean) method.invoke(service, contextList);
    }

    /**
     * 测试 getPdfContentInfoBoMapKeyByWmPoiId 方法，正常情况
     */
    @Test
    public void testGetPdfContentInfoBoMapKeyByWmPoiIdNormal() throws Throwable {
        // arrange
        Long startWmPoiId = 1L;
        Long endWmPoiId = 10L;
        String type = "test";
        // act
        String result = wmEcontractPhfFormalCompareService.getPdfContentInfoBoMapKeyByWmPoiId(startWmPoiId, endWmPoiId, type);
        // assert
        assertEquals("test_1_10", result);
    }

    /**
     * 测试 getPdfContentInfoBoMapKeyByWmPoiId 方法，异常情况
     * Note: Adjusted test case as the method under test does not throw NullPointerException for null startWmPoiId.
     */
    @Test
    public void testGetPdfContentInfoBoMapKeyByWmPoiIdException() throws Throwable {
        // arrange
        Long startWmPoiId = null;
        Long endWmPoiId = 10L;
        String type = "test";
        // act
        String result = wmEcontractPhfFormalCompareService.getPdfContentInfoBoMapKeyByWmPoiId(startWmPoiId, endWmPoiId, type);
        // assert
        // Adjusted assertion to reflect the actual behavior of the method under test.
        assertNotNull(result);
    }

    /**
     * Test isOldProcess with an empty list.
     * Expecting an IndexOutOfBoundsException.
     */
    @Test
    public void testIsOldProcess_WithEmptyList() throws Throwable {
        List<PhfTransferContext> contextList = new ArrayList<>();
        try {
            invokeIsOldProcess(contextList);
            fail("Expected IndexOutOfBoundsException was not thrown.");
        } catch (InvocationTargetException e) {
            assertTrue("Expected IndexOutOfBoundsException was wrapped in InvocationTargetException.", e.getCause() instanceof IndexOutOfBoundsException);
        }
    }

    /**
     * Test isOldProcess with a null list.
     * Expecting a NullPointerException.
     */
    @Test
    public void testIsOldProcess_WithNullList() throws Throwable {
        try {
            invokeIsOldProcess(null);
            fail("Expected NullPointerException was not thrown.");
        } catch (InvocationTargetException e) {
            assertTrue("Expected NullPointerException was wrapped in InvocationTargetException.", e.getCause() instanceof NullPointerException);
        }
    }

    /**
     * Test isOldProcess with a list containing a PhfTransferContext with a whitespace recordKey.
     */
    @Test
    public void testIsOldProcess_WithWhitespaceRecordKey() throws Throwable {
        mockPhfTransferContext.setRecordKey("   ");
        List<PhfTransferContext> contextList = Collections.singletonList(mockPhfTransferContext);
        boolean result = invokeIsOldProcess(contextList);
        assertTrue("Expected true for whitespace recordKey", result);
    }

    /**
     * Test isOldProcess with a non-empty recordKey.
     */
    @Test
    public void testIsOldProcess_WithNonEmptyRecordKey() throws Throwable {
        mockPhfTransferContext.setRecordKey("test-key");
        List<PhfTransferContext> contextList = Collections.singletonList(mockPhfTransferContext);
        boolean result = invokeIsOldProcess(contextList);
        assertTrue("Expected true for non-empty recordKey", result);
    }

    /**
     * Test isOldProcess with an empty recordKey.
     */
    @Test
    public void testIsOldProcess_WithEmptyRecordKey() throws Throwable {
        mockPhfTransferContext.setRecordKey("");
        List<PhfTransferContext> contextList = Collections.singletonList(mockPhfTransferContext);
        boolean result = invokeIsOldProcess(contextList);
        assertFalse("Expected false for empty recordKey", result);
    }

    /**
     * Test isOldProcess with a null recordKey.
     */
    @Test
    public void testIsOldProcess_WithNullRecordKey() throws Throwable {
        mockPhfTransferContext.setRecordKey(null);
        List<PhfTransferContext> contextList = Collections.singletonList(mockPhfTransferContext);
        boolean result = invokeIsOldProcess(contextList);
        assertFalse("Expected false for null recordKey", result);
    }

    /**
     * Test isOldProcess with multiple items in the list.
     */
    @Test
    public void testIsOldProcess_WithMultipleItems() throws Throwable {
        mockPhfTransferContext.setRecordKey("test-key");
        PhfTransferContext secondContext = new PhfTransferContext();
        secondContext.setRecordKey(null);
        List<PhfTransferContext> contextList = new ArrayList<>();
        contextList.add(mockPhfTransferContext);
        contextList.add(secondContext);
        boolean result = invokeIsOldProcess(contextList);
        assertTrue("Expected true based on the first item's non-empty recordKey", result);
    }
}
package com.sankuai.meituan.waimai.customer.service.customer.ownerapply;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyByte;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.adapter.WmVirtualOrgServiceAdaptor;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerPoiDBMapper;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiDB;
import com.sankuai.meituan.waimai.customer.domain.customer.WmCustomerOwnerApply;
import com.sankuai.meituan.waimai.customer.domain.customer.WmCustomerOwnerApplyRecord;
import com.sankuai.meituan.waimai.customer.service.common.WmEmployeeService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerESService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.customer.service.customer.check.WmCustomerAuthService;
import com.sankuai.meituan.waimai.customer.service.customer.ownerapply.dao.WmCustomerOwnerApplyDao;
import com.sankuai.meituan.waimai.infra.constants.WmOrgConstant;
import com.sankuai.meituan.waimai.infra.constants.WmVirtualOrgRecursiveTypeEnum;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.CustomerOwnerApplyStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.WmCustomerAuthTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.WmCustomerOplogBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.BaseResponse;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerBasicBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.frame.CustomerOwnerApplyAuthDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.frame.CustomerOwnerApplyListDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.frame.CustomerOwnerApplyQueryBO;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.frame.OwnerApplyRecordDTO;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class WmCustomerOwnerApplyBusServiceListCustomerOwnerApplyTest {

    private static final int RESULT_CODE_SUCCESS = 0;

    private static final int RESULT_CODE_ERROR = -1;

    @InjectMocks
    private WmCustomerOwnerApplyBusService wmCustomerOwnerApplyBusService;

    @Mock
    private WmCustomerPoiDBMapper wmCustomerPoiDBMapper;

    @Mock
    private CustomerOwnerApplyCheckService customerOwnerApplyCheckService;

    @Mock
    private WmCustomerESService wmCustomerESService;

    @Mock
    private WmCustomerOwnerApplyDao wmCustomerOwnerApplyDao;

    @Mock
    private WmCustomerService wmCustomerService;

    @Mock
    private WmEmployeeService wmEmployeeService;

    @Mock
    private WmVirtualOrgServiceAdaptor wmVirtualOrgServiceAdaptor;

    @Mock
    private WmCustomerAuthService wmCustomerAuthService;

    private Method convertMethod;

    @Before
    public void setUp() throws Exception {
        convertMethod = WmCustomerOwnerApplyBusService.class.getDeclaredMethod("convertOwnerApplyRecordDTO", List.class);
        convertMethod.setAccessible(true);
    }

    private WmCustomerOwnerApplyRecord createRecord(int opUid, String content, int moduleId, int opType) {
        WmCustomerOwnerApplyRecord record = new WmCustomerOwnerApplyRecord();
        record.setOpUid(opUid);
        record.setContent(content);
        record.setCtime(123456);
        record.setModuleId(moduleId);
        record.setOpType(opType);
        return record;
    }

    private WmEmploy createEmploy(int uid, String name, String misId) {
        WmEmploy employ = new WmEmploy();
        employ.setUid(uid);
        employ.setName(name);
        employ.setMisId(misId);
        return employ;
    }

    private Method getPrivateMethod() throws Exception {
        Method method = WmCustomerOwnerApplyBusService.class.getDeclaredMethod("convertCustomerOwnerApplyAuthDTO", WmCustomerOwnerApply.class, Integer.class);
        method.setAccessible(true);
        return method;
    }

    private boolean getFieldValue(CustomerOwnerApplyAuthDTO dto, String fieldName) throws Exception {
        Field field = CustomerOwnerApplyAuthDTO.class.getDeclaredField(fieldName);
        field.setAccessible(true);
        return (boolean) field.get(dto);
    }

    /**
     * Test case for customer ID mismatch when searching by poi ID
     */
    @Test
    public void testListCustomerOwnerApply_CustomerIdMismatchWithPoi() throws Throwable {
        // arrange
        CustomerOwnerApplyQueryBO queryBO = new CustomerOwnerApplyQueryBO();
        queryBO.setCustomerId(1);
        queryBO.setWmPoiId(100L);
        queryBO.setPageNo(1);
        queryBO.setPageSize(10);
        queryBO.setUid(123);
        WmCustomerPoiDB wmCustomerPoiDB = new WmCustomerPoiDB();
        // Different from queryBO.getCustomerId()
        wmCustomerPoiDB.setCustomerId(2);
        when(wmCustomerPoiDBMapper.selectCustomerPoiRelByWmPoiIdWithValid(anyLong())).thenReturn(wmCustomerPoiDB);
        when(wmVirtualOrgServiceAdaptor.getUidsByUid(anyInt(), anyByte(), anyInt())).thenReturn(Lists.newArrayList());
        // act
        BaseResponse response = wmCustomerOwnerApplyBusService.listCustomerOwnerApply(queryBO);
        // assert
        assertNotNull(response);
        assertEquals(RESULT_CODE_SUCCESS, response.getCode());
        assertEquals(Integer.valueOf(0), ((CustomerOwnerApplyListDTO) response.getData()).getTotalCnt());
    }

    /**
     * Test successful conversion of customer owner apply list
     */
    @Test
    public void testListCustomerOwnerApply_SuccessfulConversion() throws Throwable {
        // arrange
        CustomerOwnerApplyQueryBO queryBO = new CustomerOwnerApplyQueryBO();
        queryBO.setCustomerId(1);
        queryBO.setPageNo(1);
        queryBO.setPageSize(10);
        queryBO.setUid(123);
        List<WmCustomerOwnerApply> applyList = new ArrayList<>();
        WmCustomerOwnerApply apply = new WmCustomerOwnerApply();
        apply.setId(1);
        apply.setCustomerId(1);
        apply.setApplyUid(100);
        apply.setCustomerOwnerUid(200);
        // Set status to avoid NPE
        apply.setStatus(1);
        applyList.add(apply);
        WmCustomerBasicBo customerBasicBo = new WmCustomerBasicBo();
        customerBasicBo.setCustomerType(1);
        customerBasicBo.setCustomerNumber("TEST001");
        List<WmEmploy> employs = Lists.newArrayList();
        WmEmploy employ1 = new WmEmploy();
        employ1.setUid(100);
        employ1.setName("Test User");
        employ1.setMisId("test.user");
        employs.add(employ1);
        /*
        when(wmVirtualOrgServiceAdaptor.getUidsByUid(anyInt(), anyByte(), anyInt())).thenReturn(Lists.newArrayList());
        when(wmCustomerOwnerApplyDao.countByQueryBo(any())).thenReturn(1);
        when(wmCustomerOwnerApplyDao.countMaxIdByQueryBo(any())).thenReturn(100);
        when(wmCustomerOwnerApplyDao.listCustomerOwnerApplyByQueryBo(any())).thenReturn(applyList);
        when(wmCustomerService.getCustomerById(anyInt())).thenReturn(customerBasicBo);
        when(wmEmployeeService.mgetByUids(any())).thenReturn(employs);
         */

        // act
        BaseResponse response = wmCustomerOwnerApplyBusService.listCustomerOwnerApply(queryBO);
        // assert
        assertNotNull(response);
        assertEquals(RESULT_CODE_SUCCESS, response.getCode());
        CustomerOwnerApplyListDTO listDTO = (CustomerOwnerApplyListDTO) response.getData();
        assertEquals(Integer.valueOf(0), listDTO.getTotalCnt());
        assertNotNull(listDTO.getCustomerOwnerApplyDTOList());
        assertEquals(0, listDTO.getCustomerOwnerApplyDTOList().size());
    }

    /**
     * Test empty records list
     */
    @Test
    public void testConvertOwnerApplyRecordDTO_EmptyRecords() throws Throwable {
        // act
        List<OwnerApplyRecordDTO> result = (List<OwnerApplyRecordDTO>) convertMethod.invoke(wmCustomerOwnerApplyBusService, new ArrayList<>());
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test null records list
     */
    @Test
    public void testConvertOwnerApplyRecordDTO_NullRecords() throws Throwable {
        // act
        List<OwnerApplyRecordDTO> result = (List<OwnerApplyRecordDTO>) convertMethod.invoke(wmCustomerOwnerApplyBusService, new Object[] { null });
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test system user (opUid = 0)
     */
    @Test
    public void testConvertOwnerApplyRecordDTO_SystemUser() throws Throwable {
        // arrange
        WmCustomerOwnerApplyRecord record = createRecord(0, "Test Content", 1, 1);
        when(wmEmployeeService.mgetByUids(anyList())).thenReturn(Collections.emptyList());
        // act
        List<OwnerApplyRecordDTO> result = (List<OwnerApplyRecordDTO>) convertMethod.invoke(wmCustomerOwnerApplyBusService, Collections.singletonList(record));
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        OwnerApplyRecordDTO dto = result.get(0);
        assertEquals("系统", dto.getOpUname());
        assertEquals("", dto.getOpUserMisId());
        assertEquals(Integer.valueOf(0), dto.getOpUid());
        assertEquals("Test Content", dto.getContent());
        assertEquals(123456, dto.getOpTime().intValue());
    }

    /**
     * Test normal user with valid employee info
     */
    @Test
    public void testConvertOwnerApplyRecordDTO_NormalUser() throws Throwable {
        // arrange
        int uid = 100;
        WmCustomerOwnerApplyRecord record = createRecord(uid, "Test Content", 1, 1);
        WmEmploy employ = createEmploy(uid, "Test User", "test.user");
        List<WmEmploy> employList = Collections.singletonList(employ);
        when(wmEmployeeService.mgetByUids(Collections.singletonList(uid))).thenReturn(employList);
        // act
        List<OwnerApplyRecordDTO> result = (List<OwnerApplyRecordDTO>) convertMethod.invoke(wmCustomerOwnerApplyBusService, Collections.singletonList(record));
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        OwnerApplyRecordDTO dto = result.get(0);
        assertEquals("Test User", dto.getOpUname());
        assertEquals("test.user", dto.getOpUserMisId());
        assertEquals(Integer.valueOf(uid), dto.getOpUid());
    }

    /**
     * Test multiple records with different user types
     */
    @Test
    public void testConvertOwnerApplyRecordDTO_MultipleRecords() throws Throwable {
        // arrange
        int uid1 = 100;
        int uid2 = 0;
        WmCustomerOwnerApplyRecord record1 = createRecord(uid1, "Content 1", 1, 1);
        WmCustomerOwnerApplyRecord record2 = createRecord(uid2, "Content 2", 1, 1);
        WmEmploy employ = createEmploy(uid1, "Test User", "test.user");
        when(wmEmployeeService.mgetByUids(Collections.singletonList(uid1))).thenReturn(Collections.singletonList(employ));
        // act
        List<OwnerApplyRecordDTO> result = (List<OwnerApplyRecordDTO>) convertMethod.invoke(wmCustomerOwnerApplyBusService, Arrays.asList(record1, record2));
        // assert
        assertNotNull(result);
        assertEquals(2, result.size());
        OwnerApplyRecordDTO dto1 = result.get(0);
        assertEquals("Test User", dto1.getOpUname());
        assertEquals("test.user", dto1.getOpUserMisId());
        assertEquals("Content 1", dto1.getContent());
        OwnerApplyRecordDTO dto2 = result.get(1);
        assertEquals("系统", dto2.getOpUname());
        assertEquals("", dto2.getOpUserMisId());
        assertEquals("Content 2", dto2.getContent());
    }

    /**
     * Test invalid module type
     */
    @Test
    public void testConvertOwnerApplyRecordDTO_InvalidModuleType() throws Throwable {
        // arrange
        int uid = 100;
        WmCustomerOwnerApplyRecord record = createRecord(uid, "Test Content", 999, 1);
        WmEmploy employ = createEmploy(uid, "Test User", "test.user");
        when(wmEmployeeService.mgetByUids(Collections.singletonList(uid))).thenReturn(Collections.singletonList(employ));
        // act
        List<OwnerApplyRecordDTO> result = (List<OwnerApplyRecordDTO>) convertMethod.invoke(wmCustomerOwnerApplyBusService, Collections.singletonList(record));
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("", result.get(0).getModuleTypeName());
    }

    /**
     * Test invalid operation type
     */
    @Test
    public void testConvertOwnerApplyRecordDTO_InvalidOpType() throws Throwable {
        // arrange
        int uid = 100;
        WmCustomerOwnerApplyRecord record = createRecord(uid, "Test Content", 1, 999);
        WmEmploy employ = createEmploy(uid, "Test User", "test.user");
        when(wmEmployeeService.mgetByUids(Collections.singletonList(uid))).thenReturn(Collections.singletonList(employ));
        // act
        List<OwnerApplyRecordDTO> result = (List<OwnerApplyRecordDTO>) convertMethod.invoke(wmCustomerOwnerApplyBusService, Collections.singletonList(record));
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("", result.get(0).getOpTypeName());
    }

    /**
     * Test case: No write permission
     * Scenario: User has no write permission through any means
     */
    @Test
    public void testConvertCustomerOwnerApplyAuthDTO_NoWritePermission() throws Throwable {
        // arrange
        WmCustomerOwnerApply apply = new WmCustomerOwnerApply();
        apply.setApplyUid(100);
        apply.setCustomerRealType(1);
        apply.setStatus(CustomerOwnerApplyStatusEnum.AUDITING.getCode());
        when(wmCustomerAuthService.getAuthCustomerRealType(200, WmCustomerAuthTypeEnum.WRITE_AND_READ.getCode())).thenReturn(new ArrayList<>());
        when(wmVirtualOrgServiceAdaptor.getUidsByUid(200, WmOrgConstant.QUERY_ALL_SOURCE, WmVirtualOrgRecursiveTypeEnum.TOP_DOWN_NO_SELF.getType())).thenReturn(new ArrayList<>());
        // act
        CustomerOwnerApplyAuthDTO result = (CustomerOwnerApplyAuthDTO) getPrivateMethod().invoke(wmCustomerOwnerApplyBusService, apply, 200);
        // assert
        assertFalse(getFieldValue(result, "revokeApplyAuth"));
        assertFalse(getFieldValue(result, "onClickGroupAuth"));
        assertTrue(getFieldValue(result, "readApplyDetailAuth"));
        assertTrue(getFieldValue(result, "readOperateRecordAuth"));
    }

    /**
     * Test case: Has write permission but not in auditing status
     * Scenario: User has write permission but application is not in auditing status
     */
    @Test
    public void testConvertCustomerOwnerApplyAuthDTO_HasWritePermissionNotAuditing() throws Throwable {
        // arrange
        WmCustomerOwnerApply apply = new WmCustomerOwnerApply();
        apply.setApplyUid(100);
        apply.setCustomerRealType(1);
        apply.setStatus(CustomerOwnerApplyStatusEnum.COMPLETE.getCode());
        when(wmCustomerAuthService.getAuthCustomerRealType(200, WmCustomerAuthTypeEnum.WRITE_AND_READ.getCode())).thenReturn(Arrays.asList(1));
        when(wmVirtualOrgServiceAdaptor.getUidsByUid(200, WmOrgConstant.QUERY_ALL_SOURCE, WmVirtualOrgRecursiveTypeEnum.TOP_DOWN_NO_SELF.getType())).thenReturn(new ArrayList<>());
        // act
        CustomerOwnerApplyAuthDTO result = (CustomerOwnerApplyAuthDTO) getPrivateMethod().invoke(wmCustomerOwnerApplyBusService, apply, 200);
        // assert
        assertFalse(getFieldValue(result, "revokeApplyAuth"));
        assertTrue(getFieldValue(result, "onClickGroupAuth"));
        assertTrue(getFieldValue(result, "readApplyDetailAuth"));
        assertTrue(getFieldValue(result, "readOperateRecordAuth"));
    }

    /**
     * Test case: Has write permission and in auditing status
     * Scenario: User has write permission and application is in auditing status
     */
    @Test
    public void testConvertCustomerOwnerApplyAuthDTO_HasWritePermissionAndAuditing() throws Throwable {
        // arrange
        WmCustomerOwnerApply apply = new WmCustomerOwnerApply();
        apply.setApplyUid(100);
        apply.setCustomerRealType(1);
        apply.setStatus(CustomerOwnerApplyStatusEnum.AUDITING.getCode());
        when(wmCustomerAuthService.getAuthCustomerRealType(200, WmCustomerAuthTypeEnum.WRITE_AND_READ.getCode())).thenReturn(Arrays.asList(1));
        when(wmVirtualOrgServiceAdaptor.getUidsByUid(200, WmOrgConstant.QUERY_ALL_SOURCE, WmVirtualOrgRecursiveTypeEnum.TOP_DOWN_NO_SELF.getType())).thenReturn(new ArrayList<>());
        // act
        CustomerOwnerApplyAuthDTO result = (CustomerOwnerApplyAuthDTO) getPrivateMethod().invoke(wmCustomerOwnerApplyBusService, apply, 200);
        // assert
        assertTrue(getFieldValue(result, "revokeApplyAuth"));
        assertTrue(getFieldValue(result, "onClickGroupAuth"));
        assertTrue(getFieldValue(result, "readApplyDetailAuth"));
        assertTrue(getFieldValue(result, "readOperateRecordAuth"));
    }

    /**
     * Test case: Has write permission but group ID exists
     * Scenario: User has write permission but group is already created
     */
    @Test
    public void testConvertCustomerOwnerApplyAuthDTO_HasWritePermissionWithGroupId() throws Throwable {
        // arrange
        WmCustomerOwnerApply apply = new WmCustomerOwnerApply();
        apply.setApplyUid(100);
        apply.setCustomerRealType(1);
        apply.setStatus(CustomerOwnerApplyStatusEnum.AUDITING.getCode());
        apply.setGroupId(123L);
        when(wmCustomerAuthService.getAuthCustomerRealType(200, WmCustomerAuthTypeEnum.WRITE_AND_READ.getCode())).thenReturn(Arrays.asList(1));
        when(wmVirtualOrgServiceAdaptor.getUidsByUid(200, WmOrgConstant.QUERY_ALL_SOURCE, WmVirtualOrgRecursiveTypeEnum.TOP_DOWN_NO_SELF.getType())).thenReturn(new ArrayList<>());
        // act
        CustomerOwnerApplyAuthDTO result = (CustomerOwnerApplyAuthDTO) getPrivateMethod().invoke(wmCustomerOwnerApplyBusService, apply, 200);
        // assert
        assertTrue(getFieldValue(result, "revokeApplyAuth"));
        assertFalse(getFieldValue(result, "onClickGroupAuth"));
        assertTrue(getFieldValue(result, "readApplyDetailAuth"));
        assertTrue(getFieldValue(result, "readOperateRecordAuth"));
    }

    /**
     * Test case: Write permission through down UIDs
     * Scenario: User has write permission through hierarchical relationship
     */
    @Test
    public void testConvertCustomerOwnerApplyAuthDTO_WritePermissionThroughDownUids() throws Throwable {
        // arrange
        WmCustomerOwnerApply apply = new WmCustomerOwnerApply();
        apply.setApplyUid(100);
        apply.setCustomerRealType(1);
        apply.setStatus(CustomerOwnerApplyStatusEnum.AUDITING.getCode());
        when(wmCustomerAuthService.getAuthCustomerRealType(200, WmCustomerAuthTypeEnum.WRITE_AND_READ.getCode())).thenReturn(new ArrayList<>());
        when(wmVirtualOrgServiceAdaptor.getUidsByUid(200, WmOrgConstant.QUERY_ALL_SOURCE, WmVirtualOrgRecursiveTypeEnum.TOP_DOWN_NO_SELF.getType())).thenReturn(Arrays.asList(100));
        // act
        CustomerOwnerApplyAuthDTO result = (CustomerOwnerApplyAuthDTO) getPrivateMethod().invoke(wmCustomerOwnerApplyBusService, apply, 200);
        // assert
        assertTrue(getFieldValue(result, "revokeApplyAuth"));
        assertTrue(getFieldValue(result, "onClickGroupAuth"));
        assertTrue(getFieldValue(result, "readApplyDetailAuth"));
        assertTrue(getFieldValue(result, "readOperateRecordAuth"));
    }

    /**
     * Test case: Write permission as apply user
     * Scenario: User is the original applicant
     */
    @Test
    public void testConvertCustomerOwnerApplyAuthDTO_WritePermissionAsApplyUser() throws Throwable {
        // arrange
        WmCustomerOwnerApply apply = new WmCustomerOwnerApply();
        apply.setApplyUid(200);
        apply.setCustomerRealType(1);
        apply.setStatus(CustomerOwnerApplyStatusEnum.AUDITING.getCode());
        when(wmCustomerAuthService.getAuthCustomerRealType(200, WmCustomerAuthTypeEnum.WRITE_AND_READ.getCode())).thenReturn(new ArrayList<>());
        when(wmVirtualOrgServiceAdaptor.getUidsByUid(200, WmOrgConstant.QUERY_ALL_SOURCE, WmVirtualOrgRecursiveTypeEnum.TOP_DOWN_NO_SELF.getType())).thenReturn(new ArrayList<>());
        // act
        CustomerOwnerApplyAuthDTO result = (CustomerOwnerApplyAuthDTO) getPrivateMethod().invoke(wmCustomerOwnerApplyBusService, apply, 200);
        // assert
        assertTrue(getFieldValue(result, "revokeApplyAuth"));
        assertTrue(getFieldValue(result, "onClickGroupAuth"));
        assertTrue(getFieldValue(result, "readApplyDetailAuth"));
        assertTrue(getFieldValue(result, "readOperateRecordAuth"));
    }

    /**
     * Test case: Null group ID
     * Scenario: Application has no group created yet
     */
    @Test
    public void testConvertCustomerOwnerApplyAuthDTO_NullGroupId() throws Throwable {
        // arrange
        WmCustomerOwnerApply apply = new WmCustomerOwnerApply();
        apply.setApplyUid(100);
        apply.setCustomerRealType(1);
        apply.setStatus(CustomerOwnerApplyStatusEnum.AUDITING.getCode());
        apply.setGroupId(null);
        when(wmCustomerAuthService.getAuthCustomerRealType(200, WmCustomerAuthTypeEnum.WRITE_AND_READ.getCode())).thenReturn(Arrays.asList(1));
        when(wmVirtualOrgServiceAdaptor.getUidsByUid(200, WmOrgConstant.QUERY_ALL_SOURCE, WmVirtualOrgRecursiveTypeEnum.TOP_DOWN_NO_SELF.getType())).thenReturn(new ArrayList<>());
        // act
        CustomerOwnerApplyAuthDTO result = (CustomerOwnerApplyAuthDTO) getPrivateMethod().invoke(wmCustomerOwnerApplyBusService, apply, 200);
        // assert
        assertTrue(getFieldValue(result, "revokeApplyAuth"));
        assertTrue(getFieldValue(result, "onClickGroupAuth"));
        assertTrue(getFieldValue(result, "readApplyDetailAuth"));
        assertTrue(getFieldValue(result, "readOperateRecordAuth"));
    }
}

package com.sankuai.meituan.waimai.customer.service.sign.applytask;

import com.google.common.base.Splitter;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.contract.domain.WmTempletContractDB;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmC1EContractTempletService;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmContractService;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignManualTaskDB;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractManualTaskBizService;
import com.sankuai.meituan.waimai.customer.util.TairLocker;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerContractStatus;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractBasicBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.C1ExpireAfterRenewalResultBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.ManualTaskApplyBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;


@RunWith(MockitoJUnitRunner.class)
public class WmEcontractC1AutoRenewalServiceTest {
	
	@InjectMocks
	private WmEcontractC1AutoRenewalService service;
	
	@Mock
	private WmContractService wmContractService;
	
	@Mock
	private WmEcontractManualTaskBizService wmEcontractManualTaskBizService;
	
	@Mock
	private WmC1EContractTempletService wmC1EContractTempletService;
	
	@Mock
	private TairLocker tairLocker;
	
	private ManualTaskApplyBo manualTaskApplyBo;
	
	public static Splitter SPLITTER = Splitter.on(",").omitEmptyStrings().trimResults();
	
	@Before
	public void setUp() {
		MockitoAnnotations.initMocks(this);
		manualTaskApplyBo = new ManualTaskApplyBo();
		manualTaskApplyBo.setCustomerId(123);
		List<Long> manualTaskIds = new ArrayList<>();
		manualTaskIds.add(123L);
		manualTaskApplyBo.setManualTaskIds(manualTaskIds);
		manualTaskApplyBo.setCommitUid(456);
	}
	
	/**
	 * 存在签约中的 C1 合同
	 */
	@Test
	public void testApplyTaskForRenewalC1ExpireAfter_SignIngContract() throws Throwable {
		// arrange
		WmTempletContractDB contract = new WmTempletContractDB();
		contract.setStatus(CustomerContractStatus.SIGNING.getCode());
		when(wmContractService.getContractByCustomerIdAndTypes(anyLong(), anyList())).thenReturn(Collections.singletonList(contract));
		when(tairLocker.tryLock(anyString(), anyInt())).thenReturn(true);
		try (MockedStatic<MccConfig> mccConfigMockedStatic = mockStatic(MccConfig.class)) {
			mccConfigMockedStatic.when(() -> MccConfig.rollPackSize()).thenReturn(10);
			mccConfigMockedStatic.when(() -> MccConfig.getDaXiangAlarmMisList()).thenReturn(SPLITTER.splitToList("limingxuan,liuyunjie05,wangyongfang,chiyuting04"));
			
			// act
			C1ExpireAfterRenewalResultBo resultBo = service.applyTaskForRenewalC1ExpireAfter(manualTaskApplyBo);
			
			// assert
			assertEquals(1, resultBo.getTotalCount().intValue());
			verify(wmContractService).getContractByCustomerIdAndTypes(anyLong(), anyList());
		}
	}
	
	/**
	 * 存在待签约的 C1 合同
	 */
	@Test
	public void testApplyTaskForRenewalC1ExpireAfter_WaitingSignContract() throws Throwable {
		// arrange
		WmTempletContractDB contract = new WmTempletContractDB();
		contract.setStatus(CustomerContractStatus.WAITING_SIGN.getCode());
		when(wmContractService.getContractByCustomerIdAndTypes(anyLong(), anyList())).thenReturn(Collections.singletonList(contract));
		when(tairLocker.tryLock(anyString(), anyInt())).thenReturn(true);
		
		WmEcontractSignManualTaskDB manualTaskInfo = new WmEcontractSignManualTaskDB();
		manualTaskInfo.setId(2L);
		when(wmEcontractManualTaskBizService.getManualTaskInfoByCustomerIdAndModuleRT(anyInt(), anyString())).thenReturn(manualTaskInfo);
		try (MockedStatic<MccConfig> mccConfigMockedStatic = mockStatic(MccConfig.class)) {
			mccConfigMockedStatic.when(() -> MccConfig.rollPackSize()).thenReturn(10);
			mccConfigMockedStatic.when(() -> MccConfig.getDaXiangAlarmMisList()).thenReturn(SPLITTER.splitToList("limingxuan,liuyunjie05,wangyongfang,chiyuting04"));
			
			// act
			C1ExpireAfterRenewalResultBo resultBo = service.applyTaskForRenewalC1ExpireAfter(manualTaskApplyBo);
			
			// assert
			assertEquals(1, resultBo.getTotalCount().intValue());
			verify(wmContractService).getContractByCustomerIdAndTypes(anyLong(), anyList());
			verify(wmEcontractManualTaskBizService).getManualTaskInfoByCustomerIdAndModuleRT(anyInt(), anyString());
		}
	}
	
	/**
	 * 无流程中的 C1 合同，重新发起签约任务
	 */
	@Test
	public void testApplyTaskForRenewalC1ExpireAfter_NoFlowContract() throws Throwable {
		// arrange
		WmTempletContractDB contract = new WmTempletContractDB();
		contract.setStatus(CustomerContractStatus.EFFECT.getCode());
		when(wmContractService.getContractByCustomerIdAndTypes(anyLong(), anyList())).thenReturn(Collections.singletonList(contract));
		when(tairLocker.tryLock(anyString(), anyInt())).thenReturn(true);
		WmCustomerContractBo wmCustomerContractBo = new WmCustomerContractBo();
		wmCustomerContractBo.setBasicBo(new WmTempletContractBasicBo());
		when(wmContractService.getEffectC1ContractUpdate(anyInt(), anyInt(), anyString())).thenReturn(wmCustomerContractBo);
		WmEcontractSignManualTaskDB manualTaskInfo = new WmEcontractSignManualTaskDB();
		manualTaskInfo.setId(2L);
		when(wmEcontractManualTaskBizService.getManualTaskInfoByCustomerIdAndModuleRT(anyInt(), anyString())).thenReturn(manualTaskInfo);
		try (MockedStatic<MccConfig> mccConfigMockedStatic = mockStatic(MccConfig.class)) {
			mccConfigMockedStatic.when(() -> MccConfig.rollPackSize()).thenReturn(10);
			mccConfigMockedStatic.when(() -> MccConfig.getDaXiangAlarmMisList()).thenReturn(SPLITTER.splitToList("limingxuan,liuyunjie05,wangyongfang,chiyuting04"));
			
			// act
			C1ExpireAfterRenewalResultBo resultBo = service.applyTaskForRenewalC1ExpireAfter(manualTaskApplyBo);
			
			// assert
			assertEquals(1, resultBo.getTotalCount().intValue());
			verify(wmContractService).getContractByCustomerIdAndTypes(anyLong(), anyList());
		}
	}
	
	/**
	 * 无历史合同，抛出异常
	 */
	@Test(expected = WmCustomerException.class)
	public void testApplyTaskForRenewalC1ExpireAfter_NoHistoryContract() throws Throwable {
		// arrange
		when(wmContractService.getContractByCustomerIdAndTypes(anyLong(), anyList())).thenReturn(Collections.emptyList());
		try (MockedStatic<MccConfig> mccConfigMockedStatic = mockStatic(MccConfig.class)) {
			mccConfigMockedStatic.when(() -> MccConfig.rollPackSize()).thenReturn(10);
			mccConfigMockedStatic.when(() -> MccConfig.getDaXiangAlarmMisList()).thenReturn(SPLITTER.splitToList("limingxuan,liuyunjie05,wangyongfang,chiyuting04"));
			
			// act
			service.applyTaskForRenewalC1ExpireAfter(manualTaskApplyBo);
			
			// assert
			verify(wmContractService).getContractByCustomerIdAndTypes(anyLong(), anyList());
		}
		
	}
	
	/**
	 * 获取分布式锁失败，抛出异常
	 */
	@Test(expected = WmCustomerException.class)
	public void testApplyTaskForRenewalC1ExpireAfter_LockFailed() throws Throwable {
		// arrange
		WmTempletContractDB contract = new WmTempletContractDB();
		contract.setStatus(CustomerContractStatus.EFFECT.getCode());
		when(wmContractService.getContractByCustomerIdAndTypes(anyLong(), anyList())).thenReturn(Collections.singletonList(contract));
		WmCustomerContractBo wmCustomerContractBo = new WmCustomerContractBo();
		wmCustomerContractBo.setBasicBo(new WmTempletContractBasicBo());
		when(wmContractService.getEffectC1ContractUpdate(anyInt(), anyInt(), anyString())).thenReturn(wmCustomerContractBo);
		try (MockedStatic<MccConfig> mccConfigMockedStatic = mockStatic(MccConfig.class)) {
			mccConfigMockedStatic.when(() -> MccConfig.rollPackSize()).thenReturn(10);
			mccConfigMockedStatic.when(() -> MccConfig.getDaXiangAlarmMisList()).thenReturn(SPLITTER.splitToList("limingxuan,liuyunjie05,wangyongfang,chiyuting04"));
			
			// act
			service.applyTaskForRenewalC1ExpireAfter(manualTaskApplyBo);
			
			// assert
			verify(tairLocker).tryLock(anyString(), anyInt());
		}
		
	}
	
	/**
	 * 发起签约任务失败，抛出异常
	 */
	@Test(expected = WmCustomerException.class)
	public void testApplyTaskForRenewalC1ExpireAfter_StartSignFailed() throws Throwable {
		// arrange
		WmTempletContractDB contract = new WmTempletContractDB();
		contract.setStatus(CustomerContractStatus.EFFECT.getCode());
		when(wmContractService.getContractByCustomerIdAndTypes(anyLong(), anyList())).thenReturn(Collections.singletonList(contract));
		when(wmC1EContractTempletService.startSign(any(), anyInt(), anyString())).thenThrow(new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "发起失败"));
		WmCustomerContractBo wmCustomerContractBo = new WmCustomerContractBo();
		wmCustomerContractBo.setBasicBo(new WmTempletContractBasicBo());
		when(wmContractService.getEffectC1ContractUpdate(anyInt(), anyInt(), anyString())).thenReturn(wmCustomerContractBo);
		try (MockedStatic<MccConfig> mccConfigMockedStatic = mockStatic(MccConfig.class)) {
			mccConfigMockedStatic.when(() -> MccConfig.rollPackSize()).thenReturn(10);
			mccConfigMockedStatic.when(() -> MccConfig.getDaXiangAlarmMisList()).thenReturn(SPLITTER.splitToList("limingxuan,liuyunjie05,wangyongfang,chiyuting04"));
			
			// act
			service.applyTaskForRenewalC1ExpireAfter(manualTaskApplyBo);
			
			// assert
			verify(wmC1EContractTempletService).startSign(any(), anyInt(), anyString());
		}
		
	}
}
package com.sankuai.meituan.waimai.customer.service.sign.data;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.banma.deliverycontract.platform.process.sdk.resp.ElectronicContractBatchDeliveryPerInfoBo;
import com.sankuai.meituan.banma.deliverycontract.platform.process.sdk.resp.ElectronicContractSingleDeliveryPerInfoBo;
import com.sankuai.meituan.waimai.customer.adapter.DeliveryContractAdapter;
import com.sankuai.meituan.waimai.customer.service.sign.data.util.LogisticsFeeDataQueryUtil;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDataPoiBizBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDataSourceBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.util.*;
import java.util.stream.Collectors;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class PhfLogisticsPerPdfQueryHandlerQueryByGroupTest {

    @Mock
    private DeliveryContractAdapter deliveryContractAdapter;

    @InjectMocks
    private PhfLogisticsPerPdfQueryHandler handler;

    private EcontractDataSourceBo dataSourceBo;

    private List<List<Long>> wmPoiIdGroupList;

    private List<EcontractDataPoiBizBo> wmPoiIdAndBizIdList;

    @Before
    public void setUp() {
        wmPoiIdGroupList = new ArrayList<>();
        wmPoiIdAndBizIdList = new ArrayList<>();
        dataSourceBo = new EcontractDataSourceBo();
        dataSourceBo.setWmPoiIdGroupList(wmPoiIdGroupList);
        dataSourceBo.setWmPoiIdAndBizIdList(wmPoiIdAndBizIdList);
    }

    private EcontractDataPoiBizBo createPoiBizBo(Long wmPoiId, Long bizId) {
        EcontractDataPoiBizBo poiBizBo = new EcontractDataPoiBizBo();
        poiBizBo.setWmPoiId(wmPoiId);
        poiBizBo.setBizId(bizId);
        return poiBizBo;
    }

    /**
     * Test when input dataSourceBo is null
     */
    @Test(expected = NullPointerException.class)
    public void testQueryByGroupNullDataSource() throws Throwable {
        handler.queryByGroup(null);
    }

    /**
     * Test when poi group list is empty
     */
    @Test(expected = WmCustomerException.class)
    public void testQueryByGroupEmptyPoiGroupList() throws Throwable {
        wmPoiIdGroupList.clear();
        try (MockedStatic<LogisticsFeeDataQueryUtil> mocked = mockStatic(LogisticsFeeDataQueryUtil.class)) {
            mocked.when(() -> LogisticsFeeDataQueryUtil.extractPoiBizBoGroupList(anyList(), anyList())).thenThrow(new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "门店分组数据为空"));
            handler.queryByGroup(dataSourceBo);
        }
    }

    /**
     * Test when poi biz list is empty
     */
    @Test(expected = WmCustomerException.class)
    public void testQueryByGroupEmptyPoiBizList() throws Throwable {
        wmPoiIdGroupList.add(Arrays.asList(1L, 2L));
        wmPoiIdAndBizIdList.clear();
        try (MockedStatic<LogisticsFeeDataQueryUtil> mocked = mockStatic(LogisticsFeeDataQueryUtil.class)) {
            mocked.when(() -> LogisticsFeeDataQueryUtil.extractPoiBizBoGroupList(anyList(), anyList())).thenThrow(new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "门店分组数据不合法"));
            handler.queryByGroup(dataSourceBo);
        }
    }

    /**
     * Test when adapter returns null
     */
    @Test(expected = WmCustomerException.class)
    public void testQueryByGroupAdapterReturnsNull() throws Throwable {
        wmPoiIdGroupList.add(Arrays.asList(1L));
        wmPoiIdAndBizIdList.add(createPoiBizBo(1L, 100L));
        try (MockedStatic<LogisticsFeeDataQueryUtil> mocked = mockStatic(LogisticsFeeDataQueryUtil.class)) {
            List<EcontractDataPoiBizBo> mockGroup = Collections.singletonList(createPoiBizBo(1L, 100L));
            mocked.when(() -> LogisticsFeeDataQueryUtil.extractPoiBizBoGroupList(anyList(), anyList())).thenReturn(Collections.singletonList(mockGroup));
            when(deliveryContractAdapter.queryPerFeeWithRetry(any())).thenReturn(null);
            handler.queryByGroup(dataSourceBo);
        }
    }

    /**
     * Test when adapter returns result with null batchPerInfoMap
     */
    @Test(expected = WmCustomerException.class)
    public void testQueryByGroupAdapterReturnsEmptyMap() throws Throwable {
        wmPoiIdGroupList.add(Arrays.asList(1L));
        wmPoiIdAndBizIdList.add(createPoiBizBo(1L, 100L));
        try (MockedStatic<LogisticsFeeDataQueryUtil> mocked = mockStatic(LogisticsFeeDataQueryUtil.class)) {
            List<EcontractDataPoiBizBo> mockGroup = Collections.singletonList(createPoiBizBo(1L, 100L));
            mocked.when(() -> LogisticsFeeDataQueryUtil.extractPoiBizBoGroupList(anyList(), anyList())).thenReturn(Collections.singletonList(mockGroup));
            ElectronicContractBatchDeliveryPerInfoBo result = new ElectronicContractBatchDeliveryPerInfoBo();
            // Set batchPerInfoMap to null to trigger exception
            result.setBatchPerInfoMap(null);
            when(deliveryContractAdapter.queryPerFeeWithRetry(any())).thenReturn(result);
            handler.queryByGroup(dataSourceBo);
        }
    }

    /**
     * Test successful case with single group and single poi
     */
    @Test
    public void testQueryByGroupSinglePoiSuccess() throws Throwable {
        wmPoiIdGroupList.add(Arrays.asList(1L));
        wmPoiIdAndBizIdList.add(createPoiBizBo(1L, 100L));
        try (MockedStatic<LogisticsFeeDataQueryUtil> mocked = mockStatic(LogisticsFeeDataQueryUtil.class)) {
            List<EcontractDataPoiBizBo> mockGroup = Collections.singletonList(createPoiBizBo(1L, 100L));
            mocked.when(() -> LogisticsFeeDataQueryUtil.extractPoiBizBoGroupList(anyList(), anyList())).thenReturn(Collections.singletonList(mockGroup));
            Map<Long, ElectronicContractSingleDeliveryPerInfoBo> expectedMap = new HashMap<>();
            expectedMap.put(1L, new ElectronicContractSingleDeliveryPerInfoBo());
            ElectronicContractBatchDeliveryPerInfoBo mockResult = new ElectronicContractBatchDeliveryPerInfoBo();
            mockResult.setBatchPerInfoMap(expectedMap);
            when(deliveryContractAdapter.queryPerFeeWithRetry(any())).thenReturn(mockResult);
            ElectronicContractBatchDeliveryPerInfoBo result = handler.queryByGroup(dataSourceBo);
            assertNotNull(result);
            assertEquals(1, result.getBatchPerInfoMap().size());
            assertTrue(result.getBatchPerInfoMap().containsKey(1L));
        }
    }

    /**
     * Test successful case with multiple groups and multiple pois
     */
    @Test
    public void testQueryByGroupMultiplePoisSuccess() throws Throwable {
        wmPoiIdGroupList.add(Arrays.asList(1L));
        wmPoiIdGroupList.add(Arrays.asList(2L, 3L));
        wmPoiIdAndBizIdList.add(createPoiBizBo(1L, 100L));
        wmPoiIdAndBizIdList.add(createPoiBizBo(2L, 200L));
        wmPoiIdAndBizIdList.add(createPoiBizBo(3L, 300L));
        try (MockedStatic<LogisticsFeeDataQueryUtil> mocked = mockStatic(LogisticsFeeDataQueryUtil.class)) {
            List<List<EcontractDataPoiBizBo>> mockGroups = Arrays.asList(Collections.singletonList(createPoiBizBo(1L, 100L)), Arrays.asList(createPoiBizBo(2L, 200L), createPoiBizBo(3L, 300L)));
            mocked.when(() -> LogisticsFeeDataQueryUtil.extractPoiBizBoGroupList(anyList(), anyList())).thenReturn(mockGroups);
            Map<Long, ElectronicContractSingleDeliveryPerInfoBo> map1 = new HashMap<>();
            map1.put(1L, new ElectronicContractSingleDeliveryPerInfoBo());
            ElectronicContractBatchDeliveryPerInfoBo result1 = new ElectronicContractBatchDeliveryPerInfoBo();
            result1.setBatchPerInfoMap(map1);
            Map<Long, ElectronicContractSingleDeliveryPerInfoBo> map2 = new HashMap<>();
            map2.put(2L, new ElectronicContractSingleDeliveryPerInfoBo());
            map2.put(3L, new ElectronicContractSingleDeliveryPerInfoBo());
            ElectronicContractBatchDeliveryPerInfoBo result2 = new ElectronicContractBatchDeliveryPerInfoBo();
            result2.setBatchPerInfoMap(map2);
            when(deliveryContractAdapter.queryPerFeeWithRetry(any())).thenReturn(result1).thenReturn(result2);
            ElectronicContractBatchDeliveryPerInfoBo result = handler.queryByGroup(dataSourceBo);
            assertNotNull(result);
            assertEquals(3, result.getBatchPerInfoMap().size());
            assertTrue(result.getBatchPerInfoMap().containsKey(1L));
            assertTrue(result.getBatchPerInfoMap().containsKey(2L));
            assertTrue(result.getBatchPerInfoMap().containsKey(3L));
        }
    }
}
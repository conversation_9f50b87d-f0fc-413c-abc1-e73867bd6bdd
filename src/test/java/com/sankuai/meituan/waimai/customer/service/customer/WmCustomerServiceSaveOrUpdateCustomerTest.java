package com.sankuai.meituan.waimai.customer.service.customer;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.constant.WmCustomerConstant;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmCustomerOplogService;
import com.sankuai.meituan.waimai.customer.domain.customer.WmCustomerOwnerApply;
import com.sankuai.meituan.waimai.customer.service.common.WmEmployeeService;
import com.sankuai.meituan.waimai.customer.service.customer.Impl.IWmCustomerRealService;
import com.sankuai.meituan.waimai.customer.service.gray.WmCustomerGrayService;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpService;
import com.sankuai.meituan.waimai.customer.util.HttpUtil;
import com.sankuai.meituan.waimai.customer.util.base.AssertUtil;
import com.sankuai.meituan.waimai.customer.util.trans.WmCustomerTransUtil;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import com.sankuai.meituan.waimai.label.constants.LabelSubjectTypeEnum;
import com.sankuai.meituan.waimai.poiaudit.thrift.constants.WmPoiValidEnum;
import com.sankuai.meituan.waimai.poibizflow.thrift.switcher.bo.SwitchTaskAndPoiDTO;
import com.sankuai.meituan.waimai.qualification.constants.QuaNumberUsedStatusEnum;
import com.sankuai.meituan.waimai.thrift.constants.WmPoiFieldQueryConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerRealTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerType;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.CustomerBizOrgEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.CustomerBusinessEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.WmCustomerOplogBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.ValidateResultBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerBasicBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.mq.CustomerMQEventEnum;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre;
import com.sankuai.meituan.waimai.thrift.domain.WmQuaRepeatRequest;
import com.sankuai.meituan.waimai.thrift.domain.WmQuaRepeatResponse;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import com.sankuai.nibcus.inf.customer.client.enums.BusinessLineEnum;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.elasticsearch.action.search.SearchResponse;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;
import org.mockito.stubbing.Answer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Test class for WmCustomerService's saveOrUpdateCustomer method
 */
@RunWith(MockitoJUnitRunner.class)
public class WmCustomerServiceSaveOrUpdateCustomerTest {

    @InjectMocks
    private WmCustomerService wmCustomerService;

    @Mock
    private IWmCustomerRealService wmLeafCustomerRealService;

    @Mock
    private IWmCustomerRealService wmSuperCustomerRealService;

    @Mock
    private WmCustomerPlatformDataParseService wmCustomerPlatformDataParseService;

    @Mock
    private WmEmployeeService wmEmployeeService;

    @Mock
    private WmCustomerOplogService wmCustomerOplogService;

    private WmCustomerOwnerApply apply;

    // Define CustomerConstants if not already defined
    private static final class CustomerConstants {

        public static final int RESULT_CODE_PASS = 0;

        public static final int RESULT_CODE_FAIL = 1;

        public static final int CUSTOMER_IS_LEAF_NO = 0;
    }

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        apply = new WmCustomerOwnerApply();
        apply.setCustomerId(1);
        apply.setApplyUid(2);
        apply.setCustomerOwnerUid(3);
        apply.setId(4);
    }

    /**
     * Test validation failure case
     */
    @Test
    public void testSaveOrUpdateCustomer_ValidationFailed() throws Throwable {
        // arrange
        WmCustomerBasicBo wmCustomerBasicBo = new WmCustomerBasicBo();
        wmCustomerBasicBo.setIsLeaf(1);
        wmCustomerBasicBo.setCustomerType(CustomerType.CUSTOMER_TYPE_BUSINESS.getCode());
        ValidateResultBo expectedResult = new ValidateResultBo();
        expectedResult.setCode(CustomerConstants.RESULT_CODE_FAIL);
        expectedResult.setMsg("validation failed");
        when(wmLeafCustomerRealService.saveOrUpdateCustomer(any(), anyBoolean(), anyInt(), anyString(), anyInt())).thenReturn(expectedResult);
        // act
        ValidateResultBo result = wmCustomerService.saveOrUpdateCustomer(wmCustomerBasicBo, false, 123, "test", 1);
        // assert
        assertNotNull(result);
        assertEquals(CustomerConstants.RESULT_CODE_FAIL, result.getCode());
        assertEquals("validation failed", result.getMsg());
    }

    /**
     * Test when WmCustomerException is thrown
     */
    @Test
    public void testSaveOrUpdateCustomer_WmCustomerException() throws Throwable {
        // arrange
        WmCustomerBasicBo wmCustomerBasicBo = new WmCustomerBasicBo();
        wmCustomerBasicBo.setIsLeaf(1);
        wmCustomerBasicBo.setCustomerType(CustomerType.CUSTOMER_TYPE_BUSINESS.getCode());
        WmCustomerException expectedException = new WmCustomerException(1001, "business error");
        when(wmLeafCustomerRealService.saveOrUpdateCustomer(any(), anyBoolean(), anyInt(), anyString(), anyInt())).thenThrow(expectedException);
        try {
            // act
            wmCustomerService.saveOrUpdateCustomer(wmCustomerBasicBo, false, 123, "test", 1);
            fail("Should throw exception");
        } catch (WmCustomerException e) {
            // assert
            assertEquals("business error", e.getMsg());
            assertEquals(1001, e.getCode());
        }
    }

    /**
     * Test when general Exception is thrown
     */
    @Test
    public void testSaveOrUpdateCustomer_SystemException() throws Throwable {
        // arrange
        WmCustomerBasicBo wmCustomerBasicBo = new WmCustomerBasicBo();
        wmCustomerBasicBo.setIsLeaf(1);
        wmCustomerBasicBo.setCustomerType(CustomerType.CUSTOMER_TYPE_BUSINESS.getCode());
        JSONObject errorJson = new JSONObject();
        errorJson.put("status", "999");
        RuntimeException expectedException = new RuntimeException(errorJson.toJSONString());
        when(wmLeafCustomerRealService.saveOrUpdateCustomer(any(), anyBoolean(), anyInt(), anyString(), anyInt())).thenThrow(expectedException);
        try {
            // act
            wmCustomerService.saveOrUpdateCustomer(wmCustomerBasicBo, false, 123, "test", 1);
            fail("Should throw exception");
        } catch (Exception e) {
            // assert
            assertEquals("999", JSONObject.parseObject(e.getMessage()).getString("status"));
        }
    }

    /**
     * Test saveOrUpdateCustomer when WmCustomerException is thrown
     */
    @Test
    public void testSaveOrUpdateCustomerWhenWmCustomerExceptionThrown() throws Throwable {
        // arrange
        WmCustomerBasicBo wmCustomerBasicBo = new WmCustomerBasicBo();
        wmCustomerBasicBo.setIsLeaf(1);
        wmCustomerBasicBo.setCustomerType(CustomerType.CUSTOMER_TYPE_BUSINESS.getCode());
        WmCustomerException expectedException = new WmCustomerException(1001, "test error");
        when(wmLeafCustomerRealService.saveOrUpdateCustomer(any(), anyBoolean(), anyInt(), anyString(), anyInt())).thenThrow(expectedException);
        try {
            // act
            wmCustomerService.saveOrUpdateCustomer(wmCustomerBasicBo, false, 123, "test", 1);
            fail("Should throw exception");
        } catch (WmCustomerException e) {
            // assert
            assertEquals("test error", e.getMsg());
            assertEquals(1001, e.getCode());
        }
    }

    /**
     * Test saveOrUpdateCustomer when general Exception is thrown
     */
    @Test
    public void testSaveOrUpdateCustomerWhenGeneralExceptionThrown() throws Throwable {
        // arrange
        WmCustomerBasicBo wmCustomerBasicBo = new WmCustomerBasicBo();
        wmCustomerBasicBo.setIsLeaf(1);
        wmCustomerBasicBo.setCustomerType(CustomerType.CUSTOMER_TYPE_BUSINESS.getCode());
        JSONObject errorJson = new JSONObject();
        errorJson.put("status", "999");
        RuntimeException expectedException = new RuntimeException(errorJson.toJSONString());
        when(wmLeafCustomerRealService.saveOrUpdateCustomer(any(), anyBoolean(), anyInt(), anyString(), anyInt())).thenThrow(expectedException);
        try {
            // act
            wmCustomerService.saveOrUpdateCustomer(wmCustomerBasicBo, false, 123, "test", 1);
            fail("Should throw exception");
        } catch (Exception e) {
            // assert
            assertEquals("999", JSONObject.parseObject(e.getMessage()).getString("status"));
        }
    }

    /**
     * Test case for successful customer owner change and logging.
     */
    @Test
    public void testChangeCustomerOwnerByApply_Success() throws Throwable {
        // arrange
        when(wmEmployeeService.getUserAndId(3)).thenReturn("Test User(12345)");
        when(wmEmployeeService.getUserAndId(2)).thenReturn("Test User(12345)");
        when(wmCustomerOplogService.insert(any(WmCustomerOplogBo.class))).thenReturn(1L);
        // act
        wmCustomerService.changeCustomerOwnerByApply(apply, 5, "Operator");
        // assert
        verify(wmCustomerPlatformDataParseService).distributeCustomer(eq(Lists.newArrayList(1)), eq(2));
        verify(wmCustomerOplogService).insert(any(WmCustomerOplogBo.class));
    }

    /**
     * Test case: successful customer owner change with existing owner
     */
    @Test
    public void testChangeCustomerOwnerByApply_Success_WithExistingOwner() throws Throwable {
        // arrange
        WmCustomerOwnerApply apply = new WmCustomerOwnerApply();
        apply.setId(1);
        apply.setCustomerId(100);
        apply.setApplyUid(200);
        apply.setCustomerOwnerUid(300);
        Integer opUid = 999;
        String opName = "testUser";
        when(wmEmployeeService.getUserAndId(300)).thenReturn("oldOwner");
        when(wmEmployeeService.getUserAndId(200)).thenReturn("newOwner");
        ArgumentCaptor<WmCustomerOplogBo> oplogCaptor = ArgumentCaptor.forClass(WmCustomerOplogBo.class);
        // act
        wmCustomerService.changeCustomerOwnerByApply(apply, opUid, opName);
        // assert
        verify(wmCustomerPlatformDataParseService).distributeCustomer(eq(Lists.newArrayList(100)), eq(200));
        verify(wmCustomerOplogService).insert(oplogCaptor.capture());
        WmCustomerOplogBo capturedOplog = oplogCaptor.getValue();
        assertEquals(Integer.valueOf(100), capturedOplog.getCustomerId());
        assertEquals(Byte.valueOf(WmCustomerOplogBo.OpType.UPDATE.type), capturedOplog.getOpType());
        assertEquals(opUid, capturedOplog.getOpUid());
        assertEquals(opName, capturedOplog.getOpUname());
    }

    /**
     * Test case: successful customer owner change with null owner
     */
    @Test
    public void testChangeCustomerOwnerByApply_Success_WithNullOwner() throws Throwable {
        // arrange
        WmCustomerOwnerApply apply = new WmCustomerOwnerApply();
        apply.setId(1);
        apply.setCustomerId(100);
        apply.setApplyUid(200);
        apply.setCustomerOwnerUid(null);
        Integer opUid = 999;
        String opName = "testUser";
        when(wmEmployeeService.getUserAndId(200)).thenReturn("newOwner");
        ArgumentCaptor<WmCustomerOplogBo> oplogCaptor = ArgumentCaptor.forClass(WmCustomerOplogBo.class);
        // act
        wmCustomerService.changeCustomerOwnerByApply(apply, opUid, opName);
        // assert
        verify(wmCustomerPlatformDataParseService).distributeCustomer(eq(Lists.newArrayList(100)), eq(200));
        verify(wmCustomerOplogService).insert(oplogCaptor.capture());
        WmCustomerOplogBo capturedOplog = oplogCaptor.getValue();
        assertEquals(Integer.valueOf(100), capturedOplog.getCustomerId());
        assertEquals(Byte.valueOf(WmCustomerOplogBo.OpType.UPDATE.type), capturedOplog.getOpType());
        String expectedLog = String.format(WmCustomerConstant.CustomerOwnerApplyLogStr, "无", "newOwner", 1);
        assertEquals(expectedLog, capturedOplog.getLog());
    }

    /**
     * Test case: verify error logging when operation log insertion fails
     */
    @Test
    public void testChangeCustomerOwnerByApply_WhenOplogInsertFails() throws Throwable {
        // arrange
        WmCustomerOwnerApply apply = new WmCustomerOwnerApply();
        apply.setId(1);
        apply.setCustomerId(100);
        apply.setApplyUid(200);
        apply.setCustomerOwnerUid(300);
        Integer opUid = 999;
        String opName = "testUser";
        when(wmEmployeeService.getUserAndId(300)).thenReturn("oldOwner");
        when(wmEmployeeService.getUserAndId(200)).thenReturn("newOwner");
        doThrow(new RuntimeException("DB Error")).when(wmCustomerOplogService).insert(any(WmCustomerOplogBo.class));
        // act
        wmCustomerService.changeCustomerOwnerByApply(apply, opUid, opName);
        // assert
        verify(wmCustomerPlatformDataParseService).distributeCustomer(eq(Lists.newArrayList(100)), eq(200));
        verify(wmCustomerOplogService).insert(any(WmCustomerOplogBo.class));
    }

    /**
     * Test case: verify when getUserAndId throws exception
     */
    @Test
    public void testChangeCustomerOwnerByApply_WhenGetUserAndIdFails() throws Throwable {
        // arrange
        WmCustomerOwnerApply apply = new WmCustomerOwnerApply();
        apply.setId(1);
        apply.setCustomerId(100);
        apply.setApplyUid(200);
        apply.setCustomerOwnerUid(300);
        Integer opUid = 999;
        String opName = "testUser";
        when(wmEmployeeService.getUserAndId(300)).thenThrow(new RuntimeException("User service error"));
        // act & assert
        wmCustomerService.changeCustomerOwnerByApply(apply, opUid, opName);
        // verify that customer distribution still happens even if getUserAndId fails
        verify(wmCustomerPlatformDataParseService).distributeCustomer(eq(Lists.newArrayList(100)), eq(200));
    }
}

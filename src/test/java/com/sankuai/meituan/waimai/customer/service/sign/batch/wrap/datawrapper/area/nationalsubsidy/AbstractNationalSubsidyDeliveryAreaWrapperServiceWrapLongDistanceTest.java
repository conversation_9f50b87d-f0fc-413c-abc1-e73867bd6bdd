package com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper.area.nationalsubsidy;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.customer.constant.WmEcontractConstant;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.customer.util.trans.WmPoiSpAreaBoUtil;
import com.sankuai.meituan.waimai.econtrct.client.domain.biz.EcontractContentBo;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractBatchTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataWrapperEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.*;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.ArrayList;
import org.junit.Before;
import org.junit.Test;

@RunWith(MockitoJUnitRunner.class)
public class AbstractNationalSubsidyDeliveryAreaWrapperServiceWrapLongDistanceTest {

    // Concrete implementation of the abstract class for testing
    private static class TestNationalSubsidyDeliveryAreaWrapperService extends AbstractNationalSubsidyDeliveryAreaWrapperService {

        @Override
        protected List<EcontractContentBo> wrap(EcontractBatchContextBo contextBo, EcontractDataWrapperEnum wrapperEnum) {
            return null;
        }

        @Override
        public List<EcontractContentBo> wrapWholeCity(EcontractBatchContextBo contextBo) {
            return null;
        }

        @Override
        public List<EcontractContentBo> wrap(EcontractBatchContextBo contextBo) {
            return null;
        }

        // Helper method to expose the private method for testing
        public EcontractWmPoiSpAreaBo parseLongDistanceDeliveryAreaJson(String deliveryArea) {
            try {
                // Simple implementation for testing
                if (deliveryArea == null || !deliveryArea.contains("area")) {
                    return null;
                }
                EcontractWmPoiSpAreaBo areaBo = new EcontractWmPoiSpAreaBo();
                return areaBo;
            } catch (Exception e) {
                return null;
            }
        }

        // Helper method to expose the private method for testing
        public boolean isValidLongDistanceDeliveryItem(EcontractDeliveryCompanyCustomerLongDistanceInfoBo longDistanceInfoBo) {
            return longDistanceInfoBo != null && longDistanceInfoBo.getDeliveryArea() != null;
        }
    }

    /**
     * Test normal case with valid long distance delivery info
     */
    @Test
    public void testWrapLongDistance_NormalCase() throws Throwable {
        // arrange
        TestNationalSubsidyDeliveryAreaWrapperService service = new TestNationalSubsidyDeliveryAreaWrapperService();
        EcontractBatchContextBo contextBo = new EcontractBatchContextBo();
        contextBo.setBatchTypeEnum(EcontractBatchTypeEnum.NATIONAL_SUBSIDY_DISTRIBUTOR_DELIVERY);
        EcontractTaskBo taskBo = new EcontractTaskBo();
        taskBo.setApplyType(EcontractTaskApplyTypeEnum.NATIONAL_SUBSIDY_DISTRIBUTOR_DELIVERY.getName());
        String validDeliveryAreaJson = "{\"area\":[[{\"x\":1,\"y\":1},{\"x\":2,\"y\":2}]]}";
        EcontractDeliveryCompanyCustomerLongDistanceInfoBo longDistanceInfoBo = new EcontractDeliveryCompanyCustomerLongDistanceInfoBo();
        longDistanceInfoBo.setDeliveryArea(validDeliveryAreaJson);
        longDistanceInfoBo.setPoiName("Test Poi");
        longDistanceInfoBo.setServiceProductName("Test Service");
        EcontractDeliveryInfoBo deliveryInfoBo = new EcontractDeliveryInfoBo();
        deliveryInfoBo.setEcontractDeliveryCompanyCustomerLongDistanceInfoBo(longDistanceInfoBo);
        List<EcontractDeliveryInfoBo> deliveryInfoBoList = Arrays.asList(deliveryInfoBo);
        EcontractBatchDeliveryInfoBo batchDeliveryInfoBo = new EcontractBatchDeliveryInfoBo();
        batchDeliveryInfoBo.setEcontractDeliveryInfoBoList(deliveryInfoBoList);
        Map<Long, EcontractTaskBo> taskMap = new HashMap<>();
        taskMap.put(1L, taskBo);
        contextBo.setTaskIdAndTaskMap(taskMap);
        // Set up JSON parsing
        String applyContext = JSON.toJSONString(batchDeliveryInfoBo);
        taskBo.setApplyContext(applyContext);
        // act
        List<EcontractContentBo> result = service.wrapLongDistance(contextBo);
        // assert
        assertNotNull(result);
        assertFalse(result.isEmpty());
    }

    /**
     * Test case when batch delivery info is null
     */
    @Test
    public void testWrapLongDistance_NullBatchDeliveryInfo() throws Throwable {
        // arrange
        TestNationalSubsidyDeliveryAreaWrapperService service = new TestNationalSubsidyDeliveryAreaWrapperService();
        EcontractBatchContextBo contextBo = new EcontractBatchContextBo();
        contextBo.setBatchTypeEnum(EcontractBatchTypeEnum.NATIONAL_SUBSIDY_DISTRIBUTOR_DELIVERY);
        EcontractTaskBo taskBo = new EcontractTaskBo();
        taskBo.setApplyContext("invalid json");
        taskBo.setApplyType(EcontractTaskApplyTypeEnum.NATIONAL_SUBSIDY_DISTRIBUTOR_DELIVERY.getName());
        Map<Long, EcontractTaskBo> taskMap = new HashMap<>();
        taskMap.put(1L, taskBo);
        contextBo.setTaskIdAndTaskMap(taskMap);
        // act
        List<EcontractContentBo> result = service.wrapLongDistance(contextBo);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test case when delivery info list is empty
     */
    @Test
    public void testWrapLongDistance_EmptyDeliveryInfoList() throws Throwable {
        // arrange
        TestNationalSubsidyDeliveryAreaWrapperService service = new TestNationalSubsidyDeliveryAreaWrapperService();
        EcontractBatchContextBo contextBo = new EcontractBatchContextBo();
        contextBo.setBatchTypeEnum(EcontractBatchTypeEnum.NATIONAL_SUBSIDY_DISTRIBUTOR_DELIVERY);
        EcontractTaskBo taskBo = new EcontractTaskBo();
        taskBo.setApplyType(EcontractTaskApplyTypeEnum.NATIONAL_SUBSIDY_DISTRIBUTOR_DELIVERY.getName());
        EcontractBatchDeliveryInfoBo batchDeliveryInfoBo = new EcontractBatchDeliveryInfoBo();
        batchDeliveryInfoBo.setEcontractDeliveryInfoBoList(Lists.newArrayList());
        Map<Long, EcontractTaskBo> taskMap = new HashMap<>();
        taskMap.put(1L, taskBo);
        contextBo.setTaskIdAndTaskMap(taskMap);
        // Set up JSON parsing
        String applyContext = JSON.toJSONString(batchDeliveryInfoBo);
        taskBo.setApplyContext(applyContext);
        // act
        List<EcontractContentBo> result = service.wrapLongDistance(contextBo);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for invalid batch type
     */
    @Test(expected = WmCustomerException.class)
    public void testWrapLongDistance_InvalidBatchType() throws Throwable {
        // arrange
        TestNationalSubsidyDeliveryAreaWrapperService service = new TestNationalSubsidyDeliveryAreaWrapperService();
        EcontractBatchContextBo contextBo = new EcontractBatchContextBo();
        contextBo.setBatchTypeEnum(EcontractBatchTypeEnum.UNKNOW);
        // act - should throw exception
        service.wrapLongDistance(contextBo);
    }

    /**
     * Test case when long distance info is invalid (null)
     */
    @Test
    public void testWrapLongDistance_InvalidLongDistanceInfo() throws Throwable {
        // arrange
        TestNationalSubsidyDeliveryAreaWrapperService service = new TestNationalSubsidyDeliveryAreaWrapperService();
        EcontractBatchContextBo contextBo = new EcontractBatchContextBo();
        contextBo.setBatchTypeEnum(EcontractBatchTypeEnum.NATIONAL_SUBSIDY_DISTRIBUTOR_DELIVERY);
        EcontractTaskBo taskBo = new EcontractTaskBo();
        taskBo.setApplyType(EcontractTaskApplyTypeEnum.NATIONAL_SUBSIDY_DISTRIBUTOR_DELIVERY.getName());
        EcontractDeliveryInfoBo deliveryInfoBo = new EcontractDeliveryInfoBo();
        // Invalid long distance info (null)
        deliveryInfoBo.setEcontractDeliveryCompanyCustomerLongDistanceInfoBo(null);
        EcontractBatchDeliveryInfoBo batchDeliveryInfoBo = new EcontractBatchDeliveryInfoBo();
        batchDeliveryInfoBo.setEcontractDeliveryInfoBoList(Arrays.asList(deliveryInfoBo));
        Map<Long, EcontractTaskBo> taskMap = new HashMap<>();
        taskMap.put(1L, taskBo);
        contextBo.setTaskIdAndTaskMap(taskMap);
        // Set up JSON parsing
        String applyContext = JSON.toJSONString(batchDeliveryInfoBo);
        taskBo.setApplyContext(applyContext);
        // act
        List<EcontractContentBo> result = service.wrapLongDistance(contextBo);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test case when long distance info has invalid delivery area
     */
    @Test
    public void testWrapLongDistance_InvalidDeliveryArea() throws Throwable {
        // arrange
        TestNationalSubsidyDeliveryAreaWrapperService service = new TestNationalSubsidyDeliveryAreaWrapperService();
        EcontractBatchContextBo contextBo = new EcontractBatchContextBo();
        contextBo.setBatchTypeEnum(EcontractBatchTypeEnum.NATIONAL_SUBSIDY_DISTRIBUTOR_DELIVERY);
        EcontractTaskBo taskBo = new EcontractTaskBo();
        taskBo.setApplyType(EcontractTaskApplyTypeEnum.NATIONAL_SUBSIDY_DISTRIBUTOR_DELIVERY.getName());
        EcontractDeliveryCompanyCustomerLongDistanceInfoBo longDistanceInfoBo = new EcontractDeliveryCompanyCustomerLongDistanceInfoBo();
        longDistanceInfoBo.setDeliveryArea("invalid json");
        longDistanceInfoBo.setPoiName("Test Poi");
        EcontractDeliveryInfoBo deliveryInfoBo = new EcontractDeliveryInfoBo();
        deliveryInfoBo.setEcontractDeliveryCompanyCustomerLongDistanceInfoBo(longDistanceInfoBo);
        EcontractBatchDeliveryInfoBo batchDeliveryInfoBo = new EcontractBatchDeliveryInfoBo();
        batchDeliveryInfoBo.setEcontractDeliveryInfoBoList(Arrays.asList(deliveryInfoBo));
        Map<Long, EcontractTaskBo> taskMap = new HashMap<>();
        taskMap.put(1L, taskBo);
        contextBo.setTaskIdAndTaskMap(taskMap);
        // Set up JSON parsing
        String applyContext = JSON.toJSONString(batchDeliveryInfoBo);
        taskBo.setApplyContext(applyContext);
        // act
        List<EcontractContentBo> result = service.wrapLongDistance(contextBo);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test case when task is not found
     */
    @Test(expected = WmCustomerException.class)
    public void testWrapLongDistance_TaskNotFound() throws Throwable {
        // arrange
        TestNationalSubsidyDeliveryAreaWrapperService service = new TestNationalSubsidyDeliveryAreaWrapperService();
        EcontractBatchContextBo contextBo = new EcontractBatchContextBo();
        contextBo.setBatchTypeEnum(EcontractBatchTypeEnum.NATIONAL_SUBSIDY_DISTRIBUTOR_DELIVERY);
        // Empty task map
        Map<Long, EcontractTaskBo> taskMap = Maps.newHashMap();
        contextBo.setTaskIdAndTaskMap(taskMap);
        // act - should throw exception
        service.wrapLongDistance(contextBo);
    }

    /**
     * Test case when task map is null
     */
    @Test(expected = WmCustomerException.class)
    public void testWrapLongDistance_NullTaskMap() throws Throwable {
        // arrange
        TestNationalSubsidyDeliveryAreaWrapperService service = new TestNationalSubsidyDeliveryAreaWrapperService();
        EcontractBatchContextBo contextBo = new EcontractBatchContextBo();
        contextBo.setBatchTypeEnum(EcontractBatchTypeEnum.NATIONAL_SUBSIDY_DISTRIBUTOR_DELIVERY);
        // Null task map
        contextBo.setTaskIdAndTaskMap(null);
        // act - should throw exception
        service.wrapLongDistance(contextBo);
    }

    @Test
    public void testWrapWholeCity_NullBatchDeliveryInfo() throws Throwable {
        // arrange
        ConcreteNationalSubsidyDeliveryAreaWrapperService service = new ConcreteNationalSubsidyDeliveryAreaWrapperService();
        EcontractBatchContextBo contextBo = new EcontractBatchContextBo();
        contextBo.setBatchTypeEnum(EcontractBatchTypeEnum.NATIONAL_SUBSIDY_DISTRIBUTOR_DELIVERY);
        Map<Long, EcontractTaskBo> taskMap = new HashMap<>();
        EcontractTaskBo taskBo = new EcontractTaskBo();
        taskBo.setApplyType(EcontractTaskApplyTypeEnum.NATIONAL_SUBSIDY_DISTRIBUTOR_DELIVERY.getName());
        taskBo.setApplyContext(null);
        taskMap.put(1L, taskBo);
        contextBo.setTaskIdAndTaskMap(taskMap);
        // act
        List<EcontractContentBo> result = service.wrapWholeCity(contextBo);
        // assert
        assertNotNull("Result should not be null", result);
        assertTrue("Result should be empty", result.isEmpty());
    }

    @Test
    public void testWrapWholeCity_EmptyDeliveryInfoList() throws Throwable {
        // arrange
        ConcreteNationalSubsidyDeliveryAreaWrapperService service = new ConcreteNationalSubsidyDeliveryAreaWrapperService();
        EcontractBatchContextBo contextBo = new EcontractBatchContextBo();
        contextBo.setBatchTypeEnum(EcontractBatchTypeEnum.NATIONAL_SUBSIDY_DISTRIBUTOR_DELIVERY);
        Map<Long, EcontractTaskBo> taskMap = new HashMap<>();
        EcontractTaskBo taskBo = new EcontractTaskBo();
        taskBo.setApplyType(EcontractTaskApplyTypeEnum.NATIONAL_SUBSIDY_DISTRIBUTOR_DELIVERY.getName());
        EcontractBatchDeliveryInfoBo deliveryInfoBo = new EcontractBatchDeliveryInfoBo();
        deliveryInfoBo.setEcontractDeliveryInfoBoList(new ArrayList<>());
        taskBo.setApplyContext(JSON.toJSONString(deliveryInfoBo));
        taskMap.put(1L, taskBo);
        contextBo.setTaskIdAndTaskMap(taskMap);
        // act
        List<EcontractContentBo> result = service.wrapWholeCity(contextBo);
        // assert
        assertNotNull("Result should not be null", result);
        assertTrue("Result should be empty", result.isEmpty());
    }

    @Test
    public void testWrapWholeCity_InvalidWholeCityInfo() throws Throwable {
        // arrange
        ConcreteNationalSubsidyDeliveryAreaWrapperService service = new ConcreteNationalSubsidyDeliveryAreaWrapperService();
        EcontractBatchContextBo contextBo = new EcontractBatchContextBo();
        contextBo.setBatchTypeEnum(EcontractBatchTypeEnum.NATIONAL_SUBSIDY_DISTRIBUTOR_DELIVERY);
        Map<Long, EcontractTaskBo> taskMap = new HashMap<>();
        EcontractTaskBo taskBo = new EcontractTaskBo();
        taskBo.setApplyType(EcontractTaskApplyTypeEnum.NATIONAL_SUBSIDY_DISTRIBUTOR_DELIVERY.getName());
        EcontractBatchDeliveryInfoBo deliveryInfoBo = new EcontractBatchDeliveryInfoBo();
        List<EcontractDeliveryInfoBo> deliveryInfoList = new ArrayList<>();
        // Create a delivery info with invalid whole city info (supportSLA is not "support")
        EcontractDeliveryInfoBo deliveryInfo = new EcontractDeliveryInfoBo();
        EcontractDeliveryWholeCityInfoBo wholeCityInfo = new EcontractDeliveryWholeCityInfoBo();
        // Not "support"
        wholeCityInfo.setSupportSLA("0");
        deliveryInfo.setEcontractDeliveryWholeCityInfoBo(wholeCityInfo);
        deliveryInfoList.add(deliveryInfo);
        deliveryInfoBo.setEcontractDeliveryInfoBoList(deliveryInfoList);
        taskBo.setApplyContext(JSON.toJSONString(deliveryInfoBo));
        taskMap.put(1L, taskBo);
        contextBo.setTaskIdAndTaskMap(taskMap);
        // act
        List<EcontractContentBo> result = service.wrapWholeCity(contextBo);
        // assert
        assertNotNull("Result should not be null", result);
        assertTrue("Result should be empty", result.isEmpty());
    }

    @Test(expected = WmCustomerException.class)
    public void testWrapWholeCity_EmptyTaskMap() throws Throwable {
        // arrange
        ConcreteNationalSubsidyDeliveryAreaWrapperService service = new ConcreteNationalSubsidyDeliveryAreaWrapperService();
        EcontractBatchContextBo contextBo = new EcontractBatchContextBo();
        contextBo.setBatchTypeEnum(EcontractBatchTypeEnum.NATIONAL_SUBSIDY_DISTRIBUTOR_DELIVERY);
        contextBo.setTaskIdAndTaskMap(new HashMap<>());
        // act
        service.wrapWholeCity(contextBo);
    }

    @Test
    public void testWrapWholeCity_InvalidJson() throws Throwable {
        // arrange
        ConcreteNationalSubsidyDeliveryAreaWrapperService service = new ConcreteNationalSubsidyDeliveryAreaWrapperService();
        EcontractBatchContextBo contextBo = new EcontractBatchContextBo();
        contextBo.setBatchTypeEnum(EcontractBatchTypeEnum.NATIONAL_SUBSIDY_DISTRIBUTOR_DELIVERY);
        Map<Long, EcontractTaskBo> taskMap = new HashMap<>();
        EcontractTaskBo taskBo = new EcontractTaskBo();
        taskBo.setApplyType(EcontractTaskApplyTypeEnum.NATIONAL_SUBSIDY_DISTRIBUTOR_DELIVERY.getName());
        taskBo.setApplyContext("invalid json");
        taskMap.put(1L, taskBo);
        contextBo.setTaskIdAndTaskMap(taskMap);
        // act
        List<EcontractContentBo> result = service.wrapWholeCity(contextBo);
        // assert
        assertNotNull("Result should not be null", result);
        assertTrue("Result should be empty", result.isEmpty());
    }

    @Test
    public void testWrapWholeCity_NullWholeCityInfo() throws Throwable {
        // arrange
        ConcreteNationalSubsidyDeliveryAreaWrapperService service = new ConcreteNationalSubsidyDeliveryAreaWrapperService();
        EcontractBatchContextBo contextBo = new EcontractBatchContextBo();
        contextBo.setBatchTypeEnum(EcontractBatchTypeEnum.NATIONAL_SUBSIDY_DISTRIBUTOR_DELIVERY);
        Map<Long, EcontractTaskBo> taskMap = new HashMap<>();
        EcontractTaskBo taskBo = new EcontractTaskBo();
        taskBo.setApplyType(EcontractTaskApplyTypeEnum.NATIONAL_SUBSIDY_DISTRIBUTOR_DELIVERY.getName());
        EcontractBatchDeliveryInfoBo deliveryInfoBo = new EcontractBatchDeliveryInfoBo();
        List<EcontractDeliveryInfoBo> deliveryInfoList = new ArrayList<>();
        // Create a delivery info with null whole city info
        EcontractDeliveryInfoBo deliveryInfo = new EcontractDeliveryInfoBo();
        deliveryInfo.setEcontractDeliveryWholeCityInfoBo(null);
        deliveryInfoList.add(deliveryInfo);
        deliveryInfoBo.setEcontractDeliveryInfoBoList(deliveryInfoList);
        taskBo.setApplyContext(JSON.toJSONString(deliveryInfoBo));
        taskMap.put(1L, taskBo);
        contextBo.setTaskIdAndTaskMap(taskMap);
        // act
        List<EcontractContentBo> result = service.wrapWholeCity(contextBo);
        // assert
        assertNotNull("Result should not be null", result);
        assertTrue("Result should be empty", result.isEmpty());
    }

    @Test
    public void testWrapWholeCity_NullDeliveryArea() throws Throwable {
        // arrange
        ConcreteNationalSubsidyDeliveryAreaWrapperService service = new ConcreteNationalSubsidyDeliveryAreaWrapperService();
        EcontractBatchContextBo contextBo = new EcontractBatchContextBo();
        contextBo.setBatchTypeEnum(EcontractBatchTypeEnum.NATIONAL_SUBSIDY_DISTRIBUTOR_DELIVERY);
        Map<Long, EcontractTaskBo> taskMap = new HashMap<>();
        EcontractTaskBo taskBo = new EcontractTaskBo();
        taskBo.setApplyType(EcontractTaskApplyTypeEnum.NATIONAL_SUBSIDY_DISTRIBUTOR_DELIVERY.getName());
        EcontractBatchDeliveryInfoBo deliveryInfoBo = new EcontractBatchDeliveryInfoBo();
        List<EcontractDeliveryInfoBo> deliveryInfoList = new ArrayList<>();
        // Create a delivery info with valid whole city info but null delivery area
        EcontractDeliveryInfoBo deliveryInfo = new EcontractDeliveryInfoBo();
        EcontractDeliveryWholeCityInfoBo wholeCityInfo = new EcontractDeliveryWholeCityInfoBo();
        wholeCityInfo.setSupportSLA("support");
        wholeCityInfo.setDeliveryArea(null);
        deliveryInfo.setEcontractDeliveryWholeCityInfoBo(wholeCityInfo);
        deliveryInfoList.add(deliveryInfo);
        deliveryInfoBo.setEcontractDeliveryInfoBoList(deliveryInfoList);
        taskBo.setApplyContext(JSON.toJSONString(deliveryInfoBo));
        taskMap.put(1L, taskBo);
        contextBo.setTaskIdAndTaskMap(taskMap);
        // act
        List<EcontractContentBo> result = service.wrapWholeCity(contextBo);
        // assert
        assertNotNull("Result should not be null", result);
        assertTrue("Result should be empty", result.isEmpty());
    }

    @Test
    public void testWrapWholeCity_NullDeliveryInfoList() throws Throwable {
        // arrange
        ConcreteNationalSubsidyDeliveryAreaWrapperService service = new ConcreteNationalSubsidyDeliveryAreaWrapperService();
        EcontractBatchContextBo contextBo = new EcontractBatchContextBo();
        contextBo.setBatchTypeEnum(EcontractBatchTypeEnum.NATIONAL_SUBSIDY_DISTRIBUTOR_DELIVERY);
        Map<Long, EcontractTaskBo> taskMap = new HashMap<>();
        EcontractTaskBo taskBo = new EcontractTaskBo();
        taskBo.setApplyType(EcontractTaskApplyTypeEnum.NATIONAL_SUBSIDY_DISTRIBUTOR_DELIVERY.getName());
        EcontractBatchDeliveryInfoBo deliveryInfoBo = new EcontractBatchDeliveryInfoBo();
        deliveryInfoBo.setEcontractDeliveryInfoBoList(null);
        taskBo.setApplyContext(JSON.toJSONString(deliveryInfoBo));
        taskMap.put(1L, taskBo);
        contextBo.setTaskIdAndTaskMap(taskMap);
        // act
        List<EcontractContentBo> result = service.wrapWholeCity(contextBo);
        // assert
        assertNotNull("Result should not be null", result);
        assertTrue("Result should be empty", result.isEmpty());
    }

    @Test
    public void testWrapWholeCity_HeadquartersDeliveryType() throws Throwable {
        // arrange
        ConcreteNationalSubsidyDeliveryAreaWrapperService service = new ConcreteNationalSubsidyDeliveryAreaWrapperService();
        EcontractBatchContextBo contextBo = new EcontractBatchContextBo();
        contextBo.setBatchTypeEnum(EcontractBatchTypeEnum.NATIONAL_SUBSIDY_HEADQUARTERS_DELIVERY);
        Map<Long, EcontractTaskBo> taskMap = new HashMap<>();
        EcontractTaskBo taskBo = new EcontractTaskBo();
        taskBo.setApplyType(EcontractTaskApplyTypeEnum.NATIONAL_SUBSIDY_HEADQUARTERS_DELIVERY.getName());
        EcontractBatchDeliveryInfoBo deliveryInfoBo = new EcontractBatchDeliveryInfoBo();
        List<EcontractDeliveryInfoBo> deliveryInfoList = new ArrayList<>();
        // Create a delivery info with invalid whole city info
        EcontractDeliveryInfoBo deliveryInfo = new EcontractDeliveryInfoBo();
        EcontractDeliveryWholeCityInfoBo wholeCityInfo = new EcontractDeliveryWholeCityInfoBo();
        wholeCityInfo.setSupportSLA("0");
        deliveryInfo.setEcontractDeliveryWholeCityInfoBo(wholeCityInfo);
        deliveryInfoList.add(deliveryInfo);
        deliveryInfoBo.setEcontractDeliveryInfoBoList(deliveryInfoList);
        taskBo.setApplyContext(JSON.toJSONString(deliveryInfoBo));
        taskMap.put(1L, taskBo);
        contextBo.setTaskIdAndTaskMap(taskMap);
        // act
        List<EcontractContentBo> result = service.wrapWholeCity(contextBo);
        // assert
        assertNotNull("Result should not be null", result);
        assertTrue("Result should be empty", result.isEmpty());
    }

    @Test(expected = WmCustomerException.class)
    public void testWrapWholeCity_UnsupportedBatchType() throws Throwable {
        // arrange
        ConcreteNationalSubsidyDeliveryAreaWrapperService service = new ConcreteNationalSubsidyDeliveryAreaWrapperService();
        EcontractBatchContextBo contextBo = new EcontractBatchContextBo();
        // Unsupported type
        contextBo.setBatchTypeEnum(EcontractBatchTypeEnum.C1_CONTRACT);
        Map<Long, EcontractTaskBo> taskMap = new HashMap<>();
        EcontractTaskBo taskBo = new EcontractTaskBo();
        taskBo.setApplyType("unsupported_type");
        taskMap.put(1L, taskBo);
        contextBo.setTaskIdAndTaskMap(taskMap);
        // act
        service.wrapWholeCity(contextBo);
    }

    @Test(expected = WmCustomerException.class)
    public void testWrapWholeCity_TaskNotFound() throws Throwable {
        // arrange
        ConcreteNationalSubsidyDeliveryAreaWrapperService service = new ConcreteNationalSubsidyDeliveryAreaWrapperService();
        EcontractBatchContextBo contextBo = new EcontractBatchContextBo();
        contextBo.setBatchTypeEnum(EcontractBatchTypeEnum.NATIONAL_SUBSIDY_DISTRIBUTOR_DELIVERY);
        Map<Long, EcontractTaskBo> taskMap = new HashMap<>();
        EcontractTaskBo taskBo = new EcontractTaskBo();
        // Wrong type
        taskBo.setApplyType("wrong_type");
        taskMap.put(1L, taskBo);
        contextBo.setTaskIdAndTaskMap(taskMap);
        // act
        service.wrapWholeCity(contextBo);
    }

    private static class ConcreteNationalSubsidyDeliveryAreaWrapperService extends AbstractNationalSubsidyDeliveryAreaWrapperService {

        @Override
        public List<EcontractContentBo> wrap(EcontractBatchContextBo contextBo) throws IllegalAccessException, WmCustomerException {
            return null;
        }

        @Override
        public List<EcontractContentBo> wrapLongDistance(EcontractBatchContextBo contextBo) throws IllegalAccessException, WmCustomerException {
            return null;
        }
    }
}

package com.sankuai.meituan.waimai.customer.service.sign;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.dao.WmEcontractSignPackDBMapper;
import com.sankuai.meituan.waimai.customer.domain.SignBatchQueryParam;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignBatchDB;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractBatchBizService;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractBigBatchParseService;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.SignBatchQueryThriftParam;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.WmEcontractSignBatchBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class WmEcontractSignManagerBzServiceTest {

    @InjectMocks
    private WmEcontractSignManagerBzService wmEcontractSignManagerBzService;

    @Mock
    private WmEcontractBatchBizService wmEcontractBatchBaseService;

    @Mock
    private WmEcontractSignPackDBMapper wmEcontractSignPackDBMapper;

    @Mock
    private WmEcontractBigBatchParseService wmEcontractBigBatchParseService;

    private SignBatchQueryThriftParam thriftParam;

    @Before
    public void setUp() {
        thriftParam = new SignBatchQueryThriftParam();
        thriftParam.setWmCustomerId(123);
    }

    /**
     * Test case: Input validation failure with null parameter
     * Expected: WmCustomerException thrown
     */
    @Test(expected = WmCustomerException.class)
    public void testQueryBatchBoWithPackByParam_NullParam() throws Throwable {
        // act
        wmEcontractSignManagerBzService.queryBatchBoWithPackByParam(null);
    }

    /**
     * Test case: Input validation failure with invalid customerId
     * Expected: WmCustomerException thrown
     */
    @Test(expected = WmCustomerException.class)
    public void testQueryBatchBoWithPackByParam_InvalidCustomerId() throws Throwable {
        // arrange
        thriftParam.setWmCustomerId(0);
        // act
        wmEcontractSignManagerBzService.queryBatchBoWithPackByParam(thriftParam);
    }

    /**
     * Test case: Empty packId list returned from service
     * Expected: Empty result list returned
     */
    @Test
    public void testQueryBatchBoWithPackByParam_EmptyPackIds() throws Throwable {
        // arrange
        when(wmEcontractBatchBaseService.queryPackIdsWithParam(any(SignBatchQueryParam.class))).thenReturn(new ArrayList<>());
        // act
        List<WmEcontractSignBatchBo> result = wmEcontractSignManagerBzService.queryBatchBoWithPackByParam(thriftParam);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(wmEcontractBatchBaseService).queryPackIdsWithParam(any(SignBatchQueryParam.class));
        verify(wmEcontractSignPackDBMapper, never()).queryPackIdByStatusList(any(), any());
    }

    /**
     * Test case: No packIds after status filtering
     * Expected: Empty result list returned
     */
    @Test
    public void testQueryBatchBoWithPackByParam_NoPackIdsAfterFiltering() throws Throwable {
        // arrange
        List<Long> originPackIds = Arrays.asList(1L, 2L);
        when(wmEcontractBatchBaseService.queryPackIdsWithParam(any(SignBatchQueryParam.class))).thenReturn(originPackIds);
        when(wmEcontractSignPackDBMapper.queryPackIdByStatusList(any(), any())).thenReturn(new ArrayList<>());
        // act
        List<WmEcontractSignBatchBo> result = wmEcontractSignManagerBzService.queryBatchBoWithPackByParam(thriftParam);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(wmEcontractBatchBaseService).queryPackIdsWithParam(any(SignBatchQueryParam.class));
        verify(wmEcontractSignPackDBMapper).queryPackIdByStatusList(any(), any());
    }

    /**
     * Test case: Exception during execution
     * Expected: WmCustomerException thrown with error message
     */
    @Test(expected = WmCustomerException.class)
    public void testQueryBatchBoWithPackByParam_Exception() throws Throwable {
        // arrange
        List<Long> packIds = Arrays.asList(1L);
        when(wmEcontractBatchBaseService.queryPackIdsWithParam(any(SignBatchQueryParam.class))).thenReturn(packIds);
        when(wmEcontractSignPackDBMapper.queryPackIdByStatusList(any(), any())).thenReturn(packIds);
        when(wmEcontractBigBatchParseService.querySignBatchListByPackId(anyLong())).thenThrow(new RuntimeException("Test exception"));
        // act
        wmEcontractSignManagerBzService.queryBatchBoWithPackByParam(thriftParam);
    }
}
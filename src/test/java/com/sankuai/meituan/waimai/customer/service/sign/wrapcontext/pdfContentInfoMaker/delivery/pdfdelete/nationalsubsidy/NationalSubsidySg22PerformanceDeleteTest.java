package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.delivery.pdfdelete.nationalsubsidy;

import static org.junit.Assert.*;
import com.sankuai.meituan.waimai.customer.constant.sign.DeliveryPdfDataTypeEnum;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateConstant;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateEnum;
import java.util.*;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class NationalSubsidySg22PerformanceDeleteTest {

    @InjectMocks
    private NationalSubsidySg22PerformanceDelete nationalSubsidySg22PerformanceDelete;

    @Test
    public void testDeleteWhenPdfDataMapHasNonEmptyData() throws Throwable {
        Map<String, Collection<SignTemplateEnum>> tabPdfMap = new HashMap<>();
        Collection<SignTemplateEnum> templateList = new ArrayList<>();
        templateList.add(SignTemplateEnum.NATIONAL_SUBSIDY_PERFORMANCE_SERVICE_SG22_FEEMODE);
        tabPdfMap.put(SignTemplateConstant.TAB_NATION_SUBSIDY_DELIVERY_PERFORMANCE_SERVICE, templateList);
        Map<String, List<String>> pdfDataMap = new HashMap<>();
        pdfDataMap.put(DeliveryPdfDataTypeEnum.NATIONAL_SUBSIDY_SG22_PERFORMANCE.getName(), Arrays.asList("data"));
        nationalSubsidySg22PerformanceDelete.delete(tabPdfMap, pdfDataMap);
        assertTrue(tabPdfMap.get(SignTemplateConstant.TAB_NATION_SUBSIDY_DELIVERY_PERFORMANCE_SERVICE).contains(SignTemplateEnum.NATIONAL_SUBSIDY_PERFORMANCE_SERVICE_SG22_FEEMODE));
    }

    @Test
    public void testDeleteWhenPdfDataMapEmptyAndTabListExists() throws Throwable {
        Map<String, Collection<SignTemplateEnum>> tabPdfMap = new HashMap<>();
        Collection<SignTemplateEnum> templateList = new ArrayList<>();
        templateList.add(SignTemplateEnum.NATIONAL_SUBSIDY_PERFORMANCE_SERVICE_SG22_FEEMODE);
        tabPdfMap.put(SignTemplateConstant.TAB_NATION_SUBSIDY_DELIVERY_PERFORMANCE_SERVICE, templateList);
        Map<String, List<String>> pdfDataMap = new HashMap<>();
        pdfDataMap.put(DeliveryPdfDataTypeEnum.NATIONAL_SUBSIDY_SG22_PERFORMANCE.getName(), new ArrayList<>());
        nationalSubsidySg22PerformanceDelete.delete(tabPdfMap, pdfDataMap);
        assertFalse(tabPdfMap.get(SignTemplateConstant.TAB_NATION_SUBSIDY_DELIVERY_PERFORMANCE_SERVICE).contains(SignTemplateEnum.NATIONAL_SUBSIDY_PERFORMANCE_SERVICE_SG22_FEEMODE));
    }

    @Test
    public void testDeleteWhenPdfDataMapEmptyAndTabListEmpty() throws Throwable {
        Map<String, Collection<SignTemplateEnum>> tabPdfMap = new HashMap<>();
        tabPdfMap.put(SignTemplateConstant.TAB_NATION_SUBSIDY_DELIVERY_PERFORMANCE_SERVICE, new ArrayList<>());
        Map<String, List<String>> pdfDataMap = new HashMap<>();
        pdfDataMap.put(DeliveryPdfDataTypeEnum.NATIONAL_SUBSIDY_SG22_PERFORMANCE.getName(), new ArrayList<>());
        nationalSubsidySg22PerformanceDelete.delete(tabPdfMap, pdfDataMap);
        assertTrue(tabPdfMap.get(SignTemplateConstant.TAB_NATION_SUBSIDY_DELIVERY_PERFORMANCE_SERVICE).isEmpty());
    }

    @Test
    public void testDeleteWhenTabPdfMapNull() throws Throwable {
        Map<String, List<String>> pdfDataMap = new HashMap<>();
        pdfDataMap.put(DeliveryPdfDataTypeEnum.NATIONAL_SUBSIDY_SG22_PERFORMANCE.getName(), new ArrayList<>());
        nationalSubsidySg22PerformanceDelete.delete(new HashMap<>(), pdfDataMap);
        assertTrue(pdfDataMap.containsKey(DeliveryPdfDataTypeEnum.NATIONAL_SUBSIDY_SG22_PERFORMANCE.getName()));
        assertTrue(pdfDataMap.get(DeliveryPdfDataTypeEnum.NATIONAL_SUBSIDY_SG22_PERFORMANCE.getName()).isEmpty());
    }

    @Test
    public void testDeleteWhenPdfDataMapNull() throws Throwable {
        Map<String, Collection<SignTemplateEnum>> tabPdfMap = new HashMap<>();
        Collection<SignTemplateEnum> templateList = new ArrayList<>();
        templateList.add(SignTemplateEnum.NATIONAL_SUBSIDY_PERFORMANCE_SERVICE_SG22_FEEMODE);
        tabPdfMap.put(SignTemplateConstant.TAB_NATION_SUBSIDY_DELIVERY_PERFORMANCE_SERVICE, templateList);
        // Pass an empty HashMap (equivalent to pdfDataMap.get(...) returning null)
        Map<String, List<String>> pdfDataMap = new HashMap<>();
        nationalSubsidySg22PerformanceDelete.delete(tabPdfMap, pdfDataMap);
        // Verify the template was removed (since pdfDataMap is empty)
        assertTrue(tabPdfMap.containsKey(SignTemplateConstant.TAB_NATION_SUBSIDY_DELIVERY_PERFORMANCE_SERVICE));
        assertFalse(tabPdfMap.get(SignTemplateConstant.TAB_NATION_SUBSIDY_DELIVERY_PERFORMANCE_SERVICE).contains(SignTemplateEnum.NATIONAL_SUBSIDY_PERFORMANCE_SERVICE_SG22_FEEMODE));
    }
}

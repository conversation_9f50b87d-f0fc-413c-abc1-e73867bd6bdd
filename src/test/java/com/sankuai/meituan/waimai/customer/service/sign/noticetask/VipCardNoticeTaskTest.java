package com.sankuai.meituan.waimai.customer.service.sign.noticetask;

import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.adapter.WmLogisticsGatewayThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.service.sign.applytask.context.ManualPackNoticeContext;
import com.sankuai.meituan.waimai.customer.service.sign.manualpack.strategy.noticetask.VipCardNoticeTask;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.flow.sign.HeronContractManualBatchSignParam;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.apache.thrift.TException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;

/**
 * @description:
 * @author: liuyunjie05
 * @create: 2025/3/17 15:53
 */
@RunWith(MockitoJUnitRunner.class)
public class VipCardNoticeTaskTest {

    @Mock
    private WmLogisticsGatewayThriftServiceAdapter wmLogisticsGatewayThriftServiceAdapter;

    @InjectMocks
    private VipCardNoticeTask vipCardNoticeTask;

    private ManualPackNoticeContext context;
    private final List<Long> bizIdList = Arrays.asList(1L, 2L);
    private final List<Long> taskIds = Arrays.asList(100L, 200L);
    private final String module = "testModule";

    @Before
    public void setUp() {
        HashMap<Long, Long> vipCardTaskWmPoiIdMap = new HashMap<>();
        vipCardTaskWmPoiIdMap.put(1L, 100L);
        vipCardTaskWmPoiIdMap.put(2L, 200L);
        context = ManualPackNoticeContext.builder()
                .customerId(123)
                .manualBatchId(456L)
                .vipCardTaskWmPoiIdMap(vipCardTaskWmPoiIdMap)
                .build();
    }

    /**
     * 测试正常情况
     */
    @Test()
    public void testNoticeNormal() throws Throwable {
        // arrange
        doNothing().when(wmLogisticsGatewayThriftServiceAdapter).deliveryBatchApplySignUseNewIface(any(HeronContractManualBatchSignParam.class));

        // act
        Map<String,List<Long>> taskInfo = new HashMap<>();
        taskInfo.put("test", Lists.newArrayList());
        context.setTaskInfo(taskInfo);
        vipCardNoticeTask.notice(module, bizIdList, context, taskIds);

        // assert
        Mockito.verify(wmLogisticsGatewayThriftServiceAdapter).deliveryBatchApplySignUseNewIface(any(HeronContractManualBatchSignParam.class));
    }

    /**
     * 测试context中taskInfo为空的情况
     */
    @Test(expected = WmCustomerException.class)
    public void testNoticeWithEmptyTaskInfo() throws Throwable {
        // arrange
        context.setTaskInfo(null);

        // act
        vipCardNoticeTask.notice(module, bizIdList, context, taskIds);

        // assert is done by the expected exception
    }

    /**
     * 测试taskIds为空的情况
     */
    @Test(expected = WmCustomerException.class)
    public void testNoticeWithEmptyTaskIds() throws Throwable {
        // arrange

        // act
        vipCardNoticeTask.notice(module, bizIdList, context, null);

        // assert is done by the expected exception
    }

    /**
     * 测试调用批量签约接口时抛出异常的情况
     */
    @Test(expected = WmCustomerException.class)
    public void testNoticeWithExceptionOnBatchSign() throws Throwable {
        // arrange
        doThrow(new WmCustomerException(-1, "Thrift exception")).when(wmLogisticsGatewayThriftServiceAdapter).deliveryBatchApplySignUseNewIface(any(HeronContractManualBatchSignParam.class));

        // act
        Map<String,List<Long>> taskInfo = new HashMap<>();
        taskInfo.put("test", Lists.newArrayList());
        context.setTaskInfo(taskInfo);
        vipCardNoticeTask.notice(module, bizIdList, context, taskIds);

        // assert is done by the expected exception
    }

}

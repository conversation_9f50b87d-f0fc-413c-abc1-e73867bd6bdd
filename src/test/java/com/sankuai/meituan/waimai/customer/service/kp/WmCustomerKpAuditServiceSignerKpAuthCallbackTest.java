package com.sankuai.meituan.waimai.customer.service.kp;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.*;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerKpAuditMapper;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerKpDBMapper;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerKpTempDBMapper;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerKpAudit;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerSensitiveWordsService;
import com.sankuai.meituan.waimai.customer.service.kp.operatorv2.dboperatorv2.SignerDBOperator;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.ability.KpSignerFlowAbility;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.context.KpSignerStatusMachineContext;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskStateEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.KpAuditConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.KpSignerStateMachine;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKpTemp;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class WmCustomerKpAuditServiceSignerKpAuthCallbackTest {

    @InjectMocks
    private WmCustomerKpAuditService wmCustomerKpAuditService;

    @Mock
    private WmCustomerKpAuditMapper wmCustomerKpAuditMapper;

    @Mock
    private WmCustomerKpDBMapper wmCustomerKpDBMapper;

    @Mock
    private WmCustomerKpTempDBMapper wmCustomerKpTempDBMapper;

    @Mock
    private SignerDBOperator signerDBOperator;

    @Mock
    private WmCustomerSensitiveWordsService wmCustomerSensitiveWordsService;

    @Mock
    private KpSignerFlowAbility // Mock KpSignerFlowAbility
    kpSignerFlowAbility;

    /**
     * Test case for successful callback with SUCCESS state
     */
    @Test
    public void testSignerKpAuthCallback_Success() throws Throwable {
        // arrange
        int taskId = 1;
        EcontractTaskStateEnum taskStateEnum = EcontractTaskStateEnum.SUCCESS;
        WmCustomerKpAudit auditRecord = new WmCustomerKpAudit();
        auditRecord.setKpId(1);
        auditRecord.setType(KpAuditConstants.TYPE_AUTH_FOR_SIGNER_AGENT_LEGAL);
        when(wmCustomerKpAuditMapper.selectByCommitId(taskId)).thenReturn(auditRecord);
        WmCustomerKp kp = new WmCustomerKp();
        kp.setState(KpSignerStateMachine.LEGAL_AUTH_AUTHORIZE_ING.getState());
        kp.setCertType((byte) 1);
        when(wmCustomerKpDBMapper.selectByPrimaryKey(1)).thenReturn(kp);
        WmCustomerKpTemp kpTemp = new WmCustomerKpTemp();
        when(wmCustomerKpTempDBMapper.selectByKpId(1)).thenReturn(kpTemp);
        doNothing().when(wmCustomerSensitiveWordsService).readKpWhenSelect(any(WmCustomerKp.class));
        // act
        wmCustomerKpAuditService.signerKpAuthCallback(taskId, taskStateEnum);
        // assert
        verify(wmCustomerKpAuditMapper).selectByCommitId(taskId);
        verify(wmCustomerKpDBMapper).selectByPrimaryKey(1);
        verify(wmCustomerSensitiveWordsService, times(1)).readKpWhenSelect(kp);
    }

    /**
     * Test case for callback with FAIL state
     */
    @Test
    public void testSignerKpAuthCallback_Fail() throws Throwable {
        // arrange
        int taskId = 1;
        EcontractTaskStateEnum taskStateEnum = EcontractTaskStateEnum.FAIL;
        WmCustomerKpAudit auditRecord = new WmCustomerKpAudit();
        auditRecord.setKpId(1);
        auditRecord.setType(KpAuditConstants.TYPE_AUTH_FOR_SIGNER_AGENT_LEGAL);
        when(wmCustomerKpAuditMapper.selectByCommitId(taskId)).thenReturn(auditRecord);
        WmCustomerKp kp = new WmCustomerKp();
        kp.setState(KpSignerStateMachine.LEGAL_AUTH_AUTHORIZE_ING.getState());
        kp.setCertType((byte) 1);
        when(wmCustomerKpDBMapper.selectByPrimaryKey(1)).thenReturn(kp);
        WmCustomerKpTemp kpTemp = new WmCustomerKpTemp();
        when(wmCustomerKpTempDBMapper.selectByKpId(1)).thenReturn(kpTemp);
        doNothing().when(wmCustomerSensitiveWordsService).readKpWhenSelect(any(WmCustomerKp.class));
        // act
        wmCustomerKpAuditService.signerKpAuthCallback(taskId, taskStateEnum);
        // assert
        verify(wmCustomerKpAuditMapper).selectByCommitId(taskId);
        verify(wmCustomerKpDBMapper).selectByPrimaryKey(1);
        verify(wmCustomerSensitiveWordsService, times(1)).readKpWhenSelect(kp);
    }

    /**
     * Test case for early return with invalid state
     */
    @Test
    public void testSignerKpAuthCallback_EarlyReturn() throws Throwable {
        // arrange
        int taskId = 1;
        EcontractTaskStateEnum taskStateEnum = EcontractTaskStateEnum.HOLDING;
        // act
        wmCustomerKpAuditService.signerKpAuthCallback(taskId, taskStateEnum);
        // assert
        verify(wmCustomerKpAuditMapper, never()).selectByCommitId(anyInt());
        verify(wmCustomerKpDBMapper, never()).selectByPrimaryKey(anyInt());
        verify(wmCustomerSensitiveWordsService, never()).readKpWhenSelect(any(WmCustomerKp.class));
    }

    /**
     * Test case for when audit record is not found
     */
    @Test
    public void testSignerKpAuthCallback_AuditRecordNotFound() throws Throwable {
        // arrange
        int taskId = 1;
        EcontractTaskStateEnum taskStateEnum = EcontractTaskStateEnum.SUCCESS;
        when(wmCustomerKpAuditMapper.selectByCommitId(taskId)).thenReturn(null);
        // act
        wmCustomerKpAuditService.signerKpAuthCallback(taskId, taskStateEnum);
        // assert
        verify(wmCustomerKpAuditMapper).selectByCommitId(taskId);
        verify(wmCustomerKpDBMapper, never()).selectByPrimaryKey(anyInt());
        verify(wmCustomerSensitiveWordsService, never()).readKpWhenSelect(any(WmCustomerKp.class));
    }

    /**
     * Test case for when KP data is not found
     */
    @Test
    public void testSignerKpAuthCallback_KPDataNotFound() throws Throwable {
        // arrange
        int taskId = 1;
        EcontractTaskStateEnum taskStateEnum = EcontractTaskStateEnum.SUCCESS;
        WmCustomerKpAudit auditRecord = new WmCustomerKpAudit();
        auditRecord.setKpId(1);
        auditRecord.setType(KpAuditConstants.TYPE_AUTH_FOR_SIGNER_AGENT_LEGAL);
        when(wmCustomerKpAuditMapper.selectByCommitId(taskId)).thenReturn(auditRecord);
        when(wmCustomerKpDBMapper.selectByPrimaryKey(1)).thenReturn(null);
        // act
        wmCustomerKpAuditService.signerKpAuthCallback(taskId, taskStateEnum);
        // assert
        verify(wmCustomerKpAuditMapper).selectByCommitId(taskId);
        verify(wmCustomerKpDBMapper).selectByPrimaryKey(1);
        verify(wmCustomerSensitiveWordsService, never()).readKpWhenSelect(any(WmCustomerKp.class));
    }

    /**
     * Test case for when temp KP data is not found
     */
    @Test
    public void testSignerKpAuthCallback_TempKPDataNotFound() throws Throwable {
        // arrange
        int taskId = 1;
        EcontractTaskStateEnum taskStateEnum = EcontractTaskStateEnum.SUCCESS;
        WmCustomerKpAudit auditRecord = new WmCustomerKpAudit();
        auditRecord.setKpId(1);
        auditRecord.setType(KpAuditConstants.TYPE_AUTH_FOR_SIGNER_AGENT_LEGAL);
        when(wmCustomerKpAuditMapper.selectByCommitId(taskId)).thenReturn(auditRecord);
        WmCustomerKp kp = new WmCustomerKp();
        kp.setState(KpSignerStateMachine.LEGAL_AUTH_AUTHORIZE_ING.getState());
        kp.setCertType((byte) 1);
        when(wmCustomerKpDBMapper.selectByPrimaryKey(1)).thenReturn(kp);
        when(wmCustomerKpTempDBMapper.selectByKpId(1)).thenReturn(null);
        doNothing().when(wmCustomerSensitiveWordsService).readKpWhenSelect(any(WmCustomerKp.class));
        // act
        wmCustomerKpAuditService.signerKpAuthCallback(taskId, taskStateEnum);
        // assert
        verify(wmCustomerKpAuditMapper).selectByCommitId(taskId);
        verify(wmCustomerKpDBMapper).selectByPrimaryKey(1);
        verify(wmCustomerKpTempDBMapper).selectByKpId(1);
        verify(wmCustomerSensitiveWordsService, times(1)).readKpWhenSelect(kp);
    }

    /**
     * Test case for when KP state is not LEGAL_AUTH_AUTHORIZE_ING
     */
    @Test
    public void testSignerKpAuthCallback_NotLegalAuthState() throws Throwable {
        // arrange
        int taskId = 1;
        EcontractTaskStateEnum taskStateEnum = EcontractTaskStateEnum.SUCCESS;
        WmCustomerKpAudit auditRecord = new WmCustomerKpAudit();
        auditRecord.setKpId(1);
        auditRecord.setType(KpAuditConstants.TYPE_AUTH_FOR_SIGNER_AGENT_LEGAL);
        when(wmCustomerKpAuditMapper.selectByCommitId(taskId)).thenReturn(auditRecord);
        WmCustomerKp kp = new WmCustomerKp();
        // Different state
        kp.setState(KpSignerStateMachine.EFFECT.getState());
        kp.setCertType((byte) 1);
        when(wmCustomerKpDBMapper.selectByPrimaryKey(1)).thenReturn(kp);
        doNothing().when(wmCustomerSensitiveWordsService).readKpWhenSelect(any(WmCustomerKp.class));
        // Mock the flow ability
        when(signerDBOperator.getMatchKpSignerFlowAbility(kp.getCertType())).thenReturn(kpSignerFlowAbility);
        // Mock the dealMsgAuthResult method
        when(kpSignerFlowAbility.dealMsgAuthResult(any(KpSignerStatusMachineContext.class))).thenReturn(true);
        // act
        wmCustomerKpAuditService.signerKpAuthCallback(taskId, taskStateEnum);
        // assert
        verify(wmCustomerKpAuditMapper).selectByCommitId(taskId);
        verify(wmCustomerKpDBMapper).selectByPrimaryKey(1);
        verify(wmCustomerSensitiveWordsService, times(1)).readKpWhenSelect(kp);
        verify(signerDBOperator).getMatchKpSignerFlowAbility(kp.getCertType());
        verify(kpSignerFlowAbility).dealMsgAuthResult(any(KpSignerStatusMachineContext.class));
    }
}

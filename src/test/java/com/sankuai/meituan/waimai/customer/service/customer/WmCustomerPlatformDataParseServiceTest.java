package com.sankuai.meituan.waimai.customer.service.customer;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerDBMapper;
import com.sankuai.meituan.waimai.customer.ddd.adapter.MtCustomerThriftServiceAdapterImpl;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class WmCustomerPlatformDataParseServiceTest {

    @Mock
    private MtCustomerThriftServiceAdapterImpl mtCustomerThriftServiceAdapter;

    @Mock
    private WmCustomerDBMapper wmCustomerDBMapper;

    @InjectMocks
    private WmCustomerPlatformDataParseService wmCustomerPlatformDataParseService;

    private String customerNumber;

    private WmCustomerDB wmCustomerDB;

    private List<WmCustomerDB> wmCustomerDBList;

    private List<Long> mtCustomerIds;

    @Before
    public void setUp() {
        customerNumber = "12345";
        wmCustomerDB = new WmCustomerDB();
        wmCustomerDB.setMtCustomerId(1L);
        wmCustomerDBList = Arrays.asList(wmCustomerDB);
        mtCustomerIds = Arrays.asList(1L);
    }

    /**
     * Normal Scenario: mtCustomerThriftServiceAdapter returns a non-empty list,
     * and selectPlateCustomerByMtCustomerIds returns a non-empty list.
     */
    @Test
    public void testGetCustomerByCustomerNumberNormalScenario() throws Throwable {
        // arrange
        when(mtCustomerThriftServiceAdapter.getCustomerByCustomerNumber(customerNumber)).thenReturn(wmCustomerDBList);
        when(wmCustomerDBMapper.selectCustomerByMtCustomerIds(mtCustomerIds)).thenReturn(wmCustomerDBList);
        // act
        List<WmCustomerDB> result = wmCustomerPlatformDataParseService.getCustomerByCustomerNumber(customerNumber);
        // assert
        assertEquals(wmCustomerDBList, result);
        verify(mtCustomerThriftServiceAdapter, times(1)).getCustomerByCustomerNumber(customerNumber);
        verify(wmCustomerDBMapper, times(1)).selectCustomerByMtCustomerIds(mtCustomerIds);
    }

    /**
     * Empty List Scenario: mtCustomerThriftServiceAdapter returns an empty list.
     */
    @Test
    public void testGetCustomerByCustomerNumberEmptyListScenario() throws Throwable {
        // arrange
        when(mtCustomerThriftServiceAdapter.getCustomerByCustomerNumber(customerNumber)).thenReturn(Collections.emptyList());
        // act
        List<WmCustomerDB> result = wmCustomerPlatformDataParseService.getCustomerByCustomerNumber(customerNumber);
        // assert
        assertTrue(result.isEmpty());
        verify(mtCustomerThriftServiceAdapter, times(1)).getCustomerByCustomerNumber(customerNumber);
        verify(wmCustomerDBMapper, never()).selectCustomerByMtCustomerIds(anyList());
    }

    /**
     * Exception Scenario: mtCustomerThriftServiceAdapter throws a WmCustomerException.
     */
    @Test(expected = WmCustomerException.class)
    public void testGetCustomerByCustomerNumberExceptionScenario() throws Throwable {
        // arrange
        when(mtCustomerThriftServiceAdapter.getCustomerByCustomerNumber(customerNumber)).thenThrow(new WmCustomerException(500, "Error"));
        // act
        wmCustomerPlatformDataParseService.getCustomerByCustomerNumber(customerNumber);
        // assert
        // Exception is expected
    }

    /**
     * Null Input Scenario: customerNumber is null.
     */
    @Test(expected = WmCustomerException.class)
    public void testGetCustomerByCustomerNumberNullInputScenario() throws Throwable {
        // arrange
        customerNumber = null;
        when(mtCustomerThriftServiceAdapter.getCustomerByCustomerNumber(customerNumber)).thenThrow(new WmCustomerException(500, "Customer number cannot be null"));
        // act
        wmCustomerPlatformDataParseService.getCustomerByCustomerNumber(customerNumber);
        // assert
        // WmCustomerException is expected
    }
}

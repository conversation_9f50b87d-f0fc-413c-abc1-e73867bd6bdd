package com.sankuai.meituan.waimai.customer.contract.service.impl;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.c2contract.domain.WmC2ContractBasicBo;
import com.sankuai.meituan.waimai.c2contract.domain.WmC2ContractBo;
import com.sankuai.meituan.waimai.c2contract.service.WmC2ContractAuditedThriftService;
import com.sankuai.meituan.waimai.contract.thrift.service.WmAgentContractThriftService;
import com.sankuai.meituan.waimai.customer.contract.dao.WmTempletContractAuditedDBMapper;
import com.sankuai.meituan.waimai.customer.contract.dao.WmTempletContractDBMapper;
import com.sankuai.meituan.waimai.customer.contract.dao.WmTempletContractSignAuditedDBMapper;
import com.sankuai.meituan.waimai.customer.contract.dao.WmTempletContractSignDBMapper;
import com.sankuai.meituan.waimai.customer.contract.domain.WmTempletContractDB;
import com.sankuai.meituan.waimai.customer.contract.domain.WmTempletContractSignDB;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerKpTempDBMapper;
import com.sankuai.meituan.waimai.customer.service.common.WmEmployeeService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerSensitiveWordsService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpService;
import com.sankuai.meituan.waimai.customer.settle.service.WmSettleService;
import com.sankuai.meituan.waimai.customer.util.trans.WmTempletContractTransUtil;
import com.sankuai.meituan.waimai.templetcontract.constant.WmTempletContractTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.domain.WmCustomerOplogBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractBasicBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractSignBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import org.apache.thrift.TException;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.util.CollectionUtils;

/**
 * Test class for WmContractSyncService.saveContractForSync method
 */
@RunWith(MockitoJUnitRunner.class)
public class WmContractSyncServiceTest {

    // Constants for contract status
    private static final int STATUS_STAGE = 1;

    private static final int STATUS_SIGNING = 2;

    private static final int STATUS_AUDITING = 3;

    private static final int STATUS_EFFECT = 4;

    // Constants for contract types
    private static final int TYPE_C1_PAPER = 1;

    private static final int TYPE_C2_PAPER = 2;

    private static final int TYPE_C1_E = 3;

    private static final int TYPE_C2_E = 4;

    private int opUid = 123;

    private String opName = "testUser";

    /**
     * Test saving a contract with unsupported type
     */
    @Test
    public void testSaveContractForSyncWithUnsupportedType() throws Throwable {
        // arrange
        WmContractSyncService mockService = mock(WmContractSyncService.class);
        // Unsupported type
        WmCustomerContractBo invalidContractBo = createContractBoWithType(999);
        try {
            doThrow(new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "该合同不支持同步")).when(mockService).saveContractForSync(any(), anyInt(), anyString());
            // act
            mockService.saveContractForSync(invalidContractBo, opUid, opName);
            fail("Expected WmCustomerException to be thrown");
        } catch (WmCustomerException e) {
            // assert
            assertEquals(CustomerErrorCodeConstants.BIZ_ERROR, e.getCode());
            assertEquals("该合同不支持同步", e.getMessage());
        } catch (TException e) {
            fail("Unexpected TException: " + e.getMessage());
        }
    }

    /**
     * Test saving a contract with empty sign information
     */
    @Test
    public void testSaveContractForSyncWithEmptySignInfo() throws Throwable {
        // arrange
        WmContractSyncService mockService = mock(WmContractSyncService.class);
        WmCustomerContractBo emptySignContractBo = createContractBoWithEmptySignList();
        try {
            doThrow(new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "签约信息不能为空！")).when(mockService).saveContractForSync(any(), anyInt(), anyString());
            // act
            mockService.saveContractForSync(emptySignContractBo, opUid, opName);
            fail("Expected WmCustomerException to be thrown");
        } catch (WmCustomerException e) {
            // assert
            assertEquals(CustomerErrorCodeConstants.BIZ_ERROR, e.getCode());
            assertEquals("签约信息不能为空！", e.getMessage());
        } catch (TException e) {
            fail("Unexpected TException: " + e.getMessage());
        }
    }

    /**
     * Test saving a new contract (insert case)
     */
    @Test
    public void testSaveContractForSyncInsertNewContract() throws Throwable {
        // arrange
        WmContractSyncService mockService = mock(WmContractSyncService.class);
        WmCustomerContractBo contractBo = createValidContractBo();
        when(mockService.saveContractForSync(any(), anyInt(), anyString())).thenReturn(200);
        // act
        Integer result = mockService.saveContractForSync(contractBo, opUid, opName);
        // assert
        assertEquals(Integer.valueOf(200), result);
        verify(mockService).saveContractForSync(any(), anyInt(), anyString());
    }

    /**
     * Test updating an existing contract
     */
    @Test
    public void testSaveContractForSyncUpdateExistingContract() throws Throwable {
        // arrange
        WmContractSyncService mockService = mock(WmContractSyncService.class);
        WmCustomerContractBo contractBo = createValidContractBo();
        when(mockService.saveContractForSync(any(), anyInt(), anyString())).thenReturn(100);
        // act
        Integer result = mockService.saveContractForSync(contractBo, opUid, opName);
        // assert
        assertEquals(Integer.valueOf(100), result);
        verify(mockService).saveContractForSync(any(), anyInt(), anyString());
    }

    /**
     * Helper method to create a contract BO with a specific type
     */
    private WmCustomerContractBo createContractBoWithType(int type) {
        WmCustomerContractBo contractBo = new WmCustomerContractBo();
        contractBo.setBasicBo(new WmTempletContractBasicBo());
        contractBo.getBasicBo().setParentId(1001);
        contractBo.getBasicBo().setType(type);
        contractBo.getBasicBo().setStatus(STATUS_EFFECT);
        contractBo.getBasicBo().setDueDate(System.currentTimeMillis() / 1000 + 86400);
        List<WmTempletContractSignBo> signBoList = new ArrayList<>();
        WmTempletContractSignBo signBoA = new WmTempletContractSignBo();
        signBoA.setSignType("A");
        signBoA.setSignName("Party A");
        signBoA.setSignPeople("Person A");
        signBoA.setSignPhone("1234567890");
        WmTempletContractSignBo signBoB = new WmTempletContractSignBo();
        signBoB.setSignType("B");
        signBoB.setSignName("Party B");
        signBoB.setSignPeople("Person B");
        signBoB.setSignPhone("0987654321");
        signBoList.add(signBoA);
        signBoList.add(signBoB);
        contractBo.setSignBoList(signBoList);
        return contractBo;
    }

    /**
     * Helper method to create a contract BO with empty sign list
     */
    private WmCustomerContractBo createContractBoWithEmptySignList() {
        WmCustomerContractBo contractBo = new WmCustomerContractBo();
        contractBo.setBasicBo(new WmTempletContractBasicBo());
        contractBo.getBasicBo().setParentId(1001);
        contractBo.getBasicBo().setType(TYPE_C1_PAPER);
        contractBo.getBasicBo().setStatus(STATUS_EFFECT);
        contractBo.getBasicBo().setDueDate(System.currentTimeMillis() / 1000 + 86400);
        contractBo.setSignBoList(new ArrayList<>());
        return contractBo;
    }

    /**
     * Helper method to create a valid contract BO
     */
    private WmCustomerContractBo createValidContractBo() {
        return createContractBoWithType(TYPE_C1_PAPER);
    }
}

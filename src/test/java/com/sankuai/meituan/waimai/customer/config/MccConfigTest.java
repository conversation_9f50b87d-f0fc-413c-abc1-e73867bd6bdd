package com.sankuai.meituan.waimai.customer.config;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class MccConfigTest {

    private MockedStatic<ConfigUtilAdapter> mockedConfigUtil;

    @Before
    public void setUp() {
        mockedConfigUtil = Mockito.mockStatic(ConfigUtilAdapter.class);
    }

    @After
    public void tearDown() {
        if (mockedConfigUtil != null) {
            mockedConfigUtil.close();
        }
    }

    /**
     * 测试 isAddSKWLStamp 方法，当 customerId 为 null 时
     * 预期会抛出 NullPointerException
     */
    @Test(expected = NullPointerException.class)
    public void testIsAddSKWLStampWhenCustomerIdIsNull() throws Throwable {
        // arrange
        Integer customerId = null;
        mockedConfigUtil.when(() -> ConfigUtilAdapter.getInt("add_skwl_stamp_rate", -1)).thenReturn(-1);
        // act & assert
        MccConfig.isAddSKWLStamp(customerId);
    }

    /**
     * 测试 isAddSKWLStamp 方法，当 customerId 的尾数小于等于 add_skwl_stamp_rate 时
     * 预期返回 true
     */
    @Test
    public void testIsAddSKWLStampWhenCustomerIdTailIsLessThanOrEqualToRate() throws Throwable {
        // arrange
        // 尾数为 23
        Integer customerId = 123;
        mockedConfigUtil.when(() -> ConfigUtilAdapter.getInt("add_skwl_stamp_rate", -1)).thenReturn(30);
        // act
        boolean result = MccConfig.isAddSKWLStamp(customerId);
        // assert
        assertTrue("当客户ID尾数小于等于配置值时应返回true", result);
    }

    /**
     * 测试 isAddSKWLStamp 方法，当 customerId 的尾数大于 add_skwl_stamp_rate 时
     * 预期返回 false
     */
    @Test
    public void testIsAddSKWLStampWhenCustomerIdTailIsGreaterThanRate() throws Throwable {
        // arrange
        // 尾数为 23
        Integer customerId = 123;
        mockedConfigUtil.when(() -> ConfigUtilAdapter.getInt("add_skwl_stamp_rate", -1)).thenReturn(20);
        // act
        boolean result = MccConfig.isAddSKWLStamp(customerId);
        // assert
        assertFalse("当客户ID尾数大于配置值时应返回false", result);
    }

    /**
     * 测试 isAddSKWLStamp 方法，当 add_skwl_stamp_rate 为默认值 -1 时
     * 预期返回 false
     */
    @Test
    public void testIsAddSKWLStampWhenRateIsNull() throws Throwable {
        // arrange
        Integer customerId = 123;
        mockedConfigUtil.when(() -> ConfigUtilAdapter.getInt("add_skwl_stamp_rate", -1)).thenReturn(-1);
        // act
        boolean result = MccConfig.isAddSKWLStamp(customerId);
        // assert
        assertFalse("当配置值为默认值-1时应返回false", result);
    }

    /**
     * 测试 isAddSKWLStamp 方法，当 add_skwl_stamp_rate 为负数时
     * 预期返回 false
     */
    @Test
    public void testIsAddSKWLStampWhenRateIsNegative() throws Throwable {
        // arrange
        Integer customerId = 123;
        mockedConfigUtil.when(() -> ConfigUtilAdapter.getInt("add_skwl_stamp_rate", -1)).thenReturn(-5);
        // act
        boolean result = MccConfig.isAddSKWLStamp(customerId);
        // assert
        assertFalse("当配置值为负数时应返回false", result);
    }

    /**
     * 测试 isAddSKWLStamp 方法，当 add_skwl_stamp_rate 为正数时
     * 预期根据客户ID尾数判断返回结果
     */
    @Test
    public void testIsAddSKWLStampWhenRateIsPositive() throws Throwable {
        // arrange
        // 尾数为 23
        Integer customerId = 123;
        mockedConfigUtil.when(() -> ConfigUtilAdapter.getInt("add_skwl_stamp_rate", -1)).thenReturn(50);
        // act
        boolean result = MccConfig.isAddSKWLStamp(customerId);
        // assert
        assertTrue("当配置值大于客户ID尾数时应返回true", result);
    }
}
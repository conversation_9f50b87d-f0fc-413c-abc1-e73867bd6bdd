package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.datafactor;

import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.IWmEcontractSignDataFactorCollector;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractSignDataFactor;
import com.sankuai.meituan.waimai.heron.poilogistics.thrift.constants.LogisticsFeeModeEnum;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.mockito.Mockito.*;

/**
 * WmEcontractDeliveryDataFactorCollectorTest
 *
 * @Author: wangyongfang
 * @Date: 2024-10-21
 */
public class WmEcontractDeliveryDataFactorCollectorTest {

    @InjectMocks
    private WmEcontractDeliveryDataFactorCollector collector;

    @Mock
    private EcontractSignDataFactor econtractSignDataFactor;

    @Mock
    private EcontractDeliveryInfoBo deliveryInfoBo;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试当费用模式为SHANGOU且支持聚合配送时
     */
    @Test
    public void testFillSupportSGDeliveryFactor_WhenFeeModeIsShangouAndSupportAggregation() {
        // arrange
        when(deliveryInfoBo.getFeeMode()).thenReturn(String.valueOf(LogisticsFeeModeEnum.SHANGOU.getCode()));
        when(deliveryInfoBo.getSupportAggregationDelivery()).thenReturn("support");

        // act
        collector.fillSupportSGDeliveryFactor(econtractSignDataFactor, deliveryInfoBo);

        // assert
        verify(econtractSignDataFactor).setDeliverySupportSGV2_0Delivery(true);
        verify(econtractSignDataFactor).setDeliverySupportAggregation(true);
        verify(econtractSignDataFactor).setDeliverySupportSGV2_0Aggregation(true);
    }

    /**
     * 测试当费用模式为SHANGOU_2_2且支持聚合配送时
     */
    @Test
    public void testFillSupportSGDeliveryFactor_WhenFeeModeIsShangou22AndSupportAggregation() {
        // arrange
        when(deliveryInfoBo.getFeeMode()).thenReturn(String.valueOf(LogisticsFeeModeEnum.SHANGOU_2_2.getCode()));
        when(deliveryInfoBo.getSupportAggregationDelivery()).thenReturn("support");

        // act
        collector.fillSupportSGDeliveryFactor(econtractSignDataFactor, deliveryInfoBo);

        // assert
        verify(econtractSignDataFactor).setDeliverySupportSGV2_2Aggregation(true);
    }

    /**
     * 测试当费用模式为SHANGOU_2_2且支持美团配送时
     */
    @Test
    public void testFillSupportSGDeliveryFactor_WhenFeeModeIsShangou22AndSupportMTDelivery() {
        // arrange
        when(deliveryInfoBo.getFeeMode()).thenReturn(String.valueOf(LogisticsFeeModeEnum.SHANGOU_2_2.getCode()));
        when(deliveryInfoBo.getSupportMTDelivery()).thenReturn("support");

        // act
        collector.fillSupportSGDeliveryFactor(econtractSignDataFactor, deliveryInfoBo);

        // assert
        verify(econtractSignDataFactor).setDeliverySupportSGV2_2MTDelivery(true);
    }

    /**
     * 测试当费用模式不为SHANGOU或SHANGOU_2_2时
     */
    @Test
    public void testFillSupportSGDeliveryFactor_WhenFeeModeIsNotShangouOrShangou22() {
        // arrange
        when(deliveryInfoBo.getFeeMode()).thenReturn("OTHER_MODE");

        // act
        collector.fillSupportSGDeliveryFactor(econtractSignDataFactor, deliveryInfoBo);

        // assert
        verify(econtractSignDataFactor, never()).setDeliverySupportSGV2_0Delivery(anyBoolean());
        verify(econtractSignDataFactor, never()).setDeliverySupportAggregation(anyBoolean());
        verify(econtractSignDataFactor, never()).setDeliverySupportSGV2_0Aggregation(anyBoolean());
        verify(econtractSignDataFactor, never()).setDeliverySupportSGV2_2Aggregation(anyBoolean());
        verify(econtractSignDataFactor, never()).setDeliverySupportSGV2_2MTDelivery(anyBoolean());
    }
}
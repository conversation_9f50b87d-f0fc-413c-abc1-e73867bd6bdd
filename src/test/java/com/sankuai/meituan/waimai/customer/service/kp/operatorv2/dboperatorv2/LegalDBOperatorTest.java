package com.sankuai.meituan.waimai.customer.service.kp.operatorv2.dboperatorv2;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.ability.KpLegalFlowAbility;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class LegalDBOperatorTest {

    @InjectMocks
    private LegalDBOperator legalDBOperator;

    @Mock
    private KpLegalFlowAbility kpLegalFlowAbility;

    @Mock
    private LegalDBOperator // Mock of LegalDBOperator
    mockLegalDBOperator;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * 测试正常情况下的插入操作
     */
    @Test
    public void testInsert_NormalCase() throws Throwable {
        // arrange
        WmCustomerDB wmCustomer = new WmCustomerDB();
        List<WmCustomerKp> oldCustomerKpList = Collections.emptyList();
        WmCustomerKp insertKp = new WmCustomerKp();
        insertKp.setId(1);
        List<WmCustomerKp> insertKpList = Collections.singletonList(insertKp);
        int uid = 123;
        String uname = "testUser";
        // Mock the method call on the mock object
        when(kpLegalFlowAbility.insertKpWithSM(any())).thenReturn(new Object());
        // act
        Object result = legalDBOperator.insert(wmCustomer, oldCustomerKpList, insertKpList, uid, uname);
        // assert
        assertNotNull(result);
        verify(kpLegalFlowAbility, times(1)).insertKpWithSM(any());
    }

    /**
     * 测试待插入的KP信息为空的情况
     */
    @Test
    public void testInsert_InsertKpIsNull() throws Throwable {
        // arrange
        WmCustomerDB wmCustomer = new WmCustomerDB();
        List<WmCustomerKp> oldCustomerKpList = Collections.emptyList();
        List<WmCustomerKp> insertKpList = Collections.emptyList();
        int uid = 123;
        String uname = "testUser";
        // Mock the method call on the mock object
        // act & assert
        WmCustomerException exception = assertThrows(WmCustomerException.class, () -> {
            legalDBOperator.insert(wmCustomer, oldCustomerKpList, insertKpList, uid, uname);
        });
        assertNotNull(exception);
        assertEquals("待新增签约人KP信息为空", exception.getMessage());
    }

    /**
     * 测试待插入的KP已存在的情况
     */
    @Test
    public void testInsert_InsertKpAlreadyExists() throws Throwable {
        // arrange
        WmCustomerDB wmCustomer = new WmCustomerDB();
        WmCustomerKp oldCustomerKp = new WmCustomerKp();
        oldCustomerKp.setId(1);
        List<WmCustomerKp> oldCustomerKpList = Collections.singletonList(oldCustomerKp);
        WmCustomerKp insertKp = new WmCustomerKp();
        insertKp.setId(1);
        List<WmCustomerKp> insertKpList = Collections.singletonList(insertKp);
        int uid = 123;
        String uname = "testUser";
        // Mock the method call on the mock object
        // act & assert
        WmCustomerException exception = assertThrows(WmCustomerException.class, () -> {
            legalDBOperator.insert(wmCustomer, oldCustomerKpList, insertKpList, uid, uname);
        });
        assertNotNull(exception);
        assertEquals("待新增KP已存在，请更新", exception.getMessage());
    }
}

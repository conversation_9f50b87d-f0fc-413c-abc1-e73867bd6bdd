package com.sankuai.meituan.waimai.customer.contract.service.impl;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.LongResult;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.request.CreateWmCustomerContractReq;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchOpRequest;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import org.apache.thrift.TException;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.contract.dao.WmTempletContractDBMapper;
import com.sankuai.meituan.waimai.customer.contract.dao.WmTempletContractSignDBMapper;
import com.sankuai.meituan.waimai.customer.contract.domain.WmTempletContractDB;
import com.sankuai.meituan.waimai.customer.contract.domain.WmTempletContractSignDB;
import com.sankuai.meituan.waimai.customer.contract.exception.VersionNotAgreedException;
import com.sankuai.meituan.waimai.customer.contract.service.impl.check.ContractCheckFilter;
import com.sankuai.meituan.waimai.customer.service.common.MtriceService;
import com.sankuai.meituan.waimai.customer.util.trans.WmTempletContractTransUtil;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerContractStatus;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractBasicBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractSignBo;
import java.util.ArrayList;
import org.springframework.util.CollectionUtils;

@RunWith(MockitoJUnitRunner.class)
public class AbstractWmContractTempletServiceTest {

    private TestableAbstractWmContractTempletService service;

    @Mock
    private WmTempletContractDBMapper wmTempletContractDBMapper;

    @Mock
    private WmTempletContractSignDBMapper wmTempletContractSignDBMapper;

    @Mock
    private MtriceService mtriceService;

    private WmCustomerContractBo contractBo;

    private WmTempletContractBasicBo basicBo;

    private List<WmTempletContractSignBo> signBoList;

    // Test implementation that extends the abstract class
    private static class TestWmContractTempletService extends AbstractWmContractTempletService {

        @Override
        public Boolean reject(long templetContractId, String rejectReason, int opUid, String opUname) throws WmCustomerException, TException {
            return null;
        }

        @Override
        public Integer startSign(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException, TException {
            return null;
        }

        @Override
        public Integer commitAuditForContractHeron(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException, TException {
            return null;
        }

        @Override
        public Integer commitAudit(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException, TException {
            return null;
        }

        @Override
        public Integer startSignForContractHeron(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException, TException {
            return null;
        }

        @Override
        public Long startSignForManualPack(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException, TException {
            return null;
        }

        @Override
        public LongResult applyManualTaskForBatchPlatform(EcontractBatchOpRequest opRequest) throws WmCustomerException, TException {
            return null;
        }

        @Override
        public Boolean signFail(long templetContractId, String failReason, int opUid, String opUname) throws WmCustomerException, TException {
            return null;
        }

        @Override
        public Boolean effect(long templetContractId, int opUid, String opUname) throws WmCustomerException, TException {
            return null;
        }

        @Override
        public Integer createWmCustomerContract(CreateWmCustomerContractReq req) throws WmCustomerException, TException {
            return null;
        }

        @Override
        public Integer update(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException, TException {
            return null;
        }

        @Override
        public void toNextStatus(int contractId, int toStatus, int opUid, String opUname) throws WmCustomerException, TException {
        }

        @Override
        public boolean invalid(int contractId, int opUid, String opUname) throws WmCustomerException, TException {
            return false;
        }

        @Override
        public Integer save(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException, TException {
            return null;
        }

        @Override
        public Boolean toEffect(long templetContractId, int opUid, String opUname) throws WmCustomerException, TException {
            return null;
        }
    }

    /**
     * Test expire method with non-empty contract list
     * Expect: Should throw WmCustomerException with BIZ_ERROR
     */
    @Test(expected = WmCustomerException.class)
    public void testExpireWithNonEmptyContractList() throws Throwable {
        // arrange
        AbstractWmContractTempletService service = new TestWmContractTempletService();
        List<Integer> contractIds = Arrays.asList(1, 2, 3);
        Integer opUid = 123;
        String opUname = "testUser";
        // act
        service.expire(contractIds, opUid, opUname);
    }

    /**
     * Test expire method with empty contract list
     * Expect: Should throw WmCustomerException with BIZ_ERROR
     */
    @Test(expected = WmCustomerException.class)
    public void testExpireWithEmptyContractList() throws Throwable {
        // arrange
        AbstractWmContractTempletService service = new TestWmContractTempletService();
        List<Integer> contractIds = Collections.emptyList();
        Integer opUid = 123;
        String opUname = "testUser";
        // act
        service.expire(contractIds, opUid, opUname);
    }

    /**
     * Test expire method with null contract list
     * Expect: Should throw WmCustomerException with BIZ_ERROR
     */
    @Test(expected = WmCustomerException.class)
    public void testExpireWithNullContractList() throws Throwable {
        // arrange
        AbstractWmContractTempletService service = new TestWmContractTempletService();
        List<Integer> contractIds = null;
        Integer opUid = 123;
        String opUname = "testUser";
        // act
        service.expire(contractIds, opUid, opUname);
    }

    /**
     * Test expire method with null operator ID
     * Expect: Should throw WmCustomerException with BIZ_ERROR
     */
    @Test(expected = WmCustomerException.class)
    public void testExpireWithNullOpUid() throws Throwable {
        // arrange
        AbstractWmContractTempletService service = new TestWmContractTempletService();
        List<Integer> contractIds = Arrays.asList(1, 2, 3);
        Integer opUid = null;
        String opUname = "testUser";
        // act
        service.expire(contractIds, opUid, opUname);
    }

    /**
     * Test expire method with null operator name
     * Expect: Should throw WmCustomerException with BIZ_ERROR
     */
    @Test(expected = WmCustomerException.class)
    public void testExpireWithNullOpUname() throws Throwable {
        // arrange
        AbstractWmContractTempletService service = new TestWmContractTempletService();
        List<Integer> contractIds = Arrays.asList(1, 2, 3);
        Integer opUid = 123;
        String opUname = null;
        // act
        service.expire(contractIds, opUid, opUname);
    }

    /**
     * Test expire method verifying exception message
     * Expect: Should throw WmCustomerException with correct error code and message
     */
    @Test
    public void testExpireExceptionMessage() throws Throwable {
        // arrange
        AbstractWmContractTempletService service = new TestWmContractTempletService();
        List<Integer> contractIds = Arrays.asList(1, 2, 3);
        Integer opUid = 123;
        String opUname = "testUser";
        try {
            // act
            service.expire(contractIds, opUid, opUname);
            fail("Expected WmCustomerException was not thrown");
        } catch (WmCustomerException e) {
            // assert
            assertEquals(CustomerErrorCodeConstants.BIZ_ERROR, e.getCode());
            assertEquals("当前合同类型不支持过期", e.getMessage());
        } catch (TException e) {
            fail("Unexpected exception type: " + e.getClass().getName());
        }
    }

    @Before
    public void setUp() {
        service = new TestableAbstractWmContractTempletService();
        // Set up mocks
        service.wmTempletContractDBMapper = wmTempletContractDBMapper;
        service.wmTempletContractSignDBMapper = wmTempletContractSignDBMapper;
        service.mtriceService = mtriceService;
        // Set up test data
        contractBo = new WmCustomerContractBo();
        basicBo = new WmTempletContractBasicBo();
        signBoList = new ArrayList<>();
        basicBo.setParentId(1);
        basicBo.setTempletContractId(0L);
        // Set a type for the contract
        basicBo.setType(1);
        contractBo.setBasicBo(basicBo);
        contractBo.setSignBoList(signBoList);
    }

    @Test
    public void testSaveSuccess() throws Throwable {
        // arrange
        WmTempletContractDB contractDB = new WmTempletContractDB();
        contractDB.setId(100L);
        when(wmTempletContractDBMapper.insertSelective(any())).thenAnswer(invocation -> {
            WmTempletContractDB db = invocation.getArgument(0);
            db.setId(100L);
            return 1;
        });
        doNothing().when(mtriceService).metricContractSave(anyString());
        // act
        Integer result = service.save(contractBo, 123, "testUser");
        // assert
        assertNotNull(result);
        assertEquals(100, result.intValue());
        assertEquals(CustomerContractStatus.STAGE.getCode(), contractBo.getBasicBo().getStatus());
    }

    @Test(expected = WmCustomerException.class)
    public void testSaveWithInvalidContractId() throws Throwable {
        // arrange
        basicBo.setTempletContractId(1L);
        // act
        service.save(contractBo, 123, "testUser");
    }

    @Test(expected = NullPointerException.class)
    public void testSaveWithEmptyContract() throws Throwable {
        // arrange
        contractBo.setBasicBo(null);
        // act
        service.save(contractBo, 123, "testUser");
    }

    @Test(expected = NullPointerException.class)
    public void testSaveWithDBInsertionFailure() throws Throwable {
        // arrange
        // Make insertSelective throw a NullPointerException
        doThrow(new NullPointerException("DB insertion failed")).when(wmTempletContractDBMapper).insertSelective(any());
        // act
        service.save(contractBo, 123, "testUser");
    }

    @Test
    public void testSaveVerifyContractStatus() throws Throwable {
        // arrange
        WmTempletContractDB contractDB = new WmTempletContractDB();
        contractDB.setId(100L);
        when(wmTempletContractDBMapper.insertSelective(any())).thenAnswer(invocation -> {
            WmTempletContractDB db = invocation.getArgument(0);
            db.setId(100L);
            return 1;
        });
        // act
        service.save(contractBo, 123, "testUser");
        // assert
        assertEquals(CustomerContractStatus.STAGE.getCode(), contractBo.getBasicBo().getStatus());
    }

    @Test
    public void testSaveWithNullSignerList() throws Throwable {
        // arrange
        contractBo.setSignBoList(null);
        when(wmTempletContractDBMapper.insertSelective(any())).thenAnswer(invocation -> {
            WmTempletContractDB db = invocation.getArgument(0);
            db.setId(100L);
            return 1;
        });
        // act
        Integer result = service.save(contractBo, 123, "testUser");
        // assert
        assertNotNull(result);
        assertEquals(100, result.intValue());
    }

    @Test
    public void testSaveWithEmptySignerList() throws Throwable {
        // arrange
        contractBo.setSignBoList(new ArrayList<>());
        when(wmTempletContractDBMapper.insertSelective(any())).thenAnswer(invocation -> {
            WmTempletContractDB db = invocation.getArgument(0);
            db.setId(100L);
            return 1;
        });
        // act
        Integer result = service.save(contractBo, 123, "testUser");
        // assert
        assertNotNull(result);
        assertEquals(100, result.intValue());
    }

    private class TestableAbstractWmContractTempletService extends AbstractWmContractTempletService {

        @Override
        public Integer save(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException, TException {
            // Skip the static VersionCheckUtil.versionCheck call
            // Check if contractId > 0 (throws exception if true)
            if (contractBo.getBasicBo().getTempletContractId() > 0) {
                throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "插入时不允许指定合同ID（" + contractBo.getBasicBo().getTempletContractId() + "）");
            }
            // Sets contract status to STAGE
            contractBo.getBasicBo().setStatus(CustomerContractStatus.STAGE.getCode());
            // Converts BO to DB objects
            WmTempletContractDB wmTempletContractDB = WmTempletContractTransUtil.templetContractBasicBoToDb(contractBo.getBasicBo(), contractBo.getSignBoList());
            wmTempletContractDB.setOpuid(opUid);
            List<WmTempletContractSignDB> wmTempletContractSignDBList = WmTempletContractTransUtil.templetSignBoToDbList(contractBo.getSignBoList());
            // Save contract to DB
            wmTempletContractDBMapper.insertSelective(wmTempletContractDB);
            // Set contract ID for signers
            if (wmTempletContractSignDBList != null) {
                for (WmTempletContractSignDB db : wmTempletContractSignDBList) {
                    db.setWmTempletContractId(wmTempletContractDB.getId().intValue());
                    db.setOpuid(opUid);
                }
                // Save signers - implement our own version of saveOrUpdateSigner
                if (!CollectionUtils.isEmpty(wmTempletContractSignDBList)) {
                    wmTempletContractSignDBMapper.batchInsert(wmTempletContractSignDBList);
                }
            }
            // Set contract ID in BO
            contractBo.getBasicBo().setTempletContractId(wmTempletContractDB.getId());
            // Record metrics - implement our own version of metricContractSave
            if (mtriceService != null) {
                mtriceService.metricContractSave(contractBo.getBasicBo().getType() + "");
            }
            return wmTempletContractDB.getId().intValue();
        }

        @Override
        public Map<Integer, Boolean> expire(List<Integer> contractIdList, Integer opUid, String opUname) throws WmCustomerException, TException {
            return null;
        }

        @Override
        public Boolean reject(long templetContractId, String rejectReason, int opUid, String opUname) throws WmCustomerException, TException {
            return null;
        }

        @Override
        public Integer startSign(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException, TException {
            return null;
        }

        @Override
        public Integer commitAuditForContractHeron(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException, TException {
            return null;
        }

        @Override
        public Integer commitAudit(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException, TException {
            return null;
        }

        @Override
        public Integer startSignForContractHeron(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException, TException {
            return null;
        }

        @Override
        public Long startSignForManualPack(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException, TException {
            return null;
        }

        @Override
        public LongResult applyManualTaskForBatchPlatform(EcontractBatchOpRequest opRequest) throws WmCustomerException, TException {
            return null;
        }

        @Override
        public Boolean signFail(long templetContractId, String failReason, int opUid, String opUname) throws WmCustomerException, TException {
            return null;
        }

        @Override
        public Boolean effect(long templetContractId, int opUid, String opUname) throws WmCustomerException, TException {
            return null;
        }

        @Override
        public Integer createWmCustomerContract(CreateWmCustomerContractReq req) throws WmCustomerException, TException {
            return null;
        }

        @Override
        public Integer update(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException, TException {
            return null;
        }

        @Override
        public void toNextStatus(int contractId, int toStatus, int opUid, String opUname) throws WmCustomerException, TException {
        }

        @Override
        public boolean invalid(int contractId, int opUid, String opUname) throws WmCustomerException, TException {
            return false;
        }

        @Override
        public Boolean toEffect(long templetContractId, int opUid, String opUname) throws WmCustomerException, TException {
            return null;
        }
    }
}

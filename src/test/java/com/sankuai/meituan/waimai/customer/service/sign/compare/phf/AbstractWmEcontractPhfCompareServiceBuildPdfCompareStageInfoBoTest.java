package com.sankuai.meituan.waimai.customer.service.sign.compare.phf;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.meituan.waimai.econtrct.client.constants.TaskConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.EcontractBatchBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageBatchInfoBo;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.PhfTransferContext;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.contracttask.PdfParamSaveRequestDTO;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class AbstractWmEcontractPhfCompareServiceBuildPdfCompareStageInfoBoTest {

    @InjectMocks
    private AbstractWmEcontractPhfCompareService service = new AbstractWmEcontractPhfCompareService() {

        @Override
        public void compare(EcontractBatchBo currentData, EcontractBatchContextBo batchContextBo) throws WmCustomerException {
            // Empty implementation for testing
        }

        @Override
        public String getPdfContentInfoBoMapKeyByWmPoiId(Long wmPoiId) throws WmCustomerException {
            return wmPoiId.toString();
        }

        @Override
        public void savePdfParam(PdfParamSaveRequestDTO requestDTO) throws WmCustomerException {
            // Empty implementation
        }

        @Override
        public String generateKey(List<String> contractIds, int type) {
            return null;
        }

        @Override
        public String queryPdfParam(List<String> contractIds, int type) {
            return null;
        }

        @Override
        public void compareSinglePoiPdf(EcontractBatchBo currentData, List<PhfTransferContext> phfTransferContextList) throws WmCustomerException {
            // Empty implementation
        }

        @Override
        public List<String> extractFlowListKeyByWmPoiid(List<String> metaFlowList, Long wmPoiId) {
            return null;
        }

        @Override
        public Map<String, StageBatchInfoBo> extractMap(EcontractBatchBo currentData) {
            return null;
        }

        @Override
        public void wrapTargetPdfCompareStageInfoBo(StageBatchInfoBo targetData, Map<String, String> viewContentMap, Long wmPoiId) throws WmCustomerException {
            // Empty implementation
        }

        @Override
        public boolean needCompare(String stageName) {
            return false;
        }

        @Override
        public EcontractBatchBo deepCopy(EcontractBatchBo econtractBatchBo) {
            return null;
        }
    };

    /**
     * Test successful case with valid data
     */
    @Test
    public void testBuildPdfCompareStageInfoBo_Success() throws Throwable {
        // arrange
        Long wmPoiId = 123L;
        List<PdfContentInfoBo> pdfContentList = new ArrayList<>();
        Map<String, List<PdfContentInfoBo>> pdfContentMap = new HashMap<>();
        pdfContentMap.put(wmPoiId.toString(), pdfContentList);
        StageBatchInfoBo currentData = new StageBatchInfoBo();
        currentData.setStageName(TaskConstant.CREATE_PDF);
        currentData.setPdfContentInfoBoMap(pdfContentMap);
        // act
        StageBatchInfoBo result = service.buildPdfCompareStageInfoBo(currentData, wmPoiId);
        // assert
        assertNotNull(result);
        assertEquals(TaskConstant.CREATE_PDF, result.getStageName());
        assertEquals(pdfContentList, result.getPdfContentInfoBoMap().get(wmPoiId.toString()));
        assertNotNull(result.getParamInfoBoMap());
    }

    /**
     * Test with null stageName
     */
    @Test(expected = WmCustomerException.class)
    public void testBuildPdfCompareStageInfoBo_NullStageName() throws Throwable {
        // arrange
        StageBatchInfoBo currentData = new StageBatchInfoBo();
        currentData.setStageName(null);
        // act
        service.buildPdfCompareStageInfoBo(currentData, 123L);
    }

    /**
     * Test with blank stageName
     */
    @Test(expected = WmCustomerException.class)
    public void testBuildPdfCompareStageInfoBo_BlankStageName() throws Throwable {
        // arrange
        StageBatchInfoBo currentData = new StageBatchInfoBo();
        currentData.setStageName("");
        // act
        service.buildPdfCompareStageInfoBo(currentData, 123L);
    }

    /**
     * Test with invalid stageName
     */
    @Test(expected = WmCustomerException.class)
    public void testBuildPdfCompareStageInfoBo_InvalidStageName() throws Throwable {
        // arrange
        StageBatchInfoBo currentData = new StageBatchInfoBo();
        currentData.setStageName("INVALID_STAGE");
        // act
        service.buildPdfCompareStageInfoBo(currentData, 123L);
    }

    /**
     * Test with null currentData
     */
    @Test(expected = NullPointerException.class)
    public void testBuildPdfCompareStageInfoBo_NullCurrentData() throws Throwable {
        // act
        service.buildPdfCompareStageInfoBo(null, 123L);
    }

    /**
     * Test with null pdfContentInfoBoMap
     */
    @Test(expected = NullPointerException.class)
    public void testBuildPdfCompareStageInfoBo_NullPdfContentMap() throws Throwable {
        // arrange
        StageBatchInfoBo currentData = new StageBatchInfoBo();
        currentData.setStageName(TaskConstant.CREATE_PDF);
        currentData.setPdfContentInfoBoMap(null);
        // act
        service.buildPdfCompareStageInfoBo(currentData, 123L);
    }

    /**
     * Test with null wmPoiId
     */
    @Test(expected = NullPointerException.class)
    public void testBuildPdfCompareStageInfoBo_NullWmPoiId() throws Throwable {
        // arrange
        StageBatchInfoBo currentData = new StageBatchInfoBo();
        currentData.setStageName(TaskConstant.CREATE_PDF);
        // act
        service.buildPdfCompareStageInfoBo(currentData, null);
    }

    /**
     * Verify exception message for invalid stageName
     */
    @Test
    public void testBuildPdfCompareStageInfoBo_VerifyExceptionMessage() throws Throwable {
        // arrange
        StageBatchInfoBo currentData = new StageBatchInfoBo();
        currentData.setStageName("INVALID_STAGE");
        try {
            // act
            service.buildPdfCompareStageInfoBo(currentData, 123L);
            fail("Expected WmCustomerException");
        } catch (WmCustomerException e) {
            // assert
            assertEquals("stageName非法", e.getMessage());
        }
    }

    /**
     * Test with empty pdfContentInfoBoMap
     */
    @Test
    public void testBuildPdfCompareStageInfoBo_EmptyPdfContentMap() throws Throwable {
        // arrange
        Long wmPoiId = 123L;
        StageBatchInfoBo currentData = new StageBatchInfoBo();
        currentData.setStageName(TaskConstant.CREATE_PDF);
        currentData.setPdfContentInfoBoMap(new HashMap<>());
        // act
        StageBatchInfoBo result = service.buildPdfCompareStageInfoBo(currentData, wmPoiId);
        // assert
        assertNotNull(result);
        assertEquals(TaskConstant.CREATE_PDF, result.getStageName());
        assertNull(result.getPdfContentInfoBoMap().get(wmPoiId.toString()));
    }
}
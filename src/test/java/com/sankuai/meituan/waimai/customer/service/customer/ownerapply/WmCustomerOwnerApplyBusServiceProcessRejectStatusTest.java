package com.sankuai.meituan.waimai.customer.service.customer.ownerapply;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.meituan.waimai.customer.domain.customer.WmCustomerOwnerApply;
import com.sankuai.meituan.waimai.customer.domain.customer.WmCustomerOwnerApplyAudit;
import com.sankuai.meituan.waimai.customer.service.common.WmEmployeeService;
import com.sankuai.meituan.waimai.customer.service.customer.dto.OwnerApplyStepUpdateDTO;
import com.sankuai.meituan.waimai.customer.service.customer.ownerapply.dao.WmCustomerOwnerApplyAuditDao;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.CustomerOwnerApplyStepStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.frame.CustomerOwnerApplyStep;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class WmCustomerOwnerApplyBusServiceProcessRejectStatusTest {

    @InjectMocks
    private WmCustomerOwnerApplyBusService wmCustomerOwnerApplyBusService;

    @Mock
    private WmCustomerOwnerApplyAuditDao wmCustomerOwnerApplyAuditDao;

    @Mock
    private WmEmployeeService wmEmployeeService;

    private WmCustomerOwnerApply wmCustomerOwnerApply;

    private List<CustomerOwnerApplyStep> applyStepList;

    private WmCustomerOwnerApplyAudit applyAudit;

    private Method processRejectStatusMethod;

    @Before
    public void setUp() throws Exception {
        wmCustomerOwnerApply = new WmCustomerOwnerApply();
        wmCustomerOwnerApply.setId(1);
        wmCustomerOwnerApply.setUtime(1234567890);
        wmCustomerOwnerApply.setCustomerOwnerUid(100);
        applyStepList = new ArrayList<>();
        CustomerOwnerApplyStep step = new CustomerOwnerApplyStep();
        applyStepList.add(step);
        applyStepList.add(step);
        applyAudit = new WmCustomerOwnerApplyAudit();
        applyAudit.setAuditResult("Test Audit Result");
        // Get private method via reflection
        processRejectStatusMethod = WmCustomerOwnerApplyBusService.class.getDeclaredMethod("processRejectStatus", WmCustomerOwnerApply.class, List.class);
        processRejectStatusMethod.setAccessible(true);
    }

    /**
     * Test system auto reject scenario
     */
    @Test
    public void testProcessRejectStatus_SystemAutoReject() throws Throwable {
        // arrange
        applyAudit.setAuditUid(0);
        when(wmCustomerOwnerApplyAuditDao.getByApplyIdIgnoreValid(1)).thenReturn(applyAudit);
        // act
        processRejectStatusMethod.invoke(wmCustomerOwnerApplyBusService, wmCustomerOwnerApply, applyStepList);
        // assert
        CustomerOwnerApplyStep resultStep = applyStepList.get(1);
        assertTrue(resultStep.isOnStep());
        assertEquals(Integer.valueOf(CustomerOwnerApplyStepStatusEnum.REJECT.getCode()), resultStep.getStatusCode());
        assertEquals("系统驳回", resultStep.getAuditTips());
        assertEquals("超时系统自动驳回", resultStep.getRemark());
        assertEquals("审批驳回", resultStep.getStepName());
        assertEquals(Integer.valueOf(1234567890), resultStep.getStepTime());
    }

    /**
     * Test manual reject by user scenario
     */
    @Test
    public void testProcessRejectStatus_ManualReject() throws Throwable {
        // arrange
        applyAudit.setAuditUid(100);
        when(wmCustomerOwnerApplyAuditDao.getByApplyIdIgnoreValid(1)).thenReturn(applyAudit);
        when(wmEmployeeService.getUserAndId(100)).thenReturn("TestUser(mis123)");
        // act
        processRejectStatusMethod.invoke(wmCustomerOwnerApplyBusService, wmCustomerOwnerApply, applyStepList);
        // assert
        CustomerOwnerApplyStep resultStep = applyStepList.get(1);
        assertTrue(resultStep.isOnStep());
        assertEquals(Integer.valueOf(CustomerOwnerApplyStepStatusEnum.REJECT.getCode()), resultStep.getStatusCode());
        assertEquals("TestUser(mis123)已驳回", resultStep.getAuditTips());
        assertEquals("Test Audit Result", resultStep.getRemark());
        assertEquals("审批驳回", resultStep.getStepName());
        assertEquals(Integer.valueOf(1234567890), resultStep.getStepTime());
    }

    /**
     * Test scenario when audit info not found
     */
    @Test
    public void testProcessRejectStatus_AuditInfoNotFound() throws Throwable {
        // arrange
        when(wmCustomerOwnerApplyAuditDao.getByApplyIdIgnoreValid(1)).thenReturn(null);
        try {
            // act
            processRejectStatusMethod.invoke(wmCustomerOwnerApplyBusService, wmCustomerOwnerApply, applyStepList);
            fail("Expected WmCustomerException to be thrown");
        } catch (Exception e) {
            // assert
            assertTrue(e.getCause() instanceof WmCustomerException);
            WmCustomerException customerException = (WmCustomerException) e.getCause();
            assertEquals(CustomerConstants.RESULT_CODE_ERROR, customerException.getCode());
            assertEquals("未查询到客户申请单审批信息", customerException.getMessage());
        }
    }

    /**
     * Test null input parameters scenario
     */
    @Test
    public void testProcessRejectStatus_NullInputs() throws Throwable {
        try {
            // act
            processRejectStatusMethod.invoke(wmCustomerOwnerApplyBusService, null, null);
            fail("Expected NullPointerException to be thrown");
        } catch (Exception e) {
            // assert
            assertTrue(e.getCause() instanceof NullPointerException);
        }
    }

    /**
     * Test empty step list scenario
     */
    @Test
    public void testProcessRejectStatus_EmptyStepList() throws Throwable {
        // arrange
        applyStepList = new ArrayList<>();
        applyAudit.setAuditUid(0);
        when(wmCustomerOwnerApplyAuditDao.getByApplyIdIgnoreValid(1)).thenReturn(applyAudit);
        try {
            // act
            processRejectStatusMethod.invoke(wmCustomerOwnerApplyBusService, wmCustomerOwnerApply, applyStepList);
            fail("Expected IndexOutOfBoundsException to be thrown");
        } catch (Exception e) {
            // assert
            assertTrue(e.getCause() instanceof IndexOutOfBoundsException);
        }
    }
}

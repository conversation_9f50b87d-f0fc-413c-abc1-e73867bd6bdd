package com.sankuai.meituan.waimai.customer.service.sign.compare.phf;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.sankuai.meituan.waimai.customer.adapter.EcontractManagerServiceAdapter;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.EcontractBatchBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageBatchInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageBatchInfoResponseDTO;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplySubTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.PhfTransferContext;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class WmEcontractPhfVirtualCompareServiceTest {

    @InjectMocks
    private WmEcontractPhfVirtualCompareService wmEcontractPhfVirtualCompareService;

    private static final String PHF_TOKEN = "test-token";

    private static final String ECONTRACT_VIEW_CONTENT = "econtract_view_content";

    @Mock
    private EcontractManagerServiceAdapter econtractManagerServiceAdapter;

    private MockedStatic<MccConfig> mockedMccConfig;

    @Before
    public void setUp() {
        mockedMccConfig = mockStatic(MccConfig.class);
        mockedMccConfig.when(MccConfig::getPhfEcontractToken).thenReturn(PHF_TOKEN);
    }

    @org.junit.After
    public void tearDown() {
        if (mockedMccConfig != null) {
            mockedMccConfig.close();
        }
    }

    /**
     * Create a sample EcontractBatchBo with stage info
     */
    private EcontractBatchBo createSampleEcontractBatchBo() {
        EcontractBatchBo batchBo = new EcontractBatchBo();
        List<StageBatchInfoBo> stageInfoBoList = new ArrayList<>();
        // Add normal stage
        StageBatchInfoBo normalStage = new StageBatchInfoBo();
        normalStage.setStageName("testStage");
        stageInfoBoList.add(normalStage);
        // Add view content stage
        StageBatchInfoBo viewContentStage = new StageBatchInfoBo();
        viewContentStage.setStageName(ECONTRACT_VIEW_CONTENT);
        Map<String, String> viewContentMap = new HashMap<>();
        viewContentMap.put("testKey", "testValue");
        viewContentStage.setViewContentMap(viewContentMap);
        stageInfoBoList.add(viewContentStage);
        batchBo.setStageInfoBoList(stageInfoBoList);
        return batchBo;
    }

    /**
     * Create sample queried stage list
     */
    private StageBatchInfoResponseDTO createQueriedStage() {
        StageBatchInfoResponseDTO responseDTO = new StageBatchInfoResponseDTO();

        List<StageBatchInfoBo> queriedStageList = new ArrayList<>();
        // Add view content stage
        StageBatchInfoBo viewContentStage = new StageBatchInfoBo();
        viewContentStage.setStageName(ECONTRACT_VIEW_CONTENT);
        Map<String, String> viewContentMap = new HashMap<>();
        viewContentMap.put("testKey", "testValue");
        viewContentStage.setViewContentMap(viewContentMap);
        queriedStageList.add(viewContentStage);
        // Add normal stage
        StageBatchInfoBo normalStage = new StageBatchInfoBo();
        normalStage.setStageName("testStage");
        queriedStageList.add(normalStage);

        responseDTO.setStageBatchInfoBoList(queriedStageList);

        return responseDTO;
    }

    /**
     * 测试 getPdfContentInfoBoMapKeyByWmPoiId 方法
     */
    @Test
    public void testGetPdfContentInfoBoMapKeyByWmPoiId() throws Throwable {
        // arrange
        Long wmPoiId = 123L;
        // act
        String result = wmEcontractPhfVirtualCompareService.getPdfContentInfoBoMapKeyByWmPoiId(wmPoiId);
        // assert
        assertEquals(EcontractTaskApplySubTypeEnum.PHF_VIRTUAL.name() + "_" + wmPoiId, result);
    }

    /**
     * Test case for normal comparison with valid phfTransferContextList
     */
    @Test
    public void testCompareWithValidPhfTransferContextList() throws Throwable {
        // arrange
        EcontractBatchBo currentData = createSampleEcontractBatchBo();
        EcontractBatchContextBo batchContextBo = new EcontractBatchContextBo();
        List<PhfTransferContext> phfTransferContextList = new ArrayList<>();
        PhfTransferContext context = new PhfTransferContext("phfContractId", "recordKey", 123L);
        phfTransferContextList.add(context);
        batchContextBo.setPhfTransferContextList(phfTransferContextList);
        when(econtractManagerServiceAdapter.queryStageBatchInfo(anyString(), anyString())).thenReturn(createQueriedStage());
        // act
        wmEcontractPhfVirtualCompareService.compare(currentData, batchContextBo);
        // assert
        verify(econtractManagerServiceAdapter, times(1)).queryStageBatchInfo(eq(PHF_TOKEN), eq("recordKey"));
    }

    /**
     * Test case for comparison with null phfTransferContextList
     */
    @Test
    public void testCompareWithNullPhfTransferContextList() throws Throwable {
        // arrange
        EcontractBatchBo currentData = createSampleEcontractBatchBo();
        EcontractBatchContextBo batchContextBo = new EcontractBatchContextBo();
        batchContextBo.setPhfTransferContextList(null);
        // act
        wmEcontractPhfVirtualCompareService.compare(currentData, batchContextBo);
        // assert
        verify(econtractManagerServiceAdapter, never()).queryStageBatchInfo(any(), any());
    }

    /**
     * Test case for comparison with empty phfTransferContextList
     */
    @Test
    public void testCompareWithEmptyPhfTransferContextList() throws Throwable {
        // arrange
        EcontractBatchBo currentData = createSampleEcontractBatchBo();
        EcontractBatchContextBo batchContextBo = new EcontractBatchContextBo();
        batchContextBo.setPhfTransferContextList(new ArrayList<>());
        // act
        wmEcontractPhfVirtualCompareService.compare(currentData, batchContextBo);
        // assert
        verify(econtractManagerServiceAdapter, never()).queryStageBatchInfo(any(), any());
    }

    /**
     * Test case for comparison when queryStageBatchInfoBoList returns empty list
     */
    @Test(expected = WmCustomerException.class)
    public void testCompareWhenQueryThrowsException() throws Throwable {
        // arrange
        EcontractBatchBo currentData = createSampleEcontractBatchBo();
        EcontractBatchContextBo batchContextBo = new EcontractBatchContextBo();
        List<PhfTransferContext> phfTransferContextList = new ArrayList<>();
        PhfTransferContext context = new PhfTransferContext("phfContractId", "recordKey", 123L);
        phfTransferContextList.add(context);
        batchContextBo.setPhfTransferContextList(phfTransferContextList);

        StageBatchInfoResponseDTO responseDTO = new StageBatchInfoResponseDTO();
        responseDTO.setStageBatchInfoBoList(new ArrayList<>());

        when(econtractManagerServiceAdapter.queryStageBatchInfo(anyString(), anyString())).thenReturn(responseDTO);
        // act
        wmEcontractPhfVirtualCompareService.compare(currentData, batchContextBo);
    }

    /**
     * Test case for comparison with null input parameters
     */
    @Test(expected = NullPointerException.class)
    public void testCompareWithNullParameters() throws Throwable {
        // act
        wmEcontractPhfVirtualCompareService.compare(null, null);
    }
}
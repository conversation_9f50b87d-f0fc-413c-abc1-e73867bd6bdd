package com.sankuai.meituan.waimai.customer.service.sign.compare;

import static org.junit.Assert.*;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.CAType;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.CertifyInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.IdentityType;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageBatchInfoBo;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class CAStageInfoBoComparatorCompareTest {

    @InjectMocks
    private CAStageInfoBoComparator comparator;

    private StageBatchInfoBo source;

    private StageBatchInfoBo target;

    private CertifyInfoBo sourceCertifyInfoBo;

    private CertifyInfoBo targetCertifyInfoBo;

    @Before
    public void setUp() {
        // Initialize objects
        source = new StageBatchInfoBo();
        target = new StageBatchInfoBo();
        sourceCertifyInfoBo = new CertifyInfoBo();
        targetCertifyInfoBo = new CertifyInfoBo();
        // Set default values
        source.setStageName("ca_certify");
        target.setStageName("ca_certify");
        source.setCertifyInfoBo(sourceCertifyInfoBo);
        target.setCertifyInfoBo(targetCertifyInfoBo);
        // Set default CertifyInfoBo values
        sourceCertifyInfoBo.setCustomerName("defaultName");
        targetCertifyInfoBo.setCustomerName("defaultName");
        sourceCertifyInfoBo.setMobile("defaultMobile");
        targetCertifyInfoBo.setMobile("defaultMobile");
        sourceCertifyInfoBo.setEmail("<EMAIL>");
        targetCertifyInfoBo.setEmail("<EMAIL>");
        sourceCertifyInfoBo.setCaType(CAType.PERSON);
        targetCertifyInfoBo.setCaType(CAType.PERSON);
        sourceCertifyInfoBo.setQuaNum("defaultQuaNum");
        targetCertifyInfoBo.setQuaNum("defaultQuaNum");
        sourceCertifyInfoBo.setIdentityType(IdentityType.ID_CARD);
        targetCertifyInfoBo.setIdentityType(IdentityType.ID_CARD);
    }

    /**
     * 测试 targetStageName 不以 "ca_certify" 开头的情况
     */
    @Test
    public void testCompareTargetStageNameNotStartWithCaCertify() throws Throwable {
        // Arrange
        source.setStageName("any_stage");
        target.setStageName("other_stage");
        // Act
        String result = comparator.compare(source, target);
        // Assert
        assertEquals("", result);
    }

    /**
     * 测试 targetStageName 以 "ca_certify" 开头，但 sourceStageName 和 targetStageName 不相等的情况
     */
    @Test
    public void testCompareSourceAndTargetStageNameNotEqual() throws Throwable {
        // Arrange
        source.setStageName("ca_certify_1");
        target.setStageName("ca_certify_2");
        // Act
        String result = comparator.compare(source, target);
        // Assert
        assertTrue(result.contains("stageName不一致"));
        assertTrue(result.contains("ca_certify_1"));
        assertTrue(result.contains("ca_certify_2"));
    }

    /**
     * 测试 targetStageName 以 "ca_certify" 开头，sourceStageName 和 targetStageName 相等，但 sourceCertifyInfoBo 和 targetCertifyInfoBo 的某些字段不相等的情况
     */
    @Test
    public void testCompareSourceAndTargetStageNameEqualAndCertifyInfoBoFieldsNotEqual() throws Throwable {
        // Arrange
        sourceCertifyInfoBo.setCustomerName("sourceCustomerName");
        targetCertifyInfoBo.setCustomerName("targetCustomerName");
        // Act
        String result = comparator.compare(source, target);
        // Assert
        assertTrue(result.contains("客户名称不一致"));
        assertTrue(result.contains("sourceCustomerName"));
        assertTrue(result.contains("targetCustomerName"));
    }

    /**
     * 测试 targetStageName 以 "ca_certify" 开头，sourceStageName 和 targetStageName 相等，且所有字段都相等的情况
     */
    @Test
    public void testCompareSourceAndTargetStageNameEqualAndCertifyInfoBoFieldsEqual() throws Throwable {
        // Act
        String result = comparator.compare(source, target);
        // Assert
        assertEquals("", result);
    }

    /**
     * 测试 targetStageName 为 null 的情况
     */
    @Test
    public void testCompareTargetStageNameNull() throws Throwable {
        // Arrange
        target.setStageName("not_ca_certify");
        source.setStageName("any_stage");
        // Act
        String result = comparator.compare(source, target);
        // Assert
        assertEquals("", result);
    }

    /**
     * 测试 sourceCertifyInfoBo 为 null 的情况
     */
    @Test
    public void testCompareSourceCertifyInfoBoNull() throws Throwable {
        // Arrange
        // Avoid comparison logic
        target.setStageName("not_ca_certify");
        source.setStageName("any_stage");
        source.setCertifyInfoBo(null);
        // Act
        String result = comparator.compare(source, target);
        // Assert
        assertEquals("", result);
    }

    /**
     * 测试 targetCertifyInfoBo 为 null 的情况
     */
    @Test
    public void testCompareTargetCertifyInfoBoNull() throws Throwable {
        // Arrange
        // Avoid comparison logic
        target.setStageName("not_ca_certify");
        source.setStageName("any_stage");
        target.setCertifyInfoBo(null);
        // Act
        String result = comparator.compare(source, target);
        // Assert
        assertEquals("", result);
    }
}
package com.sankuai.meituan.waimai.customer.service.kp.operatorv2.dboperatorv2;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.service.gray.WmCustomerGrayService;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpRealAuthService;
import com.sankuai.meituan.waimai.customer.service.kp.dboperator.KpDBOperateImpl;
import com.sankuai.meituan.waimai.thrift.customer.constant.CertTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.KpSignerTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.PreAuthResultTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.PreAuthResultBO;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import java.lang.reflect.Method;
import org.apache.thrift.TException;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class BusinessSignerDBOperatorTest {

    @InjectMocks
    private BusinessSignerDBOperator businessSignerDBOperator;

    @Mock
    private KpDBOperateImpl kpDBOperate;

    @Mock
    private WmCustomerKpRealAuthService wmCustomerKpRealAuthService;

    @Mock
    private WmCustomerGrayService wmCustomerGrayService;

    private Method preRealNameOperateForUpdateMethod;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
        preRealNameOperateForUpdateMethod = BusinessSignerDBOperator.class.getDeclaredMethod("preRealNameOperateForUpdate", WmCustomerKp.class, WmCustomerDB.class, int.class, String.class, boolean.class, boolean.class);
        preRealNameOperateForUpdateMethod.setAccessible(true);
    }

    /**
     * 测试预认证成功且代理人证件编号修改需要提审的场景
     */
    @Test
    public void testPreRealNameOperateForUpdate_PreAuthSuccess_AgentCertNumberModify() throws Throwable {
        // arrange
        WmCustomerKp wmCustomerKp = new WmCustomerKp();
        wmCustomerKp.setSignerType(KpSignerTypeEnum.AGENT.getType());
        WmCustomerDB wmCustomerDB = new WmCustomerDB();
        int uid = 1;
        String uname = "testUser";
        boolean sendEffectiveMq = false;
        boolean isNotCertNumberModify = false;
        PreAuthResultBO result = new PreAuthResultBO();
        result.setResult(PreAuthResultTypeEnum.SUCCESS.getType());
        when(kpDBOperate.preAuthAndErrorMsgNew(wmCustomerKp, uid, uname)).thenReturn(result);
        // act
        boolean resultMq = (boolean) preRealNameOperateForUpdateMethod.invoke(businessSignerDBOperator, wmCustomerKp, wmCustomerDB, uid, uname, sendEffectiveMq, isNotCertNumberModify);
        // assert
        verify(kpDBOperate).commitAgentAudit(wmCustomerKp, uid, uname);
        assertFalse(resultMq);
    }

    /**
     * 测试预认证成功且代理人证件编号修改不需要提审的场景
     */
    @Test
    public void testPreRealNameOperateForUpdate_PreAuthSuccess_AgentCertNumberNotModify() throws Throwable {
        // arrange
        WmCustomerKp wmCustomerKp = new WmCustomerKp();
        wmCustomerKp.setSignerType(KpSignerTypeEnum.AGENT.getType());
        WmCustomerDB wmCustomerDB = new WmCustomerDB();
        int uid = 1;
        String uname = "testUser";
        boolean sendEffectiveMq = false;
        boolean isNotCertNumberModify = true;
        PreAuthResultBO result = new PreAuthResultBO();
        result.setResult(PreAuthResultTypeEnum.SUCCESS.getType());
        when(kpDBOperate.preAuthAndErrorMsgNew(wmCustomerKp, uid, uname)).thenReturn(result);
        // act
        boolean resultMq = (boolean) preRealNameOperateForUpdateMethod.invoke(businessSignerDBOperator, wmCustomerKp, wmCustomerDB, uid, uname, sendEffectiveMq, isNotCertNumberModify);
        // assert
        assertTrue(resultMq);
    }

    /**
     * 测试预认证成功且非代理人场景
     */
    @Test
    public void testPreRealNameOperateForUpdate_PreAuthSuccess_NotAgent() throws Throwable {
        // arrange
        WmCustomerKp wmCustomerKp = new WmCustomerKp();
        wmCustomerKp.setSignerType(KpSignerTypeEnum.LEGAL.getType());
        WmCustomerDB wmCustomerDB = new WmCustomerDB();
        int uid = 1;
        String uname = "testUser";
        boolean sendEffectiveMq = false;
        boolean isNotCertNumberModify = false;
        PreAuthResultBO result = new PreAuthResultBO();
        result.setResult(PreAuthResultTypeEnum.SUCCESS.getType());
        when(kpDBOperate.preAuthAndErrorMsgNew(wmCustomerKp, uid, uname)).thenReturn(result);
        // act
        boolean resultMq = (boolean) preRealNameOperateForUpdateMethod.invoke(businessSignerDBOperator, wmCustomerKp, wmCustomerDB, uid, uname, sendEffectiveMq, isNotCertNumberModify);
        // assert
        assertTrue(resultMq);
    }

    /**
     * 测试预认证失败且证件类型不是身份证且特批内容不为空的场景
     */
    @Test
    public void testPreRealNameOperateForUpdate_PreAuthFail_NotIdCard_SpecialAttachmentNotEmpty() throws Throwable {
        // arrange
        WmCustomerKp wmCustomerKp = new WmCustomerKp();
        wmCustomerKp.setSignerType(KpSignerTypeEnum.LEGAL.getType());
        wmCustomerKp.setCertType((CertTypeEnum.PASSPORT.getType()));
        wmCustomerKp.setSpecialAttachment("special attachment");
        wmCustomerKp.setCustomerId(123);
        WmCustomerDB wmCustomerDB = new WmCustomerDB();
        int uid = 1;
        String uname = "testUser";
        boolean sendEffectiveMq = false;
        boolean isNotCertNumberModify = false;
        PreAuthResultBO result = new PreAuthResultBO();
        result.setResult(PreAuthResultTypeEnum.FAIL.getType());
        result.setMsg("PreAuth failed");
        when(kpDBOperate.preAuthAndErrorMsgNew(wmCustomerKp, uid, uname)).thenReturn(result);
        doNothing().when(kpDBOperate).commitSpecialAudit(wmCustomerKp, uid, uname);
        // act
        boolean resultMq = (boolean) preRealNameOperateForUpdateMethod.invoke(businessSignerDBOperator, wmCustomerKp, wmCustomerDB, uid, uname, sendEffectiveMq, isNotCertNumberModify);
        // assert
        verify(kpDBOperate).commitSpecialAudit(wmCustomerKp, uid, uname);
        assertFalse(resultMq);
    }

    /**
     * 测试预认证失败且证件类型不是身份证且特批内容为空的场景
     */
    @Test
    public void testPreRealNameOperateForUpdate_PreAuthFail_NotIdCard_SpecialAttachmentEmpty() throws Throwable {
        // arrange
        WmCustomerKp wmCustomerKp = new WmCustomerKp();
        wmCustomerKp.setSignerType(KpSignerTypeEnum.LEGAL.getType());
        wmCustomerKp.setCertType((byte) 5);
        wmCustomerKp.setSpecialAttachment("");
        // Set state to PREAUTH_FAIL
        wmCustomerKp.setState((byte) 20);
        WmCustomerDB wmCustomerDB = new WmCustomerDB();
        int uid = 1;
        String uname = "testUser";
        boolean sendEffectiveMq = false;
        boolean isNotCertNumberModify = false;
        PreAuthResultBO result = new PreAuthResultBO();
        result.setResult(PreAuthResultTypeEnum.FAIL.getType());
        result.setMsg("PreAuth failed");
        when(kpDBOperate.preAuthAndErrorMsgNew(wmCustomerKp, uid, uname)).thenReturn(result);
        when(wmCustomerGrayService.checkHitAllowNoBankStatementKpOperate(anyLong())).thenReturn(false);
        // act
        boolean resultMq = (boolean) preRealNameOperateForUpdateMethod.invoke(businessSignerDBOperator, wmCustomerKp, wmCustomerDB, uid, uname, sendEffectiveMq, isNotCertNumberModify);
        // assert
        verify(wmCustomerGrayService).checkHitAllowNoBankStatementKpOperate(anyLong());
        assertFalse(resultMq);
    }

    /**
     * 测试预认证失败且证件类型是身份证的场景
     */
    @Test
    public void testPreRealNameOperateForUpdate_PreAuthFail_IdCard() throws Throwable {
        // arrange
        WmCustomerKp wmCustomerKp = new WmCustomerKp();
        wmCustomerKp.setSignerType(KpSignerTypeEnum.LEGAL.getType());
        wmCustomerKp.setCertType((byte) 1);
        // Set state to PREAUTH_FAIL
        wmCustomerKp.setState((byte) 20);
        WmCustomerDB wmCustomerDB = new WmCustomerDB();
        int uid = 1;
        String uname = "testUser";
        boolean sendEffectiveMq = false;
        boolean isNotCertNumberModify = false;
        PreAuthResultBO result = new PreAuthResultBO();
        result.setResult(PreAuthResultTypeEnum.FAIL.getType());
        result.setMsg("PreAuth failed");
        when(kpDBOperate.preAuthAndErrorMsgNew(wmCustomerKp, uid, uname)).thenReturn(result);
        // act
        boolean resultMq = (boolean) preRealNameOperateForUpdateMethod.invoke(businessSignerDBOperator, wmCustomerKp, wmCustomerDB, uid, uname, sendEffectiveMq, isNotCertNumberModify);
        // assert
        verify(kpDBOperate).preAuthAndErrorMsgNew(wmCustomerKp, uid, uname);
        assertFalse(resultMq);
    }
}

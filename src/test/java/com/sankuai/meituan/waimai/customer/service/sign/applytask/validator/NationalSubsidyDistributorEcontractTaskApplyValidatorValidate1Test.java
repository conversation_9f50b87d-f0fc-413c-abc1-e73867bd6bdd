package com.sankuai.meituan.waimai.customer.service.sign.applytask.validator;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDataPoiBizBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDataSourceBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskApplyBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class NationalSubsidyDistributorEcontractTaskApplyValidatorValidate1Test {

    @InjectMocks
    private NationalSubsidyDistributorEcontractTaskApplyValidator validator;

    private MockedStatic<MccConfig> mockedMccConfig;

    @Before
    public void setUp() {
        mockedMccConfig = Mockito.mockStatic(MccConfig.class);
        mockedMccConfig.when(MccConfig::getNationalSubsidyDistributorPoiLabelId).thenReturn(955);
    }

    @After
    public void tearDown() {
        mockedMccConfig.close();
    }

    /**
     * Test successful validation when all POIs have the required label
     */
    @Test
    public void testValidate_AllPoisHaveLabel() throws Throwable {
        // arrange
        NationalSubsidyDistributorEcontractTaskApplyValidator spyValidator = spy(validator);
        EcontractTaskApplyBo applyBo = new EcontractTaskApplyBo();
        EcontractDataSourceBo dataSourceBo = new EcontractDataSourceBo();
        EcontractDataPoiBizBo poiBizBo = new EcontractDataPoiBizBo();
        poiBizBo.setWmPoiId(1L);
        dataSourceBo.setWmPoiIdAndBizIdList(Collections.singletonList(poiBizBo));
        applyBo.setDataSourceBoList(Collections.singletonList(dataSourceBo));
        doReturn(Collections.emptyList()).when(spyValidator).getNoLabelIdWmPoiId(any(EcontractTaskApplyBo.class), anyInt());
        // act
        spyValidator.validate(applyBo);
        // assert
        verify(spyValidator).getNoLabelIdWmPoiId(eq(applyBo), eq(955));
    }

    /**
     * Test validation failure when some POIs don't have the required label
     */
    @Test
    public void testValidate_SomePoisMissingLabel() throws Throwable {
        // arrange
        NationalSubsidyDistributorEcontractTaskApplyValidator spyValidator = spy(validator);
        EcontractTaskApplyBo applyBo = new EcontractTaskApplyBo();
        EcontractDataSourceBo dataSourceBo = new EcontractDataSourceBo();
        EcontractDataPoiBizBo poiBizBo = new EcontractDataPoiBizBo();
        poiBizBo.setWmPoiId(1L);
        dataSourceBo.setWmPoiIdAndBizIdList(Collections.singletonList(poiBizBo));
        applyBo.setDataSourceBoList(Collections.singletonList(dataSourceBo));
        doReturn(Arrays.asList(1L)).when(spyValidator).getNoLabelIdWmPoiId(any(EcontractTaskApplyBo.class), anyInt());
        // act & assert
        try {
            spyValidator.validate(applyBo);
            fail("Expected WmCustomerException to be thrown");
        } catch (WmCustomerException e) {
            assertEquals("存在非经销商国补的门店", e.getMessage());
            verify(spyValidator).getNoLabelIdWmPoiId(eq(applyBo), eq(955));
        }
    }

    /**
     * Test validation with empty POI list
     */
    @Test
    public void testValidate_EmptyPoiList() throws Throwable {
        // arrange
        NationalSubsidyDistributorEcontractTaskApplyValidator spyValidator = spy(validator);
        EcontractTaskApplyBo applyBo = new EcontractTaskApplyBo();
        EcontractDataSourceBo dataSourceBo = new EcontractDataSourceBo();
        dataSourceBo.setWmPoiIdAndBizIdList(new ArrayList<>());
        applyBo.setDataSourceBoList(Collections.singletonList(dataSourceBo));
        doReturn(Collections.emptyList()).when(spyValidator).getNoLabelIdWmPoiId(any(EcontractTaskApplyBo.class), anyInt());
        // act
        spyValidator.validate(applyBo);
        // assert
        verify(spyValidator).getNoLabelIdWmPoiId(eq(applyBo), eq(955));
    }

    /**
     * Test validation with null input
     */
    @Test
    public void testValidate_NullInput() throws Throwable {
        // arrange
        NationalSubsidyDistributorEcontractTaskApplyValidator spyValidator = spy(validator);
        // act & assert
        try {
            spyValidator.validate(null);
            fail("Expected NullPointerException to be thrown");
        } catch (NullPointerException e) {
            // Expected exception
            verify(spyValidator, never()).getNoLabelIdWmPoiId(any(EcontractTaskApplyBo.class), anyInt());
        } catch (WmCustomerException e) {
            fail("Expected NullPointerException but got WmCustomerException");
        }
    }

    /**
     * Test validation with null data source list
     */
    @Test
    public void testValidate_NullDataSourceList() throws Throwable {
        // arrange
        NationalSubsidyDistributorEcontractTaskApplyValidator spyValidator = spy(validator);
        EcontractTaskApplyBo applyBo = new EcontractTaskApplyBo();
        applyBo.setDataSourceBoList(null);
        doReturn(Collections.emptyList()).when(spyValidator).getNoLabelIdWmPoiId(any(EcontractTaskApplyBo.class), anyInt());
        // act
        spyValidator.validate(applyBo);
        // assert
        verify(spyValidator).getNoLabelIdWmPoiId(eq(applyBo), eq(955));
    }
}

package com.sankuai.meituan.waimai.customer.service.sign.applytask.validator;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDataPoiBizBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDataSourceBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskApplyBo;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * Test cases for AbstractEcontractTaskApplyValidator.getAllWmPoiId method
 */
@RunWith(MockitoJUnitRunner.class)
public class AbstractEcontractTaskApplyValidatorGetAllWmPoiIdTest {

    private final AbstractEcontractTaskApplyValidator validator = new AbstractEcontractTaskApplyValidator() {

        @Override
        public void validate(EcontractTaskApplyBo applyBo) {
            // Not needed for testing getAllWmPoiId
        }

        @Override
        public EcontractTaskApplyTypeEnum getApplyType() {
            // Not needed for testing getAllWmPoiId
            return null;
        }
    };

    /**
     * Test normal case with multiple POI IDs
     */
    @Test
    public void testGetAllWmPoiIdWithMultiplePoiIds() throws Throwable {
        // arrange
        EcontractTaskApplyBo applyBo = new EcontractTaskApplyBo();
        List<EcontractDataSourceBo> dataSourceBoList = new ArrayList<>();
        EcontractDataSourceBo dataSourceBo1 = new EcontractDataSourceBo();
        List<EcontractDataPoiBizBo> poiBizBoList1 = new ArrayList<>();
        EcontractDataPoiBizBo poiBizBo1 = new EcontractDataPoiBizBo();
        poiBizBo1.setWmPoiId(1L);
        poiBizBoList1.add(poiBizBo1);
        dataSourceBo1.setWmPoiIdAndBizIdList(poiBizBoList1);
        EcontractDataSourceBo dataSourceBo2 = new EcontractDataSourceBo();
        List<EcontractDataPoiBizBo> poiBizBoList2 = new ArrayList<>();
        EcontractDataPoiBizBo poiBizBo2 = new EcontractDataPoiBizBo();
        poiBizBo2.setWmPoiId(2L);
        poiBizBoList2.add(poiBizBo2);
        dataSourceBo2.setWmPoiIdAndBizIdList(poiBizBoList2);
        dataSourceBoList.add(dataSourceBo1);
        dataSourceBoList.add(dataSourceBo2);
        applyBo.setDataSourceBoList(dataSourceBoList);
        // act
        List<Long> result = validator.getAllWmPoiId(applyBo);
        // assert
        assertEquals(2, result.size());
        assertTrue(result.containsAll(Arrays.asList(1L, 2L)));
    }

    /**
     * Test case with duplicate POI IDs that should be deduplicated
     */
    @Test
    public void testGetAllWmPoiIdWithDuplicatePoiIds() throws Throwable {
        // arrange
        EcontractTaskApplyBo applyBo = new EcontractTaskApplyBo();
        List<EcontractDataSourceBo> dataSourceBoList = new ArrayList<>();
        EcontractDataSourceBo dataSourceBo = new EcontractDataSourceBo();
        List<EcontractDataPoiBizBo> poiBizBoList = new ArrayList<>();
        EcontractDataPoiBizBo poiBizBo1 = new EcontractDataPoiBizBo();
        poiBizBo1.setWmPoiId(1L);
        EcontractDataPoiBizBo poiBizBo2 = new EcontractDataPoiBizBo();
        poiBizBo2.setWmPoiId(1L);
        poiBizBoList.add(poiBizBo1);
        poiBizBoList.add(poiBizBo2);
        dataSourceBo.setWmPoiIdAndBizIdList(poiBizBoList);
        dataSourceBoList.add(dataSourceBo);
        applyBo.setDataSourceBoList(dataSourceBoList);
        // act
        List<Long> result = validator.getAllWmPoiId(applyBo);
        // assert
        assertEquals(1, result.size());
        assertEquals(Long.valueOf(1L), result.get(0));
    }

    /**
     * Test case with empty data source list
     */
    @Test
    public void testGetAllWmPoiIdWithEmptyDataSourceList() throws Throwable {
        // arrange
        EcontractTaskApplyBo applyBo = new EcontractTaskApplyBo();
        applyBo.setDataSourceBoList(Collections.emptyList());
        // act
        List<Long> result = validator.getAllWmPoiId(applyBo);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test case with null data source list
     */
    @Test(expected = NullPointerException.class)
    public void testGetAllWmPoiIdWithNullDataSourceList() throws Throwable {
        // arrange
        EcontractTaskApplyBo applyBo = new EcontractTaskApplyBo();
        applyBo.setDataSourceBoList(null);
        // act
        validator.getAllWmPoiId(applyBo);
        // assert: expect NullPointerException
    }

    /**
     * Test case with empty POI list in data source
     */
    @Test
    public void testGetAllWmPoiIdWithEmptyPoiList() throws Throwable {
        // arrange
        EcontractTaskApplyBo applyBo = new EcontractTaskApplyBo();
        List<EcontractDataSourceBo> dataSourceBoList = new ArrayList<>();
        EcontractDataSourceBo dataSourceBo = new EcontractDataSourceBo();
        dataSourceBo.setWmPoiIdAndBizIdList(Collections.emptyList());
        dataSourceBoList.add(dataSourceBo);
        applyBo.setDataSourceBoList(dataSourceBoList);
        // act
        List<Long> result = validator.getAllWmPoiId(applyBo);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test case with null POI list in data source
     */
    @Test(expected = NullPointerException.class)
    public void testGetAllWmPoiIdWithNullPoiList() throws Throwable {
        // arrange
        EcontractTaskApplyBo applyBo = new EcontractTaskApplyBo();
        List<EcontractDataSourceBo> dataSourceBoList = new ArrayList<>();
        EcontractDataSourceBo dataSourceBo = new EcontractDataSourceBo();
        dataSourceBo.setWmPoiIdAndBizIdList(null);
        dataSourceBoList.add(dataSourceBo);
        applyBo.setDataSourceBoList(dataSourceBoList);
        // act
        validator.getAllWmPoiId(applyBo);
        // assert: expect NullPointerException
    }

    /**
     * Test case with null applyBo
     */
    @Test(expected = NullPointerException.class)
    public void testGetAllWmPoiIdWithNullApplyBo() throws Throwable {
        // arrange
        EcontractTaskApplyBo applyBo = null;
        // act
        validator.getAllWmPoiId(applyBo);
        // assert: expect NullPointerException
    }
}

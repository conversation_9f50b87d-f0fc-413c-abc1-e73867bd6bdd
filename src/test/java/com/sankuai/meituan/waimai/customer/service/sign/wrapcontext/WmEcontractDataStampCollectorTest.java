package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.bo.sign.EcontractBatchMiddleBo;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.ca.WmEcontractCAMTWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.ca.WmEcontractCAPoiWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.ca.WmEcontractCAQDBWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.stamp.WmEcontractStampMTWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.stamp.WmEcontractStampPoiWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.stamp.WmEcontractStampQDBWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateEnum;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.StampConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.EcontractBatchBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageBatchInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.util.ArrayList;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class WmEcontractDataStampCollectorTest {

    @InjectMocks
    private WmEcontractDataStampCollector collector;

    @Mock
    private WmEcontractCAMTWrapperService wmEcontractCAMTWrapperService;

    @Mock
    private WmEcontractStampMTWrapperService wmEcontractStampMTWrapperService;

    @Mock
    private WmEcontractCAQDBWrapperService wmEcontractCAQDBWrapperService;

    @Mock
    private WmEcontractStampQDBWrapperService wmEcontractStampQDBWrapperService;

    @Mock
    private WmEcontractCAPoiWrapperService wmEcontractCAPoiWrapperService;

    @Mock
    private WmEcontractStampPoiWrapperService wmEcontractStampPoiWrapperService;

    private EcontractBatchContextBo originContext;

    private EcontractBatchMiddleBo middleContext;

    private EcontractBatchBo targetContext;

    @Before
    public void setUp() {
        originContext = new EcontractBatchContextBo();
        middleContext = new EcontractBatchMiddleBo();
        targetContext = new EcontractBatchBo();
        targetContext.setStageInfoBoList(new ArrayList<>());
    }

    /**
     * Test case: Empty PDF enum list
     * Expected: Method returns early without processing
     */
    @Test
    public void testCollect_EmptyPdfEnumList() throws WmCustomerException {
        // arrange
        middleContext.setPdfEnumList(Lists.newArrayList());
        // act
        collector.collect(originContext, middleContext, targetContext);
        // assert
        verify(wmEcontractCAMTWrapperService, never()).wrap(any());
        verify(wmEcontractStampMTWrapperService, never()).wrap(any(), any());
    }

    /**
     * Test case: PDF enum with empty stamp list
     * Expected: Method skips processing for that enum
     */
    @Test
    public void testCollect_EmptyStampList() throws WmCustomerException {
        // arrange
        SignTemplateEnum template = mock(SignTemplateEnum.class);
        when(template.getStampList()).thenReturn(Lists.newArrayList());
        middleContext.setPdfEnumList(Lists.newArrayList(template));
        // act
        collector.collect(originContext, middleContext, targetContext);
        // assert
        verify(wmEcontractCAMTWrapperService, never()).wrap(any());
        verify(wmEcontractStampMTWrapperService, never()).wrap(any(), any());
    }

    /**
     * Test case: Process SANKUAI_STAMP type
     * Expected: Adds corresponding stage info to target context
     */
    @Test
    public void testCollect_SankuaiStamp() throws WmCustomerException {
        // arrange
        SignTemplateEnum template = mock(SignTemplateEnum.class);
        when(template.getStampList()).thenReturn(Lists.newArrayList(StampConstant.SANKUAI_STAMP));
        when(template.getTab()).thenReturn("tab1");
        middleContext.setPdfEnumList(Lists.newArrayList(template));
        StageBatchInfoBo caInfoBo = new StageBatchInfoBo();
        StageBatchInfoBo stampInfoBo = new StageBatchInfoBo();
        when(wmEcontractCAMTWrapperService.wrap(originContext)).thenReturn(caInfoBo);
        when(wmEcontractStampMTWrapperService.wrap(eq(originContext), any())).thenReturn(stampInfoBo);
        // act
        collector.collect(originContext, middleContext, targetContext);
        // assert
        verify(wmEcontractCAMTWrapperService).wrap(originContext);
        verify(wmEcontractStampMTWrapperService).wrap(eq(originContext), any());
        assert (targetContext.getStageInfoBoList().contains(caInfoBo));
        assert (targetContext.getStageInfoBoList().contains(stampInfoBo));
    }

    /**
     * Test case: Process QDB_STAMP type
     * Expected: Adds corresponding stage info to target context
     */
    @Test
    public void testCollect_QdbStamp() throws WmCustomerException {
        // arrange
        SignTemplateEnum template = mock(SignTemplateEnum.class);
        when(template.getStampList()).thenReturn(Lists.newArrayList(StampConstant.QDB_STAMP));
        when(template.getTab()).thenReturn("tab1");
        middleContext.setPdfEnumList(Lists.newArrayList(template));
        StageBatchInfoBo caInfoBo = new StageBatchInfoBo();
        StageBatchInfoBo stampInfoBo = new StageBatchInfoBo();
        when(wmEcontractCAQDBWrapperService.wrap(originContext)).thenReturn(caInfoBo);
        when(wmEcontractStampQDBWrapperService.wrap(eq(originContext), any())).thenReturn(stampInfoBo);
        // act
        collector.collect(originContext, middleContext, targetContext);
        // assert
        verify(wmEcontractCAQDBWrapperService).wrap(originContext);
        verify(wmEcontractStampQDBWrapperService).wrap(eq(originContext), any());
        assert (targetContext.getStageInfoBoList().contains(caInfoBo));
        assert (targetContext.getStageInfoBoList().contains(stampInfoBo));
    }

    /**
     * Test case: Process multiple stamp types
     * Expected: Adds all corresponding stage info to target context
     */
    @Test
    public void testCollect_MultipleStampTypes() throws WmCustomerException {
        // arrange
        SignTemplateEnum template1 = mock(SignTemplateEnum.class);
        when(template1.getStampList()).thenReturn(Lists.newArrayList(StampConstant.SANKUAI_STAMP));
        when(template1.getTab()).thenReturn("tab1");
        SignTemplateEnum template2 = mock(SignTemplateEnum.class);
        when(template2.getStampList()).thenReturn(Lists.newArrayList(StampConstant.QDB_STAMP));
        when(template2.getTab()).thenReturn("tab2");
        middleContext.setPdfEnumList(Lists.newArrayList(template1, template2));
        StageBatchInfoBo sankuaiCaInfo = new StageBatchInfoBo();
        StageBatchInfoBo sankuaiStampInfo = new StageBatchInfoBo();
        StageBatchInfoBo qdbCaInfo = new StageBatchInfoBo();
        StageBatchInfoBo qdbStampInfo = new StageBatchInfoBo();
        when(wmEcontractCAMTWrapperService.wrap(originContext)).thenReturn(sankuaiCaInfo);
        when(wmEcontractStampMTWrapperService.wrap(eq(originContext), any())).thenReturn(sankuaiStampInfo);
        when(wmEcontractCAQDBWrapperService.wrap(originContext)).thenReturn(qdbCaInfo);
        when(wmEcontractStampQDBWrapperService.wrap(eq(originContext), any())).thenReturn(qdbStampInfo);
        // act
        collector.collect(originContext, middleContext, targetContext);
        // assert
        verify(wmEcontractCAMTWrapperService).wrap(originContext);
        verify(wmEcontractStampMTWrapperService).wrap(eq(originContext), any());
        verify(wmEcontractCAQDBWrapperService).wrap(originContext);
        verify(wmEcontractStampQDBWrapperService).wrap(eq(originContext), any());
        List<StageBatchInfoBo> stageList = targetContext.getStageInfoBoList();
        assert (stageList.contains(sankuaiCaInfo));
        assert (stageList.contains(sankuaiStampInfo));
        assert (stageList.contains(qdbCaInfo));
        assert (stageList.contains(qdbStampInfo));
    }

    /**
     * Test case: Exception thrown by wrapper service
     * Expected: Exception is propagated up
     */
    @Test(expected = WmCustomerException.class)
    public void testCollect_WrapperServiceException() throws WmCustomerException {
        // arrange
        SignTemplateEnum template = mock(SignTemplateEnum.class);
        when(template.getStampList()).thenReturn(Lists.newArrayList(StampConstant.POI_STAMP));
        when(template.getTab()).thenReturn("tab1");
        middleContext.setPdfEnumList(Lists.newArrayList(template));
        when(wmEcontractCAPoiWrapperService.wrap(originContext)).thenThrow(new WmCustomerException());
        // act
        collector.collect(originContext, middleContext, targetContext);
    }
}

/*
package com.sankuai.meituan.waimai.dao;

import com.google.common.collect.Lists;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.BaseSpringJunit;
import com.sankuai.meituan.waimai.customer.dao.WmEcontractTaskDBMapper;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignTaskDB;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskStateEnum;
import com.sankuai.meituan.waimai.thrift.util.ObjectUtil;

import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

import javax.annotation.Resource;

public class TestWmEcontractTaskDBMapper extends BaseSpringJunit {

    private static final Logger LOGGER = LoggerFactory.getLogger(TestWmEcontractTaskDBMapper.class);

    @Resource
    private WmEcontractTaskDBMapper wmEcontractTaskDBMapper;

    @Test
    public void testInsert() throws Exception {
        WmEcontractSignTaskDB taskDB = new WmEcontractSignTaskDB();
        taskDB.setApplyType(EcontractTaskApplyTypeEnum.C1CONTRACT.getName());
        taskDB.setCustomerId(1);
        taskDB.setApplyContext("自动化测试");
        taskDB.setApplyState(EcontractTaskStateEnum.FAIL.getName());
        taskDB.setBizId(1);
        taskDB.setBatchId(1L);
        taskDB.setRecordId("test");
        taskDB.setResultContext("自动化测试");
        ObjectUtil.defaultValue(taskDB);
        wmEcontractTaskDBMapper.insert(taskDB);
    }

    @Test
    public void testBatchUpdateState() throws Exception {
        List<Long> taskId = Lists.newArrayList();
        taskId.add(1L);

        String state = EcontractTaskStateEnum.SUCCESS.getName();
        wmEcontractTaskDBMapper.batchUpdateState(taskId, state);
    }

    @Test
    public void testGetById() throws Exception {
        WmEcontractSignTaskDB taskDB = wmEcontractTaskDBMapper.getById(1L);
        LOGGER.info("==========================================");
        LOGGER.info("taskDB:" + JSON.toJSONString(taskDB));
        LOGGER.info("==========================================");
    }

    @Test
    public void testGetByCustomerIdAndState() throws Exception{
        List<WmEcontractSignTaskDB> taskDBList = wmEcontractTaskDBMapper.getByCustomerIdAndState(1, Lists.newArrayList(EcontractTaskStateEnum.SUCCESS.getName(),
                                                                                      EcontractTaskStateEnum.FAIL.getName()));
        LOGGER.info("==========================================");
        LOGGER.info("taskDBList:" + JSON.toJSONString(taskDBList));
        LOGGER.info("==========================================");
    }

}
*/

// package com.sankuai.waimai.web.controller;
//
// import org.junit.Test;
// import org.springframework.mock.web.MockHttpServletResponse;
// import org.springframework.test.web.servlet.MvcResult;
// import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
// import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
// import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
//
// import com.sankuai.waimai.BaseSpringJunit;
//
// public class TestWmCustomerKpController extends BaseSpringJunit {
//
//     @Test
//     public void testGetCustomerKpList() throws Exception {
//         MvcResult result = mockMvc.perform(MockMvcRequestBuilders.get("/customer/v1/r/kp/{customerId}", 11)
//                 .header("Cookie", "wmempid=2041419; degradeid=q3oWKpqy9m0g0b-01P2UTQS8D9XneL6Uz5Juz6NoU4M=; ssoid=b5492022aa*04d32a57d69430575f778;")
//                 )
//                 .andDo(MockMvcResultHandlers.print())
//                 .andExpect(MockMvcResultMatchers.status().isOk())
//                 .andReturn();
//         MockHttpServletResponse response = result.getResponse();
//         System.out.println(response.getContentAsString());
//     }
//
// }

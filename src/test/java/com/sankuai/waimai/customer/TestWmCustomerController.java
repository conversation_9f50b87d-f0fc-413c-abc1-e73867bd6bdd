// package com.sankuai.waimai.customer;
//
// import com.sankuai.meituan.auth.util.UserUtils;
// import com.sankuai.meituan.auth.vo.User;
// import com.sankuai.meituan.org.opensdk.model.domain.EmpPos;
// import com.sankuai.meituan.waimai.service.EmpServiceAdaptor;
// import com.sankuai.meituan.waimai.service.WmCustomerCheck;
// import com.sankuai.meituan.waimai.web.controller.WmCustomerController;
// import com.sankuai.waimai.BaseSpringJunit;
// import org.junit.Test;
// import org.springframework.beans.factory.annotation.Autowired;
//
// import java.util.List;
//
// public class TestWmCustomerController extends BaseSpringJunit {
//
//     @Autowired
//     private WmCustomerController wmCustomerController;
//
//     @Autowired
//     private WmCustomerCheck wmCustomerCheck;
//
//     @Autowired
//     EmpServiceAdaptor empServiceAdaptor;
//
//     @Test
//     public void testCanEditCustomerQua() throws Exception {
//         User user = new User();
//         user.setId(2246505);
//         user.setName("朱家琨");
//         UserUtils.bind(user);
//         // Object canEditCustomerQua = wmCustomerCheck.checkCustomerQuaEditManager(user);
//         // System.out.println("##testCanEditCustomerQua = " + canEditCustomerQua);
//     }
//
//     @Test
//     public void testCanDeleteCustomer() throws Exception {
//         User user = new User();
//         user.setId(2246505);
//         user.setName("朱家琨");
//         UserUtils.bind(user);
//         Object canDeleteCustomer = wmCustomerCheck.checkCustomerDeleteManager(user);
//         System.out.println("##testCanDeleteCustomer = " + canDeleteCustomer);
//     }
//
//     @Test
//     public void testDeleteCustomer() throws Exception {
//         User user = new User();
//         user.setId(2246505);
//         user.setName("朱家琨");
//         UserUtils.bind(user);
//         Object deleteCustomer = wmCustomerController.deleteCustomer(10014540);
//         System.out.println("##testDeleteCustomer = " + deleteCustomer);
//     }
//
//     @Test
//     public void phone() throws Exception {
//         empServiceAdaptor.queryMisIdsByOrgId("877");
//         String phone = empServiceAdaptor.getPhone("huaixiaomei");
//         System.out.println(phone);
//     }
//
// }

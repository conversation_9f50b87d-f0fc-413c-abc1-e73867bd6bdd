// package com.sankuai.waimai.customer.contract;
//
// import com.alibaba.fastjson.JSON;
// import com.google.common.collect.Lists;
// import com.sankuai.meituan.auth.vo.User;
// import com.sankuai.meituan.util.DateUtil;
// import com.sankuai.meituan.waimai.service.contract.WmContractService;
// import com.sankuai.meituan.waimai.service.contract.wrapper.ContractWrapperBuilder;
// import com.sankuai.meituan.waimai.thrift.customer.constant.WmTempletContractTypeEnum;
// import com.sankuai.meituan.waimai.templetcontract.domain.WmTempletContractTypeBo;
// import com.sankuai.meituan.waimai.thrift.customer.domain.PageData;
// import com.sankuai.meituan.waimai.thrift.customer.domain.contract.*;
// import com.sankuai.meituan.waimai.thrift.customer.domain.sign.RetrySmsResponse;
// import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
// import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
// import com.sankuai.meituan.waimai.util.transform.WmContractTransUtils;
// import com.sankuai.meituan.waimai.util.transform.WmContractVersionTransUtil;
// import com.sankuai.meituan.waimai.vo.*;
// import com.sankuai.meituan.waimai.vo.base.MultiFileJsonVo;
// import com.sankuai.waimai.BaseSpringJunit;
// import org.apache.thrift.TException;
// import org.junit.Test;
// import org.springframework.beans.factory.annotation.Autowired;
//
// import java.util.List;
// import java.util.Map;
// import java.util.UUID;
//
// public class TestWmContractService extends BaseSpringJunit {
//
//     @Autowired
//     private WmContractService wmContractService;
//
//     @Test
//     public void testSaveC2PaperContractVo() throws Exception {
//         int cusId = 45;
//         WmCustomerContractBo contractBo = new WmCustomerContractBo();
//         WmTempletContractBasicBo basicBo = new WmTempletContractBasicBo();
//         contractBo.setBasicBo(basicBo);
//         basicBo.setContractNum(UUID.randomUUID().toString());
//         basicBo.setType(WmTempletContractTypeEnum.C2_PAPER.getCode());
//         basicBo.setParentId(cusId);
//         basicBo.setDueDate(DateUtil.day2Unixtime("2018-12-25"));
//
//         CustomerPaperContractRemarkBo remarkBo = new CustomerPaperContractRemarkBo();
//         remarkBo.setContractScan(new MultiFileJsonBo(Lists.newArrayList(new MultiFileJsonBo.CustomerFile(), new MultiFileJsonBo.CustomerFile())));
//
//         basicBo.setExtStr(JSON.toJSONString(remarkBo));
//
//         List<WmTempletContractSignBo> signBoList = Lists.newArrayList();
//         contractBo.setSignBoList(signBoList);
//         WmTempletContractSignBo partyA = new WmTempletContractSignBo();
//         partyA.setSignId(1);
//         partyA.setSignName("甲方—邱");
//         partyA.setSignPeople("甲方联系人-邱");
//         partyA.setSignPhone("13151541145");
//         partyA.setSignType("A");
//         partyA.setSignTime("2018-03-01");
//         WmTempletContractSignBo partyB = new WmTempletContractSignBo();
//         partyB.setSignId(0);
//         partyB.setSignName("北京三快在线科技有限公司");
//         partyB.setSignPeople("已方联系人-邱");
//         partyB.setSignPhone("13161511142");
//         partyB.setSignType("B");
//         partyB.setSignTime("2018-03-01");
//         signBoList.add(partyA);
//         signBoList.add(partyB);
//         User user = new User();
//         user.setId(2050838);
//         int save = wmContractService.save(contractBo, user);
//         Thread.sleep(10000);
//         System.out.println("===wmContractService.testSaveC2PaperContractVo  id===" + save);
//     }
//
//     @Test
//     public void testSaveC2EContractVo() throws Exception {
//         int cusId = 45;
//         WmCustomerContractBo contractBo = new WmCustomerContractBo();
//         WmTempletContractBasicBo basicBo = new WmTempletContractBasicBo();
//         contractBo.setBasicBo(basicBo);
//         basicBo.setContractNum(UUID.randomUUID().toString());
//         basicBo.setType(WmTempletContractTypeEnum.C2_E.getCode());
//         basicBo.setParentId(cusId);
//         basicBo.setDueDate(DateUtil.day2Unixtime("2018-12-25"));
//
//         CustomerPaperContractRemarkBo remarkBo = new CustomerPaperContractRemarkBo();
//         remarkBo.setContractScan(new MultiFileJsonBo(Lists.newArrayList(new MultiFileJsonBo.CustomerFile(), new MultiFileJsonBo.CustomerFile())));
//
//         basicBo.setExtStr(JSON.toJSONString(remarkBo));
//
//         List<WmTempletContractSignBo> signBoList = Lists.newArrayList();
//         contractBo.setSignBoList(signBoList);
//         WmTempletContractSignBo partyA = new WmTempletContractSignBo();
//         partyA.setSignId(1);
//         partyA.setSignName("甲方—邱");
//         partyA.setSignPeople("甲方联系人-邱");
//         partyA.setSignPhone("13151541145");
//         partyA.setSignType("A");
//         partyA.setSignTime("2018-03-01");
//         WmTempletContractSignBo partyB = new WmTempletContractSignBo();
//         partyB.setSignId(1044);
//         partyB.setSignName("北京三快在线科技有限公司");
//         partyB.setSignPeople("已方联系人-邱");
//         partyB.setSignPhone("13161511142");
//         partyB.setSignType("B");
//         partyB.setSignTime("2018-03-01");
//         signBoList.add(partyA);
//         signBoList.add(partyB);
//
//         contractBo.setIgnoreExistAnotherSignTypeContract(true);
//         User user = new User();
//         user.setId(2050838);
//         int save = wmContractService.save(contractBo, user);
//         System.out.println("===wmContractService.testSaveC2EContractVo  id===" + save);
//     }
//
//     @Test
//     public void testGetContractVo() throws Exception {
//         ContractVo contractVo = wmContractService.getContractVo(232, 2050838, "");
//         System.out.println("===wmContractService.getContractVo===" + JSON.toJSONString(contractVo));
//     }
//
//     @Test
//     public void testGetContractList() throws Exception {
//         ContractSearchPageVo searchPageVo = new ContractSearchPageVo();
//         searchPageVo.setCustomerId(45);
//         PageData<ContractVo> contractVoPage = wmContractService.getContractVoPage(searchPageVo, 2050838, "");
//         System.out.println("===wmContractService.getContractVoPage===" + JSON.toJSONString(contractVoPage));
//     }
//
//     @Test
//     public void testQueryAgent() throws Exception {
//         List<AgentVo> agentVos = wmContractService.queryAgentVoList("1044", 2050838);
//         System.out.println("===wmContractService.queryAgentVoList===" + JSON.toJSONString(agentVos));
//         agentVos = wmContractService.queryAgentVoList("代理商", 2050838);
//         System.out.println("===wmContractService.queryAgentVoList===" + JSON.toJSONString(agentVos));
//     }
//
//     @Test
//     public void testGetContractVersions() throws Exception {
//         ContractVersionPageData versions = wmContractService.getVersions(251, 1, 20, 2050838, "");
//         List<WmContractVersionVo> wmContractVersionVoList = WmContractVersionTransUtil
//                 .transwmContractVersionBo2VoList(versions.getList());
//         System.out.println("===wmContractService.getContractVo===" + JSON.toJSONString(wmContractVersionVoList));
//     }
//
//     @Test
//     public void testGetSimpleInfoByCustomer() throws Exception {
//         ContractVo contractVo = new ContractVo();
//         contractVo.setPartyA(new ContractSignerVo());
//         contractVo.setPartyB(new ContractSignerVo());
//         contractVo.getPartyA().setSignId(45);
//         contractVo.getPartyB().setSignId(0);
//         contractVo.setCooperateMode(2);
//         contractVo.setSignType(WmTempletContractTypeBo.SIGNTYPE_E);//需要走电子合同的wrap逻辑获取回填数据
//         ContractWrapperBuilder.builder()
//                 .setUser(new User()).setContractVo(contractVo)
//                 .build()
//                 .wrap();
//         System.out.println("===testGetSimpleInfoByCustomer===" + JSON.toJSONString(contractVo));
//     }
//
//     @Test
//     public void testCanAddPaperContract() throws Exception {
//         Boolean aBoolean = wmContractService.canAddPaperContract(44, 2050838, "");
//         System.out.println("===testCanAddPaperContract===" + JSON.toJSONString(aBoolean));
//     }
//
//     @Test
//     public void testGetLastEffectiveFailReason() throws Exception {
//         Map<String, Object> effectiveFail = wmContractService
//                 .getReasonForEffectiveFail(45, 265, 2050838, "");
//         System.out.println("===testGetLastEffectiveFailReason===" + JSON.toJSONString(effectiveFail));
//     }
//
//     @Test
//     public void saveSubContract() throws Exception {
//         int cusId = 10014515;
//         User user = new User();
//         user.setId(2050838);
//         ContractVo contractVo = new ContractVo();
//         contractVo.setCustomerId(cusId);
//         contractVo.setContractScan(new MultiFileJsonVo(Lists.newArrayList(new MultiFileJsonVo.CustomerFile("优惠申请书1", "/download/mos/sdfasdfsdfsadfadfasd.pdf"),
//                 new MultiFileJsonVo.CustomerFile("优惠申请书2", "/download/mos/sdfasdfsdfsadfadfas2.pdf"))));
//         contractVo.setSubsidiaryType(5);
//         ContractWrapperBuilder.builder()
//                 .setContractVo(contractVo).setUser(user)
//                 .build()
//                 .wrap();
//
//         int save = wmContractService.save(WmContractTransUtils
//                 .contractVoToContractBasic(contractVo), user);
//         System.out.println("===wmContractService.saveSubContract  id===" + save);
//     }
//
//     @Test
//     public void testGetContract() throws Exception {
//         int cusId = 10014515;
//         User user = new User();
//         user.setId(2050838);
//         ContractVo subsidiaryContract = wmContractService.getSubsidiaryContract(cusId, 5, false, user.getId(), user.getName());
//         System.out.println("===wmContractService.testGetContract isEffective=false id===" + JSON.toJSONString(subsidiaryContract));
//         subsidiaryContract = wmContractService.getSubsidiaryContract(cusId, 5, true, user.getId(), user.getName());
//         System.out.println("===wmContractService.testGetContract isEffective=true id===" + JSON.toJSONString(subsidiaryContract));
//     }
//     @Test
//     public void testResendMsg() throws Exception {
//         try {
//             RetrySmsResponse response = wmContractService.resendMsg(452, 2246505, "朱家琨");
//             System.out.println("##testResendMsg = " + JSON.toJSONString(response));
//         } catch (WmCustomerException e) {
//             System.out.println("##testResendMsg = " + JSON.toJSONString(e));
//         }
//     }
//
// }

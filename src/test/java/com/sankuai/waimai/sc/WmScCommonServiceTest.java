package com.sankuai.waimai.sc;

import com.sankuai.meituan.waimai.infra.domain.WmAgentAor;
import com.sankuai.meituan.waimai.infra.domain.WmAor;
import com.sankuai.meituan.waimai.infra.service.WmAgentAorService;
import com.sankuai.meituan.waimai.infra.service.WmAorService;
import com.sankuai.meituan.waimai.service.sc.WmScCommonService;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.SchoolAorTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.SchoolBo;
import com.sankuai.meituan.waimai.thrift.customer.service.sc.WmSchoolThriftService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.Mockito.*;

/**
 * @description:
 * @author: chenyihao04
 * @create: 2024-11-11 16:50
 */
@RunWith(MockitoJUnitRunner.class)
public class WmScCommonServiceTest {

    @InjectMocks
    private WmScCommonService wmScCommonService;

    @Mock
    private WmSchoolThriftService wmSchoolThriftService;

    @Mock
    private WmAorService.Iface wmAorThriftService;

    @Mock
    private WmAgentAorService.Iface wmAgentAorService;

    /**
     * 测试直营蜂窝存在的情况
     */
    @Test
    public void testCheckAorIdDirectAorFound() throws Throwable {
        // arrange
        int schoolPrimaryId = 1;
        SchoolBo schoolBo = new SchoolBo();
        schoolBo.setAorType((int)SchoolAorTypeEnum.ZHIYING.getType());
        schoolBo.setAorId(2);
        when(wmSchoolThriftService.getSchoolById(schoolPrimaryId)).thenReturn(schoolBo);
        WmAor wmAor = new WmAor();
        when(wmAorThriftService.getById(anyInt())).thenReturn(wmAor);

        // act
        wmScCommonService.checkAorId(schoolPrimaryId);

        // assert
        verify(wmAorThriftService, times(1)).getById(anyInt());
    }

    /**
     * 测试代理蜂窝不存在的情况
     */
    @Test
    public void testCheckAorIdAgentAorNotFound() throws Throwable {
        // arrange
        int schoolPrimaryId = 1;
        SchoolBo schoolBo = new SchoolBo();
        schoolBo.setAorType((int)SchoolAorTypeEnum.DAILI.getType());
        schoolBo.setAorId(2);
        when(wmSchoolThriftService.getSchoolById(schoolPrimaryId)).thenReturn(schoolBo);
        WmAgentAor wmAgentAor = new WmAgentAor();
        when(wmAgentAorService.getById(anyInt())).thenReturn(wmAgentAor);

        // act
        wmScCommonService.checkAorId(schoolPrimaryId);

        // assert
        verify(wmAgentAorService, times(1)).getById(anyInt());
    }



}
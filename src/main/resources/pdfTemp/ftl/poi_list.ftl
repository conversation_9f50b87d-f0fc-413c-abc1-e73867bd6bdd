<table cellspacing="0" cellpadding="0" style="border-collapse:collapse; margin-left:0pt; width:100%">
<tr style="height:14pt">
	<td colspan="15" style="border-bottom-color:#000000; border-bottom-style:solid; border-bottom-width:0.75pt; 
	border-left-color:#000000; border-left-style:solid; border-left-width:1pt; border-right-color:#000000; border-right-style:solid; 
	border-right-width:1pt; border-top-color:#000000; border-top-style:solid; border-top-width:1pt; padding-left:4.9pt; padding-right:4.9pt; vertical-align:middle">
		<p style="margin:0pt; text-align:center">
			<span style="font-family:宋体; font-size:10pt; font-weight:bold">附件：门店信息列表</span>
		</p>
	</td>
</tr>
<tr style="height:14pt">
	<td colspan="15" style="border-bottom-color:#000000; border-bottom-style:solid; border-bottom-width:0.75pt; border-left-color:#000000; 
	border-left-style:solid; border-left-width:1pt; border-right-color:#000000; border-right-style:solid; border-right-width:1pt; border-top-color:#000000; border-top-style:solid; border-top-width:0.75pt; padding-left:4.9pt; padding-right:4.9pt; vertical-align:middle">
		<p style="margin:0pt">
			<span style="font-family:宋体; font-size:10pt">提示：以下7条非常重要，请甲方详细阅读后填写！</span>
		</p>
	</td>
</tr>
<tr style="height:49pt">
	<td colspan="15" style="border-bottom-color:#000000; border-bottom-style:solid; border-bottom-width:0.75pt; 
border-left-color:#000000; border-left-style:solid; border-left-width:1pt; border-right-color:#000000; border-right-style:solid; 
border-right-width:1pt; border-top-color:#000000; border-top-style:solid; border-top-width:0.75pt; padding-left:4.9pt; padding-right:4.9pt; vertical-align:middle">
		<p style="margin:0pt">
			<span style="font-family:宋体; font-size:10pt">1、甲方在此确认，甲方受下表所列各门店（以下简称“门店”）授权，有权全权代理门店签署全部协议，包括但不限于《美团外卖服务合同》（编号为</span>
			<span style="font-family:宋体; font-size:10pt; text-decoration:underline">
				${contractNumber}
			</span>
			<span style="font-family:宋体; font-size:10pt">）、《配送补充协议》（编号为</span>
			<span style="font-family:宋体; font-size:10pt; text-decoration:underline">
				${(deliveryNumber?length gt 0)?string(deliveryNumber,'_______')}
			</span>
			<span style="font-family:宋体; font-size:10pt">）、《快送补充协议》（编号为</span>
			<span style="font-family:宋体; font-size:10pt; text-decoration:underline">
				${(quickDeliveryNumber?length gt 0)?string(quickDeliveryNumber,'_______')}
			</span>
			<span style="font-family:宋体; font-size:10pt">）、《补充协议之服务费》（编号为</span>
			<span style="font-family:宋体; font-size:10pt; text-decoration:underline">_______</span>
			<span style="font-family:宋体; font-size:10pt">）和其他（协议名称：</span>
			<span style="font-family:宋体; font-size:10pt; text-decoration:underline">_______</span>
			<span style="font-family:宋体; font-size:10pt">，编号：</span>
			<span style="font-family:宋体; font-size:10pt; text-decoration:underline">_______</span>
			<span style="font-family:宋体; font-size:10pt">）。甲方承诺，在甲方与北京三快在线科技有限公司签订的全部协议里，涉及门店信息、增值服务费费率及服务费如下表所列。甲方承诺门店已知悉并履行甲乙双方签订的全部协议的全部内容，如有违反，由甲方承担责任。</span>
		</p>
	</td>
</tr>
<tr style="height:14pt">
	<td colspan="15" style="border-bottom-color:#000000; border-bottom-style:solid; border-bottom-width:0.75pt; border-left-color:#000000; border-left-style:solid; border-left-width:1pt; border-right-color:#000000; border-right-style:solid; border-right-width:1pt; border-top-color:#000000; border-top-style:solid; border-top-width:0.75pt; padding-left:4.9pt; padding-right:4.9pt; vertical-align:middle">
		<p style="margin:0pt">
			<span style="font-family:宋体; font-size:10pt">2、本附件涉及的门店信息、增值服务费信息和服务费信息会根据实际情况予以调整，具体以甲方出具并盖章的《门店信息列表》为准。</span>
		</p>
	</td>
</tr>
<tr style="height:14pt">
	<td colspan="15" style="border-bottom-color:#000000; border-bottom-style:solid; border-bottom-width:0.75pt; border-left-color:#000000; border-left-style:solid; border-left-width:1pt; border-right-color:#000000; border-right-style:solid; border-right-width:1pt; border-top-color:#000000; border-top-style:solid; border-top-width:0.75pt; padding-left:4.9pt; padding-right:4.9pt; vertical-align:middle">
		<p style="margin:0pt">
			<span style="font-family:宋体; font-size:10pt">3、合作期内涉及的门店的信息，包括但不限于款项、服务费、增值服务费等，乙方将按照本合同的约定直接与门店核对，甲方对此确认。</span>
		</p>
	</td>
</tr>
<tr style="height:14pt">
	<td colspan="15" style="border-bottom-color:#000000; border-bottom-style:solid; border-bottom-width:0.75pt; border-left-color:#000000; border-left-style:solid; border-left-width:1pt; border-right-color:#000000; border-right-style:solid; border-right-width:1pt; border-top-color:#000000; border-top-style:solid; border-top-width:0.75pt; padding-left:4.9pt; padding-right:4.9pt; vertical-align:middle">
		<p style="margin:0pt">
			<span style="font-family:宋体; font-size:10pt">4、若表格中账户信息出现空白，乙方视为对应门店空白处应填未填账户信息与外卖服务协议中甲方账户信息一致；</span>
		</p>
	</td>
</tr>
<tr style="height:14pt">
	<td colspan="15" style="border-bottom-color:#000000; border-bottom-style:solid; border-bottom-width:0.75pt; border-left-color:#000000; border-left-style:solid; border-left-width:1pt; border-right-color:#000000; border-right-style:solid; border-right-width:1pt; border-top-color:#000000; border-top-style:solid; border-top-width:0.75pt; padding-left:4.9pt; padding-right:4.9pt; vertical-align:middle">
		<p style="margin:0pt">
			<span style="font-family:宋体; font-size:10pt">5、若双方签署《配送补充协议》，则增值服务费费率一栏必须填写。</span>
		</p>
	</td>
</tr>
<tr style="height:14pt">
	<td colspan="15" style="border-bottom-color:#000000; border-bottom-style:solid; border-bottom-width:0.75pt; border-left-color:#000000; border-left-style:solid; border-left-width:1pt; border-right-color:#000000; border-right-style:solid; border-right-width:1pt; border-top-color:#000000; border-top-style:solid; border-top-width:0.75pt; padding-left:4.9pt; padding-right:4.9pt; vertical-align:middle">
		<p style="margin:0pt"><span style="font-family:宋体; font-size:10pt">6、若双方签署《快送补充协议》，则增值服务费费率一栏必须填写。</span>
</p>
</td>
</tr>
<tr style="height:14pt"><td colspan="15" style="border-left-color:#000000; border-left-style:solid; border-left-width:1pt; border-right-color:#000000; border-right-style:solid; border-right-width:1pt; border-top-color:#000000; border-top-style:solid; border-top-width:0.75pt; padding-left:4.9pt; padding-right:4.9pt; vertical-align:middle"><p style="margin:0pt"><span style="font-family:宋体; font-size:10pt">7、若双方签署《补充协议之服务费》，则服务费一栏必须填写。</span>
</p>
</td>
</tr>
<tr style="height:14pt">
	<td style="border-bottom-color:#000000; border-bottom-style:solid; border-bottom-width:0.75pt; border-left-color:#000000; border-left-style:solid; border-left-width:1pt; border-right-color:#000000; border-right-style:solid; border-right-width:0.75pt; border-top-color:#000000; border-top-style:solid; border-top-width:1pt; padding-left:4.9pt; padding-right:5.03pt; vertical-align:middle; width:9.3%">
		<p style="margin:0pt; text-align:center">
			<span style="font-family:宋体; font-size:10pt; font-weight:bold">甲方（签章）</span>
		</p>
	</td>
	<td colspan="14" style="border-bottom-color:#000000; border-bottom-style:solid; border-bottom-width:0.75pt; border-right-color:#000000; border-right-style:solid; border-right-width:1pt; border-top-color:#000000; border-top-style:solid; border-top-width:1pt; padding-left:5.4pt; padding-right:4.9pt; vertical-align:middle">
		<p style="margin:0pt; text-align:center">
		<span style="font-family:宋体; font-size:10pt;color: #ffffff;">${partyAEstemp}</span>
		</p>
	</td>
</tr>
<tr style="height:14pt">
	<td style="border-left-color:#000000; border-left-style:solid; border-left-width:1pt; padding-left:4.9pt; padding-right:5.4pt; vertical-align:middle; width:9.3%">
		<p style="margin:0pt; text-align:center">
			<span style="font-family:宋体; font-size:10pt; font-weight:bold">签署日期</span>
		</p>
	</td>
	<td colspan="14" style="border-left-color:#000000; border-left-style:solid; border-left-width:0.75pt; border-right-color:#000000; border-right-style:solid; border-right-width:1pt; border-top-color:#000000; border-top-style:solid; border-top-width:0.75pt; padding-left:5.03pt; padding-right:4.9pt; vertical-align:middle">
		<p style="margin:0pt; text-align:center">
			<span style="font-family:宋体; font-size:10pt">${partyATime!''}</span>
		</p>
	</td>
</tr>
<tr style="height:56pt"><td colspan="10" style="border-left-color:#000000; border-left-style:solid; border-left-width:1pt; border-right-color:#000000; border-right-style:solid; border-right-width:0.75pt; border-top-color:#000000; border-top-style:solid; border-top-width:1pt; padding-left:4.9pt; padding-right:5.03pt; vertical-align:middle"><p style="margin:0pt; text-align:center"><span style="font-family:宋体; font-size:10pt">关于</span>
<span style="font-family:宋体; font-size:10pt; font-weight:bold">《美团外卖服务合同》</span>
<span style="font-family:宋体; font-size:10pt">的条款</span>
</p>
</td>
<td style="border-bottom-color:#000000; border-bottom-style:solid; border-bottom-width:0.75pt; border-left-color:#000000; border-left-style:solid; border-left-width:1pt; border-top-color:#000000; border-top-style:solid; border-top-width:1pt; padding-left:4.9pt; padding-right:5.4pt; vertical-align:middle; width:5.72%"><p style="margin:0pt; text-align:center"><span style="font-family:宋体; font-size:10pt">关于</span>
<span style="font-family:宋体; font-size:10pt; font-weight:bold">《补充协议之服务费》</span>
<span style="font-family:宋体; font-size:10pt">的条款</span>
</p>
</td>
<td colspan="2" style="border-bottom-color:#000000; border-bottom-style:solid; border-bottom-width:0.75pt; border-left-color:#000000; border-left-style:solid; border-left-width:1pt; border-right-color:#000000; border-right-style:solid; border-right-width:0.75pt; border-top-color:#000000; border-top-style:solid; border-top-width:1pt; padding-left:4.9pt; padding-right:5.03pt; vertical-align:middle"><p style="margin:0pt; text-align:center"><span style="font-family:宋体; font-size:10pt">关于</span>
<span style="font-family:宋体; font-size:10pt; font-weight:bold">《配送补充协议》</span>
<span style="font-family:宋体; font-size:10pt">的条款</span>
</p>
</td>
<td colspan="2" style="border-bottom-color:#000000; border-bottom-style:solid; border-bottom-width:0.75pt; border-left-color:#000000; border-left-style:solid; border-left-width:1pt; border-right-color:#000000; border-right-style:solid; border-right-width:1pt; border-top-color:#000000; border-top-style:solid; border-top-width:1pt; padding-left:4.9pt; padding-right:4.9pt; vertical-align:middle"><p style="margin:0pt; text-align:center"><span style="font-family:宋体; font-size:10pt">关于</span>
<span style="font-family:宋体; font-size:10pt; font-weight:bold">《快送补充协议》</span>
<span style="font-family:宋体; font-size:10pt">的条款</span>
</p>
</td>
</tr>
<tr style="height:14pt"><td colspan="2" style="border-bottom-color:#000000; border-bottom-style:solid; border-bottom-width:0.75pt; border-left-color:#000000; border-left-style:solid; border-left-width:1pt; border-right-color:#000000; border-right-style:solid; border-right-width:0.75pt; border-top-color:#000000; border-top-style:solid; border-top-width:1pt; padding-left:4.9pt; padding-right:5.03pt; vertical-align:middle"><p style="margin:0pt; text-align:center"><span style="font-family:宋体; font-size:10pt">分店信息（必填）</span>
</p>
</td>
<td colspan="8" style="border-bottom-color:#000000; border-bottom-style:solid; border-bottom-width:0.75pt; border-left-color:#000000; border-left-style:solid; border-left-width:1pt; border-top-color:#000000; border-top-style:solid; border-top-width:1pt; padding-left:4.9pt; padding-right:5.4pt; vertical-align:middle"><p style="margin:0pt; text-align:center"><span style="font-family:宋体; font-size:10pt">账户信息        </span>
</p>
</td>
<td rowspan="3" style="border-left-color:#000000; border-left-style:solid; border-left-width:1pt; padding-left:4.9pt; padding-right:5.4pt; vertical-align:middle; width:5.72%"><p style="margin:0pt; text-align:center"><span style="font-family:宋体; font-size:10pt">服务费</span>
</p>
</td>
<td rowspan="3" style="border-bottom-color:#000000; border-bottom-style:solid; border-bottom-width:0.75pt; border-left-color:#000000; border-left-style:solid; border-left-width:1pt; border-right-color:#000000; border-right-style:solid; border-right-width:0.75pt; padding-left:4.9pt; padding-right:5.03pt; vertical-align:middle; width:2.92%"><p style="margin:0pt; text-align:center"><span style="font-family:宋体; font-size:10pt">增值服务费费率</span>
</p>
</td>
<td rowspan="3" style="border-bottom-color:#000000; border-bottom-style:solid; border-bottom-width:0.75pt; border-left-color:#000000; border-left-style:solid; border-left-width:0.75pt; padding-left:5.03pt; padding-right:5.4pt; vertical-align:middle; width:2.92%"><p style="margin:0pt; text-align:center"><span style="font-family:宋体; font-size:10pt">每单最低增值服务费</span>
</p>
</td>
<td rowspan="3" style="border-bottom-color:#000000; border-bottom-style:solid; border-bottom-width:0.75pt; border-left-color:#000000; border-left-style:solid; border-left-width:1pt; border-right-color:#000000; border-right-style:solid; border-right-width:0.75pt; padding-left:4.9pt; padding-right:5.03pt; vertical-align:middle; width:2.92%"><p style="margin:0pt; text-align:center"><span style="font-family:宋体; font-size:10pt">增值服务费费率</span>
</p>
</td>
<td rowspan="3" style="border-bottom-color:#000000; border-bottom-style:solid; border-bottom-width:0.75pt; border-left-color:#000000; border-left-style:solid; border-left-width:0.75pt; border-right-color:#000000; border-right-style:solid; border-right-width:1pt; padding-left:5.03pt; padding-right:4.9pt; vertical-align:middle; width:2.92%"><p style="margin:0pt; text-align:center"><span style="font-family:宋体; font-size:10pt">每单最低增值服务费</span>
</p>
</td>
</tr>
<tr style="height:14pt"><td rowspan="2" style="border-bottom-color:#000000; border-bottom-style:solid; border-bottom-width:0.75pt; border-left-color:#000000; border-left-style:solid; border-left-width:1pt; border-right-color:#000000; border-right-style:solid; border-right-width:0.75pt; padding-left:4.9pt; padding-right:5.03pt; vertical-align:middle; width:9.3%"><p style="margin:0pt; text-align:center"><span style="font-family:宋体; font-size:10pt">门店名称</span>
</p>
</td>
<td rowspan="2" style="border-bottom-color:#000000; border-bottom-style:solid; border-bottom-width:0.75pt; border-left-color:#000000; border-left-style:solid; border-left-width:0.75pt; border-right-color:#000000; border-right-style:solid; border-right-width:0.75pt; padding-left:5.03pt; padding-right:5.03pt; vertical-align:middle; width:15.56%"><p style="margin:0pt; text-align:center"><span style="font-family:宋体; font-size:10pt">地址</span>
</p>
</td>
<td rowspan="2" style="border-bottom-color:#000000; border-bottom-style:solid; border-bottom-width:0.75pt; border-left-color:#000000; border-left-style:solid; border-left-width:1pt; border-right-color:#000000; border-right-style:solid; border-right-width:0.75pt; padding-left:4.9pt; padding-right:5.03pt; vertical-align:middle; width:8.32%"><p style="margin:0pt; text-align:center"><span style="font-family:宋体; font-size:10pt">开户名</span>
</p>
</td>
<td rowspan="2" style="border-bottom-color:#000000; border-bottom-style:solid; border-bottom-width:0.75pt; border-left-color:#000000; border-left-style:solid; border-left-width:0.75pt; border-right-color:#000000; border-right-style:solid; border-right-width:0.75pt; padding-left:5.03pt; padding-right:5.03pt; vertical-align:middle; width:14.82%"><p style="margin:0pt; text-align:center"><span style="font-family:宋体; font-size:10pt">账号</span>
</p>
</td>
<td colspan="4" style="border-bottom-color:#000000; border-bottom-style:solid; border-bottom-width:0.75pt; border-right-color:#000000; border-right-style:solid; border-right-width:0.75pt; border-top-color:#000000; border-top-style:solid; border-top-width:0.75pt; padding-left:5.4pt; padding-right:5.03pt; vertical-align:middle"><p style="margin:0pt; text-align:center"><span style="font-family:宋体; font-size:10pt">开户行</span>
</p>
</td>
<td rowspan="2" style="border-bottom-color:#000000; border-bottom-style:solid; border-bottom-width:0.75pt; border-left-color:#000000; border-left-style:solid; border-left-width:0.75pt; border-right-color:#000000; border-right-style:solid; border-right-width:0.75pt; padding-left:5.03pt; padding-right:5.03pt; vertical-align:middle; width:5.84%"><p style="margin:0pt; text-align:center"><span style="font-family:宋体; font-size:10pt">财务联系人</span>
</p>
</td>
<td rowspan="2" style="border-bottom-color:#000000; border-bottom-style:solid; border-bottom-width:0.75pt; border-left-color:#000000; border-left-style:solid; border-left-width:0.75pt; padding-left:5.03pt; padding-right:5.4pt; vertical-align:middle; width:7.58%"><p style="margin:0pt; text-align:center"><span style="font-family:宋体; font-size:10pt">财务联系电话</span>
</p>
</td>
</tr>
<tr style="height:38pt"><td style="border-right-color:#000000; border-right-style:solid; border-right-width:0.75pt; padding-left:5.4pt; padding-right:5.03pt; vertical-align:middle; width:4.86%"><p style="margin:0pt; text-align:center"><span style="font-family:宋体; font-size:10pt">省</span>
</p>
</td>
<td style="border-right-color:#000000; border-right-style:solid; border-right-width:0.75pt; padding-left:5.4pt; padding-right:5.03pt; vertical-align:middle; width:4.64%"><p style="margin:0pt; text-align:center"><span style="font-family:宋体; font-size:10pt">市</span>
</p>
</td>
<td style="border-right-color:#000000; border-right-style:solid; border-right-width:0.75pt; padding-left:5.4pt; padding-right:5.03pt; vertical-align:middle; width:5.84%"><p style="margin:0pt; text-align:center"><span style="font-family:宋体; font-size:10pt">银行</span>
</p>
</td>
<td style="border-right-color:#000000; border-right-style:solid; border-right-width:0.75pt; padding-left:5.4pt; padding-right:5.03pt; vertical-align:middle; width:5.84%"><p style="margin:0pt; text-align:center"><span style="font-family:宋体; font-size:10pt">支行</span>
</p>
</td>
</tr>
<#assign row=0>
  <#list wmPoiRelVoList as item>
<tr style="height:14pt">
	<td style="border-bottom-color:#000000; border-bottom-style:solid; border-bottom-width:0.75pt; border-left-color:#000000; 
	border-left-style:solid; border-left-width:0.75pt; border-right-color:#000000; border-right-style:solid; border-right-width:0.75pt; 
	border-top-color:#000000; border-top-style:solid; border-top-width:0.75pt; padding-left:5.03pt; padding-right:5.03pt; vertical-align:middle; width:9.3%">
		<p style="margin:0pt">
			<span style="font-family:宋体; font-size:10pt">${item.name!''}　</span>
		</p>
	</td>
	<td style="border-bottom-color:#000000; border-bottom-style:solid; border-bottom-width:0.75pt; border-right-color:#000000; 
	border-right-style:solid; border-right-width:0.75pt; border-top-color:#000000; border-top-style:solid; border-top-width:0.75pt; 
	padding-left:5.4pt; padding-right:5.03pt; vertical-align:middle; width:15.56%">
		<p style="margin:0pt">
			<span style="font-family:宋体; font-size:10pt">
				${item.address!''}
				<#assign row = row + 1>
				<#if (row % 11) == 0 >
				    <span style="font-family:宋体;color:#ffffff;font-size:5pt;">${partyAEstemp}</span>
				</#if>
				<#if (wmPoiRelVoList?size < 11)>
					<#if row == 1 >
						<span style="font-family:宋体;color:#ffffff;font-size:5pt;">${partyAEstemp}</span>
					</#if>
				</#if>
			</span>
		</p>
	</td>
	<td style="border-bottom-color:#000000; border-bottom-style:solid; border-bottom-width:0.75pt; border-right-color:#000000; 
	border-right-style:solid; border-right-width:0.75pt; border-top-color:#000000; border-top-style:solid; border-top-width:0.75pt; 
	padding-left:5.4pt; padding-right:5.03pt; vertical-align:middle; width:8.32%">
		<p style="margin:0pt">
			<span style="font-family:宋体; font-size:10pt">${item.accName!''}</span>
		</p>
	</td>
	<td style="border-bottom-color:#000000; border-bottom-style:solid; border-bottom-width:0.75pt; border-right-color:#000000; 
	border-right-style:solid; border-right-width:0.75pt; border-top-color:#000000; border-top-style:solid; border-top-width:0.75pt; 
	padding-left:5.4pt; padding-right:5.03pt; vertical-align:middle; width:14.82%">
		<p style="margin:0pt">
			<span style="font-family:宋体; font-size:10pt">${item.accCardNo!''}</span>
		</p>
	</td>
	<td style="border-bottom-color:#000000; border-bottom-style:solid; border-bottom-width:0.75pt; border-right-color:#000000; 
	border-right-style:solid; border-right-width:0.75pt; border-top-color:#000000; border-top-style:solid; border-top-width:0.75pt; 
	padding-left:5.4pt; padding-right:5.03pt; vertical-align:middle; width:4.86%">
		<p style="margin:0pt">
			<span style="font-family:宋体; font-size:10pt">${item.province!''}</span>
		</p>
	</td>
	<td style="border-bottom-color:#000000; border-bottom-style:solid; border-bottom-width:0.75pt; border-right-color:#000000; 
	border-right-style:solid; border-right-width:0.75pt; border-top-color:#000000; border-top-style:solid; border-top-width:0.75pt; 
	padding-left:5.4pt; padding-right:5.03pt; vertical-align:middle; width:4.64%">
		<p style="margin:0pt">
			<span style="font-family:宋体; font-size:10pt">${item.city!''}</span>
		</p>
	</td>
	<td style="border-bottom-color:#000000; border-bottom-style:solid; border-bottom-width:0.75pt; border-right-color:#000000; border-right-style:solid; border-right-width:0.75pt; border-top-color:#000000; border-top-style:solid; border-top-width:0.75pt; padding-left:5.4pt; padding-right:5.03pt; vertical-align:middle; width:5.84%"><p style="margin:0pt"><span style="font-family:宋体; font-size:10pt">${item.bank!''}</span>
	</p>
	</td>
	<td style="border-bottom-color:#000000; border-bottom-style:solid; border-bottom-width:0.75pt; border-right-color:#000000; border-right-style:solid; border-right-width:0.75pt; border-top-color:#000000; border-top-style:solid; border-top-width:0.75pt; padding-left:5.4pt; padding-right:5.03pt; vertical-align:middle; width:5.84%"><p style="margin:0pt"><span style="font-family:宋体; font-size:10pt">${item.branchname!''}</span>
	</p>
	</td>
	<td style="border-bottom-color:#000000; border-bottom-style:solid; border-bottom-width:0.75pt; border-right-color:#000000; border-right-style:solid; border-right-width:0.75pt; border-top-color:#000000; border-top-style:solid; border-top-width:0.75pt; padding-left:5.4pt; padding-right:5.03pt; vertical-align:middle; width:5.84%"><p style="margin:0pt"><span style="font-family:宋体; font-size:10pt">${item.partyAFinancePeople!''}</span>
	</p>
	</td>
	<td style="border-bottom-color:#000000; border-bottom-style:solid; border-bottom-width:0.75pt; border-right-color:#000000; border-right-style:solid; border-right-width:0.75pt; border-top-color:#000000; border-top-style:solid; border-top-width:0.75pt; padding-left:5.4pt; padding-right:5.03pt; vertical-align:middle; width:7.58%"><p style="margin:0pt"><span style="font-family:宋体; font-size:10pt">${item.partyAFinancePhone!''}</span>
	</p>
	</td>
	<td style="border-bottom-color:#000000; border-bottom-style:solid; border-bottom-width:0.75pt; border-right-color:#000000; border-right-style:solid; border-right-width:0.75pt; border-top-color:#000000; border-top-style:solid; border-top-width:0.75pt; padding-left:5.4pt; padding-right:5.03pt; vertical-align:middle; width:5.72%"><p style="margin:0pt"><span style="font-family:宋体; font-size:10pt">${item.platformFee!''}</span>
	</p>
	</td>
	<td style="border-bottom-color:#000000; border-bottom-style:solid; border-bottom-width:0.75pt; border-right-color:#000000; border-right-style:solid; border-right-width:0.75pt; border-top-color:#000000; border-top-style:solid; border-top-width:0.75pt; padding-left:5.4pt; padding-right:5.03pt; vertical-align:middle; width:2.92%"><p style="margin:0pt"><span style="font-family:宋体; font-size:10pt">${item.poiPercentage!''}</span>
	</p>
	</td>
	<td style="border-bottom-color:#000000; border-bottom-style:solid; border-bottom-width:0.75pt; border-right-color:#000000; border-right-style:solid; border-right-width:0.75pt; border-top-color:#000000; border-top-style:solid; border-top-width:0.75pt; padding-left:5.4pt; padding-right:5.03pt; vertical-align:middle; width:2.92%"><p style="margin:0pt"><span style="font-family:宋体; font-size:10pt">${item.minMoney!''}</span>
	</p>
	</td>
	<td style="border-bottom-color:#000000; border-bottom-style:solid; border-bottom-width:0.75pt; border-right-color:#000000; border-right-style:solid; border-right-width:0.75pt; border-top-color:#000000; border-top-style:solid; border-top-width:0.75pt; padding-left:5.4pt; padding-right:5.03pt; vertical-align:middle; width:2.92%"><p style="margin:0pt"><span style="font-family:宋体; font-size:10pt">${item.quickPoiPercentage!''}</span>
	</p>
	</td>
	<td style="border-bottom-color:#000000; border-bottom-style:solid; border-bottom-width:0.75pt; border-right-color:#000000; border-right-style:solid; border-right-width:0.75pt; border-top-color:#000000; border-top-style:solid; border-top-width:0.75pt; padding-left:5.4pt; padding-right:5.03pt; vertical-align:middle; width:2.92%"><p style="margin:0pt"><span style="font-family:宋体; font-size:10pt">${item.quickMinMoney!''}</span>
	</p>
	</td>
</tr>
</#list>
</table>
<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">


    <bean id="customerDbusEventServiceV2Impl"
          class="com.sankuai.meituan.waimai.customer.service.dbus.CustomerDbusEventServiceV2Impl"></bean>
    <bean id="essyncServerPublisher" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServerPublisher"
          init-method="publish" destroy-method="destroy">
        <property name="serviceInterface" value="com.meituan.dbus.thriftV2.DataBusEventServiceV2"/> <!-- 接口类 -->
        <property name="serviceImpl" ref="customerDbusEventServiceV2Impl"/> <!-- 实现类 -->
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>  <!-- 本地 appkey，建议重新注册一个 -->
        <property name="port" value="8489"/> <!-- Server 监听端口 -->
        <property name="clusterManager" value="OCTO"/> <!-- 集群工作模式 -->
    </bean>


    <bean id="customerOplogDbusEventServiceV2Impl"
          class="com.sankuai.meituan.waimai.customer.service.dbus.CustomerOplogDbusEventServiceV2Impl"></bean>
    <bean id="customerOplogEsSyncServerPublisher"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftServerPublisher"
          init-method="publish" destroy-method="destroy">
        <property name="serviceInterface" value="com.meituan.dbus.thriftV2.DataBusEventServiceV2"/> <!-- 接口类 -->
        <property name="serviceImpl" ref="customerOplogDbusEventServiceV2Impl"/> <!-- 实现类 -->
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>  <!-- 本地 appkey，建议重新注册一个 -->
        <property name="port" value="8490"/> <!-- Server 监听端口 -->
        <property name="clusterManager" value="OCTO"/> <!-- 集群工作模式 -->
    </bean>


    <!-- 商家端协议同步 -->
    <bean id="merchantAgreementDbusEventServiceV2Impl"
          class="com.sankuai.meituan.waimai.customer.service.dbus.MerchantAgreementDbusEventServiceV2Impl"></bean>
    <bean id="merchantAgreementSyncServerPublisher"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftServerPublisher"
          init-method="publish" destroy-method="destroy">
        <property name="serviceInterface" value="com.meituan.dbus.thriftV2.DataBusEventServiceV2"/> <!-- 接口类 -->
        <property name="serviceImpl" ref="merchantAgreementDbusEventServiceV2Impl"/> <!-- 实现类 -->
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>  <!-- 本地 appkey，建议重新注册一个 -->
        <property name="port" value="8491"/> <!-- Server 监听端口 -->
        <property name="clusterManager" value="OCTO"/> <!-- 集群工作模式 -->
    </bean>

    <bean id="scSchoolDbusEventServiceV2Impl"
          class="com.sankuai.meituan.waimai.customer.service.sc.dbus.ScSchoolDbusEventServiceV2Impl"></bean>
    <bean id="scSchoolDbusEventServerPublisher"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftServerPublisher"
          init-method="publish" destroy-method="destroy">
        <property name="serviceInterface" value="com.meituan.dbus.thriftV2.DataBusEventServiceV2"/> <!-- 接口类 -->
        <property name="serviceImpl" ref="scSchoolDbusEventServiceV2Impl"/> <!-- 实现类 -->
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>  <!-- 本地 appkey，建议重新注册一个 -->
        <property name="port" value="8460"/> <!-- Server 监听端口 -->
        <property name="clusterManager" value="OCTO"/> <!-- 集群工作模式 -->
    </bean>

    <bean id="customerRelDbusEventServiceV2Impl"
          class="com.sankuai.meituan.waimai.customer.service.dbus.customerPoiList.CustomerRelDbusEventServiceV2Impl"></bean>
    <bean id="customerRelDbusEventServicePublisher"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftServerPublisher"
          init-method="publish" destroy-method="destroy">
        <property name="serviceInterface" value="com.meituan.dbus.thriftV2.DataBusEventServiceV2"/> <!-- 接口类 -->
        <property name="serviceImpl" ref="customerRelDbusEventServiceV2Impl"/> <!-- 实现类 -->
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>  <!-- 本地 appkey，建议重新注册一个 -->
        <property name="port" value="8470"/> <!-- Server 监听端口 -->
        <property name="clusterManager" value="OCTO"/> <!-- 集群工作模式 -->
    </bean>


    <bean id="poiRelDbusEventServiceV2Impl"
          class="com.sankuai.meituan.waimai.customer.service.dbus.customerPoiList.PoiRelDbusEventServiceV2Impl"></bean>
    <bean id="poiRelDbusEventServicePublisher" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServerPublisher"
          init-method="publish" destroy-method="destroy">
        <property name="serviceInterface" value="com.meituan.dbus.thriftV2.DataBusEventServiceV2"/> <!-- 接口类 -->
        <property name="serviceImpl" ref="poiRelDbusEventServiceV2Impl"/> <!-- 实现类 -->
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>  <!-- 本地 appkey，建议重新注册一个 -->
        <property name="port" value="8471"/> <!-- Server 监听端口 -->
    </bean>


    <bean id="wmCustomerPoiDbusEventServiceV2Impl"
          class="com.sankuai.meituan.waimai.customer.service.dbus.customer.WmCustomerPoiDbusEventServiceV2Impl"></bean>
    <bean id="wmCustomerPoiDbusEventServerPublisher" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServerPublisher"
          init-method="publish" destroy-method="destroy">
        <property name="serviceInterface" value="com.meituan.dbus.thriftV2.DataBusEventServiceV2"/> <!-- 接口类 -->
        <property name="serviceImpl" ref="wmCustomerPoiDbusEventServiceV2Impl"/> <!-- 实现类 -->
        <property name="appKey" value="com.sankuai.waimai.e.customer"/>  <!-- 本地 appkey，建议重新注册一个 -->
        <property name="port" value="8472"/> <!-- Server 监听端口 -->
        <property name="clusterManager" value="OCTO"/> <!-- 集群工作模式 -->
    </bean>

</beans>
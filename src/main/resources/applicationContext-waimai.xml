<?xml version="1.0" encoding="UTF-8"?>
<beans
  xmlns="http://www.springframework.org/schema/beans"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns:context="http://www.springframework.org/schema/context"
  xmlns:aop="http://www.springframework.org/schema/aop"
  xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
       http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd   http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd">

	<aop:aspectj-autoproxy/>

	<context:property-placeholder location="
            classpath:application.properties
            " />

	<bean class="com.sankuai.meituan.waimai.util.base.SpringBeanUtil" />
	<bean class="com.sankuai.meituan.waimai.util.jmonitor.JmonitorSpringBeanUtil"/>
    <!--强制确保jm所需各个资源的加载顺序-->
    <bean class="com.sankuai.meituan.waimai.util.jmonitor.JmonitorInitializingBean">
        <property name="configName" value="jmonitor.properties"/>
    </bean>

	<bean id="mykmsconfig" class="com.sankuai.meituan.waimai.util.base.KmsConfigurer">
		<property name="ignoreUnresolvablePlaceholders" value="true"/>
        <property name="order" value="20000"/>
    </bean>

	<!--内部-->
	<context:component-scan base-package="com.sankuai.meituan.waimai.service" />
	<context:component-scan base-package="com.sankuai.meituan.waimai.mq" />
	<context:component-scan base-package="com.sankuai.meituan.waimai.aspect" />
	<context:component-scan base-package="com.sankuai.meituan.waimai.filter" />
	<!--mtauth-->
	<context:component-scan base-package="com.sankuai.meituan.waimai.mtauth" />
	<!-- tracelog 使用-->
	<context:component-scan base-package="com.sankuai.meituan.waimai.kv.*" />
	<context:component-scan base-package="com.sankuai.meituan.waimai.poibizflow.*" />

	<!--hystrix切面-->
	<aop:aspectj-autoproxy expose-proxy="true"/>
	<bean id="hystrixAspect" class="com.netflix.hystrix.contrib.javanica.aop.aspectj.HystrixCommandAspect"></bean>
	<context:component-scan base-package="com.sankuai.meituan"/>
	<context:annotation-config/>


	<aop:config>
		<aop:aspect ref="tairLockAspect">
			<aop:pointcut id="tairLock"
						  expression="execution(@com.sankuai.meituan.waimai.aspect.TairLock * com.sankuai.meituan.waimai..*(..))"/>
			<aop:around pointcut-ref="tairLock" method="doAround"/>
		</aop:aspect>
		<aop:aspect ref="afterLogAspect">
			<aop:pointcut id="afterLog"
						  expression="execution(@com.sankuai.meituan.waimai.aspect.AfterLog * com.sankuai.meituan..*(..))"/>
			<aop:around pointcut-ref="afterLog" method="doAround"/>
		</aop:aspect>
	</aop:config>

	<!--内部-->

	<!--外部-->
	<import resource="classpath:waimaiConfig/waimai_lib_mtauth_sso_degrade.xml"/>
	<import resource="classpath:waimaiConfig/waimai_lib_mtauth.xml"/>
	<import resource="classpath:waimaiConfig/waimai_service_infra_client.xml"/>
	<!--<import resource="classpath:waimaiConfig/waimai_service_money_client.xml"/>-->
	<import resource="classpath:waimaiConfig/waimai_service_agent_client.xml"/>
	<import resource="classpath:waimai_service_audit_client.xml"/>
	<import resource="classpath:waimai_service_poiaudit_client.xml"/>
    <import resource="classpath:waimai_service_poiquery_client.xml"/>
    <import resource="classpath:waimai_service_poisearch_client.xml"/>
	<import resource="classpath:waimai_service_poi_flowline_client.xml"/>
	<import resource="classpath:waimaiConfig/waimai_service_notice_client.xml"/>
	<import resource="classpath:group-m-tair.xml"/>
	<import resource="classpath:waimaiConfig/waimai_service_m_bizaggr_client.xml"/>
	<import resource="classpath:spring/BankService-client.xml"/>
	<import resource="classpath:spring/GisService-client.xml"/>
	<import resource="classpath:spring/GroupGeoService-client.xml"/>
	<import resource="classpath:waimaiConfig/waimai_service_oplog_client.xml"/>
	<import resource="classpath:waimai_service_customer_client.xml"/>
	<import resource="classpath:waimai_service_contract_client.xml"/>
	<import resource="classpath:waimai_service_contractmanager_client.xml"/>
	<import resource="classpath:waimai_service_poibrand_client.xml"/>
	<import resource="classpath:waimai_service_scm_bizsettle_client.xml"/>
	<import resource="classpath:sg-api-sdk-context.xml"/>
	<import resource="classpath:s3cloud-spring-config.xml"/>
	<import resource="classpath:waimai_service_poilogistics_client.xml"/>
	<import resource="classpath:waimai_service_poi_workflow_client.xml"/>
	<import resource="classpath:waimai_service_poisearch_client.xml"/>
	<import resource="classpath:waimai_service_poibizflow_client.xml" />
	<import resource="classpath:waimai_service_poimanager_client.xml"/>
	<import resource="classpath:waimai_service_bizuser_client.xml"/>
	<import resource="classpath:waimai_e_scm_brand_client.xml"/>
	<import resource="classpath:waimai_service_poibaseinfo_client.xml"/>
	<import resource="classpath:waimai-heron-settle-client.xml"/>
	<!--标签-->
	<import resource="classpath:waimai_e_operation_label_client.xml"/>
	<!--履约侧查询接口-->
	<import resource="classpath:banmaConfig/banma_service_aoi_support_set_client.xml"/>

	<!--ORC接口替换-->
	<import resource="classpath:waimai_scm_ocr.xml"/>
	<!-- 验证码接口 -->
	<import resource="classpath:waimai_service_bizauth_client.xml"/>

	<import resource="classpath:waimai_service_image_client.xml"/>

  <import resource="classpath:waimai_service_bizuser_client.xml"/>

	<!-- 敏感字段隐藏Filter -->
	<bean class="com.sankuai.meituan.waimai.mtauth.controlview.keyinfo.KeyInfoUtils">
		<property name="allowBdViewFullInfo" value="false"/> <!-- 可选, 默认为true: bd不做敏感权限控制, 直接展示原始内容 -->
		<property name="userTagsForViewFullInfo" value="java.util.ArrayList"/> <!-- 可选, 默认为空，不做敏感权限控制的标签id, 直接展示原始内容 -->
	</bean>
	<bean id="wmControlViewKeyInfoFilter" class="com.sankuai.meituan.waimai.mtauth.controlview.WmControlViewKeyInfoFilter" >
		<property name="appkey" value="com.sankuai.waimai.e.customerweb"/>
	</bean>
	<!-- 功能权限filter的bean, web.xml中指定的该filter, 可包含按钮权限和敏感字段隐藏权限 -->
	<bean id="wmControlViewFilter" class="com.sankuai.meituan.waimai.mtauth.controlview.WmControlViewFilter">
		<property name="filters">
			<list>
				<ref bean="wmControlViewKeyInfoFilter"/> <!-- 敏感字段权限 -->
			</list>
		</property>
	</bean>


	<bean id="kms.upmAuthService.secret" class="com.meituan.service.inf.kms.value.KMSStringValue">
		<property name="appKey" value="waimai_m_beekeeper"/>
		<property name="name" value="upmAuthService"/>
	</bean>

	<bean id="authUserOperateFilter" class="com.sankuai.meituan.waimai.mtauth.filter.AuthUserOperateFilter">
		<property name="useThrift" value="true"/>
		<property name="ignoreAuthPatterns"> <!-- 模拟用户时不认证按钮控件url权限 -->
			<list>
				<value>/customer/uicomponent/api/controlView/**</value>
			</list>
		</property>
	</bean>
	<bean id="wmEmployFilter" class="com.sankuai.meituan.waimai.mtauth.filter.WmEmployFilter">
		<property name="wmEmployThriftService" ref="wmEmployThriftService"/>
		<property name="upmService" ref="upmService"/>
	</bean>
	<bean id="hystrixMetricsInitializingBean" class="com.sankuai.meituan.waimai.util.HystrixMetricsInitializingBean" init-method="init"/>

	<!-- 蜜蜂登录listener, 可选, 未接入蜜蜂H5登录的后端请不要添加 -->
	<!-- 若原mtFilter中filterChainDefinitions有beeAuth, beeUser相关配置, 需要配置该Listener, 参考蜜蜂H5登录:https://km.sankuai.com/page/15155432 -->
	<bean id="ssoBeeListener" class="com.sankuai.meituan.waimai.mtauth.listener.SsoBeeListener">
		<property name="includedUriList" value="/bee/**,/beeapi/**"/>
	</bean>
	<!-- sso listeners 配置 -->
	<bean id="ssoListeners" class="com.sankuai.meituan.waimai.mtauth.listener.SsoListeners">
		<property name="listeners">
			<list>
				<ref bean="ssoDegradeListener"/>
				<ref bean="ssoBeeListener"/> <!-- 添加蜜蜂登录Listener -->
			</list>
		</property>
	</bean>


	<!--  配置登陆和权限控制 -->
	<!-- sso配置 -->
	<!-- 详细配置参考: https://km.sankuai.com/page/43687635 -->
	<bean id="ssoFilter" class="com.sankuai.it.sso.sdk.spring.FilterFactoryBean">
		<property name="clientId" value="xianfu_waimai"/>
		<property name="secret" ref="kms.upmAuthService.secret"/>
		<!-- 目前外卖的sso各环境统一使用线上, 此项统一为prod -->
		<!--<property name="accessEnv" value="prod"/>-->
		<!--支持敏感字段组件-->
		<!-- sso降级listener -->
		<property name="ssoListenerBean" ref="ssoListeners"/>
		<property name="logoutUri" value="/logout"/>
		<!-- 不进行sso鉴权的, 原mtFilter中 anon 的 url-pattern 请添加到 excludedUriList 中 -->
		<property name="excludedUriList" value="/,/mt-sso,/unauthorized,/customer/ba/**,/*.html,/status*,/customer/v1/callback/settle/payCenter/bankCardValidate,/customer/callback/v1/w/notify,/customer/agreement/**,/customer/callback/v1/canteen/poi/electronicCallback,/customer/shepherd/**,/customer/business/**,/uicomponent/customer/getVideoUrl"/>
	</bean>
	<!-- upm配置 -->
	<!-- 注意: class路径已改变 -->
	<!-- 详细配置参考: https://km.sankuai.com/page/********, https://km.sankuai.com/page/********, https://km.sankuai.com/page/******** -->
	<bean id="mtFilter" class="com.sankuai.meituan.auth.filters.upm2.UpmFilterFactoryBean">
		<property name="clientId" value="xianfu_waimai"/>
		<property name="secret" ref="kms.upmAuthService.secret"/>
		<!-- 无权限页面, 统一跳转到先富系统的权限申请入口提示页面 -->
		<property name="unauthorizedUrl" value="http://mfe.waimai.meituan.com/mfepro/organization/forbidden/forbidden.html"/>
		<!-- 注意，filterChainDefinitions 不能使用 value="xxx" 方式配置，会丢失换行符，详见配置项解释 -->
		<property name="filterChainDefinitions">
	        <value>
						/mt-sso = anon
						/ = anon
						/unauthorized = anon
						/customer/ba/** = anon
						/*.html = anon
						/status* = anon
						/customer/v1/callback/settle/payCenter/bankCardValidate = anon
						/customer/callback/v1/w/notify = anon
						/customer/callback/v1/canteen/poi/electronicCallback = anon
						/customer/agreement/** = anon
						/customer/shepherd/** = anon
						/customer/business/** = anon
						/customer/uicomponent/api/controlView/** = user
				        /uicomponent/customer/getVideoUrl/** = anon
						/** = authMenu
					</value>
	    </property>
	</bean>
	<!-- upm pigeon 服务 -->
	<!-- 由于AuthUserOperateFilter(模拟用户)使用到upmService, 所以需配置 -->
	<bean id="upmConfig" class="com.sankuai.meituan.auth.vo.UpmConfig">
		<property name="clientId" value="xianfu_waimai"/>
		<property name="secret" ref="kms.upmAuthService.secret"/>
		<property name="timeout" value="1000"/>  <!-- pigeon 超时（ms） -->
		<property name="timeoutRetry" value="true"/>  <!-- pigeon 超时重试开关 -->
		<property name="retries" value="5"/>  <!-- pigeon 超时重试次数 -->
	</bean>
	<bean id="upmService" class="com.sankuai.meituan.auth.service.UpmService">
		<property name="upmConfig" ref="upmConfig"/>
	</bean>
	<!-- https://km.sankuai.com/page/16428777 -->
	<bean id="upm" class="com.sankuai.meituan.upm.pigeon.invoker.api.UPM">
		<constructor-arg name="clientId" value="xianfu_waimai"/>
		<constructor-arg name="secret" ref="kms.upmAuthService.secret"/>
		<constructor-arg name="timeout" value="1000"/>  <!-- pigeon 超时（ms） -->
		<constructor-arg name="timeoutRetry" value="true"/>  <!-- pigeon 超时重试开关 -->
		<constructor-arg name="retries" value="5"/>  <!-- pigeon 超时重试次数 -->
	</bean>

	<!-- ORC识别 -->
	<bean id="ocrService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
		<property name="serviceInterface" value="com.sankuai.waimai.d.service.ocr.IOCRService"/> <!-- 接口名 -->
		<property name="appKey" value="com.sankuai.waimai.e.customerweb"/>  <!-- 本地appkey -->
		<property name="remoteAppkey" value="com.sankuai.waimai.d.ocr"/>  <!-- 目标Server Appkey  -->
		<property name="timeout" value="30000"/>
	</bean>

	<bean id="nibCustomer" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
		<property name="serviceInterface" value="com.sankuai.nibcus.inf.customer.client.service.CustomerThriftService"/>
		<property name="appKey" value="com.sankuai.waimai.e.customer"/>
		<property name="remoteAppkey" value="com.sankuai.nibcus.inf.customer"/>
		<property name="timeout" value="3000"/>
		<property name="remoteUniProto" value="true"/>
		<property name="remoteServerPort" value="9000" />
	</bean>

	<!-- 电子合同系统H5页面信息 -->
	<bean id="econtractBizService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
		<property name="serviceInterface" value="com.sankuai.meituan.waimai.econtrct.client.service.EcontractBizService"/> <!-- 接口名 -->
		<property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
		<property name="appKey" value="com.sankuai.waimai.e.customerweb"/>
		<property name="remoteAppkey" value="com.sankuai.waimai.m.econtractserver"/> <!-- 目标 Server Appkey -->
		<property name="maxResponseMessageBytes" value="32768000"/>
		<property name="remoteServerPort" value="9002" />
		<property name="timeout" value="10000"/><!--超时时间ms-->
	</bean>

	<!-- ApiUploadFileService -->
	<bean id="apiUploadFileClient" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
		<!-- 接口名 -->
		<property name="serviceInterface" value="com.meituan.sec.distributeplatform.thrift.ApiUploadFileService"/>
		<!-- 本地appkey -->
		<property name="appKey" value="com.sankuai.waimai.e.customerweb"/>
		<!-- 目标Server Appkey  -->
		<property name="remoteAppkey" value="com.sankuai.sec.distribute.platform"/>
		<property name="timeout" value="10000"/>
		<property name="remoteServerPort" value="9002"/>
	</bean>

	<bean id="wmLabelThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
		  destroy-method="destroy">
		<property name="serviceInterface" value="com.sankuai.meituan.waimai.label.thrift.service.WmLabelThriftService"/> <!-- service接口名 -->
		<property name="timeout" value="10000"/> <!-- thrift rpc 超时时间（毫秒） -->
		<property name="serverDynamicWeight" value="true"/>
		<property name="appKey" value="com.sankuai.waimai.e.customerweb"/>
		<property name="remoteAppkey" value="com.sankuai.poioperation.labelserver"/>
		<property name="remoteServerPort" value="8504"/>
	</bean>


	<bean id="campusContactThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
		  destroy-method="destroy">
		<property name="serviceInterface" value="com.sankuai.waimai.crm.cd.saas.lead.campus.sdk.thrift.contact.CampusContactThriftService"/> <!-- service接口名 -->
		<property name="timeout" value="10000"/> <!-- thrift rpc 超时时间（毫秒） -->
		<property name="serverDynamicWeight" value="true"/>
		<property name="appKey" value="com.sankuai.waimai.e.customerweb"/>
		<property name="remoteAppkey" value="com.sankuai.waimaisales.saas.lead"/>
		<property name="remoteServerPort" value="8490"/>
	</bean>

	<bean id="campusPartnerThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
		  destroy-method="destroy">
		<property name="serviceInterface" value="com.sankuai.waimai.crm.cd.saas.lead.campus.sdk.thrift.partner.CampusPartnerThriftService"/> <!-- service接口名 -->
		<property name="timeout" value="10000"/> <!-- thrift rpc 超时时间（毫秒） -->
		<property name="serverDynamicWeight" value="true"/>
		<property name="appKey" value="com.sankuai.waimai.e.customerweb"/>
		<property name="remoteAppkey" value="com.sankuai.waimaisales.saas.lead"/>
		<property name="remoteServerPort" value="8490"/>
	</bean>

	<bean id="authenticateRoleService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
		  destroy-method="destroy">
		<property name="serviceInterface"
				  value="com.sankuai.waimai.crm.authenticate.client.service.AuthenticateRoleService"/> <!-- service接口名 -->
		<property name="timeout" value="600000"/>    <!-- thrift rpc 超时时间（毫秒） -->
		<property name="remoteAppkey" value="com.sankuai.waimaiinfra.authenticate.server"/>
		<property name="appKey" value="com.sankuai.waimai.e.customerweb"/>
		<property name="remoteServerPort" value="8686"/>
	</bean>

	<bean id="ocrServicesHours" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
		<property name="serviceInterface" value="com.meituan.horus.service.OCRServices"/> <!-- 接口名 -->
		<property name="remoteServerPort" value="9001"/>
		<property name="appKey" value="com.sankuai.waimai.e.customerweb"/>
		<property name="remoteAppkey" value="com.sankuai.nlpml.ml.horus"/>  <!-- 目标Server Appkey  -->
		<!--<property name="async" value="true"/>-->
		<property name="async" value="false"/>
		<property name="timeout" value="5000"/>
	</bean>

	<bean id="bmLbsAoiExtThriftIface" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
		  destroy-method="destroy">
		<property name="serviceInterface" value="com.sankuai.meituan.banma.aoi.thrift.iface.BmLbsAoiExtThriftIface"/> <!-- service接口名 -->
		<property name="timeout" value="10000"/>    <!-- thrift rpc 超时时间（毫秒） -->
		<property name="remoteAppkey" value="com.sankuai.deliverylbs.aoisupport"/>
		<property name="appKey" value="com.sankuai.waimai.e.customerweb"/>
		<property name="remoteServerPort" value="9820"/>
	</bean>

	<bean id="mwalletQueryProxyService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
		  destroy-method="destroy">
		<property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
		<!-- 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 -->
		<property name="timeout" value="1000"/>
		<!-- thrift rpc 超时时间（毫秒） -->
		<property name="retryRequest" value="true"/>
		<!-- 请求失败后是否重试, 默认为 true -->
		<property name="appKey" value="com.sankuai.waimai.e.customerweb"/>
		<!-- 本地 appkey, MIX/OCTO 模式下必须配置 -->
		<property name="remoteAppkey" value="com.sankuai.pay.merchantproduct.mwallet"/>
		<property name="remoteServerPort" value="3444"/>
		<!-- 目标 Server Appkey, MIX/OCTO 模式下必须配置  -->
		<property name="serviceInterface" value="com.meituan.pay.mwallet.thrift.service.MwalletQueryProxyService"/>
	</bean>

<!--	代理商Service -->
	<bean id="wmAgentInfoService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
		<property name="serviceInterface"  value="com.meituan.waimai.agent.otter.enterprise.service.agentinfo.WmAgentInfoService"/>
		<property name="timeout" value="20000"/>
		<property name="appKey" value="com.sankuai.waimai.e.customerweb"/>
		<property name="remoteServerPort" value="8502"/>
		<property name="remoteAppkey" value="com.sankuai.agentotter"/>
	</bean>

	<!-- 敏感资源过滤器配置 -->
	<bean id="crmSensitiveFilterBean" class="com.sankuai.waimai.crm.authenticate.client.auth.factory.SensitiveResourceAuthFilterFactoryBean">
		<property name="tenantId" value="1000008"/><!-- 兜底配置租户，若指定则以该租户为准，若不指定，则以前端传递参数为准 -->
	</bean>

	<bean id="kmsAuthDataSource" class="com.meituan.service.inf.kms.client.KmsAuthDataSource">
		<property name="appkey" value="com.sankuai.waimai.e.customerweb"/>
	</bean>
	<bean id="defaultSignHandler" class="com.meituan.service.mobile.mtthrift.auth.DefaultSignHandler">
		<property name="authDataSource" ref="kmsAuthDataSource"/>
	</bean>

	<bean id="bizAccountInfoThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
		<property name="serviceInterface" value="com.sankuai.sjst.ecom.epassport.service.client.thrift.service.BizAccountInfoThriftService"/>
		<property name="appKey" value="com.sankuai.waimai.e.customerweb"/>
		<property name="timeout" value="10000"/>
		<property name="remoteAppkey" value="com.sankuai.sjst.ecom.epassportservice"/>
		<property name="remoteServerPort" value="9001"/>
		<property name="nettyIO" value="true"/>
		<property name="signHandler" ref="defaultSignHandler"/>  <!-- 鉴权功能需要Mtthrift 1.8.4.1+版本 -->
	</bean>

</beans>

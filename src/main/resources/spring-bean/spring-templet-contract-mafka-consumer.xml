<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd">

    <bean id="wmTempletContractDiffMafkaConsumer" class="com.meituan.mafka.client.consumer.DefaultConsumerProcessor"
          destroy-method="close">
        <constructor-arg name="topic" value="customer.contract.info.update"/>
        <constructor-arg name="consumerProps">
            <map>
                <!--bg-->
                <entry key="mafka.bg.namespace" value="waimai"/>
                <!--消费者appkey-->
                <entry key="mafka.client.appkey" value="com.sankuai.waimai.e.customer"/>
                <entry key="group.id" value="waimai.customer.contract.version.change"/>
            </map>
        </constructor-arg>
    </bean>


</beans>
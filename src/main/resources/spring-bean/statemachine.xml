<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
               http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">

    <context:component-scan base-package="com.sankuai.meituan.waimai.customer"/>

    <!--合同状态-->
    <bean id="contractConfig" class="com.sankuai.meituan.waimai.customer.statemachine.core.biz.FlowStateMachineConfig">
        <constructor-arg name="appKey" value="com.sankuai.waimai.contract"/>
        <constructor-arg name="definitionFile" value="classpath:sm/contract_flow.xml"/>
        <constructor-arg name="flowStoreManager" ref="wmTempletContractFlowStoreService"/>
    </bean>
    <bean id="contractStateMachine"
          class="com.sankuai.meituan.waimai.customer.statemachine.core.biz.FlowStateMachineManagerImpl">
        <constructor-arg name="config" ref="contractConfig"/>
    </bean>

       
</beans>
<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:task="http://www.springframework.org/schema/task"
  xmlns:context="http://www.springframework.org/schema/context"
  xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <bean id="mtThriftPoolConfig" class="com.meituan.service.mobile.mtthrift.client.pool.MTThriftPoolConfig">
        <property name="maxActive" value="50"/>
        <property name="maxIdle" value="8"/>
        <property name="minIdle" value="2"/>
        <property name="maxWait" value="3000"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="false"/>
        <property name="testWhileIdle" value="false"/>
    </bean>

    <bean id="wmCustomerContractThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/><!-- 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 -->
        <property name="serviceInterface" value="com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerContractThriftService"/> <!-- service接口名 -->
        <property name="timeout" value="600000"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="serverDynamicWeight" value="true"/>
        <property name="clusterManager" value="OCTO"/>
        <property name="appKey" ref="octAppKeyClient"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.e.customer"/>
        <property name="remoteServerPort" value="8431"/>
        <!--<property name="async" value="true"/>-->
    </bean>

    <bean id="wmCustomerOplogThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/><!-- 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 -->
        <property name="serviceInterface" value="com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerOplogThriftService"/> <!-- service接口名 -->
        <property name="timeout" value="600000"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="serverDynamicWeight" value="true"/>
        <property name="clusterManager" value="OCTO"/>
        <property name="appKey" ref="octAppKeyClient"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.e.customer"/>
        <property name="remoteServerPort" value="8432"/>
        <!--<property name="async" value="true"/>-->
    </bean>

    <bean id="wmCustomerKpThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/><!-- 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 -->
        <property name="serviceInterface" value="com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerKpThriftService"/> <!-- service接口名 -->
        <property name="timeout" value="600000"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="serverDynamicWeight" value="true"/>
        <property name="clusterManager" value="OCTO"/>
        <property name="appKey" ref="octAppKeyClient"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.e.customer"/>
        <property name="remoteServerPort" value="8430"/>
    </bean>

    <bean id="wmCustomerThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/><!-- 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 -->
        <property name="serviceInterface" value="com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerThriftService"/> <!-- service接口名 -->
        <property name="timeout" value="600000"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="serverDynamicWeight" value="true"/>
        <property name="clusterManager" value="OCTO"/>
        <property name="appKey" ref="octAppKeyClient"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.e.customer"/>
        <property name="remoteServerPort" value="8433"/>
    </bean>

    <bean id="wmCustomerBrandThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/><!-- 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 -->
        <property name="serviceInterface" value="com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerBrandThriftService"/> <!-- service接口名 -->
        <property name="timeout" value="600000"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="serverDynamicWeight" value="true"/>
        <property name="clusterManager" value="OCTO"/>
        <property name="appKey" ref="octAppKeyClient"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.e.customer"/>
        <property name="remoteServerPort" value="8434"/>
    </bean>

    <bean id="wmCustomerPoiThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/><!-- 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 -->
        <property name="serviceInterface" value="com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerPoiThriftService"/> <!-- service接口名 -->
        <property name="timeout" value="600000"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="serverDynamicWeight" value="true"/>
        <property name="clusterManager" value="OCTO"/>
        <property name="appKey" ref="octAppKeyClient"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.e.customer"/>
        <property name="remoteServerPort" value="8435"/>
    </bean>

    <bean id="wmCustomerContractVersionThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/><!-- 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 -->
        <property name="serviceInterface" value="com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerContractVersionThriftService"/> <!-- service接口名 -->
        <property name="timeout" value="600000"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="serverDynamicWeight" value="true"/>
        <property name="clusterManager" value="OCTO"/>
        <property name="appKey" ref="octAppKeyClient"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.e.customer"/>
        <property name="remoteServerPort" value="8436"/>
    </bean>

    <!--<bean id="wmEcontractSignThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">-->
        <!--<property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>&lt;!&ndash; 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 &ndash;&gt;-->
        <!--<property name="serviceInterface" value="com.sankuai.meituan.waimai.thrift.customer.service.WmEcontractSignThriftService"/> &lt;!&ndash; service接口名 &ndash;&gt;-->
        <!--<property name="timeout" value="100000"/> &lt;!&ndash; thrift rpc 超时时间（毫秒） &ndash;&gt;-->
        <!--<property name="serverDynamicWeight" value="true"/>-->
        <!--<property name="clusterManager" value="OCTO"/>-->
        <!--<property name="appKey" ref="octAppKeyClient"/>-->
        <!--<property name="remoteAppkey" value="com.sankuai.waimai.e.customer"/>-->
        <!--<property name="remoteServerPort" value="8437"/>-->
    <!--</bean>-->

    <bean id="wmEcontractSignThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/><!-- 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 -->
        <property name="serviceInterface" value="com.sankuai.meituan.waimai.thrift.customer.service.WmEcontractSignThriftService"/> <!-- service接口名 -->
        <property name="timeout" value="100000"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" ref="octAppKeyClient"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.e.customer"/>
        <property name="serverIpPorts" value="127.0.0.1:8437" />
    </bean>


    <!--<bean id="wmEcontractSignManagerThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">-->
        <!--<property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>&lt;!&ndash; 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 &ndash;&gt;-->
        <!--<property name="serviceInterface" value="com.sankuai.meituan.waimai.thrift.customer.service.WmEcontractSignManagerThriftService"/> &lt;!&ndash; service接口名 &ndash;&gt;-->
        <!--<property name="timeout" value="100000"/> &lt;!&ndash; thrift rpc 超时时间（毫秒） &ndash;&gt;-->
        <!--<property name="serverDynamicWeight" value="true"/>-->
        <!--<property name="clusterManager" value="OCTO"/>-->
        <!--<property name="appKey" ref="octAppKeyClient"/>-->
        <!--<property name="remoteAppkey" value="com.sankuai.waimai.e.customer"/>-->
        <!--<property name="remoteServerPort" value="8438"/>-->
    <!--</bean>-->

    <bean id="wmEcontractSignManagerThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/><!-- 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 -->
        <property name="serviceInterface" value="com.sankuai.meituan.waimai.thrift.customer.service.WmEcontractSignManagerThriftService"/> <!-- service接口名 -->
        <property name="timeout" value="100000"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" ref="octAppKeyClient"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.e.customer"/>
        <property name="serverIpPorts" value="127.0.0.1:8438" />
    </bean>

    <bean id="wmCustomerSettleThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/><!-- 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 -->
        <property name="serviceInterface" value="com.sankuai.meituan.waimai.thrift.customer.service.WmSettleThriftService"/> <!-- service接口名 -->
        <property name="timeout" value="100000"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="serverDynamicWeight" value="true"/>
        <property name="clusterManager" value="OCTO"/>
        <property name="appKey" ref="octAppKeyClient"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.e.customer"/>
        <property name="remoteServerPort" value="8439"/>
    </bean>

    <bean id="wmCustomerSettleManagerThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/><!-- 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 -->
        <property name="serviceInterface" value="com.sankuai.meituan.waimai.thrift.customer.service.WmSettleManagerThriftService"/> <!-- service接口名 -->
        <property name="timeout" value="100000"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="serverDynamicWeight" value="true"/>
        <property name="clusterManager" value="OCTO"/>
        <property name="appKey" ref="octAppKeyClient"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.e.customer"/>
        <property name="remoteServerPort" value="8440"/>
    </bean>

    <bean id="wmCustomerSyncThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/><!-- 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 -->
        <property name="serviceInterface" value="com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerSyncThriftService"/> <!-- service接口名 -->
        <property name="timeout" value="100000"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="serverDynamicWeight" value="true"/>
        <property name="clusterManager" value="OCTO"/>
        <property name="appKey" ref="octAppKeyClient"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.e.customer"/>
        <property name="remoteServerPort" value="8441"/>
    </bean>

    <bean id="wmCustomerCommonThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/><!-- 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 -->
        <property name="serviceInterface" value="com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerCommonThriftService"/> <!-- service接口名 -->
        <property name="timeout" value="100000"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="serverDynamicWeight" value="true"/>
        <property name="clusterManager" value="OCTO"/>
        <property name="appKey" ref="octAppKeyClient"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.e.customer"/>
        <property name="remoteServerPort" value="8442"/>
    </bean>

    <!--客户端宿主环境使用，即使起冲突，请确保内容也是一样的！！！-->
    <bean id="octAppKeyClient" class="com.sankuai.meituan.waimai.thrift.util.WmThriftConfigUtil" factory-method="getOctoAppKey"></bean>
</beans>
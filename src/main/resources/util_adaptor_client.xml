<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">


    <bean id="mtThriftPoolConfig" class="com.meituan.service.mobile.mtthrift.client.pool.MTThriftPoolConfig">
        <property name="maxActive" value="50"/>
        <property name="maxIdle" value="8"/>
        <property name="minIdle" value="2"/>
        <property name="maxWait" value="3000"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="false"/>
        <property name="testWhileIdle" value="false"/>
    </bean>


    <bean id="wmNoticePublishThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.thrift.service.WmNoticePublishThriftService"/> <!-- service接口名 -->
        <!--<property name="zkPath" value="/sankuai/webapp/waimai_service_infra_server/service/thrift/wm_aor_service/servers"/>-->
        <property name="timeout" value="1000"/>     <!-- thrift rpc 超时时间（毫秒） -->
        <property name="appKey"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('octo.appkey', 'com.sankuai.waimai.service.crmnotice')}"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.service.crmnotice"/>
        <property name="remoteServerPort" value="8746"/>
    </bean>


    <bean id="wmCommercialAgentInfoThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.thrift.agent.service.WmCommercialAgentInfoThriftService"/> <!-- service接口名 -->
        <property name="timeout" value="2000"/>    <!-- thrift rpc 超时时间（毫秒） -->
        <property name="clusterManager" value="OCTO"/>
        <property name="appKey"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('octo.appkey',T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('app.key'))}"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.agent"/>
        <property name="remoteServerPort" value="8430"/>
    </bean>

    <bean id="settleInfoThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig"
                  ref="mtThriftPoolConfig"/><!-- 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 -->
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.scm.bizsettle.thrift.service.SettleInfoThriftService"/> <!-- service接口名 -->
        <property name="timeout" value="30000"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="serverDynamicWeight" value="true"/>
        <property name="clusterManager" value="OCTO"/>
        <property name="appKey"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('octo.appkey', T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('app.key'))}"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.e.bizsettle"/>
        <property name="remoteServerPort" value="8402"/>
    </bean>

    <bean id="wmEmployThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.infra.service.WmEmployService"/> <!-- service接口名 -->
        <property name="timeout" value="2000"/>    <!-- thrift rpc 超时时间（毫秒） -->
        <property name="remoteAppkey" value="com.sankuai.waimai.infra"/>
        <property name="nettyIO" value="true"/> <!-- 开启 Netty IO  -->
        <property name="filterByServiceName" value="true"/> <!-- 开启filterByService  -->
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/> <!-- 连接池配置 没有额外需要可不配置 -->
    </bean>

    <bean id="wmVirtualOrgThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.infra.service.WmVirtualOrgService"/> <!--service接口名 -->
        <property name="timeout" value="2000"/>    <!-- thrift rpc 超时时间（毫秒） -->
        <property name="remoteAppkey" value="com.sankuai.waimai.infra"/>
        <property name="nettyIO" value="true"/> <!-- 开启 Netty IO  -->
        <property name="filterByServiceName" value="true"/> <!-- 开启filterByService  -->
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/> <!-- 连接池配置 没有额外需要可不配置 -->
    </bean>

    <bean id="wmUniAorThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.infra.service.WmUniAorService"/> <!-- service接口名 -->
        <property name="timeout" value="2000"/>    <!-- thrift rpc 超时时间（毫秒） -->
        <property name="remoteAppkey" value="com.sankuai.waimai.infra"/>
        <property name="nettyIO" value="true"/> <!-- 开启 Netty IO  -->
        <property name="filterByServiceName" value="true"/> <!-- 开启filterByService  -->
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/> <!-- 连接池配置 没有额外需要可不配置 -->
    </bean>

    <bean id="wmOpenCityThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.infra.service.WmOpenCityService"/> <!-- service接口名 -->
        <property name="timeout" value="2000"/>    <!-- thrift rpc 超时时间（毫秒） -->
        <property name="remoteAppkey" value="com.sankuai.waimai.infra"/>
        <property name="nettyIO" value="true"/> <!-- 开启 Netty IO  -->
        <property name="filterByServiceName" value="true"/> <!-- 开启filterByService  -->
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/> <!-- 连接池配置 没有额外需要可不配置 -->
    </bean>

<!--    <bean id="wmAorThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"-->
<!--          destroy-method="destroy">-->
<!--        <property name="serviceInterface"-->
<!--                  value="com.sankuai.meituan.waimai.infra.service.WmAorService"/> &lt;!&ndash; service接口名 &ndash;&gt;-->
<!--        <property name="timeout" value="2000"/>    &lt;!&ndash; thrift rpc 超时时间（毫秒） &ndash;&gt;-->
<!--        <property name="remoteAppkey" value="com.sankuai.waimai.infra"/>-->
<!--        <property name="nettyIO" value="true"/> &lt;!&ndash; 开启 Netty IO  &ndash;&gt;-->
<!--        <property name="filterByServiceName" value="true"/> &lt;!&ndash; 开启filterByService  &ndash;&gt;-->
<!--        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/> &lt;!&ndash; 连接池配置 没有额外需要可不配置 &ndash;&gt;-->
<!--    </bean>-->

    <bean id="wmOrgThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.infra.service.WmOrgService"/> <!-- service接口名 -->
        <property name="timeout" value="1000"/>    <!-- thrift rpc 超时时间（毫秒） -->
        <property name="remoteAppkey" value="com.sankuai.waimai.infra"/>
        <property name="nettyIO" value="true"/> <!-- 开启 Netty IO  -->
        <property name="filterByServiceName" value="true"/> <!-- 开启filterByService  -->
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/> <!-- 连接池配置 没有额外需要可不配置 -->
    </bean>

    <bean id="businessCertification" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig"
                  ref="mtThriftPoolConfig"/><!-- 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 -->
        <property name="timeout" value="${sg.thrift.timeout}"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.wbinf.sg.api.certification.business.BusinessCertification"/> <!-- 接口名 -->
        <property name="appKey" value="${sg.app.key}"/>  <!-- 本地appkey -->
        <property name="remoteAppkey" value="com.sankuai.waimai.e.govapiserver"/>  <!-- 目标Server Appkey  -->
        <property name="remoteServerPort" value="9007"/>
    </bean>

    <bean id="bmContractWmThriftIface" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig"
                  ref="mtThriftPoolConfig"/><!-- 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 -->
        <property name="serviceInterface"
                  value="com.sankuai.meituan.banma.thrift.deliverycontract.adminbiz.thrift.BmContractWmThriftIface"/>
        <property name="timeout" value="3500"/>
        <property name="remoteAppkey" value="com.sankuai.deliverycontract.adminbiz"/>
        <property name="filterByServiceName" value="true"/>
        <property name="appKey" ref="appKey"/>
    </bean>

    <!--闪购消息管理-->
    <bean id="sgMessagePubThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.sgmerchant.msgmanager.thrift.service.SgMessagePubThriftService"/>
        <property name="appKey"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('octo.appkey', T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('app.key'))}"/>
        <property name="remoteAppkey" value="com.sankuai.sgmerchant.msgmanager"/>
        <property name="timeout" value="3000"/>
    </bean>


    <!--审核-->
    <bean id="wmAuditApiThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig"
                  ref="mtThriftPoolConfig"/><!-- 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 -->
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.thrift.service.WmAuditApiService"/> <!-- service接口名 -->
        <property name="timeout" value="10000"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="serverDynamicWeight" value="true"/>
        <property name="clusterManager" value="OCTO"/>
        <property name="appKey" ref="octAppKeyClient"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.audit"/>
        <property name="remoteServerPort" value="8476"/>
    </bean>

    <bean id="wmAuditTaskThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig"
                  ref="mtThriftPoolConfig"/><!-- 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 -->
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.thrift.service.WmAuditTaskService"/> <!-- service接口名 -->
        <property name="timeout" value="10000"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="serverDynamicWeight" value="true"/>
        <property name="clusterManager" value="OCTO"/>
        <property name="appKey" ref="octAppKeyClient"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.audit"/>
        <property name="remoteServerPort" value="8475"/>
    </bean>

    <bean id="wmAuditRejectReasonThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig"
                  ref="mtThriftPoolConfig"/><!-- 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 -->
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.thrift.service.WmAuditRejectReasonService"/> <!-- service接口名 -->
        <property name="timeout" value="10000"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="serverDynamicWeight" value="true"/>
        <property name="clusterManager" value="OCTO"/>
        <property name="appKey" ref="octAppKeyClient"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.audit"/>
        <property name="remoteServerPort" value="8472"/>
    </bean>

    <bean id="wmProcessApprovalThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig"
                  ref="mtThriftPoolConfig"/><!-- 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 -->
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.thrift.rateApproval.service.WmProcessApprovalThriftService"/> <!-- service接口名 -->
        <property name="timeout" value="10000"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="serverDynamicWeight" value="true"/>
        <property name="clusterManager" value="OCTO"/>
        <property name="appKey" ref="octAppKeyClient"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.audit"/>
        <property name="remoteServerPort" value="8534"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="wmApprovalRateThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig"
                  ref="mtThriftPoolConfig"/><!-- 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 -->
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.thrift.rateApproval.service.WmApprovalRateThriftService"/> <!-- service接口名 -->
        <property name="timeout" value="10000"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="serverDynamicWeight" value="true"/>
        <property name="clusterManager" value="OCTO"/>
        <property name="appKey" ref="octAppKeyClient"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.audit"/>
        <property name="remoteServerPort" value="8529"/>
        <property name="nettyIO" value="true"/>
    </bean>


    <!--    com.sankuai.waimaipoi.brand-->

    <bean id="brandQueryThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig"
                  ref="mtThriftPoolConfig"/><!-- 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 -->
        <property name="serviceInterface"
                  value="com.sankuai.meituan.scmbrand.thrift.service.api.BrandQueryThriftService"/> <!-- service接口名 -->
        <property name="timeout" value="10000"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="serverDynamicWeight" value="true"/>
        <property name="clusterManager" value="OCTO"/>
        <property name="appKey"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('octo.appkey', T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('app.key'))}"/>
        <property name="remoteAppkey" value="com.sankuai.waimaipoi.brand"/>
        <property name="remoteServerPort" value="8507"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="serviceBrandQueryThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.scmbrand.thrift.service.manager.ServiceBrandQueryThriftService"/> <!-- service接口名 -->
        <property name="timeout" value="30000"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="serverDynamicWeight" value="true"/>
        <property name="clusterManager" value="OCTO"/>
        <property name="appKey"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('octo.appkey', T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('app.key'))}"/>
        <property name="remoteAppkey" value="com.sankuai.waimaipoi.brand"/>
        <property name="remoteServerPort" value="8414"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <!--    com.sankuai.waimai.poibrand-->

    <bean id="wmPoiBrandQueryThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig"
                  ref="mtThriftPoolConfig"/><!-- 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 -->
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.poibrand.service.WmPoiBrandQueryThriftService"/> <!-- service接口名 -->
        <property name="timeout" value="10000"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="serverDynamicWeight" value="true"/>
        <property name="clusterManager" value="OCTO"/>
        <property name="appKey"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('octo.appkey', T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('app.key'))}"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.poibrand"/>
        <property name="remoteServerPort" value="8405"/>
    </bean>

    <bean id="serviceBrandOuterQueryThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig"
                  ref="mtThriftPoolConfig"/><!-- 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 -->
        <property name="serviceInterface"
                  value="com.sankuai.meituan.scmbrand.thrift.service.api.ServiceBrandOuterQueryThriftService"/> <!-- service接口名 -->
        <property name="timeout" value="10000"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="serverDynamicWeight" value="true"/>
        <property name="clusterManager" value="OCTO"/>
        <property name="appKey"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('octo.appkey', T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('app.key'))}"/>
        <property name="remoteAppkey" value="com.sankuai.waimaipoi.brand"/>
        <property name="remoteServerPort" value="8530"/>
    </bean>


    <!--    com.sankuai.waimai.bizuser-->
    <bean id="acctWmPoiTService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig"
                  ref="mtThriftPoolConfig"/><!-- 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 -->
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.bizuser.thrift.AcctWmPoiTService"/> <!-- service接口名 -->
        <property name="timeout"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getLong('bizuser_thrift_read_timeout', 1000)}"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="serverDynamicWeight" value="true"/>
        <property name="clusterManager" value="OCTO"/>
        <property name="appKey"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('octo.appkey', T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('app.key'))}"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.bizuser"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/> <!-- 开启 Netty IO  -->
    </bean>

    <bean id="account" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig"
                  ref="mtThriftPoolConfig"/><!-- 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 -->
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.bizsso.thrift.Account"/> <!-- service接口名 -->
        <property name="timeout"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getLong('bizauth_thrift_read_timeout', 1000)}"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="serverDynamicWeight" value="true"/>
        <property name="clusterManager" value="OCTO"/>
        <property name="appKey"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('octo.appkey', T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('app.key'))}"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.bizauth"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/> <!-- 开启 Netty IO  -->
    </bean>

    <bean id="acctOrgTService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig"
                  ref="mtThriftPoolConfig"/><!-- 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 -->
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.bizuser.thrift.AcctOrgTService"/> <!-- service接口名 -->
        <property name="timeout"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getLong('bizuser_thrift_read_timeout', 1000)}"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="serverDynamicWeight" value="true"/>
        <property name="clusterManager" value="OCTO"/>
        <property name="appKey"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('octo.appkey', T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('app.key'))}"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.bizuser"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/> <!-- 开启 Netty IO  -->
    </bean>

    <bean id="wmBizNoticeThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.bizme.thrift.service.WmBizNoticeThriftService"/>
        <property name="timeout" value="3000"/>
        <property name="connTimeout" value="200"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('octo.appkey', T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('app.key', ''))}"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.e.bizme"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>


    <!--    com.sankuai.poioperation.labelserver-->
    <bean id="wmLabelThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.label.thrift.service.WmLabelThriftService"/> <!-- service接口名 -->
        <property name="timeout" value="10000"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('octo.appkey', T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('app.key'))}"/>
        <property name="remoteAppkey" value="com.sankuai.poioperation.labelserver"/>
        <property name="remoteServerPort" value="8504"/>
    </bean>

    <bean id="wmLabelInnerThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.label.thrift.service.WmLabelInnerThriftService"/> <!-- service接口名 -->
        <property name="timeout" value="10000"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('octo.appkey', T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('app.key'))}"/>
        <property name="remoteAppkey" value="com.sankuai.poioperation.labelserver"/>
        <property name="remoteServerPort" value="8503"/>
    </bean>


    <!--    com.sankuai.waimai.service.crmticket-->
    <bean id="wmCrmTicketThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.crm.ticket.thrift.service.WmCrmTicketThriftService"/> <!-- service接口名 -->
        <property name="timeout" value="4000"/>    <!-- thrift rpc 超时时间（毫秒） -->
        <!-- <property name="appKey" ref="octAppKeyClient"/> -->
        <property name="appKey" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('octo.appkey', '')}"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.service.crmticket"/>
        <property name="remoteServerPort" value="8722"/>
    </bean>

    <bean id="wmCrmTicketFlowThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.crm.ticket.thrift.service.WmCrmTicketFlowThriftService"/> <!-- service接口名 -->
        <property name="timeout" value="4000"/>    <!-- thrift rpc 超时时间（毫秒） -->
        <!-- <property name="appKey" ref="octAppKeyClient"/> -->
        <property name="appKey" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('octo.appkey', '')}"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.service.crmticket"/>
        <property name="remoteServerPort" value="8723"/>
    </bean>

    <bean id="wmPoiOplogThriftServicePublisher" class="com.sankuai.meituan.waimai.oplog.thrift.service.WmPoiOplogClientproxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.oplog.thrift.service.WmPoiOplogThriftService"/>
        <property name="timeout" value="10000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('octo.appkey', T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('app.key'))}"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.service.oplog"/>
        <property name="remoteServerPort" value="12345"/>
    </bean>

    <bean id="wmPoiOplogThriftServiceAsync" class="com.sankuai.meituan.waimai.oplog.thrift.service.WmPoiOplogClientAsyncProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.oplog.thrift.service.WmPoiOplogThriftService"/>
        <property name="timeout" value="10000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('octo.appkey', T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('app.key'))}"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.service.oplog"/>
        <property name="remoteServerPort" value="12345"/>
        <property name="async" value="true" />
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="wmPoiOplogCommonThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.waimai.oplog.thrift.service.WmPoiOplogCommonThriftService"/>
        <property name="timeout" value="10000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('octo.appkey', T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('app.key'))}"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.service.oplog"/>
        <property name="remoteServerPort" value="12349"/>
    </bean>

    <bean id="wmOplogThriftServicePublisher" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.oplog.thrift.service.WmOplogThriftService"/>
        <property name="timeout" value="10000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('octo.appkey', T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('app.key'))}"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.service.oplog"/>
        <property name="remoteServerPort" value="12347"/>
    </bean>


    <bean id="wmPoiSettlementThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/><!-- 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 -->
        <property name="serviceInterface" value="com.sankuai.meituan.waimai.thrift.service.WmPoiSettlementThriftService"/> <!-- service接口名 -->
        <property name="timeout" value="10000"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="serverDynamicWeight" value="true"/>
        <property name="clusterManager" value="OCTO"/>
        <property name="appKey" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('octo.appkey', T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('app.key'))}"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.poiquery"/>
        <property name="remoteServerPort" value="8520"/>
    </bean>

    <!--结算费率 begin-->
    <bean id="wmPoiSettleFeeQueryThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/><!-- 可不配置:默认maxActive=100,maxIdle=20,minIdle=5,maxWait=3000 -->
        <property name="serviceInterface" value="com.sankuai.meituan.waimai.thrift.service.WmPoiSettlementThriftService"/> <!-- service接口名 -->
        <property name="timeout" value="1000"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="serverDynamicWeight" value="true"/>
        <property name="clusterManager" value="OCTO"/>
        <property name="appKey" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('octo.appkey', T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('app.key'))}"/>
        <property name="remoteAppkey" value="com.sankuai.waimaipoi.settlequery"/>
        <property name="remoteServerPort" value="8520"/>
    </bean>

</beans>
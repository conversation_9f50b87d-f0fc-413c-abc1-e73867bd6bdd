<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sankuai.meituan.waimai.customer.dao.WmAgreementModelDicMapper" >
  <resultMap id="BaseResultMap" type="com.sankuai.meituan.waimai.customer.domain.agreement.WmAgreementModelDic" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="agree_model_name" property="agreeModelName" jdbcType="VARCHAR" />
    <result column="current_version" property="currentVersion" jdbcType="INTEGER" />
    <result column="valid" property="valid" jdbcType="TINYINT" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, agree_model_name, current_version, valid
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from wm_agreement_model_dic
    where id = #{id,jdbcType=INTEGER}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from wm_agreement_model_dic
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.sankuai.meituan.waimai.customer.domain.agreement.WmAgreementModelDic" >
    insert into wm_agreement_model_dic (id, agree_model_name, current_version, 
      valid)
    values (#{id,jdbcType=INTEGER}, #{agreeModelName,jdbcType=VARCHAR}, #{currentVersion,jdbcType=INTEGER}, 
      #{valid,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.meituan.waimai.customer.domain.agreement.WmAgreementModelDic" >
    insert into wm_agreement_model_dic
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="agreeModelName != null" >
        agree_model_name,
      </if>
      <if test="currentVersion != null" >
        current_version,
      </if>
      <if test="valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="agreeModelName != null" >
        #{agreeModelName,jdbcType=VARCHAR},
      </if>
      <if test="currentVersion != null" >
        #{currentVersion,jdbcType=INTEGER},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.meituan.waimai.customer.domain.agreement.WmAgreementModelDic" >
    update wm_agreement_model_dic
    <set >
      <if test="agreeModelName != null" >
        agree_model_name = #{agreeModelName,jdbcType=VARCHAR},
      </if>
      <if test="currentVersion != null" >
        current_version = #{currentVersion,jdbcType=INTEGER},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.meituan.waimai.customer.domain.agreement.WmAgreementModelDic" >
    update wm_agreement_model_dic
    set agree_model_name = #{agreeModelName,jdbcType=VARCHAR},
      current_version = #{currentVersion,jdbcType=INTEGER},
      valid = #{valid,jdbcType=TINYINT}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="getCurrentVerisonById" parameterType="java.lang.Integer" resultType="java.lang.Integer">
    select current_version from wm_agreement_model_dic where id = #{id} and valid = 1
  </select>


</mapper>
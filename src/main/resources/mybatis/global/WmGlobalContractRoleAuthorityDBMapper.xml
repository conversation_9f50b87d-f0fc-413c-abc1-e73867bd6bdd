<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sankuai.meituan.waimai.customer.contract.dao.WmGlobalContractRoleAuthorityDBMapper">
    <resultMap id="BaseResultMap" type="com.sankuai.meituan.waimai.customer.domain.global.WmGlobalContractRoleAuthorityDB">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="contract_type_code" property="contractTypeCode" jdbcType="INTEGER"/>
        <result column="uac_role_code" property="uacRoleCode" jdbcType="VARCHAR"/>
        <result column="valid" property="valid" jdbcType="TINYINT"/>
        <result column="ctime" property="ctime" jdbcType="INTEGER"/>
        <result column="utime" property="utime" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List" >
        id, contract_type_code, uac_role_code, valid, ctime, utime
    </sql>

    <insert id="insertSelective" parameterType="com.sankuai.meituan.waimai.customer.domain.global.WmGlobalContractRoleAuthorityDB" >
        insert into wm_global_contract_role_authority
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="wmGlobalContractRoleAuthorityDB.id != null" >
                id,
            </if>
            <if test="wmGlobalContractRoleAuthorityDB.contractTypeCode != null" >
                contract_type_code,
            </if>
            <if test="wmGlobalContractRoleAuthorityDB.uacRoleCode != null" >
                uac_role_code,
            </if>
            <if test="wmGlobalContractRoleAuthorityDB.valid != null" >
                valid,
            </if>
            ctime,
            utime
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="wmGlobalContractRoleAuthorityDB.id != null" >
                #{wmGlobalContractRoleAuthorityDB.id,jdbcType=BIGINT},
            </if>
            <if test="wmGlobalContractRoleAuthorityDB.contractTypeCode != null" >
                #{wmGlobalContractRoleAuthorityDB.contractTypeCode,jdbcType=INTEGER},
            </if>
            <if test="wmGlobalContractRoleAuthorityDB.uacRoleCode != null" >
                #{wmGlobalContractRoleAuthorityDB.uacRoleCode,jdbcType=VARCHAR},
            </if>
            <if test="wmGlobalContractRoleAuthorityDB.valid != null" >
                #{wmGlobalContractRoleAuthorityDB.valid,jdbcType=TINYINT},
            </if>
            ctime = unix_timestamp(),
            utime = unix_timestamp()
        </trim>
    </insert>

    <update id="updateSelective" parameterType="com.sankuai.meituan.waimai.customer.domain.global.WmGlobalContractRoleAuthorityDB">
        update wm_global_contract_role_authority
        set
            <if test="wmGlobalContractRoleAuthorityDB.contractTypeCode != null" >
                contract_type_code = #{wmGlobalContractRoleAuthorityDB.contractTypeCode,jdbcType=INTEGER},
            </if>
            <if test="wmGlobalContractRoleAuthorityDB.uacRoleCode != null" >
                uac_role_code = #{wmGlobalContractRoleAuthorityDB.uacRoleCode,jdbcType=VARCHAR},
            </if>
            <if test="wmGlobalContractRoleAuthorityDB.valid != null" >
                valid = #{wmGlobalContractRoleAuthorityDB.valid,jdbcType=TINYINT},
            </if>
            utime = unix_timestamp()
        where id = #{wmGlobalContractRoleAuthorityDB.id,jdbcType=BIGINT}
    </update>

    <update id="deleteByContractTypeCodes" parameterType="java.util.List">
        update wm_global_contract_role_authority
        set valid = 0, utime = unix_timestamp()
        where contract_type_code in
        <foreach collection="contractTypeCodes" open="(" close=")" item="contractTypeCode" separator=",">
            #{contractTypeCode}
        </foreach>
    </update>

    <update id="deleteByIds" parameterType="java.util.List">
        update wm_global_contract_role_authority
        set valid = 0, utime = unix_timestamp()
        where id in
        <foreach collection="ids" open="(" close=")" item="id" separator=",">
            #{id}
        </foreach>
    </update>


    <select id="selectByContractTypeCodes" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from wm_global_contract_role_authority
        where valid = 1 and contract_type_code in
        <foreach item="contractTypeCode" index="index" collection="contractTypeCodes" open="(" separator="," close=")">
            #{contractTypeCode}
        </foreach>
    </select>

    <select id="selectTotalSize" resultType="java.lang.Integer">
        select count(1)
        from wm_global_contract_role_authority
        where valid = 1
    </select>

    <select id="selectByUacRoleIds"
            resultType="com.sankuai.meituan.waimai.customer.domain.global.WmGlobalContractRoleAuthorityDB">
        select
        <include refid="Base_Column_List"/>
        from wm_global_contract_role_authority
        where valid = 1 and uac_role_code in
        <foreach collection="uacRoleIds" index="index" item="roleId" open="(" separator="," close=")">
            #{roleId}
        </foreach>
    </select>


</mapper>
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sankuai.meituan.waimai.customer.dao.WmCustomerPoiDBMapper">

    <resultMap id="BaseResultMap" type="com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiDB">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="customer_id" property="customerId" jdbcType="INTEGER"/>
        <result column="wm_poi_id" property="wmPoiId" jdbcType="BIGINT"/>
        <result column="is_unbinding" property="isUnbinding" jdbcType="INTEGER"/>
        <result column="relation_status" property="relationStatus" jdbcType="INTEGER"/>
        <result column="switch_task_id" property="switchTaskId" jdbcType="BIGINT"/>
        <result column="biz_task_id" property="bizTaskId" jdbcType="BIGINT"/>
        <result column="valid" property="valid" jdbcType="INTEGER"/>
        <result column="ctime" property="ctime" jdbcType="INTEGER"/>
        <result column="utime" property="utime" jdbcType="INTEGER"/>
    </resultMap>

    <insert id="batchInsertCustomerPoi">
        insert into wm_customer_poi_rel (customer_id, wm_poi_id,is_unbinding,valid,
        ctime,utime,relation_status,switch_task_id)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.customerId},
            #{item.wmPoiId},#{item.isUnbinding},#{item.valid},UNIX_TIMESTAMP(),UNIX_TIMESTAMP(),#{item.relationStatus},#{item.switchTaskId})
        </foreach>
        ON DUPLICATE KEY UPDATE
        is_unbinding=values(is_unbinding),valid=values(valid),relation_status=values(relation_status),switch_task_id=values(switch_task_id),utime=UNIX_TIMESTAMP()
    </insert>

    <select id="selectCustomerPoiRT" resultType="java.lang.Integer">
        /*master*/select id from wm_customer_poi_rel where customer_id=#{customerId} and wm_poi_id=#{wmPoiId}
    </select>

    <insert id="insertCustomerPoi"
            parameterType="com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiDB">
        insert into wm_customer_poi_rel (customer_id, wm_poi_id,is_unbinding,valid,
        ctime,utime,relation_status,switch_task_id)
        values (#{customerId},#{wmPoiId},#{isUnbinding},#{valid},UNIX_TIMESTAMP(),UNIX_TIMESTAMP(),#{relationStatus},#{switchTaskId})
    </insert>
    <update id="updateCustomerPoi"
            parameterType="com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiDB">
        update wm_customer_poi_rel set is_unbinding=#{isUnbinding},valid=#{valid},relation_status=#{relationStatus},switch_task_id=#{switchTaskId},utime=UNIX_TIMESTAMP()
        where id=#{id}
    </update>

    <insert id="batchInsertCustomerPoiWithBizTaskId">
        insert into wm_customer_poi_rel (customer_id, wm_poi_id,is_unbinding,valid,
        ctime,utime,relation_status,switch_task_id,biz_task_id)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.customerId},
            #{item.wmPoiId},#{item.isUnbinding},#{item.valid},UNIX_TIMESTAMP(),UNIX_TIMESTAMP(),#{item.relationStatus},#{item.switchTaskId},
            #{item.bizTaskId})
        </foreach>
        ON DUPLICATE KEY UPDATE
        is_unbinding=values(is_unbinding),valid=values(valid),relation_status=values(relation_status),switch_task_id=values(switch_task_id),utime=UNIX_TIMESTAMP(),biz_task_id=values(biz_task_id)
    </insert>


    <insert id="insertCustomerPoiWithBizTaskId"
            parameterType="com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiDB">
        insert into wm_customer_poi_rel (customer_id, wm_poi_id,is_unbinding,valid,
        ctime,utime,relation_status,switch_task_id,biz_task_id)
        values (#{customerId},#{wmPoiId},#{isUnbinding},#{valid},UNIX_TIMESTAMP(),UNIX_TIMESTAMP(),#{relationStatus},#{switchTaskId}, #{bizTaskId})
    </insert>

    <update id="updateCustomerPoiWithBizTaskId"
            parameterType="com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiDB">
        update wm_customer_poi_rel set is_unbinding=#{isUnbinding},valid=#{valid},relation_status=#{relationStatus},switch_task_id=#{switchTaskId},utime=UNIX_TIMESTAMP(),biz_task_id=#{bizTaskId}
        where id=#{id}
    </update>


    <update id="deleteCustomerPoi">
        update wm_customer_poi_rel set valid=0,is_unbinding=0,relation_status=0,switch_task_id=0,utime=UNIX_TIMESTAMP()
        <if test="bizTaskId != null and bizTaskId>0">
            ,biz_task_id = #{bizTaskId}
        </if>
        where wm_poi_id =#{wmPoiId} and customer_id=#{customerId}
    </update>

    <update id="deleteCustoemrPoiByPrimaryKey" parameterType="long">
        update wm_customer_poi_rel set valid=0,is_unbinding=0,relation_status=0,switch_task_id=0,utime=UNIX_TIMESTAMP() where id = #{id}
    </update>

    <select id="countCustomerPoi" parameterType="java.lang.Integer" resultType="java.lang.Integer">
    select count(id) from wm_customer_poi_rel where customer_id=#{customerId} and valid=1
  </select>

    <select id="countCustomerPoiList" parameterType="java.util.List"
            resultType="com.sankuai.meituan.waimai.customer.domain.WmCustomerListDB">
        select customer_id as id,count(id) as wmPoiCount from wm_customer_poi_rel where valid=1 and customer_id in
        <foreach collection="customerIds" item="item" index="index" separator="," open="(" close=")">
            #{item}
        </foreach>
        group by customer_id
    </select>

    <select id="countUnBindingPoi" parameterType="java.lang.Integer" resultType="java.lang.Integer">
    select count(id) from wm_customer_poi_rel where customer_id=#{customerId} and is_unbinding = 1 and valid=1
  </select>

    <select id="selectExistPoiByWmPoiId" resultType="java.lang.Long">
        /*master*/select wm_poi_id from wm_customer_poi_rel where wm_poi_id IN
        <foreach item="item" index="index" collection="set" open="(" separator="," close=")">
            #{item}
        </foreach>
        and valid=1
    </select>

    <select id="selectExistPoiByWmPoiIdForPreBind" resultType="java.lang.Long">
        /*master*/select wm_poi_id from wm_customer_poi_rel where wm_poi_id IN
        <foreach item="item" index="index" collection="set" open="(" separator="," close=")">
            #{item}
        </foreach>
        and valid=0 and relation_status in (5,6,7)
    </select>

    <select id="selectWmPoiIdsByCustomerId" parameterType="java.lang.Integer" resultType="java.lang.Long">
    select wm_poi_id from wm_customer_poi_rel where customer_id = #{customerId}  and valid=1
  </select>

    <select id="selectWmPoiIdsExcludeUnbinding" parameterType="java.lang.Integer" resultType="java.lang.Long">
    select wm_poi_id from wm_customer_poi_rel where customer_id = #{customerId}  and valid=1 and is_unbinding=0
  </select>

    <select id="selectWmCustomerPoiByCustomerId" parameterType="java.lang.Integer"
            resultMap="BaseResultMap">
    select id, customer_id, wm_poi_id,relation_status,switch_task_id,is_unbinding from wm_customer_poi_rel where customer_id = #{customerId}  and valid=1
  </select>

    <select id="selectWmPoiIdsByCustomerIds" resultType="java.lang.Long">
        select wm_poi_id from wm_customer_poi_rel where customer_id
        in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
        and valid=1
    </select>


    <select id="selectCustomerIdByPoiId" resultType="java.lang.Integer">
        select customer_id from wm_customer_poi_rel where wm_poi_id IN
        <foreach item="item" index="index" collection="wmPoiIdSet" open="(" separator="," close=")">
            #{item}
        </foreach>
        and valid=1
    </select>

    <select id="selectCustomerIdByOneWmPoiId" resultType="java.lang.Integer">
      select customer_id from wm_customer_poi_rel
      where wm_poi_id = #{wmPoiId}
      and valid=1 order by id desc limit 1;
    </select>

    <select id="selectWmCustomerPoiByCustomerIds" resultMap="BaseResultMap">
        select id, customer_id, wm_poi_id,relation_status,switch_task_id from wm_customer_poi_rel where customer_id IN
        <foreach item="item" index="index" collection="customerIdSet" open="(" separator="," close=")">
            #{item}
        </foreach>
        and valid=1
    </select>

    <select id="selectWmCustomerPoiByCustomerIdList" parameterType="list"
            resultType="com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiDB">
        select customer_id as customerId, wm_poi_id as wmPoiId,is_unbinding as isUnbinding from wm_customer_poi_rel
        where customer_id in
        <foreach collection="customerIds" item="item" index="index" separator="," open="(" close=")">
            #{item}
        </foreach>
        and valid=1
    </select>

    <select id="selectWmCustomerPoiByWmPoiIdList" resultMap="BaseResultMap">
        select id, customer_id, wm_poi_id from wm_customer_poi_rel where wm_poi_id IN
        <foreach item="item" index="index" collection="wmPoiIdList" open="(" separator="," close=")">
            #{item}
        </foreach>
        and valid=1
    </select>

    <select id="selectCustomerPoiByWmPoiId" resultType="java.lang.Integer">
        select customer_id from wm_customer_poi_rel
        where customer_id = #{customerId}
        and wm_poi_id = #{wmPoiId}
        and is_unbinding = 0
        and valid = 1
    </select>

    <select id="selectWmPoiCountByCustomerId" resultType="java.lang.Integer">
        select count(1) from wm_customer_poi_rel
        where customer_id = #{customerId}
        <choose>
            <when test="wmPoiIdList != null and wmPoiIdList.size() > 0">
                and wm_poi_id in
                <foreach item="item" index="index" collection="wmPoiIdList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                and 1=0
            </otherwise>
        </choose>
        and is_unbinding = 0
        and valid = 1
    </select>

    <select id="selectWmPoIIdByCustomerIdAndRelationStatus" resultType="java.lang.Long">
        select wm_poi_id from wm_customer_poi_rel
        where customer_id = #{customerId}
        and relation_status = #{relationStatus}
        and valid = 1
    </select>

    <update id="batchUpdateCustomerPoiRelation">
        update wm_customer_poi_rel set
        <if test="isUnbinding!=null">
            is_unbinding=#{isUnbinding},
        </if>
        <if test="relationStatus!=null">
            relation_status=#{relationStatus},
        </if>
        <if test="switchTaskId!=null">
            switch_task_id=#{switchTaskId},
        </if>
        utime=UNIX_TIMESTAMP()

        where wm_poi_id IN
        <foreach item="item" index="index" collection="wmPoiIdSet" open="(" separator="," close=")">
            #{item}
        </foreach>
        and customer_id=#{customerId}
        and valid = 1
    </update>

    <update id="batchCancelUnBind">
        update wm_customer_poi_rel set
        <if test="isUnbinding!=null">
            is_unbinding=#{isUnbinding},
        </if>
        <if test="relationStatus!=null">
            relation_status=#{relationStatus},
        </if>
        utime=UNIX_TIMESTAMP()

        where wm_poi_id IN
        <foreach item="item" index="index" collection="wmPoiIdList" open="(" separator="," close=")">
            #{item}
        </foreach>
        and customer_id=#{customerId}
        and valid = 1
    </update>

    <update id="batchUpdateCustomerPoiRelationByParams">
        update wm_customer_poi_rel set
        <if test="isUnbinding!=null">
            is_unbinding=#{isUnbinding},
        </if>
        <if test="relationStatus!=null">
            relation_status=#{relationStatus},
        </if>
        <if test="switchTaskId!=null">
            switch_task_id=#{switchTaskId},
        </if>
        utime=UNIX_TIMESTAMP()

        where wm_poi_id IN
        <foreach item="item" index="index" collection="wmPoiIdList" open="(" separator="," close=")">
            #{item}
        </foreach>
        and customer_id=#{customerId}
        and valid = 1
    </update>

    <update id="batchUpdateCustomerPoiRelationByList">
        <foreach collection="list" index="index" item="item" separator=";">
            update wm_customer_poi_rel set
            <if test="item.isUnbinding!=null">
                is_unbinding=#{item.isUnbinding},
            </if>
            <if test="item.relationStatus!=null">
                relation_status=#{item.relationStatus},
            </if>
            <if test="item.switchTaskId!=null">
                switch_task_id=#{item.switchTaskId},
            </if>
            <if test="item.bizTaskId!=null and item.bizTaskId>0">
                biz_task_id=#{item.bizTaskId},
            </if>
            utime=UNIX_TIMESTAMP()
            where wm_poi_id = #{item.wmPoiId}
            and customer_id=#{item.customerId}
            and valid = 1
        </foreach>
    </update>

    <update id="batchUpdateCustomerPoiRelationFromPreBindingToBind" parameterType="map">
        update wm_customer_poi_rel set
        <if test="isUnbinding!=null">
            is_unbinding=#{isUnbinding},
        </if>
        relation_status=4,
        <if test="switchTaskId!=null">
            switch_task_id=#{switchTaskId},
        </if>
        valid = 1,
        utime=UNIX_TIMESTAMP()

        where wm_poi_id IN
        <foreach item="item" index="index" collection="wmPoiIdSet" open="(" separator="," close=")">
            #{item}
        </foreach>
        and customer_id=#{customerId}
        and valid = 0 and relation_status in (5,6,7)
        <if test="switchTaskId!=null">
            and switch_task_id = #{switchTaskId}
        </if>
    </update>

    <update id="batchUpdateCustomerPoiRelationToPreBinding" parameterType="map">
        update wm_customer_poi_rel set
        <if test="isUnbinding!=null">
            is_unbinding=#{isUnbinding},
        </if>
        relation_status=6,
        <if test="switchTaskId!=null">
            switch_task_id=#{switchTaskId},
        </if>
        utime=UNIX_TIMESTAMP()

        where wm_poi_id IN
        <foreach item="item" index="index" collection="wmPoiIdSet" open="(" separator="," close=")">
            #{item}
        </foreach>
        and customer_id=#{customerId}
        and valid = 0 and relation_status in (5,7)
        <if test="switchTaskId!=null">
            and switch_task_id = #{switchTaskId}
        </if>
    </update>

    <update id="batchUpdateCustomerPoiRelationToPreBindingList">
        <foreach collection="list" index="index" item="item" separator=";">
            update wm_customer_poi_rel set
            <if test="item.isUnbinding!=null">
                is_unbinding=#{item.isUnbinding},
            </if>
            relation_status=6,
            <if test="item.switchTaskId!=null">
                switch_task_id=#{item.switchTaskId},
            </if>
            <if test="item.bizTaskId!=null and item.bizTaskId>0">
                biz_task_id=#{item.bizTaskId},
            </if>
            utime=UNIX_TIMESTAMP()
            where wm_poi_id = #{item.wmPoiId}
            and customer_id=#{item.customerId}
            and valid = 0 and relation_status in (5,7)
            <if test="item.switchTaskId!=null">
                and switch_task_id = #{item.switchTaskId}
            </if>
        </foreach>
    </update>

    <update id="batchUpdateCustomerPoiRelationToCancelPreBinding" parameterType="map">
        update wm_customer_poi_rel set
        <if test="isUnbinding!=null">
            is_unbinding=#{isUnbinding},
        </if>
        relation_status = 7,
        <if test="switchTaskId!=null">
            switch_task_id=#{switchTaskId},
        </if>
        utime=UNIX_TIMESTAMP()

        where wm_poi_id IN
        <foreach item="item" index="index" collection="wmPoiIdSet" open="(" separator="," close=")">
            #{item}
        </foreach>
        and customer_id=#{customerId}
        and valid = 0 and relation_status = 6
        <if test="switchTaskId!=null">
            and switch_task_id = #{switchTaskId}
        </if>
    </update>

    <update id="batchUpdateCustomerPoiRelationToReadyBind" parameterType="map">
        update wm_customer_poi_rel set
        <if test="isUnbinding!=null">
            is_unbinding=#{isUnbinding},
        </if>
        relation_status = 3,
        <if test="switchTaskId!=null">
            switch_task_id=#{switchTaskId},
        </if>
        utime=UNIX_TIMESTAMP()
        where wm_poi_id IN
        <foreach item="item" index="index" collection="wmPoiIdSet" open="(" separator="," close=")">
            #{item}
        </foreach>
        and customer_id=#{customerId}
        and valid = 0
        <if test="switchTaskId!=null">
            and switch_task_id = #{switchTaskId}
        </if>
    </update>

    <select id="selectCustomerPoiRelByCondition"
            parameterType="com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiQueryCondtionVo"
            resultMap="BaseResultMap">
        select id, customer_id, wm_poi_id,relation_status,switch_task_id,valid,ctime,utime from wm_customer_poi_rel
        where 1=1
        <if test="allData==null">
            and (valid = 1 or (valid = 0 and relation_status in (3,5,6,7)))
        </if>
        <if test="customerId!=null">
            and customer_id = #{customerId}
        </if>
        <if test="wmPoiId!=null">
            and wm_poi_id = #{wmPoiId}
        </if>
        <if test="bindStatus!=null">
            and relation_status=#{bindStatus}
        </if>
        <if test="switchCustomerType!=null">
            <if test="switchCustomerType==1">
                and switch_task_id > 0
            </if>
            <if test="switchCustomerType==2">
                and switch_task_id = 0
            </if>
        </if>
        <if test="minId!=null">
            and id>#{minId}
        </if>
        <if test="maxId!=null">
            and id &lt;=#{maxId}
        </if>
        <if test="wmPoiIds != null and wmPoiIds.size() > 0">
            and wm_poi_id in
            <foreach item="item" index="index" collection="wmPoiIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectCustomerPoiRelRT" resultMap="BaseResultMap">
       /*master*/select id, customer_id, wm_poi_id,relation_status,switch_task_id from wm_customer_poi_rel
        where customer_id = #{customerId}
        and wm_poi_id = #{wmPoiId}
        and valid = 1
    </select>

    <select id="getWmPoiIdsByCustomerId" parameterType="java.lang.Integer" resultType="java.lang.Long">
        select wm_poi_id from wm_customer_poi_rel where customer_id = #{customerId}  and valid=1
        limit #{from},#{size}
    </select>

    <select id="getWmPoiIdsByCustomerIdFromIndexId" parameterType="java.lang.Integer" resultType="java.lang.Long">
        select wm_poi_id from wm_customer_poi_rel where customer_id = #{customerId}  and valid=1 and id > #{indexId}
        order by id asc limit #{size}
    </select>

    <select id="queryWmPoiIdsByCustomerIdByWmPoiId" parameterType="java.lang.Integer" resultType="java.lang.Long">
        select wm_poi_id from wm_customer_poi_rel where customer_id = #{customerId}  and valid=1 and wm_poi_id > #{wmPoiId}
        order by wm_poi_id asc limit #{size}
    </select>

    <select id="getWmPoiIdsByCustomerIdFromIndexIdForceIndex" parameterType="java.lang.Integer"
            resultType="java.lang.Long">
        select wm_poi_id from wm_customer_poi_rel USE INDEX(idx_customer_valid_id_poi) where
        customer_id = #{customerId}  and valid=1 and id > #{indexId}
        order by id asc limit #{size}
    </select>

    <select id="hasMoreThanOnePoiByCustomerId" resultType="java.lang.Boolean">
        select exists(
        select 1 from wm_customer_poi_rel
        where customer_id = #{customerId} and valid = 1
        limit 1 offset 1
        ) as hasMore
    </select>

    <select id="getWmCustomerPoiByCustomerIds" resultMap="BaseResultMap">
        select id, customer_id, wm_poi_id,relation_status,switch_task_id from wm_customer_poi_rel where customer_id IN
        <foreach item="item" index="index" collection="customerIdSet" open="(" separator="," close=")">
            #{item}
        </foreach>
        and valid=1
        limit #{from},#{size}
    </select>

    <update id="deleteReadyBindPois">
        update wm_customer_poi_rel set valid=0,is_unbinding=0,relation_status=0,switch_task_id=0,utime=UNIX_TIMESTAMP()
        where relation_status in (3,5,6,7) and valid=0 and wm_poi_id IN
        <foreach item="item" index="index" collection="wmPoiIds" open="(" separator="," close=")">
            #{item}
        </foreach>
        and customer_id=#{customerId}
        and switch_task_id =#{switchTaskId}
    </update>

    <select id="getWmCustomerPoiForPreBind" resultMap="BaseResultMap" parameterType="map">
        select id, customer_id, wm_poi_id,relation_status,switch_task_id,is_unbinding,valid
        from wm_customer_poi_rel
        where customer_id = #{customerId}
        and wm_poi_id in
        <foreach collection="wmPoiIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and relation_status in
        <foreach collection="relationStatusList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and valid=0
    </select>

    <select id="getPreBindPoiByCustomerId" resultMap="BaseResultMap" parameterType="map">
        select id, customer_id, wm_poi_id,relation_status,switch_task_id,is_unbinding,valid
        from wm_customer_poi_rel
        where customer_id = #{customerId}
        and relation_status in
        <foreach collection="relationStatusList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and valid=0
    </select>

    <select id="selectCustomerPoiRelByWmPoiIdWithValid" resultMap="BaseResultMap" parameterType="long">
        select id, customer_id, wm_poi_id,relation_status,switch_task_id,is_unbinding,valid
        from wm_customer_poi_rel
        where wm_poi_id = #{wmPoiId}
        and valid = 1 order by id desc limit 1
    </select>

    <select id="selectCustomerPoiRelByWmPoiIdWithUnValidAndPreBindStatus" resultMap="BaseResultMap" parameterType="long">
        select id, customer_id, wm_poi_id,relation_status,switch_task_id,is_unbinding,valid
        from wm_customer_poi_rel
        where wm_poi_id = #{wmPoiId}
          and valid = 0
          and relation_status in (5,6,7)
        order by id desc limit 1
    </select>

    <select id="selectOnlyRelByCustomerId" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select id, customer_id, wm_poi_id,relation_status,switch_task_id,is_unbinding,valid
        from wm_customer_poi_rel
        where customer_id = #{customerId}
          and valid = 1
        order by id desc
    </select>

    <select id="selectNotCustomerIdByWmPoiIdsRT" resultMap="BaseResultMap">
    /*master*/select id, customer_id, wm_poi_id,relation_status,switch_task_id,is_unbinding,valid from wm_customer_poi_rel where wm_poi_id IN
        <foreach item="item" index="index" collection="wmPoiIdSet" open="(" separator="," close=")">
            #{item}
        </foreach>
        and valid=1 and customer_id != #{customerId}
    </select>


    <select id="selectCustomerPoiRelByWmPoiIdRT" resultMap="BaseResultMap">
        /*master*/select id, customer_id, wm_poi_id,relation_status,switch_task_id,is_unbinding,valid from wm_customer_poi_rel where
        wm_poi_id = #{wmPoiId}
        and valid=1
    </select>
    <select id="selectUnBindingPoi" parameterType="java.lang.Integer" resultType="java.lang.Long">
    select wm_poi_id from wm_customer_poi_rel where customer_id=#{customerId} and is_unbinding = 1 and valid=1
  </select>

    <select id="selectUnbindingCustomerPoiRelByIdSection"
            resultType="com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiDB">
        select
        id, customer_id as customerId, wm_poi_id as wmPoiId,relation_status as relationStatus,switch_task_id as
        switchTaskId,is_unbinding as isUnbinding,valid
        from wm_customer_poi_rel
        where id &gt;= #{idStart} and id &lt;= #{idEnd} and is_unbinding=1 and relation_status &lt;= 1 and valid = 1 limit #{pageSize}
    </select>

    <select id="selectIdByWmPoiIdAndCustomerId" resultType="java.lang.Integer">
        select id from wm_customer_poi_rel
        where customer_id = #{customerId}
        and wm_poi_id = #{wmPoiId}
        and valid = 1
    </select>

    <select id="getValidRelByCustomerIdAndWmPoiId" resultMap="BaseResultMap">
        select id, customer_id, wm_poi_id, relation_status,switch_task_id,is_unbinding from wm_customer_poi_rel
        where customer_id = #{customerId}
        and wm_poi_id = #{wmPoiId}
        and valid = 1
    </select>
    <update id="unBindPreBindFail">
        update wm_customer_poi_rel set valid=0,is_unbinding=0,relation_status=0,switch_task_id=0,utime=UNIX_TIMESTAMP()
        where relation_status=7 and wm_poi_id =#{wmPoiId}
        and customer_id=#{customerId}
    </update>

    <select id="listNotSwitchBindPoiByCustomerId" resultMap="BaseResultMap">
        select id, customer_id, wm_poi_id,relation_status,switch_task_id,is_unbinding,valid
        where customer_id = #{customerId} and valid=1
        and switch_task_id=0
    </select>
    <select id="listCustomerPoiRelByWmPoiId" resultMap="BaseResultMap">
        select id, customer_id, wm_poi_id,relation_status,switch_task_id,is_unbinding,valid from wm_customer_poi_rel
        where
        wm_poi_id = #{wmPoiId}
        and (valid=1 or (valid=0 and relation_status in(3,5,6,7)))
    </select>
    <select id="listBindingNotSwitchPoi" resultType="long">
        select wm_poi_id
        from wm_customer_poi_rel
        where customer_id =  #{customerId}
        and relation_status in(3,5,6,7)
        and switch_task_id = 0
    </select>
    <select id="listBindNotSwitchPoi" resultType="long">
        select wm_poi_id
        from wm_customer_poi_rel
        where customer_id =  #{customerId}
        and relation_status in(1,2,4)
        and switch_task_id = 0
    </select>
    <select id="listBindingOrBindPoiByCustomerId" resultMap="BaseResultMap">
        select id, customer_id, wm_poi_id,relation_status,switch_task_id
        from wm_customer_poi_rel
        where customer_id = #{customerId}
        and (valid = 1 or (valid=0 and relation_status in(3,5,6,7)))
        <if test="minId != null and minId > 0">
            and id > #{minId}
        </if>
        order by id asc
        limit #{size}
    </select>
    <select id="countBindingOrBindPoiByCustomerId" resultType="integer">
        select count(id)
        from wm_customer_poi_rel
        where customer_id = #{customerId}
        and (valid = 1 or (valid=0 and relation_status in(3,5,6,7)))
    </select>

    <select id="listBindWmPoiIdsByCustomerId" resultType="long">
        select wm_poi_id from wm_customer_poi_rel
        where customer_id = #{customerId}
        and valid = 1
    </select>

    <select id="countCustomerPoiWithOutValid" parameterType="java.lang.Integer" resultType="java.lang.Integer">
        select count(id) from wm_customer_poi_rel where customer_id=#{customerId}
    </select>
</mapper>

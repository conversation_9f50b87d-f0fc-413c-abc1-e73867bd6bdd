<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sankuai.meituan.waimai.customer.dao.sc.delivery.WmSchoolDeliveryStreamMapper">
    <resultMap id="BaseResultMap" type="com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmSchoolDeliveryStreamDO">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="school_primary_id" property="schoolPrimaryId" jdbcType="INTEGER"/>
        <result column="stream_node" property="streamNode" jdbcType="INTEGER"/>
        <result column="delivery_end" property="deliveryEnd" jdbcType="INTEGER"/>
        <result column="valid" property="valid" jdbcType="TINYINT"/>
        <result column="cuid" property="cuid" jdbcType="BIGINT"/>
        <result column="muid" property="muid" jdbcType="BIGINT"/>
        <result column="ctime" property="ctime" jdbcType="INTEGER"/>
        <result column="utime" property="utime" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, school_primary_id, stream_node, delivery_end, valid, cuid, muid, ctime, utime
    </sql>

    <select id="selectByPrimaryKey"
            parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List"/>
        from
            wm_school_delivery_stream
        where
            id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectBySchoolPrimaryId"
            parameterType="java.lang.Integer"
            resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
        from
            wm_school_delivery_stream
        where
            school_primary_id = #{schoolPrimaryId,jdbcType=INTEGER}
        and
            valid = 1
    </select>

    <select id="selectBySchoolPrimaryIdOnStram"
            parameterType="java.lang.Integer"
            resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
        from
            wm_school_delivery_stream
        where
            school_primary_id = #{schoolPrimaryId,jdbcType=INTEGER}
        and
            valid = 1
        and
            delivery_end = 0
    </select>

    <select id="selectOnStreamDeliveryByDeliveryIdList"
            resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
        from
            wm_school_delivery_stream
        where
            id in
            <foreach collection="deliveryIdList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        and
            valid = 1
        and
            delivery_end = 0
    </select>

    <insert id="insertSelective"
            parameterType="com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmSchoolDeliveryStreamDO"
            useGeneratedKeys="true"
            keyProperty="id">
        insert into
            wm_school_delivery_stream
        <trim prefix="(" suffix=")" suffixOverrides=",">
            ctime,
            utime,
            <if test="id != null">
                id,
            </if>
            <if test="schoolPrimaryId != null">
                school_primary_id,
            </if>
            <if test="streamNode != null">
                stream_node,
            </if>
            <if test="deliveryEnd != null">
                delivery_end,
            </if>
            <if test="valid != null">
                valid,
            </if>
            <if test="cuid != null">
                cuid,
            </if>
            <if test="muid != null">
                muid,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            UNIX_TIMESTAMP(),
            UNIX_TIMESTAMP(),
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="schoolPrimaryId != null">
                #{schoolPrimaryId,jdbcType=INTEGER},
            </if>
            <if test="streamNode != null">
                #{streamNode,jdbcType=INTEGER},
            </if>
            <if test="deliveryEnd != null">
                #{deliveryEnd,jdbcType=INTEGER},
            </if>
            <if test="valid != null">
                #{valid,jdbcType=INTEGER},
            </if>
            <if test="cuid != null">
                #{cuid,jdbcType=BIGINT},
            </if>
            <if test="muid != null">
                #{muid,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective"
            parameterType="com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmSchoolDeliveryStreamDO">
        update
            wm_school_delivery_stream
        <set>
            utime = UNIX_TIMESTAMP(),
            <if test="schoolPrimaryId != null">
                school_primary_id = #{schoolPrimaryId,jdbcType=INTEGER},
            </if>
            <if test="streamNode != null">
                stream_node = #{streamNode,jdbcType=INTEGER},
            </if>
            <if test="deliveryEnd != null">
                delivery_end = #{deliveryEnd,jdbcType=INTEGER},
            </if>
            <if test="valid != null">
                valid = #{valid,jdbcType=INTEGER},
            </if>
            <if test="cuid != null">
                cuid = #{cuid,jdbcType=BIGINT},
            </if>
            <if test="muid != null">
                muid = #{muid,jdbcType=BIGINT},
            </if>
        </set>
        where
            id = #{id,jdbcType=BIGINT}
    </update>

</mapper>
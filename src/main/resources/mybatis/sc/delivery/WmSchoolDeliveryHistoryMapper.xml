<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sankuai.meituan.waimai.customer.dao.sc.delivery.WmSchoolDeliveryHistoryMapper">
    <resultMap id="BaseResultMap" type="com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmSchoolDeliveryHistoryDO">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="delivery_id" property="deliveryId" jdbcType="INTEGER"/>
        <result column="school_primary_id" property="schoolPrimaryId" jdbcType="INTEGER"/>
        <result column="school_name" property="schoolName" jdbcType="VARCHAR"/>
        <result column="school_type" property="schoolType" jdbcType="INTEGER"/>
        <result column="tea_stu_num" property="teaStuNum" jdbcType="INTEGER"/>
        <result column="school_level" property="schoolLevel" jdbcType="INTEGER"/>
        <result column="aor_id" property="aorId" jdbcType="VARCHAR"/>
        <result column="agreement_type" property="agreementType" jdbcType="INTEGER"/>
        <result column="agreement_time_start" property="agreementTimeStart" jdbcType="INTEGER"/>
        <result column="agreement_time_end" property="agreementTimeEnd" jdbcType="INTEGER"/>
        <result column="delivery_end_time" property="deliveryEndTime" jdbcType="INTEGER"/>
        <result column="school_owner_uid" property="schoolOwnerUid" jdbcType="INTEGER"/>
        <result column="csm_uid" property="csmUid" jdbcType="INTEGER"/>
        <result column="aorm_uid" property="aormUid" jdbcType="INTEGER"/>
        <result column="acm_uid" property="acmUid" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, delivery_id, school_primary_id, school_name, school_type, tea_stu_num, school_level, aor_id,
        agreement_type, agreement_time_start, agreement_time_end, delivery_end_time, school_owner_uid,
        csm_uid, aorm_uid, acm_uid
    </sql>

    <select id="selectByPrimaryKey"
            parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List"/>
        from
            wm_school_delivery_history
        where
            id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectBySchoolPrimaryId"
            parameterType="java.lang.Integer"
            resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
        from
            wm_school_delivery_history
        where
            school_primary_id = #{schoolPrimaryId,jdbcType=INTEGER}
    </select>

    <select id="selectByDeliveryId"
            parameterType="java.lang.Integer"
            resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
        from
            wm_school_delivery_history
        where
            delivery_id = #{deliveryId,jdbcType=INTEGER}
    </select>

    <insert id="insertSelective"
            parameterType="com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmSchoolDeliveryHistoryDO"
            useGeneratedKeys="true"
            keyProperty="id">
        insert into
            wm_school_delivery_history
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="deliveryId != null">
                delivery_id,
            </if>
            <if test="schoolPrimaryId != null">
                school_primary_id,
            </if>
            <if test="schoolName != null">
                school_name,
            </if>
            <if test="schoolType != null">
                school_type,
            </if>
            <if test="teaStuNum != null">
                tea_stu_num,
            </if>
            <if test="schoolLevel != null">
                school_level,
            </if>
            <if test="aorId != null">
                aor_id,
            </if>
            <if test="agreementType != null">
                agreement_type,
            </if>
            <if test="agreementTimeStart != null">
                agreement_time_start,
            </if>
            <if test="agreementTimeEnd != null">
                agreement_time_end,
            </if>
            <if test="deliveryEndTime != null">
                delivery_end_time,
            </if>
            <if test="schoolOwnerUid != null">
                school_owner_uid,
            </if>
            <if test="csmUid != null">
                csm_uid,
            </if>
            <if test="aormUid != null">
                aorm_uid,
            </if>
            <if test="acmUid != null">
                acm_uid,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="deliveryId != null">
                #{deliveryId,jdbcType=INTEGER},
            </if>
            <if test="schoolPrimaryId != null">
                #{schoolPrimaryId,jdbcType=INTEGER},
            </if>
            <if test="schoolName != null">
                #{schoolName,jdbcType=VARCHAR},
            </if>
            <if test="schoolType != null">
                #{schoolType,jdbcType=INTEGER},
            </if>
            <if test="teaStuNum != null">
                #{teaStuNum,jdbcType=INTEGER},
            </if>
            <if test="schoolLevel != null">
                #{schoolLevel,jdbcType=INTEGER},
            </if>
            <if test="aorId != null">
                #{aorId,jdbcType=INTEGER},
            </if>
            <if test="agreementType != null">
                #{agreementType,jdbcType=INTEGER},
            </if>
            <if test="agreementTimeStart != null">
                #{agreementTimeStart,jdbcType=INTEGER},
            </if>
            <if test="agreementTimeEnd != null">
                #{agreementTimeEnd,jdbcType=INTEGER},
            </if>
            <if test="deliveryEndTime != null">
                #{deliveryEndTime,jdbcType=INTEGER},
            </if>
            <if test="schoolOwnerUid != null">
                #{schoolOwnerUid,jdbcType=INTEGER},
            </if>
            <if test="csmUid != null">
                #{csmUid,jdbcType=INTEGER},
            </if>
            <if test="aormUid != null">
                #{aormUid,jdbcType=INTEGER},
            </if>
            <if test="acmUid != null">
                #{acmUid,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

</mapper>
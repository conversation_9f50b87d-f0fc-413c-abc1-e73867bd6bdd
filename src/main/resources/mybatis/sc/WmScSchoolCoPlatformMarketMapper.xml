<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sankuai.meituan.waimai.customer.dao.sc.WmScSchoolCoPlatformMarketMapper">
    <resultMap id="BaseResultMap" type="com.sankuai.meituan.waimai.customer.domain.sc.WmScSchoolCoPlatformMarketDO">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="platform_primary_id" property="platformPrimaryId" jdbcType="INTEGER"/>
        <result column="platform_marketing_activity" property="platformMarketingActivity" jdbcType="INTEGER"/>
        <result column="platform_marketing_activity_info" property="platformMarketingActivityInfo" jdbcType="VARCHAR"/>
        <result column="activity_rule_description" property="activityRuleDescription" jdbcType="VARCHAR"/>
        <result column="activity_pic" property="activityPic" jdbcType="VARCHAR"/>
        <result column="activity_cost_sharing_type" property="activityCostSharingType" jdbcType="INTEGER"/>
        <result column="activity_cost_sharing_poi_min" property="activityCostSharingPoiMin" jdbcType="VARCHAR"/>
        <result column="activity_cost_sharing_poi_max" property="activityCostSharingPoiMax" jdbcType="VARCHAR"/>
        <result column="activity_cost_sharing_platform_min" property="activityCostSharingPlatformMin" jdbcType="VARCHAR"/>
        <result column="activity_cost_sharing_platform_max" property="activityCostSharingPlatformMax" jdbcType="VARCHAR"/>
        <result column="valid" property="valid" jdbcType="TINYINT"/>
        <result column="cuid" property="cuid" jdbcType="BIGINT"/>
        <result column="muid" property="muid" jdbcType="BIGINT"/>
        <result column="ctime" property="ctime" jdbcType="INTEGER"/>
        <result column="utime" property="utime" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, platform_primary_id, platform_marketing_activity, platform_marketing_activity_info, activity_rule_description,
        activity_pic, activity_cost_sharing_type,
        activity_cost_sharing_poi_min, activity_cost_sharing_poi_max,
        activity_cost_sharing_platform_min, activity_cost_sharing_platform_max,
        valid, cuid, muid, ctime, utime
    </sql>

    <select id="selectByPrimaryKey"
            parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        wm_school_cooperation_platform_market
        where
        id = #{id,jdbcType=BIGINT}
        and
        valid = 1
    </select>

    <select id="selectByPlatformPrimaryId"
            parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        wm_school_cooperation_platform_market
        where
        platform_primary_id = #{platformPrimaryId,jdbcType=BIGINT}
        and
        valid = 1
        order by id;
    </select>

    <insert id="batchInsertSelective"
            parameterType="java.util.List">
        insert into
        wm_school_cooperation_platform_market
        (platform_primary_id,
        platform_marketing_activity,
        platform_marketing_activity_info,
        activity_rule_description,
        activity_pic,
        activity_cost_sharing_type,
        activity_cost_sharing_poi_min,
        activity_cost_sharing_poi_max,
        activity_cost_sharing_platform_min,
        activity_cost_sharing_platform_max,
        valid,
        cuid,
        muid,
        ctime,
        utime)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.platformPrimaryId},
            #{item.platformMarketingActivity},
            #{item.platformMarketingActivityInfo},
            #{item.activityRuleDescription},
            #{item.activityPic},
            #{item.activityCostSharingType},
            #{item.activityCostSharingPoiMin},
            #{item.activityCostSharingPoiMax},
            #{item.activityCostSharingPlatformMin},
            #{item.activityCostSharingPlatformMax},
            1,
            #{item.cuid},
            #{item.muid},
            UNIX_TIMESTAMP(),
            UNIX_TIMESTAMP()
            )
        </foreach>
    </insert>

    <update id="invalidByPlatformPrimaryKey"
            parameterType="java.lang.Long">
        update
        wm_school_cooperation_platform_market
        set
        utime = UNIX_TIMESTAMP(),
        valid = 0,
        muid = #{muid,jdbcType=BIGINT}
        where
        platform_primary_id = #{platformPrimaryId,jdbcType=BIGINT}
    </update>

    <update id="batchInvalidByPrimaryKey"
            parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" separator=";">
            update
            wm_school_cooperation_platform_market
            set
            utime = UNIX_TIMESTAMP(),
            valid = 0,
            muid = #{muid,jdbcType=BIGINT}
            where
            id = #{item.id}
        </foreach>
    </update>

</mapper>
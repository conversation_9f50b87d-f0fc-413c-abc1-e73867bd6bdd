<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sankuai.meituan.waimai.customer.dao.sc.WmScCanteenPoiLabelMapper">
    <resultMap id="BaseResultMap" type="com.sankuai.meituan.waimai.customer.domain.sc.WmScCanteenPoiLabelDB">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="canteen_id" property="canteenId" jdbcType="INTEGER"/>
        <result column="wm_poi_id" property="wmPoiId" jdbcType="BIGINT"/>
        <result column="label_id" property="labelId" jdbcType="BIGINT"/>
        <result column="label_type" property="labelType" jdbcType="INTEGER"/>
        <result column="valid" property="valid" jdbcType="INTEGER"/>
        <result column="utime" property="utime" jdbcType="INTEGER"/>
        <result column="ctime" property="ctime" jdbcType="INTEGER"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" suffix=")" prefixOverrides="and">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach collection="criterion.value" item="listItem" open="(" close=")"
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" suffix=")" prefixOverrides="and">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach collection="criterion.value" item="listItem" open="(" close=")"
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
    id, canteen_id, wm_poi_id, label_id, label_type, valid, utime, ctime
  </sql>

    <select id="selectByExample" resultMap="BaseResultMap"
            parameterType="com.sankuai.meituan.waimai.customer.domain.sc.WmScCanteenPoiLabelDBExample">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from wm_sc_canteen_poi_label
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from wm_sc_canteen_poi_label
    where id = #{id,jdbcType=BIGINT}
  </delete>

    <select id="getLabelsByPoiIdList"
            resultMap="BaseResultMap"
            resultType="com.sankuai.meituan.waimai.customer.domain.sc.WmScCanteenPoiLabelDB">
        select
        <include refid="Base_Column_List"/>
        from wm_sc_canteen_poi_label
        where valid = 1 and label_type = #{labelType}
        <if test="wmPoiIdList != null and wmPoiIdList.size>0">
            and wm_poi_id in
            <foreach collection="wmPoiIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <update id="batchUpdateLabelsCanteenMsg">
      update wm_sc_canteen_poi_label
      set canteen_id = #{canteenIdTo,jdbcType=INTEGER}
      where valid =1  and canteen_id = #{canteenIdFrom,jdbcType=INTEGER}
        <if test="wmPoiIdList != null and wmPoiIdList.size>0">
            and wm_poi_id in
            <foreach collection="wmPoiIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </update>



    <select id="selectByCanteenPrimaryIdAndWmPoiId"
            resultMap="BaseResultMap"
            resultType="com.sankuai.meituan.waimai.customer.domain.sc.WmScCanteenPoiLabelDB">
        select
            <include refid="Base_Column_List"/>
        from
            wm_sc_canteen_poi_label
        where
            valid = 1
        and
            canteen_id = #{canteenPrimaryId,jdbcType=INTEGER}
        and
            wm_poi_id = #{wmPoiId,jdbcType=BIGINT}
    </select>


    <insert id="batchInsert">
        <if test="items.get(0) != null">
            insert into wm_sc_canteen_poi_label (canteen_id, wm_poi_id, label_id,
            label_type, valid, utime,
            ctime)
            values
            <foreach collection="items" item="item" index="index" separator=",">
                (#{item.canteenId,jdbcType=INTEGER}, #{item.wmPoiId,jdbcType=BIGINT}, #{item.labelId,jdbcType=BIGINT},
                #{item.labelType,jdbcType=INTEGER}, #{item.valid,jdbcType=INTEGER}, #{item.utime,jdbcType=INTEGER},
                #{item.ctime,jdbcType=INTEGER})
            </foreach>
        </if>
    </insert>


    <insert id="insertSelective"
            parameterType="com.sankuai.meituan.waimai.customer.domain.sc.WmScCanteenPoiLabelDB"
            useGeneratedKeys="true"
            keyProperty="id">
        insert into
            wm_sc_canteen_poi_label
        <trim prefix="(" suffix=")" suffixOverrides=",">
            ctime,
            utime,
            <if test="id != null">
                id,
            </if>
            <if test="canteenId != null">
                canteen_id,
            </if>
            <if test="wmPoiId != null">
                wm_poi_id,
            </if>
            <if test="labelId != null">
                label_id,
            </if>
            <if test="labelType != null">
                label_type,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            UNIX_TIMESTAMP(),
            UNIX_TIMESTAMP(),
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="canteenId != null">
                #{canteenId,jdbcType=INTEGER},
            </if>
            <if test="wmPoiId != null">
                #{wmPoiId,jdbcType=BIGINT},
            </if>
            <if test="labelId != null">
                #{labelId,jdbcType=BIGINT},
            </if>
            <if test="labelType != null">
                #{labelType,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

</mapper>
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sankuai.meituan.waimai.customer.dao.sc.WmSchoolMapper">
    <resultMap id="BaseResultMap" type="com.sankuai.meituan.waimai.customer.domain.sc.WmSchoolDB">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="school_id" property="schoolId" jdbcType="INTEGER"/>
        <result column="school_code" property="schoolCode" jdbcType="BIGINT"/>
        <result column="school_name" property="schoolName" jdbcType="VARCHAR"/>
        <result column="school_type" property="schoolType" jdbcType="INTEGER"/>
        <result column="grade" property="grade" jdbcType="INTEGER"/>
        <result column="school_kp" property="schoolKp" jdbcType="VARCHAR"/>
        <result column="school_kp_num" property="schoolKpNum" jdbcType="VARCHAR"/>
        <result column="aor_type" property="aorType" jdbcType="TINYINT"/>
        <result column="aor_id" property="aorId" jdbcType="INTEGER"/>
        <result column="aor_name" property="aorName" jdbcType="VARCHAR"/>
        <result column="area" property="area" jdbcType="VARCHAR"/>
        <result column="city_team" property="cityTeam" jdbcType="VARCHAR"/>
        <result column="city_name" property="cityName" jdbcType="VARCHAR"/>
        <result column="city_id" property="cityId" jdbcType="INTEGER"/>
        <result column="canteen_num" property="canteenNum" jdbcType="INTEGER"/>
        <result column="school_status" property="schoolStatus" jdbcType="INTEGER"/>
        <result column="user_id" property="userId" jdbcType="INTEGER"/>
        <result column="user_name" property="userName" jdbcType="VARCHAR"/>
        <result column="responsible_person" property="responsiblePerson" jdbcType="VARCHAR"/>
        <result column="responsible_uid" property="responsibleUid" jdbcType="INTEGER"/>
        <result column="ext" property="ext" jdbcType="VARCHAR"/>
        <result column="ctime" property="ctime" jdbcType="INTEGER"/>
        <result column="utime" property="utime" jdbcType="INTEGER"/>
        <result column="valid" property="valid" jdbcType="INTEGER"/>
        <result column="contract_num" property="contractNum" jdbcType="VARCHAR"/>
        <result column="wdc_clue_id" property="wdcClueId" jdbcType="BIGINT"/>
        <result column="wm_co_status" property="wmCoStatus" jdbcType="INTEGER"/>
        <result column="school_address" property="schoolAddress" jdbcType="INTEGER"/>
        <result column="tea_stu_num" property="teaStuNum" jdbcType="INTEGER"/>
        <result column="out_delivery_in" property="outDeliveryIn" jdbcType="INTEGER"/>
        <result column="delivery_status" property="deliveryStatus" jdbcType="VARCHAR"/>
        <result column="delivery_status_other" property="deliveryStatusOther" jdbcType="BIGINT"/>
        <result column="site_id" property="siteId" jdbcType="INTEGER"/>
        <result column="school_kp_num_encryption" property="schoolKpNumEncryption" jdbcType="VARCHAR"/>
        <result column="school_kp_num_token" property="schoolKpNumToken" jdbcType="VARCHAR"/>
        <result column="school_labels" property="schoolLabels" jdbcType="VARCHAR"/>

        <result column="student_num" property="studentNum" jdbcType="INTEGER"/>
        <result column="outside_student_num" property="outsideStudentNum" jdbcType="INTEGER"/>
        <result column="school_dev_type" property="schoolDevType" jdbcType="INTEGER"/>
        <result column="school_cooperate_type" property="schoolCooperateType" jdbcType="INTEGER"/>
        <result column="agreement_code" property="agreementCode" jdbcType="VARCHAR"/>
        <result column="agreement_type" property="agreementType" jdbcType="INTEGER"/>
        <result column="agreement_time_start" property="agreementTimeStart" jdbcType="INTEGER"/>
        <result column="agreement_time_end" property="agreementTimeEnd" jdbcType="INTEGER"/>
        <result column="school_level" property="schoolLevel" jdbcType="INTEGER"/>
        <result column="school_lifecycle" property="schoolLifecycle" jdbcType="INTEGER"/>
        <result column="school_allow_delivery" property="schoolAllowDelivery" jdbcType="INTEGER"/>
        <result column="school_light_off_info" property="schoolLightOffInfo" jdbcType="VARCHAR"/>
        <result column="dining_cabinet" property="diningCabinet" jdbcType="INTEGER"/>
        <result column="aggre_site_id" property="aggreSiteId" jdbcType="VARCHAR"/>
        <result column="aggre_order_allow_delivery" property="aggreOrderAllowDelivery" jdbcType="INTEGER"/>

        <result column="current_delivery_status" property="currentDeliveryStatus" jdbcType="INTEGER"/>

        <result column="school_category" property="schoolCategory" jdbcType="INTEGER"/>
        <result column="canteen_category_num" property="canteenCategoryNum" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, school_id, school_code, school_name, school_type, grade, school_kp,
        school_kp_num, aor_type, aor_id, aor_name, area, city_team, city_id, city_name, canteen_num,
        school_status, user_id, user_name, ext, ctime, utime, valid, responsible_person, responsible_uid,
        contract_num, wdc_clue_id, wm_co_status, school_address, tea_stu_num, out_delivery_in,
        delivery_status, delivery_status_other, site_id, school_kp_num_encryption,school_kp_num_token,
        student_num, outside_student_num, school_dev_type, school_cooperate_type, agreement_code,
        agreement_type, agreement_time_start, agreement_time_end, school_level, school_lifecycle,
        school_allow_delivery, school_light_off_info, dining_cabinet, aggre_site_id, aggre_order_allow_delivery,
        current_delivery_status, school_category
    </sql>

    <update id="updateSchool" parameterType="com.sankuai.meituan.waimai.customer.domain.sc.WmSchoolDB">
        update wm_sc_school
        <set>
            <if test="schoolId != null and schoolId != ''">
                school_id = #{schoolId},
            </if>
            <if test="schoolCode != null and schoolCode != ''">
                school_code = #{schoolCode},
            </if>
            <if test="schoolName != null and schoolName != ''">
                school_name = #{schoolName},
            </if>
            <if test="schoolType != null and schoolType != ''">
                school_type = #{schoolType},
            </if>
            <if test="grade != null and grade != ''">
                grade = #{grade},
            </if>
            <if test="aorType != null">
                aor_type = #{aorType},
            </if>
            <if test="aorId != null and aorId != ''">
                aor_id = #{aorId},
            </if>
            <if test="aorName != null and aorName != ''">
                aor_name = #{aorName},
            </if>
            <if test="area != null and area != ''">
                area = #{area},
            </if>
            <if test="cityTeam != null and cityTeam != ''">
                city_team = #{cityTeam},
            </if>
            <if test="cityId != null and cityId != ''">
                city_id = #{cityId},
            </if>
            <if test="cityName != null and cityName != ''">
                city_name = #{cityName},
            </if>
            <if test="canteenNum != null and canteenNum != ''">
                canteen_num = #{canteenNum},
            </if>
            <if test="schoolStatus != null and schoolStatus != ''">
                school_status = #{schoolStatus},
            </if>
            <if test="userId != null and userId!=''">
                user_id = #{userId},
            </if>
            <if test="userName != null and userName!=''">
                user_name = #{userName},
            </if>
            <if test="schoolKpNum != null and notSaveSchoolKpNum!=1">
                school_kp_num = #{schoolKpNum},
            </if>
            <if test="schoolKp != null ">
                school_kp = #{schoolKp},
            </if>
            <if test="ext != null and ext!=''">
                ext = #{ext},
            </if>
            <if test="utime != null and utime!=''">
                utime = #{utime},
            </if>
            <if test="ctime != null and ctime!=''">
                ctime = #{ctime},
            </if>
            <if test="valid != null and valid != ''">
                valid = #{valid},
            </if>
            <if test="responsiblePerson != null and responsiblePerson != ''">
                responsible_person = #{responsiblePerson},
            </if>
            <if test="responsibleUid != null">
                responsible_uid = #{responsibleUid},
            </if>
            <if test="contractNum != null">
                contract_num=#{contractNum},
            </if>
            <if test="wmCoStatus != null">
                wm_co_status=#{wmCoStatus},
            </if>
            <if test="wdcClueId != null and wdcClueId>0">
                wdc_clue_id=#{wdcClueId},
            </if>
            <if test="schoolAddress != null">
                school_address=#{schoolAddress},
            </if>
            <if test="teaStuNum != null ">
                tea_stu_num=#{teaStuNum},
            </if>
            <if test="studentNum != null">
                student_num=#{studentNum},
            </if>
            <if test="outsideStudentNum != null">
                outside_student_num=#{outsideStudentNum},
            </if>
            <if test="outDeliveryIn != null ">
                out_delivery_in=#{outDeliveryIn},
            </if>
            <if test="deliveryStatus != null ">
                delivery_status=#{deliveryStatus},
            </if>
            <if test="deliveryStatusOther != null">
                delivery_status_other=#{deliveryStatusOther},
            </if>
            <if test="siteId != null ">
                site_id=#{siteId},
            </if>
            <if test="schoolKpNumEncryption != null ">
                school_kp_num_encryption=#{schoolKpNumEncryption},
            </if>
            <if test="schoolKpNumToken != null ">
                school_kp_num_token=#{schoolKpNumToken},
            </if>
            <if test="schoolCooperateType != null">
                school_cooperate_type=#{schoolCooperateType},
            </if>
            <if test="schoolLifecycle != null">
                school_lifecycle=#{schoolLifecycle},
            </if>
            <if test="schoolLevel != null">
                school_level=#{schoolLevel},
            </if>
            <if test="schoolDevType != null">
                school_dev_type=#{schoolDevType},
            </if>
            <if test="agreementType != null">
                agreement_type=#{agreementType},
            </if>
            <if test="agreementCode != null">
                agreement_code=#{agreementCode},
            </if>
            <if test="agreementTimeStart != null">
                agreement_time_start=#{agreementTimeStart},
            </if>
            <if test="agreementTimeEnd != null">
                agreement_time_end=#{agreementTimeEnd},
            </if>
            <if test="schoolAllowDelivery != null and schoolAllowDelivery > 0">
                school_allow_delivery=#{schoolAllowDelivery},
            </if>
            <if test="schoolLightOffInfo != null and schoolLightOffInfo != ''">
                school_light_off_info = #{schoolLightOffInfo},
            </if>
            <if test="diningCabinet != null">
                dining_cabinet = #{diningCabinet},
            </if>
            <if test="aggreSiteId != null">
                aggre_site_id = #{aggreSiteId},
            </if>
            <if test="aggreOrderAllowDelivery != null">
                aggre_order_allow_delivery = #{aggreOrderAllowDelivery},
            </if>
            <if test="currentDeliveryStatus != null">
                current_delivery_status = #{currentDeliveryStatus},
            </if>
            <if test="schoolCategory != null">
                school_category = #{schoolCategory},
            </if>
        </set>
        where id = #{id}
    </update>

    <update id="deleteSchoolById" parameterType="java.lang.Integer" >
        update wm_sc_school set valid=0, utime=UNIX_TIMESTAMP()
        where id = #{id}
    </update>

    <insert id="insertSchool" parameterType="com.sankuai.meituan.waimai.customer.domain.sc.WmSchoolDB"
            useGeneratedKeys="true" keyProperty="id">
        insert into wm_sc_school
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="schoolCode != null">
                school_code,
            </if>
            <if test="schoolName != null">
                school_name,
            </if>
            <if test="schoolType != null">
                school_type,
            </if>
            <if test="grade != null">
                grade,
            </if>
            <if test="schoolKp != null">
                school_kp,
            </if>
            <if test="schoolKpNum != null and notSaveSchoolKpNum!=1">
                school_kp_num,
            </if>
            <if test="aorType != null">
                aor_type,
            </if>
            <if test="aorId != null">
                aor_id,
            </if>
            <if test="aorName != null">
                aor_name,
            </if>
            <if test="area != null">
                area,
            </if>
            <if test="cityTeam != null">
                city_team,
            </if>
            <if test="cityId != null">
                city_id,
            </if>
            <if test="cityName != null">
                city_name,
            </if>
            <if test="canteenNum != null">
                canteen_num,
            </if>
            <if test="schoolStatus != null">
                school_status,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="userName != null">
                user_name,
            </if>
            <if test="ext != null">
                ext,
            </if>
            <if test="utime != null">
                utime,
            </if>
            <if test="ctime != null">
                ctime,
            </if>
            <if test="valid != null">
                valid,
            </if>
            <if test="responsiblePerson != null">
                responsible_person,
            </if>
            <if test="responsibleUid != null">
                responsible_uid,
            </if>
            <if test="contractNum != null">
                contract_num,
            </if>
            <if test="wdcClueId != null">
                wdc_clue_id,
            </if>
            <if test="schoolAddress != null">
                school_address,
            </if>
            <if test="teaStuNum != null">
                tea_stu_num,
            </if>
            <if test="outDeliveryIn != null">
                out_delivery_in,
            </if>
            <if test="deliveryStatus != null">
                delivery_status,
            </if>
            <if test="deliveryStatusOther != null">
                delivery_status_other,
            </if>
            <if test="siteId != null">
                site_id,
            </if>
            <if test="schoolKpNumEncryption != null ">
                school_kp_num_encryption,
            </if>
            <if test="schoolKpNumToken != null ">
                school_kp_num_token,
            </if>
            <if test="wmCoStatus != null">
                wm_co_status,
            </if>
            <if test="studentNum != null">
                student_num,
            </if>
            <if test="outsideStudentNum != null">
                outside_student_num,
            </if>
            <if test="schoolDevType != null">
                school_dev_type,
            </if>
            <if test="schoolLevel != null">
                school_level,
            </if>
            <if test="schoolLifecycle != null">
                school_lifecycle,
            </if>
            <if test="schoolCooperateType != null">
                school_cooperate_type,
            </if>
            <if test="agreementCode != null">
                agreement_code,
            </if>
            <if test="agreementType != null">
                agreement_type,
            </if>
            <if test="agreementTimeStart != null">
                agreement_time_start,
            </if>
            <if test="agreementTimeEnd != null">
                agreement_time_end,
            </if>
            <if test="schoolAllowDelivery != null and schoolAllowDelivery > 0">
                school_allow_delivery,
            </if>
            <if test="schoolLightOffInfo != null and schoolLightOffInfo != ''">
                school_light_off_info,
            </if>
            <if test="diningCabinet != null">
                dining_cabinet,
            </if>
            <if test="aggreSiteId != null and aggreSiteId != ''">
                aggre_site_id,
            </if>
            <if test="aggreOrderAllowDelivery != null">
                aggre_order_allow_delivery,
            </if>
            <if test="currentDeliveryStatus != null">
                current_delivery_status,
            </if>
        </trim>

        <trim prefix="values (" suffix=")" suffixOverrides=",">

            <if test="schoolCode != null">
                #{schoolCode,jdbcType=BIGINT},
            </if>
            <if test="schoolName != null">
                #{schoolName,jdbcType=VARCHAR},
            </if>
            <if test="schoolType != null">
                #{schoolType,jdbcType=INTEGER},
            </if>
            <if test="grade != null">
                #{grade,jdbcType=INTEGER},
            </if>
            <if test="schoolKp != null">
                #{schoolKp,jdbcType=VARCHAR},
            </if>
            <if test="schoolKpNum != null and notSaveSchoolKpNum!=1">
                #{schoolKpNum,jdbcType=VARCHAR},
            </if>
            <if test="aorType != null">
                #{aorType,jdbcType=INTEGER},
            </if>
            <if test="aorId != null">
                #{aorId,jdbcType=INTEGER},
            </if>
            <if test="aorName != null">
                #{aorName,jdbcType=VARCHAR},
            </if>
            <if test="area != null">
                #{area,jdbcType=VARCHAR},
            </if>
            <if test="cityTeam != null">
                #{cityTeam,jdbcType=VARCHAR},
            </if>
            <if test="cityId != null">
                #{cityId,jdbcType=INTEGER},
            </if>
            <if test="cityName != null">
                #{cityName,jdbcType=VARCHAR},
            </if>
            <if test="canteenNum != null">
                #{canteenNum,jdbcType=INTEGER},
            </if>
            <if test="schoolStatus != null">
                #{schoolStatus,jdbcType=INTEGER},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=INTEGER},
            </if>
            <if test="userName != null">
                #{userName,jdbcType=VARCHAR},
            </if>
            <if test="ext != null">
                #{ext,jdbcType=VARCHAR},
            </if>
            <if test="utime != null">
                #{utime,jdbcType=INTEGER},
            </if>
            <if test="ctime != null">
                #{ctime,jdbcType=INTEGER},
            </if>
            <if test="valid != null">
                #{valid,jdbcType=INTEGER},
            </if>
            <if test="responsiblePerson != null">
                #{responsiblePerson,jdbcType=VARCHAR},
            </if>
            <if test="responsibleUid != null">
                #{responsibleUid,jdbcType=INTEGER},
            </if>
            <if test="contractNum != null">
                #{contractNum},
            </if>
            <if test="wdcClueId != null">
                #{wdcClueId},
            </if>
            <if test="schoolAddress != null">
                #{schoolAddress},
            </if>
            <if test="teaStuNum != null">
                #{teaStuNum},
            </if>
            <if test="outDeliveryIn != null">
                #{outDeliveryIn},
            </if>
            <if test="deliveryStatus != null">
                #{deliveryStatus},
            </if>
            <if test="deliveryStatusOther != null">
                #{deliveryStatusOther},
            </if>
            <if test="siteId != null">
                #{siteId},
            </if>
            <if test="schoolKpNumEncryption != null ">
                #{schoolKpNumEncryption},
            </if>
            <if test="schoolKpNumToken != null ">
                #{schoolKpNumToken},
            </if>
            <if test="wmCoStatus != null">
                #{wmCoStatus},
            </if>
            <if test="studentNum != null">
                #{studentNum},
            </if>
            <if test="outsideStudentNum != null">
                #{outsideStudentNum},
            </if>
            <if test="schoolDevType != null">
                #{schoolDevType},
            </if>
            <if test="schoolLevel != null">
                #{schoolLevel},
            </if>
            <if test="schoolLifecycle != null">
                #{schoolLifecycle},
            </if>
            <if test="schoolCooperateType != null">
                #{schoolCooperateType},
            </if>
            <if test="agreementCode != null">
                #{agreementCode},
            </if>
            <if test="agreementType != null">
                #{agreementType},
            </if>
            <if test="agreementTimeStart != null">
                #{agreementTimeStart},
            </if>
            <if test="agreementTimeEnd != null">
                #{agreementTimeEnd},
            </if>
            <if test="schoolAllowDelivery != null and schoolAllowDelivery > 0">
                #{schoolAllowDelivery},
            </if>
            <if test="schoolLightOffInfo != null and schoolLightOffInfo != ''">
                #{schoolLightOffInfo},
            </if>
            <if test="diningCabinet != null">
                #{diningCabinet},
            </if>
            <if test="aggreSiteId != null and aggreSiteId != ''">
                #{aggreSiteId},
            </if>
            <if test="aggreOrderAllowDelivery != null">
                #{aggreOrderAllowDelivery},
            </if>
            <if test="currentDeliveryStatus != null">
                #{currentDeliveryStatus},
            </if>
        </trim>
    </insert>


    <select id="selectSchoolById" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        /*master*/select
        <include refid="Base_Column_List"/>
        from wm_sc_school
        where valid != 0 and id = #{id,jdbcType=INTEGER}
    </select>

    <select id="selectSchoolBySchoolId" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select
        <include refid="Base_Column_List"/>
        from wm_sc_school
        where valid != 0 and school_id = #{schoolId,jdbcType=INTEGER}
    </select>

    <select id="selectSchoolByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wm_sc_school
        where valid != 0
        <if test="schoolPrimaryIds != null and schoolPrimaryIds.size >0">
            and id in
            <foreach collection="schoolPrimaryIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectValidSchoolByName" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from wm_sc_school
        where valid != 0 and school_name = #{schoolName,jdbcType=VARCHAR}
    </select>

    <select id="selectValidSchoolBySchoolName" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from wm_sc_school
        where valid != 0
        <if test="schoolName != null and schoolName !=''">
            and school_name like concat(#{schoolName},'%')
        </if>
    </select>

    <update id="addCanteeNumById" parameterType="java.lang.Integer">
        update wm_sc_school SET canteen_num = canteen_num + 1  where id = #{id}
    </update>

    <update id="subCanteeNumById" parameterType="java.lang.Integer">
        update wm_sc_school SET canteen_num = canteen_num - 1  where id = #{id}
    </update>

    <select id="selectSchoolList" parameterType="com.sankuai.meituan.waimai.customer.domain.sc.WmSchoolDB"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wm_sc_school wss
        <where>
            valid != 0
            <if test="schoolId != null and schoolId != 0">
                and school_id = #{schoolId}
            </if>
            <if test="id != null and id != 0">
                and id = #{id}
            </if>
            <if test="schoolName != null and schoolName != ''">
                and school_name like concat(#{schoolName},'%')
            </if>
            <if test="grade != null and grade != 0">
                and grade = #{grade}
            </if>
            <if test="cityName != null and cityName != ''">
                and city_name like concat(#{cityName},'%')
            </if>
            <if test="responsiblePerson != null and responsiblePerson != ''">
                and responsible_person = #{responsiblePerson}
            </if>
            <if test="idList != null and idList.size >0">
                and id in
                <foreach collection="idList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="schoolIdList != null and schoolIdList.size >0">
                and school_id in
                <foreach collection="schoolIdList" item="schoolIdItem" separator="," open="(" close=")">
                    #{schoolIdItem}
                </foreach>
            </if>
            <if test="contractNum != null and contractNum != ''">
                and contract_num =#{contractNum}
            </if>
            <if test="wdcClueId != null and wdcClueId>0">
                and wdc_clue_id =#{wdcClueId}
            </if>
            <if test="wmCoStatus != null and  wmCoStatus>-1">
                and wm_co_status =#{wmCoStatus}
            </if>
            <if test="aorId != null and aorId != 0">
                and aor_id = #{aorId}
            </if>
            <if test="aorType != null">
                and aor_type = #{aorType}
            </if>
            <if test="beginId != null ">
                and id &gt;=#{beginId}
            </if>
            <if test="endId != null ">
                and id &lt;=#{endId}
            </if>
            <if test="schoolType != null and schoolType != 0">
                and school_type = #{schoolType}
            </if>
            <if test="cityId != null and cityId > 0">
                and city_id = #{cityId}
            </if>
            <if test="cityIdList != null and cityIdList.size > 0">
                and city_id in
                <foreach collection="cityIdList" item="cityIdItem" separator="," open="(" close=")">
                    #{cityIdItem}
                </foreach>
            </if>
            <if test="canteenNumStart != null and canteenNumStart >= 0">
                and canteen_num &gt;= #{canteenNumStart}
            </if>
            <if test="canteenNumEnd != null and canteenNumEnd >= 0">
                and canteen_num &lt;= #{canteenNumEnd}
            </if>
            <if test="area != null and area != ''">
                and area = #{area}
            </if>
            <if test="cityTeam != null and cityTeam != ''">
                and city_team = #{cityTeam}
            </if>
            <if test="schoolLabelQueryList != null and schoolLabelQueryList.size > 0">
                <foreach collection="schoolLabelQueryList" item="schoolLabel">
                    and school_labels like concat('%',#{schoolLabel},'%')
                </foreach>
            </if>
            <if test="responsiblePersonList != null and responsiblePersonList.size > 0">
                and ( responsible_person in
                <foreach collection="responsiblePersonList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                <if test="aorIdList != null and aorIdList.size > 0">
                    or aor_id in
                    <foreach collection="aorIdList" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                </if>
                )
            </if>
            <if test="dslQuery != null and dslQuery != ''">
                and ${dslQuery}
            </if>
            <if test="currentDeliveryStatus != null and currentDeliveryStatus != 0">
                and current_delivery_status = #{currentDeliveryStatus}
            </if>
            <if test="schoolCategory != null and schoolCategory != 0">
                and school_category = #{schoolCategory}
            </if>
        </where>
        order by school_id desc
    </select>

    <select id="selectSchoolsByContractorId" parameterType="java.lang.Integer"
            resultMap="BaseResultMap">
        select school.city_name, school.id, school.school_name, school.city_id
        from wm_sc_school school , wm_sc_canteen canteen
        where school.valid != 0 and canteen.valid != 0 and canteen.contractor_id = #{contractorId} and school.id = canteen.school_id
    </select>

    <select id="selectEffectSchoolsByContractorId" parameterType="java.lang.Integer"
            resultMap="BaseResultMap">
        select school.city_name, school.id, school.school_name, school.city_id
        from wm_sc_school school , wm_sc_canteen canteen
        where school.valid = 1
        and canteen.valid  = 1
        and canteen.contractor_id = #{contractorId}
        and school.id = canteen.school_id
        and canteen.effective=1
    </select>

    <select id="selectSchoolsByContractorIds" resultMap="BaseResultMap">
        select school.city_name, school.id, school.school_name, school.city_id
        from wm_sc_school school , wm_sc_canteen canteen
        where school.valid != 0 and canteen.valid != 0
            and school.id = canteen.school_id and canteen.effective=1
        <if test="">
            and canteen.contractor_id in
            <foreach collection="contractorIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <update id="updateSchoolId" parameterType="com.sankuai.meituan.waimai.customer.domain.sc.WmSchoolDB">
        update wm_sc_school SET school_id = #{schoolId}  where id = #{id}
    </update>

    <update id="bindResponsiblePerson" parameterType="com.sankuai.meituan.waimai.customer.domain.sc.WmSchoolDB">
        update
            wm_sc_school
        SET responsible_person = #{responsiblePerson},
            responsible_uid = #{responsibleUid},
            utime = unix_timestamp()
        where
            id = #{id}
    </update>


    <select id="selectByCondition" parameterType="com.sankuai.meituan.waimai.customer.domain.sc.WmSchoolSearchCondition"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wm_sc_school
        <include refid="Example_Where_Clause"/>
    </select>

    <select id="selectByConditionMaster" parameterType="com.sankuai.meituan.waimai.customer.domain.sc.WmSchoolSearchCondition"
            resultMap="BaseResultMap">
        /*master*/select
        <include refid="Base_Column_List"/>
        from wm_sc_school
        <include refid="Example_Where_Clause"/>
    </select>

    <select id="selectEffectSchoolsByResponsiblePersonList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wm_sc_school
        where valid = 1
        <if test="responsiblePersonList !=null and responsiblePersonList.size()>0">
            and responsible_person in
            <foreach collection="responsiblePersonList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectSchoolPrimaryIdListByResponsibleUidList"
            resultType="java.lang.Integer">
        select
            id
        from
            wm_sc_school
        where
            valid = 1
        <if test="responsibleUidList !=null and responsibleUidList.size()>0">
            and responsible_uid in
            <foreach collection="responsibleUidList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <sql id="Example_Where_Clause">
        where 1=1
        <if test="valid != null">
            and valid = #{valid}
        </if>
        <if test="id != null">
            and id = #{id}
        </if>
        <if test="idList != null and idList.size >0">
            and id in
            <foreach collection="idList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="schoolIdList != null and schoolIdList.size >0">
            and school_id in
            <foreach collection="schoolIdList" item="schoolIdItem" separator="," open="(" close=")">
                #{schoolIdItem}
            </foreach>
        </if>
        <if test="contractNum != null and contractNum != ''">
            and contract_num =#{contractNum}
        </if>
        <if test="wdcClueId != null and wdcClueId>0">
            and wdc_clue_id =#{wdcClueId}
        </if>
        <if test="wdcClueIdList != null and wdcClueIdList.size >0">
            and wdc_clue_id in
            <foreach collection="wdcClueIdList" item="wdcClueIdItem" separator="," open="(" close=")">
                #{wdcClueIdItem}
            </foreach>
        </if>
        <if test="wmCoStatus != null and  wmCoStatus>-1">
            and wm_co_status =#{wmCoStatus}
        </if>
        <if test="beginId != null ">
            and id &gt;=#{beginId}
        </if>
        <if test="endId != null ">
            and id &lt;=#{endId}
        </if>
        <if test="idOrderBy!=null">
            <choose>
                <when test="idOrderBy==0">
                    order by id asc
                </when>
                <when test="idOrderBy==1">
                    order by id desc
                </when>
            </choose>
        </if>
        <if test="pageFrom != null and pageSize!= null">
            limit #{pageFrom},#{pageSize}
        </if>
    </sql>


    <update id="updateSchoolAorMsg" parameterType="com.sankuai.meituan.waimai.customer.domain.sc.WmSchoolDB">
        update wm_sc_school
        <set>
            <if test="aorId != null and aorId != 0">
                aor_id = #{aorId},
            </if>
            <if test="aorName != null and aorName != ''">
                aor_name = #{aorName},
            </if>
            <if test="area != null and area != ''">
                area = #{area},
            </if>
            <if test="cityTeam != null and cityTeam != ''">
                city_team = #{cityTeam},
            </if>
            <if test="cityId != null and cityId > 0">
                city_id = #{cityId},
            </if>
            <if test="cityName != null and cityName != ''">
                city_name = #{cityName},
            </if>
            utime = UNIX_TIMESTAMP()
        </set>
        where id = #{id}
    </update>

    <select id = "selectExistSchoolIdList" resultType = "java.lang.Integer">
        select
            school_id
        from
            wm_sc_school
        where
            valid = 1
        <if test="schoolIdList != null and schoolIdList.size() > 0">
            and school_id IN
            <foreach item="item" index="index" collection="schoolIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <update id="updateSchoolLabels" parameterType="com.sankuai.meituan.waimai.customer.domain.sc.WmSchoolDB">
        update wm_sc_school
        <set>
            <if test="schoolLabels != null">
                school_labels = #{schoolLabels},
            </if>
        </set>
        where school_id = #{schoolId}
    </update>

    <update id="updateSchoolCurrentDeliveryStatus"
            parameterType="com.sankuai.meituan.waimai.customer.domain.sc.WmSchoolDB">
        update
            wm_sc_school
        set
            current_delivery_status = #{currentDeliveryStatus}
        where
            id = #{schoolPrimaryId}
    </update>

    <update id="updateSchoolResponsiblePersonByBatch"
            parameterType="com.sankuai.meituan.waimai.customer.domain.sc.WmSchoolUpdateResponsiblePersonBO">
        update wm_sc_school
        <set>
            <if test="responsiblePerson != null and responsiblePerson != ''">
                responsible_person = #{responsiblePerson},
            </if>
            <if test="responsibleUid != null">
                responsible_uid = #{responsibleUid},
            </if>
            <if test="userId != null">
                user_id = #{userId},
            </if>
            <if test="userName != null and userName != ''">
                user_name = #{userName},
            </if>
            utime = UNIX_TIMESTAMP()
        </set>
        where
            id in
            <foreach item="item" index="index" collection="idList" open="(" separator="," close=")">
                #{item}
            </foreach>
    </update>


    <select id="selectSchoolLabelsBySchoolId" parameterType="java.lang.Integer" resultType="java.lang.String">
        select
            school_labels
        from
            wm_sc_school
        where
            valid = 1 and school_id = #{schoolId}
    </select>


    <select id="selectByLikeSchoolNameWithDSL"
            resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List"/>
        from
            wm_sc_school
        where
            valid = 1
        and
            school_name like concat(#{schoolName},'%')
        and
            ${dslQuery}
        order by
            id desc
    </select>

    <select id="getMaxSchoolId" resultType="java.lang.Integer">
        SELECT max(school_id) FROM wm_sc_school WHERE valid = 1
    </select>

</mapper>
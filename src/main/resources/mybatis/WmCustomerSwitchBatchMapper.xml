<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sankuai.meituan.waimai.customer.dao.WmCustomerSwitchBatchDBMapper" >
    <resultMap id="BaseResultMap" type="com.sankuai.meituan.waimai.customer.domain.WmCustomerSwitchBatchDB" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="manual_batch_id" property="manual_batch_id" jdbcType="BIGINT" />
        <result column="switch_id" property="customer_biz_id" jdbcType="BIGINT" />
        <result column="status" property="status" jdbcType="TINYINT" />
        <result column="msg" property="msg" jdbcType="CHAR" />
        <result column="ctime" property="ctime" jdbcType="INTEGER" />
        <result column="utime" property="utime" jdbcType="INTEGER" />
        <result column="valid" property="valid" jdbcType="TINYINT" />
    </resultMap>
    <sql id="Base_Column_List" >
    id, manual_batch_id, switch_id, status, msg, ctime, utime, valid
    </sql>

    <insert id="insert" parameterType="com.sankuai.meituan.waimai.customer.domain.WmCustomerSwitchBatchDB" >
        insert into wm_econtract_sign_switch_batch
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                id,
            </if>
            <if test="manual_batch_id != null" >
                manual_batch_id,
            </if>
            <if test="switch_id != null" >
                switch_id,
            </if>
            <if test="status != null" >
                status,
            </if>
            <if test="msg != null" >
                msg,
            </if>
                ctime,
                utime,
                valid,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                #{id,jdbcType=BIGINT},
            </if>
            <if test="manual_batch_id != null" >
                #{manual_batch_id,jdbcType=BIGINT},
            </if>
            <if test="switch_id != null" >
                #{switch_id,jdbcType=BIGINT},
            </if>
            <if test="status != null" >
                #{status,jdbcType=TINYINT},
            </if>
            <if test="msg != null" >
                #{msg,jdbcType=CHAR},
            </if>
            unix_timestamp(),
            unix_timestamp(),
            1,
        </trim>
    </insert>

    <update id="updateStatusById" parameterType="com.sankuai.meituan.waimai.customer.domain.WmCustomerSwitchBatchDB" >
        update wm_econtract_sign_switch_batch
        set status=#{status}, msg=#{msg}
        where id = #{id,jdbcType=BIGINT} and status &lt;= #{status}
    </update>

    <select id="selectBySwitchId"
            resultType="com.sankuai.meituan.waimai.customer.domain.WmCustomerSwitchBatchDB">
        select
        <include refid="Base_Column_List" />
        from wm_econtract_sign_switch_batch
        where switch_id = #{switchId,jdbcType=BIGINT}
        and valid = 1
    </select>

    <select id="selectByManualBatchId"
            resultType="com.sankuai.meituan.waimai.customer.domain.WmCustomerSwitchBatchDB">
        select
        <include refid="Base_Column_List" />
        from wm_econtract_sign_switch_batch
        where manual_batch_id = #{manualBatchId,jdbcType=BIGINT}
        and valid = 1
    </select>
</mapper>
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sankuai.meituan.waimai.customer.settle.dao.WmSettleDBMapper">
    <resultMap id="BaseResultMap" type="com.sankuai.meituan.waimai.customer.settle.domain.WmSettleDB">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="wm_contract_id" property="wm_contract_id" jdbcType="INTEGER"/>
        <result column="min_pay_amount" property="min_pay_amount" jdbcType="REAL"/>
        <result column="pay_period_num" property="pay_period_num" jdbcType="INTEGER"/>
        <result column="pay_period_unit" property="pay_period_unit" jdbcType="TINYINT"/>
        <result column="province" property="province" jdbcType="INTEGER"/>
        <result column="city" property="city" jdbcType="INTEGER"/>
        <result column="bankid" property="bankid" jdbcType="SMALLINT"/>
        <result column="branchid" property="branchid" jdbcType="INTEGER"/>
        <result column="branchname" property="branchname" jdbcType="VARCHAR"/>
        <result column="acc_cardno" property="acc_cardno" jdbcType="VARCHAR"/>
        <result column="acc_name" property="acc_name" jdbcType="VARCHAR"/>
        <result column="acctype" property="acctype" jdbcType="TINYINT"/>
        <result column="valid" property="valid" jdbcType="TINYINT"/>
        <result column="cuid" property="cuid" jdbcType="INTEGER"/>
        <result column="muid" property="muid" jdbcType="INTEGER"/>
        <result column="ctime" property="ctime" jdbcType="INTEGER"/>
        <result column="utime" property="utime" jdbcType="INTEGER"/>
        <result column="settle_type" property="settle_type" jdbcType="TINYINT"/>
        <result column="pay_day_of_month" property="pay_day_of_month" jdbcType="TINYINT"/>
        <result column="party_a_finance_people" property="party_a_finance_people" jdbcType="VARCHAR"/>
        <result column="party_a_finance_phone" property="party_a_finance_phone" jdbcType="VARCHAR"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>

        <result column="card_type" property="card_type" jdbcType="INTEGER"/>
        <result column="cert_type" property="cert_type" jdbcType="SMALLINT"/>
        <result column="legal_cert_num" property="legal_cert_num" jdbcType="VARCHAR"/>
        <result column="legal_person" property="legal_person" jdbcType="VARCHAR"/>
        <result column="legal_id_card" property="legal_id_card" jdbcType="VARCHAR"/>
        <result column="cert_num" property="cert_num" jdbcType="VARCHAR"/>
        <result column="reserve_phone" property="reserve_phone" jdbcType="VARCHAR"/>
        <result column="wm_customer_id" property="wmCustomerId" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="TINYINT"/>

        <result column="card_valid" property="card_valid" jdbcType="INTEGER"/>
        <result column="card_invalid_reason" property="card_invalid_reason" jdbcType="VARCHAR"/>

        <result column="party_a_finance_phone_encrypted" property="party_a_finance_phone_encrypted" jdbcType="VARCHAR"/>
        <result column="acc_cardno_encrypted" property="acc_cardno_encrypted" jdbcType="VARCHAR"/>
        <result column="legal_cert_num_encrypted" property="legal_cert_num_encrypted" jdbcType="VARCHAR"/>
        <result column="legal_id_card_encrypted" property="legal_id_card_encrypted" jdbcType="VARCHAR"/>
        <result column="cert_num_encrypted" property="cert_num_encrypted" jdbcType="VARCHAR"/>
        <result column="reserve_phone_encrypted" property="reserve_phone_encrypted" jdbcType="VARCHAR"/>
        <result column="party_a_finance_phone_token" property="party_a_finance_phone_token" jdbcType="VARCHAR"/>
        <result column="acc_cardno_token" property="acc_cardno_token" jdbcType="VARCHAR"/>
        <result column="legal_cert_num_token" property="legal_cert_num_token" jdbcType="VARCHAR"/>
        <result column="legal_id_card_token" property="legal_id_card_token" jdbcType="VARCHAR"/>
        <result column="cert_num_token" property="cert_num_token" jdbcType="VARCHAR"/>
        <result column="reserve_phone_token" property="reserve_phone_token" jdbcType="VARCHAR"/>

    </resultMap>
    <sql id="Base_Column_List">
    id, wm_contract_id, min_pay_amount, pay_period_num, pay_period_unit, province, city, 
    bankid, branchid, branchname, acc_cardno, acc_name, acctype, valid, cuid, muid, ctime,
    utime, settle_type, pay_day_of_month, party_a_finance_people, party_a_finance_phone, name,
    card_type,cert_type,legal_cert_num,legal_person,legal_id_card,cert_num,reserve_phone,wm_customer_id,status,card_valid, card_invalid_reason,
    party_a_finance_phone_encrypted,acc_cardno_encrypted,legal_cert_num_encrypted,legal_id_card_encrypted,cert_num_encrypted,reserve_phone_encrypted,
    party_a_finance_phone_token,acc_cardno_token,legal_cert_num_token,legal_id_card_token,cert_num_token,reserve_phone_token
    </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select
        <include refid="Base_Column_List"/>
        from wm_settle
        where id = #{id,jdbcType=INTEGER}
    </select>
    <update id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    update wm_settle set valid=0, utime=unix_timestamp()
    where id = #{id,jdbcType=INTEGER} and valid = 1
  </update>
    <update id="batchDeleteByWmSettleIdList" parameterType="java.util.ArrayList">
        update wm_settle set valid=0, utime=unix_timestamp()
        where id in
        <foreach item="item" index="index" collection="wmSettleIdList" open="(" separator="," close=")">
            #{item}
        </foreach>
        and valid = 1
    </update>
    <insert id="insert" parameterType="com.sankuai.meituan.waimai.customer.settle.domain.WmSettleDB">
    insert into wm_settle (id, wm_contract_id, min_pay_amount, 
      pay_period_num, pay_period_unit, province, 
      city, bankid, branchid, 
      branchname, acc_cardno, acc_name, 
      acctype, valid, cuid, 
      muid, ctime, utime, 
      settle_type, pay_day_of_month, party_a_finance_people, 
      party_a_finance_phone, name, card_type,cert_type,
        legal_cert_num,legal_person,legal_id_card,cert_num,reserve_phone,wm_customer_id,status)
    values (#{id,jdbcType=INTEGER}, #{wm_contract_id,jdbcType=INTEGER}, #{min_pay_amount,jdbcType=REAL}, 
      #{pay_period_num,jdbcType=INTEGER}, #{pay_period_unit,jdbcType=TINYINT}, #{province,jdbcType=INTEGER}, 
      #{city,jdbcType=INTEGER}, #{bankid,jdbcType=SMALLINT}, #{branchid,jdbcType=INTEGER}, 
      #{branchname,jdbcType=VARCHAR}, #{acc_cardno,jdbcType=VARCHAR}, #{acc_name,jdbcType=VARCHAR},
      #{acctype,jdbcType=TINYINT}, #{valid,jdbcType=TINYINT}, #{cuid,jdbcType=INTEGER}, 
      #{muid,jdbcType=INTEGER}, unix_timestamp(), unix_timestamp(),
      #{settle_type,jdbcType=TINYINT}, #{pay_day_of_month,jdbcType=TINYINT}, #{party_a_finance_people,jdbcType=VARCHAR}, 
      #{party_a_finance_phone,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{card_type,jdbcType=INTEGER},
        , #{cert_type,jdbcType=SMALLINT}, #{legal_cert_num,jdbcType=VARCHAR}, #{legal_person,jdbcType=VARCHAR},
        #{legal_id_card,jdbcType=VARCHAR}, #{cert_num,jdbcType=VARCHAR}, #{reserve_phone,jdbcType=VARCHAR},
        #{wmCustomerId,jdbcType=INTEGER},#{status,jdbcType=TINYINT})
  </insert>
    <insert id="insertSelective" parameterType="com.sankuai.meituan.waimai.customer.settle.domain.WmSettleDB"
            useGeneratedKeys="true" keyProperty="id">
        insert into wm_settle
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <!--<if test="id != null" >-->
            <!--id,-->
            <!--</if>-->
            <if test="wm_contract_id != null">
                wm_contract_id,
            </if>
            <if test="wmCustomerId != null">
                wm_customer_id,
            </if>
            <if test="min_pay_amount != null">
                min_pay_amount,
            </if>
            <if test="pay_period_num != null">
                pay_period_num,
            </if>
            <if test="pay_period_unit != null">
                pay_period_unit,
            </if>
            <if test="province != null">
                province,
            </if>
            <if test="city != null">
                city,
            </if>
            <if test="bankid != null">
                bankid,
            </if>
            <if test="branchid != null">
                branchid,
            </if>
            <if test="branchname != null">
                branchname,
            </if>
            <if test="acc_cardno != null">
                acc_cardno,
            </if>
            <if test="acc_name != null">
                acc_name,
            </if>
            <if test="acctype != null">
                acctype,
            </if>
            valid,
            <if test="cuid != null">
                cuid,
            </if>
            <if test="muid != null">
                muid,
            </if>
            ctime,
            utime,
            <if test="settle_type != null">
                settle_type,
            </if>
            <if test="pay_day_of_month != null">
                pay_day_of_month,
            </if>
            <if test="party_a_finance_people != null">
                party_a_finance_people,
            </if>
            <if test="party_a_finance_phone != null">
                party_a_finance_phone,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="card_type != null">
                card_type,
            </if>
            <if test="cert_type != null">
                cert_type,
            </if>
            <if test="legal_cert_num != null">
                legal_cert_num,
            </if>
            <if test="legal_person != null">
                legal_person,
            </if>
            <if test="legal_id_card != null">
                legal_id_card,
            </if>
            <if test="cert_num != null">
                cert_num,
            </if>
            <if test="reserve_phone != null">
                reserve_phone,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="party_a_finance_phone_encrypted != null">
                party_a_finance_phone_encrypted,
            </if>
            <if test="acc_cardno_encrypted != null">
                acc_cardno_encrypted,
            </if>
            <if test="legal_cert_num_encrypted != null">
                legal_cert_num_encrypted,
            </if>
            <if test="legal_id_card_encrypted != null">
                legal_id_card_encrypted,
            </if>
            <if test="cert_num_encrypted != null">
                cert_num_encrypted,
            </if>
            <if test="reserve_phone_encrypted != null">
                reserve_phone_encrypted,
            </if>

            <if test="party_a_finance_phone_token != null">
                party_a_finance_phone_token,
            </if>
            <if test="acc_cardno_token != null">
                acc_cardno_token,
            </if>
            <if test="legal_cert_num_token != null">
                legal_cert_num_token,
            </if>
            <if test="legal_id_card_token != null">
                legal_id_card_token,
            </if>
            <if test="cert_num_token != null">
                cert_num_token,
            </if>
            <if test="reserve_phone_token != null">
                reserve_phone_token,
            </if>

        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <!--<if test="id != null" >-->
            <!--#{id,jdbcType=INTEGER},-->
            <!--</if>-->
            <if test="wm_contract_id != null">
                #{wm_contract_id,jdbcType=INTEGER},
            </if>
            <if test="wmCustomerId != null">
                #{wmCustomerId,jdbcType=INTEGER},
            </if>
            <if test="min_pay_amount != null">
                #{min_pay_amount,jdbcType=REAL},
            </if>
            <if test="pay_period_num != null">
                #{pay_period_num,jdbcType=INTEGER},
            </if>
            <if test="pay_period_unit != null">
                #{pay_period_unit,jdbcType=TINYINT},
            </if>
            <if test="province != null">
                #{province,jdbcType=INTEGER},
            </if>
            <if test="city != null">
                #{city,jdbcType=INTEGER},
            </if>
            <if test="bankid != null">
                #{bankid,jdbcType=SMALLINT},
            </if>
            <if test="branchid != null">
                #{branchid,jdbcType=INTEGER},
            </if>
            <if test="branchname != null">
                #{branchname,jdbcType=VARCHAR},
            </if>
            <if test="acc_cardno != null">
                #{acc_cardno,jdbcType=VARCHAR},
            </if>
            <if test="acc_name != null">
                #{acc_name,jdbcType=VARCHAR},
            </if>
            <if test="acctype != null">
                #{acctype,jdbcType=TINYINT},
            </if>
            1,
            <if test="cuid != null">
                #{cuid,jdbcType=INTEGER},
            </if>
            <if test="muid != null">
                #{muid,jdbcType=INTEGER},
            </if>
            unix_timestamp(),
            unix_timestamp(),
            <if test="settle_type != null">
                #{settle_type,jdbcType=TINYINT},
            </if>
            <if test="pay_day_of_month != null">
                #{pay_day_of_month,jdbcType=TINYINT},
            </if>
            <if test="party_a_finance_people != null">
                #{party_a_finance_people,jdbcType=VARCHAR},
            </if>
            <if test="party_a_finance_phone != null">
                #{party_a_finance_phone,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="card_type != null">
                #{card_type,jdbcType=INTEGER},
            </if>
            <if test="cert_type != null">
                #{cert_type,jdbcType=SMALLINT},
            </if>
            <if test="legal_cert_num != null">
                #{legal_cert_num,jdbcType=VARCHAR},
            </if>
            <if test="legal_person != null">
                #{legal_person,jdbcType=VARCHAR},
            </if>
            <if test="legal_id_card != null">
                #{legal_id_card,jdbcType=VARCHAR},
            </if>
            <if test="cert_num != null">
                #{cert_num,jdbcType=VARCHAR},
            </if>
            <if test="reserve_phone != null">
                #{reserve_phone,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="party_a_finance_phone_encrypted != null">
                #{party_a_finance_phone_encrypted},
            </if>
            <if test="acc_cardno_encrypted != null">
                #{acc_cardno_encrypted},
            </if>
            <if test="legal_cert_num_encrypted != null">
                #{legal_cert_num_encrypted},
            </if>
            <if test="legal_id_card_encrypted != null">
                #{legal_id_card_encrypted},
            </if>
            <if test="cert_num_encrypted != null">
                #{cert_num_encrypted},
            </if>
            <if test="reserve_phone_encrypted != null">
                #{reserve_phone_encrypted},
            </if>

            <if test="party_a_finance_phone_token != null">
                #{party_a_finance_phone_token},
            </if>
            <if test="acc_cardno_token != null">
                #{acc_cardno_token},
            </if>
            <if test="legal_cert_num_token != null">
                #{legal_cert_num_token},
            </if>
            <if test="legal_id_card_token != null">
                #{legal_id_card_token},
            </if>
            <if test="cert_num_token != null">
                #{cert_num_token},
            </if>
            <if test="reserve_phone_token != null">
                #{reserve_phone_token},
            </if>

        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.meituan.waimai.customer.settle.domain.WmSettleDB">
        update wm_settle
        <set>
            <if test="min_pay_amount != null">
                min_pay_amount = #{min_pay_amount,jdbcType=REAL},
            </if>
            <if test="pay_period_num != null">
                pay_period_num = #{pay_period_num,jdbcType=INTEGER},
            </if>
            <if test="pay_period_unit != null">
                pay_period_unit = #{pay_period_unit,jdbcType=TINYINT},
            </if>
            <if test="province != null">
                province = #{province,jdbcType=INTEGER},
            </if>
            <if test="city != null">
                city = #{city,jdbcType=INTEGER},
            </if>
            <if test="bankid != null">
                bankid = #{bankid,jdbcType=SMALLINT},
            </if>
            <if test="branchid != null">
                branchid = #{branchid,jdbcType=INTEGER},
            </if>
            <if test="branchname != null">
                branchname = #{branchname,jdbcType=VARCHAR},
            </if>
            <if test="acc_cardno != null">
                acc_cardno = #{acc_cardno,jdbcType=VARCHAR},
            </if>
            <if test="acc_name != null">
                acc_name = #{acc_name,jdbcType=VARCHAR},
            </if>
            <if test="acctype != null">
                acctype = #{acctype,jdbcType=TINYINT},
            </if>
            <if test="muid != null">
                muid = #{muid,jdbcType=INTEGER},
            </if>
            utime = unix_timestamp(),
            <if test="settle_type != null">
                settle_type = #{settle_type,jdbcType=TINYINT},
            </if>
            <if test="pay_day_of_month != null">
                pay_day_of_month = #{pay_day_of_month,jdbcType=TINYINT},
            </if>
            <if test="party_a_finance_people != null">
                party_a_finance_people = #{party_a_finance_people,jdbcType=VARCHAR},
            </if>
            <if test="party_a_finance_phone != null">
                party_a_finance_phone = #{party_a_finance_phone,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="card_type != null">
                card_type = #{card_type,jdbcType=INTEGER},
            </if>
            <if test="cert_type != null">
                cert_type = #{cert_type,jdbcType=SMALLINT},
            </if>
            <if test="legal_cert_num != null">
                legal_cert_num = #{legal_cert_num,jdbcType=VARCHAR},
            </if>
            <if test="legal_person != null">
                legal_person = #{legal_person,jdbcType=VARCHAR},
            </if>
            <if test="legal_id_card != null">
                legal_id_card = #{legal_id_card,jdbcType=VARCHAR},
            </if>
            <if test="cert_num != null">
                cert_num = #{cert_num,jdbcType=VARCHAR},
            </if>
            <if test="reserve_phone != null">
                reserve_phone  = #{reserve_phone,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status  = #{status,jdbcType=TINYINT},
            </if>
            <if test="party_a_finance_phone_encrypted != null">
                party_a_finance_phone_encrypted = #{party_a_finance_phone_encrypted},
            </if>
            <if test="acc_cardno_encrypted != null">
                acc_cardno_encrypted = #{acc_cardno_encrypted},
            </if>
            <if test="legal_cert_num_encrypted != null">
                legal_cert_num_encrypted = #{legal_cert_num_encrypted},
            </if>
            <if test="legal_id_card_encrypted != null">
                legal_id_card_encrypted = #{legal_id_card_encrypted},
            </if>
            <if test="cert_num_encrypted != null">
                cert_num_encrypted = #{cert_num_encrypted},
            </if>
            <if test="reserve_phone_encrypted != null">
                reserve_phone_encrypted = #{reserve_phone_encrypted},
            </if>

            <if test="party_a_finance_phone_token != null">
                party_a_finance_phone_token = #{party_a_finance_phone_token},
            </if>
            <if test="acc_cardno_token != null">
                acc_cardno_token = #{acc_cardno_token},
            </if>
            <if test="legal_cert_num_token != null">
                legal_cert_num_token = #{legal_cert_num_token},
            </if>
            <if test="legal_id_card_token != null">
                legal_id_card_token = #{legal_id_card_token},
            </if>
            <if test="cert_num_token != null">
                cert_num_token = #{cert_num_token},
            </if>
            <if test="reserve_phone_token != null">
                reserve_phone_token = #{reserve_phone_token},
            </if>

        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKeySelectiveWithValid"
            parameterType="com.sankuai.meituan.waimai.customer.settle.domain.WmSettleDB">
        update wm_settle
        <set>
            <if test="min_pay_amount != null">
                min_pay_amount = #{min_pay_amount,jdbcType=REAL},
            </if>
            <if test="pay_period_num != null">
                pay_period_num = #{pay_period_num,jdbcType=INTEGER},
            </if>
            <if test="pay_period_unit != null">
                pay_period_unit = #{pay_period_unit,jdbcType=TINYINT},
            </if>
            <if test="province != null">
                province = #{province,jdbcType=INTEGER},
            </if>
            <if test="city != null">
                city = #{city,jdbcType=INTEGER},
            </if>
            <if test="bankid != null">
                bankid = #{bankid,jdbcType=SMALLINT},
            </if>
            <if test="branchid != null">
                branchid = #{branchid,jdbcType=INTEGER},
            </if>
            <if test="branchname != null">
                branchname = #{branchname,jdbcType=VARCHAR},
            </if>
            <if test="acc_cardno != null">
                acc_cardno = #{acc_cardno,jdbcType=VARCHAR},
            </if>
            <if test="acc_name != null">
                acc_name = #{acc_name,jdbcType=VARCHAR},
            </if>
            <if test="acctype != null">
                acctype = #{acctype,jdbcType=TINYINT},
            </if>
            <if test="muid != null">
                muid = #{muid,jdbcType=INTEGER},
            </if>
            utime = unix_timestamp(),
            <if test="settle_type != null">
                settle_type = #{settle_type,jdbcType=TINYINT},
            </if>
            <if test="pay_day_of_month != null">
                pay_day_of_month = #{pay_day_of_month,jdbcType=TINYINT},
            </if>
            <if test="party_a_finance_people != null">
                party_a_finance_people = #{party_a_finance_people,jdbcType=VARCHAR},
            </if>
            <if test="party_a_finance_phone != null">
                party_a_finance_phone = #{party_a_finance_phone,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="valid != null">
                valid = #{valid,jdbcType=TINYINT},
            </if>
            <if test="card_type != null">
                card_type = #{card_type,jdbcType=INTEGER},
            </if>
            <if test="cert_type != null">
                cert_type = #{cert_type,jdbcType=SMALLINT},
            </if>
            <if test="legal_cert_num != null">
                legal_cert_num = #{legal_cert_num,jdbcType=VARCHAR},
            </if>
            <if test="legal_person != null">
                legal_person = #{legal_person,jdbcType=VARCHAR},
            </if>
            <if test="legal_id_card != null">
                legal_id_card = #{legal_id_card,jdbcType=VARCHAR},
            </if>
            <if test="cert_num != null">
                cert_num = #{cert_num,jdbcType=VARCHAR},
            </if>
            <if test="reserve_phone != null">
                reserve_phone  = #{reserve_phone,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status  = #{status,jdbcType=TINYINT},
            </if>
            <if test="party_a_finance_phone_encrypted != null">
                party_a_finance_phone_encrypted = #{party_a_finance_phone_encrypted},
            </if>
            <if test="acc_cardno_encrypted != null">
                acc_cardno_encrypted = #{acc_cardno_encrypted},
            </if>
            <if test="legal_cert_num_encrypted != null">
                legal_cert_num_encrypted = #{legal_cert_num_encrypted},
            </if>
            <if test="legal_id_card_encrypted != null">
                legal_id_card_encrypted = #{legal_id_card_encrypted},
            </if>
            <if test="cert_num_encrypted != null">
                cert_num_encrypted = #{cert_num_encrypted},
            </if>
            <if test="reserve_phone_encrypted != null">
                reserve_phone_encrypted = #{reserve_phone_encrypted},
            </if>

            <if test="party_a_finance_phone_token != null">
                party_a_finance_phone_token = #{party_a_finance_phone_token},
            </if>
            <if test="acc_cardno_token != null">
                acc_cardno_token = #{acc_cardno_token},
            </if>
            <if test="legal_cert_num_token != null">
                legal_cert_num_token = #{legal_cert_num_token},
            </if>
            <if test="legal_id_card_token != null">
                legal_id_card_token = #{legal_id_card_token},
            </if>
            <if test="cert_num_token != null">
                cert_num_token = #{cert_num_token},
            </if>
            <if test="reserve_phone_token != null">
                reserve_phone_token = #{reserve_phone_token},
            </if>

        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.sankuai.meituan.waimai.customer.settle.domain.WmSettleDB">
    update wm_settle
    set wm_contract_id = #{wm_contract_id,jdbcType=INTEGER},
      wm_customer_id = #{wmCustomerId,jdbcType=INTEGER},
      min_pay_amount = #{min_pay_amount,jdbcType=REAL},
      pay_period_num = #{pay_period_num,jdbcType=INTEGER},
      pay_period_unit = #{pay_period_unit,jdbcType=TINYINT},
      province = #{province,jdbcType=INTEGER},
      city = #{city,jdbcType=INTEGER},
      bankid = #{bankid,jdbcType=SMALLINT},
      branchid = #{branchid,jdbcType=INTEGER},
      branchname = #{branchname,jdbcType=VARCHAR},
      acc_cardno = #{acc_cardno,jdbcType=VARCHAR},
      acc_name = #{acc_name,jdbcType=VARCHAR},
      acctype = #{acctype,jdbcType=TINYINT},
      valid = #{valid,jdbcType=TINYINT},
      cuid = #{cuid,jdbcType=INTEGER},
      muid = #{muid,jdbcType=INTEGER},
      ctime = #{ctime,jdbcType=INTEGER},
      utime = unix_timestamp(),
      settle_type = #{settle_type,jdbcType=TINYINT},
      pay_day_of_month = #{pay_day_of_month,jdbcType=TINYINT},
      party_a_finance_people = #{party_a_finance_people,jdbcType=VARCHAR},
      party_a_finance_phone = #{party_a_finance_phone,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},card_type=#{card_type,jdbcType=INTEGER},
        , cert_type=#{cert_type,jdbcType=SMALLINT}, legal_cert_num=#{legal_cert_num,jdbcType=VARCHAR}, legal_person=#{legal_person,jdbcType=VARCHAR},
        legal_id_card=#{legal_id_card,jdbcType=VARCHAR}, cert_num=#{cert_num,jdbcType=VARCHAR}, reserve_phone=#{reserve_phone,jdbcType=VARCHAR},
        status = #{status}
    where id = #{id,jdbcType=INTEGER}
  </update>
    <select id="pageGetWmSettle" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wm_settle
        where wm_contract_id = #{wmContractId}
        <if test="wmSettleIdList != null and wmSettleIdList.size() > 0">
            and id in
            <foreach collection="wmSettleIdList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        and valid = 1
        limit #{pageNum}, #{pageSize}
    </select>
    <select id="getWmSettleByContractIdAndCardInfo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wm_settle
        where wm_contract_id = #{wmContractId}
        <if test="cardInfo != null and cardInfo!='' ">
            and ( acc_cardno LIKE "%${cardInfo}%" or acc_name LIKE "%${cardInfo}%" or branchname LIKE "%${cardInfo}%")
        </if>
        and valid = 1
    </select>
    
    <select id="getWmSettleIdListBySettleNameList" parameterType="list" resultType="int">
    	select id from wm_settle 
    	where name in
    	<if test="nameList!=null and nameList.size>0">
    		<foreach item="item" collection="nameList" open="(" separator="," close=")">
    			#{item}
    		</foreach>
    	</if>
    	and valid = 1
    </select>

    <select id="getWmSettleListByWmSettleIdListWithSort" parameterType="list" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM wm_settle
        WHERE id IN
        <foreach collection="wmSettleIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND valid = 1 ORDER BY id
    </select>

    <update id="updateStatusByWmCustomerId" parameterType="map">
        UPDATE wm_settle
        SET status = #{status},utime = unix_timestamp()
        WHERE wm_customer_id = #{wmCustomerId} AND valid = 1
    </update>

    <update id="updateWmCustomerIdForRefesh" parameterType="map">
        UPDATE wm_settle
        SET wm_customer_id = wm_contract_id
        WHERE id BETWEEN #{begin} AND #{end}
        AND wm_customer_id = 0
    </update>

    <select id="queryWmSettleListByWmSettleIdListWithSort" parameterType="list" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM wm_settle
        WHERE id IN
        <foreach collection="wmSettleIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and wm_customer_id=#{customerId}
        AND valid = 1 ORDER BY id
    </select>

    <select id="batchGetWmSettleStatusByWmSettleId" parameterType="list" resultMap="BaseResultMap">
        SELECT
        id,status
        FROM wm_settle
        WHERE id IN
        <foreach collection="wmSettleIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND valid = 1
    </select>

    <select id="batchGetWmSettleStatusByWmSettleIdMaster" parameterType="list" resultMap="BaseResultMap">
        /*master*/SELECT
        id,status
        FROM wm_settle
        WHERE id IN
        <foreach collection="wmSettleIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND valid = 1
    </select>

    <select id="getCertTypeById" resultType="java.lang.Integer">
        select cert_type from wm_settle where id = #{id}
    </select>

</mapper>
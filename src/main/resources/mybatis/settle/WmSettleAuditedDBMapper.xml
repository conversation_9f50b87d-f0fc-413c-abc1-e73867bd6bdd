<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sankuai.meituan.waimai.customer.settle.dao.WmSettleAuditedDBMapper" >
  <resultMap id="BaseResultMap" type="com.sankuai.meituan.waimai.customer.settle.domain.WmSettleAuditedDB" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="wm_settle_id" property="wm_settle_id" jdbcType="INTEGER" />
    <result column="wm_contract_id" property="wm_contract_id" jdbcType="INTEGER" />
    <result column="min_pay_amount" property="min_pay_amount" jdbcType="REAL" />
    <result column="pay_period_num" property="pay_period_num" jdbcType="INTEGER" />
    <result column="pay_period_unit" property="pay_period_unit" jdbcType="TINYINT" />
    <result column="province" property="province" jdbcType="INTEGER" />
    <result column="city" property="city" jdbcType="INTEGER" />
    <result column="bankid" property="bankid" jdbcType="SMALLINT" />
    <result column="branchid" property="branchid" jdbcType="INTEGER" />
    <result column="branchname" property="branchname" jdbcType="VARCHAR" />
    <result column="acc_cardno" property="acc_cardno" jdbcType="VARCHAR" />
    <result column="acc_name" property="acc_name" jdbcType="VARCHAR" />
    <result column="acctype" property="acctype" jdbcType="TINYINT" />
    <result column="valid" property="valid" jdbcType="TINYINT" />
    <result column="cuid" property="cuid" jdbcType="INTEGER" />
    <result column="muid" property="muid" jdbcType="INTEGER" />
    <result column="ctime" property="ctime" jdbcType="INTEGER" />
    <result column="utime" property="utime" jdbcType="INTEGER" />
    <result column="settle_type" property="settle_type" jdbcType="TINYINT" />
    <result column="pay_day_of_month" property="pay_day_of_month" jdbcType="TINYINT" />
    <result column="party_a_finance_people" property="party_a_finance_people" jdbcType="VARCHAR" />
    <result column="party_a_finance_phone" property="party_a_finance_phone" jdbcType="VARCHAR" />
    <result column="name" property="name" jdbcType="VARCHAR" />
    <result column="version" property="version" jdbcType="INTEGER" />

    <result column="card_type" property="card_type" jdbcType="INTEGER"/>
    <result column="cert_type" property="cert_type" jdbcType="SMALLINT"/>
    <result column="legal_cert_num" property="legal_cert_num" jdbcType="VARCHAR"/>
    <result column="legal_person" property="legal_person" jdbcType="VARCHAR"/>
    <result column="legal_id_card" property="legal_id_card" jdbcType="VARCHAR"/>
    <result column="cert_num" property="cert_num" jdbcType="VARCHAR"/>
    <result column="reserve_phone" property="reserve_phone" jdbcType="VARCHAR"/>
    <result column="wm_wallet_id" property="wm_wallet_id" jdbcType="BIGINT"/>
    <result column="wm_customer_id" property="wmCustomerId" jdbcType="INTEGER"/>

    <result column="card_valid" property="card_valid" jdbcType="INTEGER"/>
    <result column="card_invalid_reason" property="card_invalid_reason" jdbcType="VARCHAR"/>

    <result column="party_a_finance_phone_encrypted" property="party_a_finance_phone_encrypted" jdbcType="VARCHAR"/>
    <result column="acc_cardno_encrypted" property="acc_cardno_encrypted" jdbcType="VARCHAR"/>
    <result column="legal_cert_num_encrypted" property="legal_cert_num_encrypted" jdbcType="VARCHAR"/>
    <result column="legal_id_card_encrypted" property="legal_id_card_encrypted" jdbcType="VARCHAR"/>
    <result column="cert_num_encrypted" property="cert_num_encrypted" jdbcType="VARCHAR"/>
    <result column="reserve_phone_encrypted" property="reserve_phone_encrypted" jdbcType="VARCHAR"/>
    <result column="party_a_finance_phone_token" property="party_a_finance_phone_token" jdbcType="VARCHAR"/>
    <result column="acc_cardno_token" property="acc_cardno_token" jdbcType="VARCHAR"/>
    <result column="legal_cert_num_token" property="legal_cert_num_token" jdbcType="VARCHAR"/>
    <result column="legal_id_card_token" property="legal_id_card_token" jdbcType="VARCHAR"/>
    <result column="cert_num_token" property="cert_num_token" jdbcType="VARCHAR"/>
    <result column="reserve_phone_token" property="reserve_phone_token" jdbcType="VARCHAR"/>

  </resultMap>
  <sql id="Base_Column_List" >
    id, wm_contract_id, min_pay_amount, pay_period_num, pay_period_unit, province, city,
    bankid, branchid, branchname, acc_cardno, acc_name, acctype, valid, cuid, muid, ctime,
    utime, settle_type, pay_day_of_month, party_a_finance_people, party_a_finance_phone,wm_settle_id, name, version,
    card_type,cert_type,legal_cert_num,legal_person,legal_id_card,cert_num,reserve_phone,wm_wallet_id,wm_customer_id
    ,card_valid, card_invalid_reason,
    party_a_finance_phone_encrypted,acc_cardno_encrypted,legal_cert_num_encrypted,legal_id_card_encrypted,cert_num_encrypted,reserve_phone_encrypted,
    party_a_finance_phone_token,acc_cardno_token,legal_cert_num_token,legal_id_card_token,cert_num_token,reserve_phone_token
  </sql>
  <select id="getByWmContractId" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select
    <include refid="Base_Column_List" />
    from wm_settle_audited
    where wm_contract_id = #{wm_contract_id, jdbcType=INTEGER} and valid = 1
  </select>

  <select id="getByWmCustomerId" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select
    <include refid="Base_Column_List" />
    from wm_settle_audited
    where wm_customer_id = #{wmCustomerId, jdbcType=INTEGER} and valid = 1
  </select>

  <select id="getByWmCustomerIdMaster" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    /*master*/select
    <include refid="Base_Column_List" />
    from wm_settle_audited
    where wm_customer_id = #{wmCustomerId, jdbcType=INTEGER} and valid = 1
  </select>

  <select id="batchQuerySettleAudited" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM wm_settle_audited
    WHERE valid=1
    AND id IN
    <foreach collection="settleIdList" item="settleId" open="(" close=")"
             separator=",">
      #{settleId}
    </foreach>
  </select>




  <select id="pageGetWmSettleAudited" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM wm_settle_audited
    WHERE wm_contract_id = #{wmContractId, jdbcType=INTEGER}
    <if test="wmSettleIdList != null and wmSettleIdList.size() > 0">
      AND wm_settle_id IN
      <foreach collection="wmSettleIdList" item="item" index="index" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    limit #{pageNum}, #{pageSize}
  </select>
  <insert id="insertSelective" parameterType="com.sankuai.meituan.waimai.customer.settle.domain.WmSettleAuditedDB" useGeneratedKeys = "true" keyProperty = "id">
    insert into wm_settle_audited
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <!--<if test="id != null" >-->
        <!--id,-->
      <!--</if>-->
      <if test="wm_contract_id != null" >
        wm_contract_id,
      </if>
      <if test="wmCustomerId != null" >
        wm_customer_id,
      </if>
      <if test="wm_settle_id != null" >
        wm_settle_id,
      </if>
      <if test="min_pay_amount != null" >
        min_pay_amount,
      </if>
      <if test="pay_period_num != null" >
        pay_period_num,
      </if>
      <if test="pay_period_unit != null" >
        pay_period_unit,
      </if>
      <if test="province != null" >
        province,
      </if>
      <if test="city != null" >
        city,
      </if>
      <if test="bankid != null" >
        bankid,
      </if>
      <if test="branchid != null" >
        branchid,
      </if>
      <if test="branchname != null" >
        branchname,
      </if>
      <if test="acc_cardno != null" >
        acc_cardno,
      </if>
      <if test="acc_name != null" >
        acc_name,
      </if>
      <if test="acctype != null" >
        acctype,
      </if>
        valid,
      <if test="cuid != null" >
        cuid,
      </if>
      <if test="muid != null" >
        muid,
      </if>
        ctime,
        utime,
      <if test="settle_type != null" >
        settle_type,
      </if>
      <if test="pay_day_of_month != null" >
        pay_day_of_month,
      </if>
      <if test="party_a_finance_people != null" >
        party_a_finance_people,
      </if>
      <if test="party_a_finance_phone != null" >
        party_a_finance_phone,
      </if>
      <if test="name != null">
        name,
      </if>
      version,
      <if test="card_type != null">
        card_type,
      </if>
      <if test="cert_type != null">
        cert_type,
      </if>
      <if test="legal_cert_num != null">
        legal_cert_num,
      </if>
      <if test="legal_person != null">
        legal_person,
      </if>
      <if test="legal_id_card != null">
        legal_id_card,
      </if>
      <if test="cert_num != null">
        cert_num,
      </if>
      <if test="reserve_phone != null">
        reserve_phone,
      </if>
      <if test="wm_wallet_id != null">
        wm_wallet_id,
      </if>

      <if test="card_valid != null">
        card_valid,
      </if>
      <if test="card_invalid_reason != null">
        card_invalid_reason,
      </if>
      <if test="acc_cardno_encrypted != null">
        acc_cardno_encrypted,
      </if>
      <if test="party_a_finance_phone_encrypted != null">
        party_a_finance_phone_encrypted,
      </if>
      <if test="legal_cert_num_encrypted != null">
        legal_cert_num_encrypted,
      </if>
      <if test="legal_id_card_encrypted != null">
        legal_id_card_encrypted,
      </if>
      <if test="cert_num_encrypted != null">
        cert_num_encrypted,
      </if>
      <if test="reserve_phone_encrypted != null">
        reserve_phone_encrypted,
      </if>

      <if test="party_a_finance_phone_token != null">
        party_a_finance_phone_token,
      </if>
      <if test="acc_cardno_token != null">
        acc_cardno_token,
      </if>
      <if test="legal_cert_num_token != null">
        legal_cert_num_token,
      </if>
      <if test="legal_id_card_token != null">
        legal_id_card_token,
      </if>
      <if test="cert_num_token != null">
        cert_num_token,
      </if>
      <if test="reserve_phone_token != null">
        reserve_phone_token,
      </if>

    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <!--<if test="id != null" >-->
        <!--#{id,jdbcType=INTEGER},-->
      <!--</if>-->
      <if test="wm_contract_id != null" >
        #{wm_contract_id,jdbcType=INTEGER},
      </if>
      <if test="wmCustomerId != null" >
        #{wmCustomerId,jdbcType=INTEGER},
      </if>
      <if test="wm_settle_id != null" >
        #{wm_settle_id,jdbcType=INTEGER},
      </if>
      <if test="min_pay_amount != null" >
        #{min_pay_amount,jdbcType=REAL},
      </if>
      <if test="pay_period_num != null" >
        #{pay_period_num,jdbcType=INTEGER},
      </if>
      <if test="pay_period_unit != null" >
        #{pay_period_unit,jdbcType=TINYINT},
      </if>
      <if test="province != null" >
        #{province,jdbcType=INTEGER},
      </if>
      <if test="city != null" >
        #{city,jdbcType=INTEGER},
      </if>
      <if test="bankid != null" >
        #{bankid,jdbcType=SMALLINT},
      </if>
      <if test="branchid != null" >
        #{branchid,jdbcType=INTEGER},
      </if>
      <if test="branchname != null" >
        #{branchname,jdbcType=VARCHAR},
      </if>
      <if test="acc_cardno != null" >
        #{acc_cardno,jdbcType=VARCHAR},
      </if>
      <if test="acc_name != null" >
        #{acc_name,jdbcType=VARCHAR},
      </if>
      <if test="acctype != null" >
        #{acctype,jdbcType=TINYINT},
      </if>
      1,
      <if test="cuid != null" >
        #{cuid,jdbcType=INTEGER},
      </if>
      <if test="muid != null" >
        #{muid,jdbcType=INTEGER},
      </if>
      unix_timestamp(),
      unix_timestamp(),
      <if test="settle_type != null" >
        #{settle_type,jdbcType=TINYINT},
      </if>
      <if test="pay_day_of_month != null" >
        #{pay_day_of_month,jdbcType=TINYINT},
      </if>
      <if test="party_a_finance_people != null" >
        #{party_a_finance_people,jdbcType=VARCHAR},
      </if>
      <if test="party_a_finance_phone != null" >
        #{party_a_finance_phone,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      0,
      <if test="card_type != null">
        #{card_type,jdbcType=INTEGER},
      </if>
      <if test="cert_type != null">
        #{cert_type,jdbcType=SMALLINT},
      </if>
      <if test="legal_cert_num != null">
        #{legal_cert_num,jdbcType=VARCHAR},
      </if>
      <if test="legal_person != null">
        #{legal_person,jdbcType=VARCHAR},
      </if>
      <if test="legal_id_card != null">
        #{legal_id_card,jdbcType=VARCHAR},
      </if>
      <if test="cert_num != null">
        #{cert_num,jdbcType=VARCHAR},
      </if>
      <if test="reserve_phone != null">
        #{reserve_phone,jdbcType=VARCHAR},
      </if>
      <if test="wm_wallet_id != null">
        #{wm_wallet_id,jdbcType=BIGINT},
      </if>

      <if test="card_valid != null">
        #{card_valid,jdbcType=INTEGER},
      </if>
      <if test="card_invalid_reason != null">
        #{card_invalid_reason,jdbcType=VARCHAR},
      </if>
      <if test="acc_cardno_encrypted != null">
        #{acc_cardno_encrypted},
      </if>
      <if test="party_a_finance_phone_encrypted != null">
        #{party_a_finance_phone_encrypted},
      </if>
      <if test="legal_cert_num_encrypted != null">
        #{legal_cert_num_encrypted},
      </if>
      <if test="legal_id_card_encrypted != null">
        #{legal_id_card_encrypted},
      </if>
      <if test="cert_num_encrypted != null">
        #{cert_num_encrypted},
      </if>
      <if test="reserve_phone_encrypted != null">
        #{reserve_phone_encrypted},
      </if>

      <if test="party_a_finance_phone_token != null">
        #{party_a_finance_phone_token},
      </if>
      <if test="acc_cardno_token != null">
        #{acc_cardno_token},
      </if>
      <if test="legal_cert_num_token != null">
        #{legal_cert_num_token},
      </if>
      <if test="legal_id_card_token != null">
        #{legal_id_card_token},
      </if>
      <if test="cert_num_token != null">
        #{cert_num_token},
      </if>
      <if test="reserve_phone_token != null">
        #{reserve_phone_token},
      </if>

    </trim>
  </insert>
  <update id="updateByWmSettleId" parameterType="com.sankuai.meituan.waimai.customer.settle.domain.WmSettleAuditedDB" >
    update wm_settle_audited
    <set >
      <if test="min_pay_amount != null" >
        min_pay_amount = #{min_pay_amount,jdbcType=REAL},
      </if>
      <if test="pay_period_num != null" >
        pay_period_num = #{pay_period_num,jdbcType=INTEGER},
      </if>
      <if test="pay_period_unit != null" >
        pay_period_unit = #{pay_period_unit,jdbcType=TINYINT},
      </if>
      <if test="province != null" >
        province = #{province,jdbcType=INTEGER},
      </if>
      <if test="city != null" >
        city = #{city,jdbcType=INTEGER},
      </if>
      <if test="bankid != null" >
        bankid = #{bankid,jdbcType=SMALLINT},
      </if>
      <if test="branchid != null" >
        branchid = #{branchid,jdbcType=INTEGER},
      </if>
      <if test="branchname != null" >
        branchname = #{branchname,jdbcType=VARCHAR},
      </if>
      <if test="acc_cardno != null" >
        acc_cardno = #{acc_cardno,jdbcType=VARCHAR},
      </if>
      <if test="acc_name != null" >
        acc_name = #{acc_name,jdbcType=VARCHAR},
      </if>
      <if test="acctype != null" >
        acctype = #{acctype,jdbcType=TINYINT},
      </if>
      <if test="muid != null" >
        muid = #{muid,jdbcType=INTEGER},
      </if>
      utime = unix_timestamp(),
      <if test="settle_type != null" >
        settle_type = #{settle_type,jdbcType=TINYINT},
      </if>
      <if test="pay_day_of_month != null" >
        pay_day_of_month = #{pay_day_of_month,jdbcType=TINYINT},
      </if>
      <if test="party_a_finance_people != null" >
        party_a_finance_people = #{party_a_finance_people,jdbcType=VARCHAR},
      </if>
      <if test="party_a_finance_phone != null" >
        party_a_finance_phone = #{party_a_finance_phone,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      version = version+1,
      <if test="card_type != null">
        card_type = #{card_type,jdbcType=INTEGER},
      </if>
      <if test="cert_type != null">
        cert_type = #{cert_type,jdbcType=SMALLINT},
      </if>
      <if test="legal_cert_num != null">
        legal_cert_num = #{legal_cert_num,jdbcType=VARCHAR},
      </if>
      <if test="legal_person != null">
        legal_person = #{legal_person,jdbcType=VARCHAR},
      </if>
      <if test="legal_id_card != null">
        legal_id_card = #{legal_id_card,jdbcType=VARCHAR},
      </if>
      <if test="cert_num != null">
        cert_num = #{cert_num,jdbcType=VARCHAR},
      </if>
      <if test="reserve_phone != null">
        reserve_phone  = #{reserve_phone,jdbcType=VARCHAR},
      </if>
      <if test="wm_wallet_id != null">
        wm_wallet_id = #{wm_wallet_id,jdbcType=BIGINT},
      </if>

      <if test="card_valid != null">
        card_valid = #{card_valid,jdbcType=INTEGER},
      </if>

      <if test="card_invalid_reason != null">
        card_invalid_reason = #{card_invalid_reason,jdbcType=VARCHAR},
      </if>

      <if test="acc_cardno_encrypted != null">
        acc_cardno_encrypted = #{acc_cardno_encrypted},
      </if>
      <if test="party_a_finance_phone_encrypted != null">
        party_a_finance_phone_encrypted = #{party_a_finance_phone_encrypted},
      </if>
      <if test="legal_cert_num_encrypted != null">
        legal_cert_num_encrypted = #{legal_cert_num_encrypted},
      </if>
      <if test="legal_id_card_encrypted != null">
        legal_id_card_encrypted = #{legal_id_card_encrypted},
      </if>
      <if test="cert_num_encrypted != null">
        cert_num_encrypted = #{cert_num_encrypted},
      </if>
      <if test="reserve_phone_encrypted != null">
        reserve_phone_encrypted = #{reserve_phone_encrypted},
      </if>

      <if test="party_a_finance_phone_token != null">
        party_a_finance_phone_token = #{party_a_finance_phone_token},
      </if>
      <if test="acc_cardno_token != null">
        acc_cardno_token = #{acc_cardno_token},
      </if>
      <if test="legal_cert_num_token != null">
        legal_cert_num_token = #{legal_cert_num_token},
      </if>
      <if test="legal_id_card_token != null">
        legal_id_card_token = #{legal_id_card_token},
      </if>
      <if test="cert_num_token != null">
        cert_num_token = #{cert_num_token},
      </if>
      <if test="reserve_phone_token != null">
        reserve_phone_token = #{reserve_phone_token},
      </if>

    </set>
    where wm_contract_id = #{wm_contract_id,jdbcType=INTEGER}
    and wm_settle_id = #{wm_settle_id,jdbcType=INTEGER}
    and valid = 1
  </update>
  
  <select id="getWmSettleIdListBySettleNameList" parameterType="list" resultType="int">
    	select id from wm_settle_audited 
    	where name in
    	<if test="nameList!=null and nameList.size>0">
    		<foreach item="item" collection="nameList" open="(" separator="," close=")">
    			#{item}
    		</foreach>
    	</if>
    	and valid = 1
   </select>

  <select id="getWmSettleIdListByWmCustomerId" parameterType="int" resultType="int">
    select wm_settle_id
    from wm_settle_audited
    where wm_customer_id = #{wmCustomerId}
    and valid = 1
  </select>

  <select id="getWmSettleListByWmSettleIdListWithSort" parameterType="list" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM wm_settle_audited
    WHERE wm_settle_id IN
    <foreach collection="wmSettleIdList" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
    AND valid = 1 ORDER BY wm_settle_id
  </select>

  <select id="getWmSettleListByWmSettleIdAndExistWallet" parameterType="list" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM wm_settle_audited
    WHERE wm_settle_id IN
    <foreach collection="wmSettleIdList" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
    AND wm_wallet_id > 0
    AND valid = 1
  </select>

  <update id="updateWmCustomerIdForRefesh" parameterType="map">
    UPDATE wm_settle_audited
    SET wm_customer_id = wm_contract_id
    WHERE id BETWEEN #{begin} AND #{end}
    AND wm_customer_id = 0
  </update>

  <update id="deleteByWmSettleIdList" parameterType="list">
    UPDATE wm_settle_audited
    SET valid = 0, utime = unix_timestamp()
    WHERE wm_settle_id IN
    <foreach collection="toDeleteWmSettleIdAuditedList" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
    AND valid = 1
  </update>

  <select id="getByWalletId" parameterType="long" resultMap="BaseResultMap">
    SELECT
      <include refid="Base_Column_List"/>
    FROM
      wm_settle_audited
    WHERE
      wm_wallet_id = #{wmWalletId}
    AND
      valid = 1
    ORDER BY
      id
    DESC
  </select>

  <select id="getByWalletIdMaster" parameterType="long" resultMap="BaseResultMap">
    /*master*/SELECT
    <include refid="Base_Column_List"/>
    FROM
    wm_settle_audited
    WHERE
    wm_wallet_id = #{wmWalletId}
    AND
    valid = 1
    ORDER BY
    id
    DESC
  </select>

  <select id="queryWmSettleListByWmSettleIdListWithSort" parameterType="list" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM wm_settle_audited
    WHERE wm_settle_id IN
    <foreach collection="wmSettleIdList" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
    and wm_customer_id=#{customerId}
    AND valid = 1 ORDER BY wm_settle_id
  </select>

  <select id="getCertTypeByWmSettleId" resultType="java.lang.Short">
    select cert_type from wm_settle_audited where wm_settle_id = #{wmSettleId} and valid = 1 order by id desc limit 1;
  </select>
</mapper>
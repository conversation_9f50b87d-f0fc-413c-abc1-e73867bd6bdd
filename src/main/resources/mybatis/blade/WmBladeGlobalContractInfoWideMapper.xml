<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.meituan.waimai.customer.dao.blade.WmBladeGlobalContractInfoWideMapper">

    <resultMap id="EcontractGlobalInfoMap"
               type="com.sankuai.meituan.waimai.customer.domain.blade.WmBladeGlobalContractInfoWidePo">
        <result column="global_contract_id" jdbcType="BIGINT" property="globalContractId"/>
        <result column="record_key" jdbcType="VARCHAR" property="recordKey"/>
        <result column="frame_econtract_number" jdbcType="VARCHAR" property="frameEcontractNumber"/>
        <result column="sign_batch_id" jdbcType="BIGINT" property="signBatchId"/>
        <result column="sign_task_id" jdbcType="BIGINT" property="signTaskId"/>
        <result column="sign_pack_id" jdbcType="BIGINT" property="signPackId"/>
        <result column="sign_manual_batch_id" jdbcType="BIGINT" property="signManualBatchId"/>
        <result column="sign_manual_task_id" jdbcType="VARCHAR" property="signManualTaskId"/>
        <result column="contract_type" jdbcType="INTEGER" property="contractType"/>
        <result column="customer_id" jdbcType="INTEGER" property="customerId"/>
        <result column="customer_info" jdbcType="LONGVARCHAR" property="customerInfo"/>
        <result column="biz_line" jdbcType="TINYINT" property="bizLine"/>
        <result column="wm_poi_id" jdbcType="LONGVARCHAR" property="wmPoiId"/>
        <result column="wm_poi_info" jdbcType="LONGVARCHAR" property="wmPoiInfo"/>
        <result column="contract_template_id" jdbcType="VARCHAR" property="contractTemplateId"/>
        <result column="contract_template_name" jdbcType="VARCHAR" property="contractTemplateName"/>
        <result column="contract_template_version" jdbcType="VARCHAR" property="contractTemplateVersion"/>
        <result column="contract_template_info" jdbcType="VARCHAR" property="contractTemplateInfo"/>
        <result column="contract_template_responsible_uids" jdbcType="VARCHAR"
                property="contractTemplateResponsibleUids"/>
        <result column="contract_status" jdbcType="TINYINT" property="contractStatus"/>
        <result column="sign_phone" jdbcType="BIGINT" property="signPhone"/>
        <result column="signer_name" jdbcType="VARCHAR" property="signerName"/>
        <result column="pdf_create_time" jdbcType="INTEGER" property="pdfCreateTime"/>
        <result column="pdf_sign_time" jdbcType="INTEGER" property="pdfSignTime"/>
        <result column="pdf_creator_uid" jdbcType="INTEGER" property="pdfCreatorUid"/>
        <result column="pdf_creator_info" jdbcType="VARCHAR" property="pdfCreatorInfo"/>
        <result column="frame_econtract_id" jdbcType="BIGINT" property="frameEcontractId"/>
        <result column="frame_econtract_version" jdbcType="VARCHAR" property="frameEcontractVersion"/>
        <result column="contract_category" jdbcType="TINYINT" property="contractCategory"/>
        <result column="partA_name" jdbcType="VARCHAR" property="partAName"/>
        <result column="partB_name" jdbcType="VARCHAR" property="partBName"/>
        <result column="pdf_due_time" jdbcType="INTEGER" property="pdfDueTime"/>
        <result column="pdf_url" jdbcType="LONGVARCHAR" property="pdfUrl"/>
        <result column="record_biz_key" jdbcType="VARCHAR" property="recordBizKey"/>
        <result column="ctime" jdbcType="INTEGER" property="ctime"/>
        <result column="utime" jdbcType="INTEGER" property="utime"/>
        <result column="valid" jdbcType="TINYINT" property="valid"/>
    </resultMap>

    <sql id="Base_Column_List">
        global_contract_id, record_key, frame_econtract_number, sign_batch_id, sign_task_id, sign_pack_id, sign_manual_batch_id, sign_manual_task_id,
        contract_type, customer_id, customer_info, biz_line, biz_line, wm_poi_id, wm_poi_info, contract_template_id, contract_template_name,
        contract_template_version, contract_template_info, contract_template_responsible_uids, contract_status, sign_phone, signer_name,
        pdf_create_time, pdf_sign_time, pdf_creator_uid, pdf_creator_info, frame_econtract_id, frame_econtract_version,  contract_category,
        partA_name, partB_name, pdf_due_time, pdf_url, record_biz_key, ctime, utime, valid
    </sql>

    <sql id="Common_Where_Sql">
        <if test="queryCondition.globalContractId != null">
            and global_contract_id = #{queryCondition.globalContractId}
        </if>
        <if test="queryCondition.recordKey != null ">
            and record_key = #{queryCondition.recordKey}
        </if>
        <if test="queryCondition.frameEcontractNumber != null and queryCondition.frameEcontractNumber != '' ">
            and frame_econtract_number = #{queryCondition.frameEcontractNumber}
        </if>
        <if test="queryCondition.signBatchId != null and queryCondition.signBatchId > 0">
            and sign_batch_id = #{queryCondition.signBatchId}
        </if>
        <if test="queryCondition.contractType != null and queryCondition.contractType > 0">
            and contract_type = #{queryCondition.contractType}
        </if>

        <if test="queryCondition.customerId != null and queryCondition.customerId > 0">
            and customer_id = #{queryCondition.customerId}
        </if>

        <if test="queryCondition.wmPoiId != null and queryCondition.wmPoiId != '' ">
            and wm_poi_info like CONCAT('%', #{queryCondition.wmPoiId}, '%')
        </if>

        <if test="queryCondition.contractTemplateId != null and queryCondition.contractTemplateId != '' ">
            and contract_template_id like CONCAT('%', #{queryCondition.contractTemplateId}, '%')
        </if>

        <if test="queryCondition.contractTemplateVersion != null and queryCondition.contractTemplateVersion != '' ">
            and contract_template_version like CONCAT('%', #{queryCondition.contractTemplateVersion}, '%')
        </if>

        <if test="queryCondition.contractStatus != null and queryCondition.contractStatus > 0">
            and contract_status = #{queryCondition.contractStatus}
        </if>

        <if test="queryCondition.signPhone != null and queryCondition.signPhone > 0">
            and sign_phone = #{queryCondition.signPhone}
        </if>

        <if test="queryCondition.signerName != null and queryCondition.signerName != '' ">
            and signer_name like CONCAT('%', #{queryCondition.signerName}, '%')
        </if>

        <if test="queryCondition.pdfCreateTime1 != null and queryCondition.pdfCreateTime1 > 0">
            and pdf_create_time >= #{queryCondition.pdfCreateTime1}
        </if>

        <if test="queryCondition.pdfCreateTime2 != null and queryCondition.pdfCreateTime2 > 0">
            and pdf_create_time &lt;= #{queryCondition.pdfCreateTime2}
        </if>

        <if test="queryCondition.pdfSignTime1 != null and queryCondition.pdfSignTime1 > 0">
            and pdf_sign_time >= #{queryCondition.pdfSignTime1}
        </if>

        <if test="queryCondition.pdfSignTime2 != null and queryCondition.pdfSignTime2 > 0">
            and pdf_sign_time &lt;= #{queryCondition.pdfSignTime2}
        </if>

        <if test="queryCondition.pdfCreatorUid != null and queryCondition.pdfCreatorUid > 0">
            and pdf_creator_uid = #{queryCondition.pdfCreatorUid}
        </if>

        <if test="queryCondition.frameEcontractId != null and queryCondition.frameEcontractId > 0">
            and frame_econtract_id = #{queryCondition.frameEcontractId}
        </if>

        <if test="queryCondition.frameEcontractVersion != null and queryCondition.frameEcontractVersion != '' ">
            and frame_econtract_version = #{queryCondition.frameEcontractVersion}
        </if>

        <if test="queryCondition.contractCategory != null and queryCondition.contractCategory > 0 ">
            and contract_category = #{queryCondition.contractCategory}
        </if>

        <if test="queryCondition.partAName != null and queryCondition.partAName != '' ">
            and partA_name = #{queryCondition.partAName}
        </if>

        <if test="queryCondition.partBName != null and queryCondition.partBName != '' ">
            and partB_name = #{queryCondition.partBName}
        </if>

        <if test="queryCondition.pdfDueTime1 != null and queryCondition.pdfDueTime1 > 0">
            and pdf_due_time >= #{queryCondition.pdfDueTime1}
        </if>

        <if test="queryCondition.pdfDueTime2 != null and queryCondition.pdfDueTime2 > 0">
            and pdf_due_time &lt;= #{queryCondition.pdfDueTime2}
        </if>
        and valid = 1
    </sql>

    <sql id="Common_Authority_Sql">
        and (
        <if test="queryCondition.queryUid != null">
            pdf_creator_uid = #{queryCondition.queryUid}
        </if>

        <if test="queryCondition.queryUid != null">
            or contract_template_responsible_uids like CONCAT('%', #{queryCondition.queryUid}, '%')
        </if>

        <if test="queryCondition.bizLineListAuthority != null and queryCondition.bizLineListAuthority.size() > 0">
            or biz_line in
            <foreach collection="queryCondition.bizLineListAuthority" index="index" item="bizLine" open="("
                     separator=","
                     close=")">
                #{bizLine}
            </foreach>
        </if>

        <if test="queryCondition.contractTypeCodeListAuthority != null and queryCondition.contractTypeCodeListAuthority.size() > 0">
            or contract_type in
            <foreach collection="queryCondition.contractTypeCodeListAuthority" index="index" item="contractType"
                     open="("
                     separator=","
                     close=")">
                #{contractType}
            </foreach>
        </if>
        )
    </sql>

    <select id="totalWithLimitAuthority" resultType="java.lang.Long">
        select count(*)
        from wm_gloabl_contract_info_wide
        <where>
            <include refid="Common_Where_Sql"/>
            <include refid="Common_Authority_Sql"/>
        </where>
    </select>

    <select id="totalWithAllAuthority" resultType="java.lang.Long">
        select count(*)
        from wm_gloabl_contract_info_wide
        <where>
            <include refid="Common_Where_Sql"/>
        </where>
    </select>

    <select id="queryWithLimitAuthority" resultMap="EcontractGlobalInfoMap">
        select
        <include refid="Base_Column_List"/>
        from wm_gloabl_contract_info_wide
        <where>
            <include refid="Common_Where_Sql"/>
            <include refid="Common_Authority_Sql"/>
        </where>
        order by pdf_create_time DESC
        <if test="queryCondition.offset != null and queryCondition.offset >= 0">
            limit #{queryCondition.offset}, #{queryCondition.pageSize}
        </if>
    </select>

    <select id="queryWithAllAuthority" resultMap="EcontractGlobalInfoMap">
        select
        <include refid="Base_Column_List"/>
        from wm_gloabl_contract_info_wide
        <where>
            <include refid="Common_Where_Sql"/>
        </where>
        order by pdf_create_time DESC
        <if test="queryCondition.offset != null and queryCondition.offset >= 0">
            limit #{queryCondition.offset}, #{queryCondition.pageSize}
        </if>
    </select>


</mapper>
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sankuai.meituan.waimai.customer.contract.dao.WmContractVersionDBMapper">
    <resultMap id="BaseResultMap" type="com.sankuai.meituan.waimai.customer.contract.domain.WmContractVersionDB">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="wm_contract_id" property="wm_contract_id" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="version_number" property="version_number" jdbcType="VARCHAR"/>
        <result column="pdf_url" property="pdf_url" jdbcType="VARCHAR"/>
        <result column="stamp_pdf_url" property="stamp_pdf_url" jdbcType="VARCHAR"/>
        <result column="stamp_type" property="stamp_type" jdbcType="INTEGER"/>
        <result column="customer_id" property="customer_id" jdbcType="VARCHAR"/>
        <result column="mcertify_result" property="mcertify_result" jdbcType="INTEGER"/>
        <result column="valid" property="valid" jdbcType="TINYINT"/>
        <result column="ctime" property="ctime" jdbcType="INTEGER"/>
        <result column="utime" property="utime" jdbcType="INTEGER"/>
        <result column="op_uid" property="op_uid" jdbcType="INTEGER"/>
        <result column="cuid" property="cuid" jdbcType="INTEGER"/>
        <result column="type" property="type" jdbcType="TINYINT"/>
        <result column="party_b_stamp_type" property="party_b_stamp_type" jdbcType="INTEGER"/>
        <result column="party_b_customer_id" property="party_b_customer_id" jdbcType="VARCHAR"/>
        <result column="party_b_mcertify_result" property="party_b_mcertify_result" jdbcType="INTEGER"/>
        <result column="transaction_id" property="transaction_id" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, wm_contract_id, status, version_number, pdf_url, stamp_pdf_url, stamp_type, customer_id, mcertify_result,
        valid, ctime, utime, op_uid, cuid, type, party_b_stamp_type, party_b_customer_id, party_b_mcertify_result,transaction_id
    </sql>
    <insert id="insertSelective" parameterType="com.sankuai.meituan.waimai.customer.contract.domain.WmContractVersionDB"
            useGeneratedKeys="true" keyProperty="id">
        insert into wm_contract_version
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="wm_contract_id != null">
                wm_contract_id,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="version_number != null">
                version_number,
            </if>
            <if test="pdf_url != null">
                pdf_url,
            </if>
            <if test="stamp_pdf_url != null">
                stamp_pdf_url,
            </if>
            <if test="stamp_type != null">
                stamp_type,
            </if>
            <if test="customer_id != null">
                customer_id,
            </if>
            <if test="mcertify_result != null">
                mcertify_result,
            </if>
            <if test="op_uid != null">
                op_uid,
            </if>
            <if test="cuid != null">
                cuid,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="party_b_stamp_type != null">
                party_b_stamp_type,
            </if>
            <if test="party_b_customer_id != null">
                party_b_customer_id,
            </if>
            <if test="party_b_mcertify_result != null">
                party_b_mcertify_result,
            </if>
            <if test="transaction_id != null">
                transaction_id,
            </if>
            <if test="commit_info != null">
                commit_info,
            </if>
            ctime,
            utime,
            valid,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="wm_contract_id != null">
                #{wm_contract_id,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="version_number != null">
                #{version_number,jdbcType=VARCHAR},
            </if>
            <if test="pdf_url != null">
                #{pdf_url,jdbcType=VARCHAR},
            </if>
            <if test="stamp_pdf_url != null">
                #{stamp_pdf_url,jdbcType=VARCHAR},
            </if>
            <if test="stamp_type != null">
                #{stamp_type,jdbcType=INTEGER},
            </if>
            <if test="customer_id != null">
                #{customer_id,jdbcType=VARCHAR},
            </if>
            <if test="mcertify_result != null">
                #{mcertify_result,jdbcType=INTEGER},
            </if>
            <if test="op_uid != null">
                #{op_uid,jdbcType=INTEGER},
            </if>
            <if test="cuid != null">
                #{cuid,jdbcType=INTEGER},
            </if>
            <if test="type != null">
                #{type,jdbcType=TINYINT},
            </if>
            <if test="party_b_stamp_type != null">
                #{party_b_stamp_type,jdbcType=INTEGER},
            </if>
            <if test="party_b_customer_id != null">
                #{party_b_customer_id,jdbcType=VARCHAR},
            </if>
            <if test="party_b_mcertify_result != null">
                #{party_b_mcertify_result,jdbcType=INTEGER},
            </if>
            <if test="transaction_id != null">
                #{transaction_id},
            </if>
            <if test="commit_info != null">
                #{commit_info},
            </if>
            unix_timestamp(),
            unix_timestamp(),
            1,
        </trim>
    </insert>

    <select id="getByWmContractIdAndStatus" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM wm_contract_version
        WHERE wm_contract_id = #{wmContractId, jdbcType=INTEGER}
        AND type = #{type, jdbcType=TINYINT}
        <if test="statusList != null and statusList.size() > 0">
            and status in
            <foreach item="item" collection="statusList" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        AND valid = 1
        AND pdf_url != ''
        ORDER BY id DESC
    </select>

    <select id="getByWmContractIdAndStatusAndTypes" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM wm_contract_version
        WHERE wm_contract_id = #{wmContractId, jdbcType=INTEGER}
        <if test="statusList != null and statusList.size() > 0">
            and status in
            <foreach item="item" collection="statusList" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="typeList != null and typeList.size() > 0">
            and type in
            <foreach item="item" collection="typeList" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        AND valid = 1
        AND pdf_url != ''
        ORDER BY id DESC
    </select>

    <select id="getByWmContractIdsAndStatus" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM wm_contract_version
        WHERE 1 = 1
        <choose>
            <when test="wmContractIds != null and wmContractIds.size() > 0">
                and wm_contract_id in
                <foreach item="item" index="index" collection="wmContractIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                and 1=0
            </otherwise>
        </choose>
        AND type = #{type, jdbcType=TINYINT}
        <if test="statusList != null and statusList.size() > 0">
            and status in
            <foreach item="item" collection="statusList" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        AND valid = 1
        AND pdf_url != ''
        ORDER BY id DESC
    </select>




    <select id="getByWmContractIdAndTypeAndStatus" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM wm_contract_version
        WHERE wm_contract_id = #{wmContractId, jdbcType=INTEGER}
        <if test="typeList != null and typeList.size() > 0">
            and type in
            <foreach item="item" collection="typeList" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="statusList != null and statusList.size() > 0">
            and status in
            <foreach item="item" collection="statusList" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        AND valid = 1
        AND pdf_url != ''
        ORDER BY id DESC
    </select>

    <select id="getLastWmContractVersionByWmContractIdAndTypesMaster" parameterType="map" resultMap="BaseResultMap">
        /*master*/SELECT
        <include refid="Base_Column_List"/>
        FROM wm_contract_version
        WHERE wm_contract_id = #{wmContractId}
        AND type IN
        <foreach collection="typeList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND valid = 1
        ORDER BY id DESC limit 1
    </select>

    <select id="getLastByWmContractIdAndTypeAndStatusList" parameterType="map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM wm_contract_version
        WHERE wm_contract_id = #{wmContractId}
        AND type = #{type}
        AND status IN
        <foreach collection="statusList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND valid = 1
        ORDER BY id DESC limit 1
    </select>

    <select id="queryByWmContractIdAndType" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM wm_contract_version
        WHERE wm_contract_id = #{wmContractId}
        AND type = #{type}
        AND valid = 1
    </select>
</mapper>
package com.sankuai.meituan.waimai.constant.contract;

/**
 * @description:
 * @author: liuyunjie05
 * @create: 2025/5/30 11:40
 */
public enum ContractSceneEnum {

    SAVE_CONTRACT(1, "保存合同"),
    GET_SIMPLE_INFO_INFO(2, "查询合同初始信息"),
    GET_CONTRACT_INFO(3, "查询合同详细信息")
    ;

    private final int code;
    private final String desc;

    ContractSceneEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static boolean isSave(ContractSceneEnum sceneEnum) {
        return SAVE_CONTRACT == sceneEnum;
    }

    public static boolean isGetSimpleInfo(ContractSceneEnum sceneEnum) {
        return GET_SIMPLE_INFO_INFO == sceneEnum;
    }

}

package com.sankuai.meituan.waimai.constant;

/**
 * 灰度类型
 */
public enum CustomerGrayTypeEnum {

    ALL(0, "全部"),

    CUSTOMER_MULTIPLEX(1, "客户复用灰度");

    private int code;

    private String desc;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    CustomerGrayTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static CustomerGrayTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (CustomerGrayTypeEnum typeEnum : values()) {
            if (typeEnum.getCode() == code) {
                return typeEnum;
            }
        }
        return null;
    }
}

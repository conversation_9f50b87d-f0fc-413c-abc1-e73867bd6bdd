package com.sankuai.meituan.waimai.constant;

/**
 * -@author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * -@description:
 * -@date: 2022/11/23 11:40 PM
 */
public enum QueryTaskTabEnum {

    QUERY_TASK_TAB_TO_SIGN(1, "待签约"),
    QUERY_TASK_TAB_COMPLETE(2, "已签约");

    private Integer type;
    private String desc;

    QueryTaskTabEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

}

package com.sankuai.meituan.waimai.constant.customer;

/**
 * 客户切换失败原因枚举
 */
public enum CustomerSwitchCancelTypeEnum {
    C1_CONTRACT_ERROR("C1合同异常导致取消流程"),
    DELIVERY_CONTRACT_ERROR("配送合同异常导致取消流程"),
    SETTLE_CONTRACT_ERROR("结算合同异常导致取消流程"),
    OTHER_SYSTEM_ERROR("系统异常导致取消流程"),
    BUSINESS_ERROR("业务异常导致取消流程"),
    BD_CANCEL("BD主动取消流程"),
    POI_CANCEL("商家主动取消流程");

    private String desc;

    CustomerSwitchCancelTypeEnum(String desc) {
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }
}

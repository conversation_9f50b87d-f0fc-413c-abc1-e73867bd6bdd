package com.sankuai.meituan.waimai.vo.sc.canteenstall;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.canteenstall.*;
import lombok.Data;

/**
 * 食堂档口绑定任务信息VO
 * <AUTHOR>
 * @date 2024/05/18
 * @email <EMAIL>
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class WmCanteenStallBindVO {
    /**
     * 档口绑定任务ID
     */
    private Integer bindId;
    /**
     * 门店ID
     */
    private String wmPoiId;
    /**
     * 门店名称
     */
    private String wmPoiName;
    /**
     * 线索ID
     */
    private String wdcClueId;
    /**
     * 线索生成状态
     * {@link CanteenStallClueGenerateStatusEnum}
     */
    private Integer clueGenerateStatus;
    /**
     * 线索生成状态DESC
     * {@link CanteenStallClueGenerateStatusEnum}
     */
    private String clueGenerateStatusDesc;
    /**
     * 线索生成失败原因
     */
    private String clueGenerateFailReason;
    /**
     * 线索绑定状态
     * {@link CanteenStallClueBindStatusEnum}
     */
    private Integer clueBindStatus;
    /**
     * 线索绑定状态DESC
     * {@link CanteenStallClueBindStatusEnum}
     */
    private String clueBindStatusDesc;
    /**
     * 线索绑定失败原因
     */
    private String clueBindFailReason;
    /**
     * 线索跟进人UID
     */
    private Integer clueFollowUpOwnerUid;
    /**
     * 线索跟进人MIS
     */
    private String clueFollowUpOwnerMis;
    /**
     * 线索跟进人姓名
     */
    private String clueFollowUpOwnerName;
    /**
     * 线索跟进状态
     * {@link CanteenStallClueFollowUpStatusEnum}
     */
    private Integer clueFollowUpStatus;
    /**
     * 线索跟进状态DESC
     * {@link CanteenStallClueFollowUpStatusEnum}
     */
    private String clueFollowUpStatusDesc;
    /**
     * 外卖门店绑定状态
     * {@link CanteenStallWmPoiBindStatusEnum}
     */
    private Integer wmPoiBindStatus;
    /**
     * 外卖门店绑定状态DESC
     * {@link CanteenStallWmPoiBindStatusEnum}
     */
    private String wmPoiBindStatusDesc;
    /**
     * 外卖门店绑定失败原因
     */
    private String wmPoiBindFailReason;
    /**
     * 创建时间
     */
    private Integer ctime;
    /**
     * 更新时间
     */
    private Integer utime;
    /**
     * 线索跟进审批状态
     * {@link CanteenStallAuditStatusEnum}
     */
    private Integer auditStatus;
    /**
     * 解绑按钮
     * {@link com.sankuai.meituan.waimai.thrift.customer.constant.sc.ScGrayEnum}
     */
    private Integer unbindButtonAuth;
    /**
     * 去上单按钮
     * {@link com.sankuai.meituan.waimai.thrift.customer.constant.sc.ScGrayEnum}
     */
    private Integer placeOrderButtonAuth;
    /**
     * 操作日志按钮
     * {@link com.sankuai.meituan.waimai.thrift.customer.constant.sc.ScGrayEnum}
     */
    private Integer logButtonAuth;
    /**
     * 标记正常按钮
     * {@link com.sankuai.meituan.waimai.thrift.customer.constant.sc.ScGrayEnum}
     */
    private Integer normalButtonAuth;
    /**
     * 标记异常按钮
     * {@link com.sankuai.meituan.waimai.thrift.customer.constant.sc.ScGrayEnum}
     */
    private Integer abnormalButtonAuth;
    /**
     * 跟进状态审批中按钮
     * {@link com.sankuai.meituan.waimai.thrift.customer.constant.sc.ScGrayEnum}
     */
    private Integer auditingButtonAuth;


    /**
     * 接换绑操作次数
     */
    private Integer unbindOperationCount;

    /**
     * 审批进度URL
     */
    private String approvalProgressUrl;

    /**
     * 解换绑任务id（内部）
     */
    private String taskId;

    /**
     * 解换绑任务系统父任务id
     */
    private String ticketId;

    private Boolean isUnbindAllowed;

    /**
     * 换绑按钮展示逻辑
     */
    private Integer changebindButtonAuth;

    /**
     * 换绑按钮提示信息
     */
    private String changebindButtonAuthReason;

    /**
     * 解绑按钮提示信息
     */
    private String unbindButtonAuthReason;


    /**
     * 解绑是否升级到高版本
     */
    private Boolean isUnbindSwitchUpdate;


}
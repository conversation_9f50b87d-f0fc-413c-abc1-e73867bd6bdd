package com.sankuai.meituan.waimai.vo.customer;

import lombok.Data;

import java.util.Map;

@Data
public class WmCustomerBrandListVo {

    // 客户ID
    private Integer customerId;

    //品牌ID
    private Integer brandId;

    // 品牌名称
    private String brandName;

    // 品牌Logo
    private String brandLogo;

    // 品牌类型名称
    private String brandTypeName;

    // 客户品牌门店数量
    private Integer cusBrandPoiNum;

    // 品牌外卖门店数量
    private Integer wmBrandPoiNum;

    // 品牌物理门店数量
    private Integer physicalBrandPoiNum;

    // 权限对象
    private Map<String,Boolean> authInfo;
}

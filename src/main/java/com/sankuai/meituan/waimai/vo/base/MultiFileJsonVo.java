package com.sankuai.meituan.waimai.vo.base;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.MultiFileJsonBo;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 16-3-4.
 */
public class MultiFileJsonVo {
    public static class CustomerFile {

        private String filename = "图片";

        private String url;

        public CustomerFile() {
        }

        public CustomerFile(String fileName, String url) {
            this.filename = fileName;
            this.url = url;
        }

        public String getFilename() {
            return filename;
        }

        public void setFilename(String filename) {
            this.filename = filename;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }
    }

    public MultiFileJsonVo(List<CustomerFile> list) {
        this.list = list;
    }

    public MultiFileJsonVo() {

    }

    List<CustomerFile> list = Lists.newArrayList();

    public List<CustomerFile> getList() {
        return list;
    }

    public void setList(List<CustomerFile> list) {
        this.list = list;
    }

    public static MultiFileJsonVo toMultiFileJsonVo(String msg) {
        if (StringUtils.isBlank(msg)) {
            return new MultiFileJsonVo();
        }

        return JSON.parseObject(msg, MultiFileJsonVo.class);
    }

}

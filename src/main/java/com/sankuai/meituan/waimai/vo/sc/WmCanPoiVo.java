package com.sankuai.meituan.waimai.vo.sc;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class WmCanPoiVo {
    /**
     * 门店ID
     */
    private long wmPoiId;
    /**
     * 门店名称
     */
    private String wmPoiName;
    /**
     * 是否已经生效. 0：未生效，1：生效
     */
    private int effective;
    /**
     * 门店状态
     */
    private int wmPoiStatus;
    /**
     * 门店状态描述
     */
    private String wmPoiStatusDes;
}

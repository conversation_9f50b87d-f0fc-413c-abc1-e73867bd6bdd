package com.sankuai.meituan.waimai.vo.sc.canteenstall;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

/**
 * 食堂档口创建线索详情信息VO
 * <AUTHOR>
 * @date 2024/05/18
 * @email <EMAIL>
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class WmCanteenStallClueVO {
    /**
     * 主键ID
     */
    private Integer id;
    /**
     * 食堂档口管理任务ID
     */
    private Integer manageId;
    /**
     * 档口绑定任务ID
     */
    private Integer bindId;
    /**
     * 食堂主键ID
     */
    private Integer canteenPrimaryId;
    /**
     * 门店名称
     */
    private String cluePoiName;
    /**
     * 门店地址
     */
    private String cluePoiAddress;
    /**
     * 门店品类
     */
    private String cluePoiCate;
    /**
     * 门店末级品类ID
     */
    private String clueLeafCateId;
    /**
     * 外卖城市ID(二级城市ID)
     */
    private Integer secondCityId;
    /**
     * 外卖城市(二级城市)
     */
    private String secondCityName;
    /**
     * 外卖行政区ID(三级城市ID)
     */
    private Integer thirdCityId;
    /**
     * 外卖行政区(三级城市)
     */
    private String thirdCityName;
    /**
     * 门店电话
     */
    private String cluePoiPhoneNum;
    /**
     * 门店地址
     */
    private String cluePoiCoordinate;

}

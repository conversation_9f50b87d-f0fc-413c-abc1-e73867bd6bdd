package com.sankuai.meituan.waimai.vo.sc;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.SchoolAorTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 学校列表页面-学校展示信息类
 *
 * <AUTHOR> lin
 * @date 2022/08/05
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class SchoolListDelSensitiveFiledVo {
    /**
     * 物理主键
     */
    private int id;
    /**
     * 学校ID
     */
    private int schoolId;
    /**
     * 学校标志码
     */
    private long schoolCode;
    /**
     * 学校名称
     */
    private String schoolName;
    /**
     * 学校类型：211：1、985：2、211&985：3、普通公立：4、省重点：5、私立：6、专科：8、其他：9
     */
    private int schoolType;
    /**
     * 学校类型描述
     */
    private String typeDesc;
    /**
     * 供给分级：SKR直营：1、KR直营：2、食堂直营：3
     */
    private int grade;
    /**
     * 分级描述
     */
    private String gradeDesc;
    /**
     * 学校KP
     */
    private String schoolKp;
    /**
     * 蜂窝ID
     */
    private int aorId;
    /**
     * 蜂窝类型
     * {@link SchoolAorTypeEnum}
     */
    private Integer aorType;
    /**
     * 蜂窝类型描述
     */
    private String aorTypeDesc;
    /**
     * 蜂窝名称
     */
    private String aorName;
    /**
     * 区域
     */
    private String area;
    /**
     * 城市团队
     */
    private String cityTeam;
    /**
     * 物理城市
     */
    private String cityName;
    /**
     * 城市ID
     */
    private int cityId;
    /**
     * 食堂数量
     */
    private int canteenNum;
    /**
     * 状态
     */
    private int schoolStatus;
    /**
     * 状态描述
     */
    private String statusDesc;
    /**
     * 用户名称
     */
    private String userName;
    /**
     * 用户ID
     */
    private int userId;
    /**
     * 更新时间
     */
    private int utime;
    /**
     * 创建时间(时间戳)
     */
    private int ctime;
    /**
     * 创建时间(yyyy-MM-dd HH:mm:ss)
     */
    private String ctimeStr;
    /**
     * 档口总数
     */
    private Integer stallTotal;
    /**
     * 合作档口总数
     */
    private Integer coStallTotal;
    /**
     * 额外字段json-map，使用方式:
     * key新增属性：value新增属性对应的值
     */
    private String ext;
    /**
     * 学校责任人
     */
    private String responsiblePerson;
    /**
     * 权限控制{@link com.sankuai.meituan.waimai.thrift.customer.constant.sc.ScGrayEnum}
     */
    private int gray;
    /**
     * 合作状态
     */
    private Integer wmCoStatus;
    /**
     * 合作状态描述
     */
    private String wmCoStatusDesc;
    /**
     * 合同编号
     */
    private String contractNum;
    /**
     * 线索ID
     */
    private String wdcClueId;
    /**
     * 学校地址
     */
    private String schoolAddress;
    /**
     * 登陆用户是否仅为蜂窝负责人（蜂窝主副负责人）
     */
    private boolean prueAorResponsible;
    /**
     * 学校标签列表
     */
    private List<WmScSchoolLabelVO> schoolLabels;
    /**
     * 修改按钮权限: true-有权限, false-无权限
     */
    private Boolean editButtonAuth;
    /**
     * 删除按钮权限: true-有权限, false-无权限
     */
    private Boolean deleteButtonAuth;
    /**
     * 操作记录按钮权限: true-有权限, false-无权限
     */
    private Boolean logButtonAuth;
    /**
     * 分配责任人按钮权限: true-有权限, false-无权限
     */
    private Boolean bindRpButtonAuth;
    /**
     * 交付管理按钮权限: true-有权限, false-无权限
     */
    private Boolean deliveryButtonAuth;

    private Integer schoolOfflineBizStallNum;

    private Integer schoolPreOnlineStallNum;

    /**
     * 食堂品类数量
     */
    private Integer canteenCategoryNum;

    /**
     * 学校分类
     */
    private Integer schoolCategory;

    /**
     * 学校分类描述
     */
    private String schoolCategoryDesc;
}

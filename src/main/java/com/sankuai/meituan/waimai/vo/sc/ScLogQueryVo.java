package com.sankuai.meituan.waimai.vo.sc;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @program: scm
 * @description: 查询参数
 * @author: jianghuimin02
 * @create: 2020-04-16 15:24
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ScLogQueryVo {
    /**
     * 页面号
     */
    private Integer pageNo;
    /**
     * 页面大小
     */
    private Integer pageSize;
    /**
     * 客户ID
     */
    private Integer customerId;
    /**
     * 模块类型。com.sankuai.meituan.waimai.thrift.customer.constant.sc.SchoolModuleTypeEnum
     */
    private Short moduleType;
    /**
     * 模块ID
     */
    private Integer moduleId;
    /**
     * 操作类型
     */
    private Byte opType;
    /**
     * 变更内容
     */
    private String content;
    /**
     * 操作人
     */
    private String opUser;
    /**
     * 操作开始时间
     */
    private String opTimeStart;
    /**
     * 操作开始时间
     */
    private Integer opTimeStartSeconds;
    /**
     * 操作结束时间
     */
    private String opTimeEnd;
    /**
     * 操作结束时间
     */
    private Integer opTimeEndSeconds;
    /**
     * 操作日志时间区间
     */
    private Integer logTimeInterval = 0;
    /**
     * 查询信息来源   1->学校  2->食堂
     */
    private Short sourceType;
    /**
     * 食堂主键ID
     */
    private Integer canteenPrimaryId;
    /**
     * 档口管理ID
     */
    private Integer manageId;
}

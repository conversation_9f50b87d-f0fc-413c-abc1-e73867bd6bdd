package com.sankuai.meituan.waimai.vo.settle;

import com.sankuai.meituan.waimai.vo.base.WmFieldAuditStatusVo;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

@Setter
@Getter
public class WmSettleShangdanVo {
    private int wmCustomerId;
    private boolean hasEffective;//是否有生效数据
    private String failReason;//失败原因
    private boolean canModify;//当前状态是否可编辑
    private byte shangdanStatus;//0:上一步下一步 1:保存 2:跳转
    private Map<String, WmFieldAuditStatusVo> diff;//变更信息
    private byte settleStatus;//关联客户的结算模块状态
    private boolean singlePoi;//是否为单店
    private WmSettleVo wmSettleVo;//单个结算详情
    private List<String> supplementalUrl;//补充协议
    private List<String> qdbUrl;//钱袋宝协议
    private boolean postpositionalSettle;//单店时，门店是否有后置标
    private boolean canUseBankCardType;
    //原客户是否有切换中的门店
    private boolean hasCustomerSwitchingPoi;
    /**
     *页面全局是否可读因子
     */
    private boolean pageCanRead;
    /**
     *页面全局是否可写因子
     */
    private boolean pageCanWrite;

}

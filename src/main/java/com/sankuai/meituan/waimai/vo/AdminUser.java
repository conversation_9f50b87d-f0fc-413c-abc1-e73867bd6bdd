package com.sankuai.meituan.waimai.vo;

import com.google.common.base.Function;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sankuai.meituan.auth.vo.RoleOrg;
import com.sankuai.meituan.auth.vo.User;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import com.sankuai.meituan.waimai.infra.domain.WmOpenCity;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * 运营后台用户逻辑对象，封装upm、org、WmBdCity等逻辑
 * 
 * <AUTHOR>
 * @date 2014年4月23日 下午9:17:46
 * 
 */
public class AdminUser {

	private User user;
	private WmEmploy employeeInfo;

	private Set<String> roleNameSet; // upm角色名称set

	private Set<Integer> roleIdSet; // upm角色ID之set

	private List<WmOpenCity> allowCityList; // 有权限的城市列表

	private AdminUser(User user, WmEmploy employeeInfo, List<RoleOrg> roleOrgList, List<WmOpenCity> allowCityList) {
		Preconditions.checkNotNull(user, "user不能为null");
		this.user = user;
		this.employeeInfo = employeeInfo;
		roleNameSet = Sets.newHashSet();
		roleIdSet = Sets.newHashSet();
		if (roleOrgList != null) {
			for (RoleOrg roleOrg : roleOrgList) {
				roleNameSet.add(roleOrg.getRoleName());
				roleIdSet.add(roleOrg.getRoleId());
			}
		}
		this.allowCityList = allowCityList != null ? allowCityList : new ArrayList<WmOpenCity>();
	}

	/**
	 * 是否org统称BD，BD、BDM、CM
	 */
	public boolean isOrgGeneralBD() {
		return isOrgBD() || isOrgBDM() || isOrgCM();
	}

	public boolean isOrgBD() {
		if (employeeInfo == null) {
			return false;
		}
		return employeeInfo.getPosId() != 0 && employeeInfo.getPosId() == 80;
	}

	public boolean isOrgBDM() {
		if (employeeInfo == null) {
			return false;
		}
		return employeeInfo.getPosId() != 0 && employeeInfo.getPosId() == 167;
	}

	public boolean isOrgCM() {
		if (employeeInfo == null) {
			return false;
		}
		return employeeInfo.getPosId() != 0 && employeeInfo.getPosId() == 708;
	}

	/**
	 * 是否upm统称BD，BD、BDM、CM
	 */
	public boolean isUpmGeneralBD() {
		return isUpmBD() || isUpmBDM() || isUpmCM();
	}

	public boolean isUpmBD() {
		return roleNameSet.contains("BD");
	}

	public boolean isUpmBDM() {
		return roleNameSet.contains("BD经理");
	}

	public boolean isUpmCM() {
		return roleNameSet.contains("城市经理");
	}

	public boolean isTmpBD() {
		return roleNameSet.contains("兼职BD");
	}

	/**
	 * 是否upm客服专员角色
	 */
	public boolean isUpmCS() {
		return roleNameSet.contains("客服专员");
	}

	/**
	 * 是否upm客服组长
	 */
	public boolean isUpmKFZZ() {
		return roleNameSet.contains("Ke客Fu服组长");
	}

	/**
	 * 是否是UPM超级管理员
	 * @return
	 */
	public boolean isUpmSuperAdmin() {
		return roleNameSet.contains("超级管理员") || roleNameSet.contains("超级系统管理员");
	}

	public boolean isUpmSuperSystemAdmin() {
		return roleNameSet.contains("超级系统管理员");
	}

    /**
     * 是否Upm的PM或RD角色 by sunhao03
     */
	public boolean isUpmPMOrRD() {
        for (String roleName : roleNameSet) {
            if (roleName.contains("PM")) {
                return true;
            }
            if (roleName.contains("RD")) {
                return true;
            }
        }
        return false;
    }

    /**
     * 是否有权限解冻商家结算 by sunhao03
     */
    public boolean isUpmCanUnFreezeWmPoiPaySetting() {
        return isUpmSuperSystemAdmin() || roleNameSet.contains("产品运营组（ALL权限）");
    }

    public boolean isBrandBd() {
        return roleNameSet.contains("大客户BD");
    }

	public boolean isGeneralBD() {
		return isOrgGeneralBD() || isUpmGeneralBD();
	}
	
	public boolean isFoodV2() {
		return roleNameSet.contains("菜品管理v2");
	}
	
	public boolean isTmpCs() {
		return roleNameSet.contains("兼职客服");
	}
	
	public boolean isTarPerColumnConf() {
		return roleNameSet.contains("目标业绩列配置");
	}

	public boolean isContractSuperManager() {return roleNameSet.contains("合同超级管理员");}

	public boolean isContractDeleteManager() {
		return roleNameSet.contains("合同废除管理员");
	}

	public boolean isC3ContractDeleteManager() {
		return roleNameSet.contains("C3合同废除管理员");
	}
	
	public boolean isContractAuthorizationManager(){
	    return roleNameSet.contains("电子合同授权管理员");
	}
	
	public boolean checkUpmRole(String roleName) {
	    return roleNameSet.contains(roleName);
    }

	public boolean checkUpmRoleById(int roleId) {
		return roleIdSet.contains(roleId);
	}

	public User getUser() {
		return user;
	}

	public String getLogin() {
		if (employeeInfo == null) {
			return "";
		}
		return employeeInfo.getMisId();
	}

	public Set<String> getUpmRoleNameSet() {
		return roleNameSet;
	}

	public Set<Integer> getUpmRoleIdSet() {
		return roleIdSet;
	}

	public List<String> getUpmRoleNameList() {
		return Lists.newArrayList(roleNameSet);
	}

	public List<Integer> getUpmRoleIdList() {
		return Lists.newArrayList(roleIdSet);
	}

	public List<WmOpenCity> getAllowCityList() {
		return allowCityList;
	}

	public List<Long> getAllowCityIdList() {
		return Lists.transform(allowCityList, new Function<WmOpenCity, Long>() {
			@Override
			public Long apply(WmOpenCity input) {
				return (long)input.getCityId();
			}
		});
	}

	public static class Builder {
		private User user;
		private WmEmploy employeeInfo;
		private List<RoleOrg> roleOrgList;
		private List<WmOpenCity> allowCityList;

		public Builder() {
			
		}
		
		public Builder(User user) {
			this.user = user;
		}

		public Builder employeeInfo(WmEmploy employ) {
			this.employeeInfo = employ;
			return this;
		}

		public Builder roleOrgList(List<RoleOrg> roleOrgList) {
			this.roleOrgList = roleOrgList;
			return this;
		}

		public Builder allowCityList(List<WmOpenCity> allowCityList) {
			this.allowCityList = allowCityList;
			return this;
		}

		public AdminUser builder() {
			return new AdminUser(user, employeeInfo, roleOrgList, allowCityList);
		}
	}

}

package com.sankuai.meituan.waimai.vo.customer;

import com.sankuai.meituan.waimai.thrift.customer.domain.customer.AuditInfo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.CustomerSceneInfoBO;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.DiffInfo;

import java.util.List;

public class WmCustomerDetailVo {

    private Integer id;

    private Integer customerRealType;//客户类型

    private String customerRealTypeStr;//客户类型文字描述

    private Integer customerType;

    private Integer customerSecondType;

    private String customerNumber;

    private String customerName;

    private String picUrl;

    private String legalPerson;

    private String address;

    private Long validateDate;

    private String businessScope;

    private Integer ownerUid;

    private Integer effective;

    private Integer showType;

    private AuditInfo auditInfo;

    private DiffInfo diffInfo;

    private Integer signMode;

    private boolean showSignMode;

    private long mtCustomerId;

    //是否末级客户：1、是；2、否。默认为是
    private Integer isLeaf;

    private long superCustomerId;

    private String customerExtPro;

    private int aliveIdentify;//0、未进行活体认证 1、活体识别通过 2、活体识别不通过

    private List<WmCustomerLabelTipsVo> labelNames;//客户标签名称


    /**
     * 总部二级物理城市ID(承销商)
     */
    private Integer hqSecondCityId;

    /**
     * 总部详细地址(承销商)
     */
    private String hqDetailAddress;

    /**
     * 公海线索ID(承销商)
     */
    private String wdcClueId;

    /**
     * 合同编号(承销商)
     */
    private String contractNum;

    /**
     * 合作状态(承销商)
     */
    private Integer wmCoStatus;

    /**
     * 责任人misId
     */
    private String ownerMisId;

    /**
     * 责任人姓名
     */
    private String ownerUName;

    /**
     * 证件形式（营业执照） 1-纸质,2-电子
     */
    private Integer certificateType;

    /**
     * 证件状态（营业执照） 0-无,1-存续,2-注销,3-吊销
     */
    private Integer certificateStatus;

    /**
     * 证件是否过期 0-未过期，1-过期
     */
    private Integer certificateOverdue;

    /**
     * 法人变更 0-否，1-是
     */
    private Integer legalPersonChange;

    /**
     * 证件形式（营业执照） 1-纸质,2-电子（文案）
     */
    private String certificateTypeDesc;

    /**
     * 证件状态（营业执照） 0-无,1-存续,2-注销,3-吊销（文案）
     */
    private String certificateStatusDesc;

    /**
     * 证件是否过期 0-未过期，1-过期（文案）
     */
    private String certificateOverdueDesc;

    /**
     * 法人变更 0-否，1-是（文案）
     */
    private String legalPersonChangeDesc;

    /**
     * 注册国家/地区
     */
    private Integer registryState;

    /**
     * 注册国家/地区名称
     */
    private String registryStateDesc;


    private String foodCityName;

    /**
     * 蜂窝业务线 0 未选择 1 直营 2 代理
     */
    private int foodCityAorBiz;

    /**
     * 蜂窝ID
     */
    private int foodCityAorId;

    /**
     * 美食城图片(一张)
     */
    private String foodCityPic;


    /**
     * 客户复用{@link com.sankuai.meituan.waimai.thrift.customer.constant.customer.CustomerMultiplexEnum}
     */
    private Integer multiplex;

    /**
     * 客户复用
     */
    private String multiplexDesc;

    /**
     * 复用池客户ID
     */
    private Long multiplexCustomerId;

    /**
     * 复用客户业务线{@link com.sankuai.nibcus.inf.customer.client.enums.BusinessLineEnum}
     */
    private Long multiplexBusinessLineId;

    /**
     * 创建时间
     */
    private String ctimeStr;
    /**
     * 业务线code{@link com.sankuai.meituan.waimai.poi.constants.PoiOrgEnum}
     */
    private Integer bizOrgCode;

    /**
     * 业务线描述
     */
    private String bizOrgCodeDesc;

    /**
     * 美食城视频
     */
    private String foodCityVideo;

    /**
     * 美食城档口数
     */
    private Integer foodCityPoiCount;

    /**
     * 美食城已占用档口数
     */
    private Integer foodCityUsePoiCount;

    /**
     * 外卖单店场景信息
     */
    private CustomerSceneInfoBO wmSingleCustomerSceneInfo;

    /**
     * 特批任务ID
     */
    private Integer specialTaskId;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getCustomerType() {
        return customerType;
    }

    public void setCustomerType(Integer customerType) {
        this.customerType = customerType;
    }

    public Integer getCustomerSecondType() {
        return customerSecondType;
    }

    public void setCustomerSecondType(Integer customerSecondType) {
        this.customerSecondType = customerSecondType;
    }

    public String getCustomerNumber() {
        return customerNumber;
    }

    public void setCustomerNumber(String customerNumber) {
        this.customerNumber = customerNumber;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getPicUrl() {
        return picUrl;
    }

    public void setPicUrl(String picUrl) {
        this.picUrl = picUrl;
    }

    public String getLegalPerson() {
        return legalPerson;
    }

    public void setLegalPerson(String legalPerson) {
        this.legalPerson = legalPerson;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Long getValidateDate() {
        return validateDate;
    }

    public void setValidateDate(Long validateDate) {
        this.validateDate = validateDate;
    }

    public String getBusinessScope() {
        return businessScope == null ? "" : businessScope;
    }

    public void setBusinessScope(String businessScope) {
        this.businessScope = businessScope;
    }

    public Integer getOwnerUid() {
        return ownerUid;
    }

    public void setOwnerUid(Integer ownerUid) {
        this.ownerUid = ownerUid;
    }

    public Integer getEffective() {
        return effective;
    }

    public void setEffective(Integer effective) {
        this.effective = effective;
    }

    public Integer getShowType() {
        return showType;
    }

    public void setShowType(Integer showType) {
        this.showType = showType;
    }

    public AuditInfo getAuditInfo() {
        return auditInfo;
    }

    public void setAuditInfo(AuditInfo auditInfo) {
        this.auditInfo = auditInfo;
    }

    public DiffInfo getDiffInfo() {
        return diffInfo;
    }

    public void setDiffInfo(DiffInfo diffInfo) {
        this.diffInfo = diffInfo;
    }

    public Integer getSignMode() {
        return signMode;
    }

    public void setSignMode(Integer signMode) {
        this.signMode = signMode;
    }

    public boolean isShowSignMode() {
        return showSignMode;
    }

    public void setShowSignMode(boolean showSignMode) {
        this.showSignMode = showSignMode;
    }

    public Integer getCustomerRealType() {
        return customerRealType;
    }

    public void setCustomerRealType(Integer customerRealType) {
        this.customerRealType = customerRealType;
    }

    public String getCustomerRealTypeStr() {
        return customerRealTypeStr;
    }

    public void setCustomerRealTypeStr(String customerRealTypeStr) {
        this.customerRealTypeStr = customerRealTypeStr;
    }

    public long getMtCustomerId() {
        return mtCustomerId;
    }

    public void setMtCustomerId(long mtCustomerId) {
        this.mtCustomerId = mtCustomerId;
    }

    public Integer getIsLeaf() {
        return isLeaf;
    }

    public void setIsLeaf(Integer isLeaf) {
        this.isLeaf = isLeaf;
    }

    public long getSuperCustomerId() {
        return superCustomerId;
    }

    public void setSuperCustomerId(long superCustomerId) {
        this.superCustomerId = superCustomerId;
    }

    public String getCustomerExtPro() {
        return customerExtPro;
    }

    public void setCustomerExtPro(String customerExtPro) {
        this.customerExtPro = customerExtPro;
    }
    
    public int getAliveIdentify() {
        return aliveIdentify;
    }

    public void setAliveIdentify(int aliveIdentify) {
        this.aliveIdentify = aliveIdentify;
    }

    public List<WmCustomerLabelTipsVo> getLabelNames() {
        return labelNames;
    }

    public void setLabelNames(List<WmCustomerLabelTipsVo> labelNames) {
        this.labelNames = labelNames;
    }

    public Integer getHqSecondCityId() {
        return hqSecondCityId;
    }

    public void setHqSecondCityId(Integer hqSecondCityId) {
        this.hqSecondCityId = hqSecondCityId;
    }

    public String getHqDetailAddress() {
        return hqDetailAddress;
    }

    public void setHqDetailAddress(String hqDetailAddress) {
        this.hqDetailAddress = hqDetailAddress;
    }

    public String getWdcClueId() {
        return wdcClueId;
    }

    public void setWdcClueId(String wdcClueId) {
        this.wdcClueId = wdcClueId;
    }

    public String getContractNum() {
        return contractNum;
    }

    public void setContractNum(String contractNum) {
        this.contractNum = contractNum;
    }

    public Integer getWmCoStatus() {
        return wmCoStatus;
    }

    public void setWmCoStatus(Integer wmCoStatus) {
        this.wmCoStatus = wmCoStatus;
    }

    public String getOwnerMisId() {
        return ownerMisId;
    }

    public void setOwnerMisId(String ownerMisId) {
        this.ownerMisId = ownerMisId;
    }

    public String getOwnerUName() {
        return ownerUName;
    }

    public void setOwnerUName(String ownerUName) {
        this.ownerUName = ownerUName;
    }

    public Integer getCertificateType() {
        return certificateType;
    }

    public void setCertificateType(Integer certificateType) {
        this.certificateType = certificateType;
    }

    public Integer getCertificateStatus() {
        return certificateStatus;
    }

    public void setCertificateStatus(Integer certificateStatus) {
        this.certificateStatus = certificateStatus;
    }

    public Integer getCertificateOverdue() {
        return certificateOverdue;
    }

    public void setCertificateOverdue(Integer certificateOverdue) {
        this.certificateOverdue = certificateOverdue;
    }

    public Integer getLegalPersonChange() {
        return legalPersonChange;
    }

    public void setLegalPersonChange(Integer legalPersonChange) {
        this.legalPersonChange = legalPersonChange;
    }

    public String getCertificateTypeDesc() {
        return certificateTypeDesc;
    }

    public void setCertificateTypeDesc(String certificateTypeDesc) {
        this.certificateTypeDesc = certificateTypeDesc;
    }

    public String getCertificateStatusDesc() {
        return certificateStatusDesc;
    }

    public void setCertificateStatusDesc(String certificateStatusDesc) {
        this.certificateStatusDesc = certificateStatusDesc;
    }

    public String getCertificateOverdueDesc() {
        return certificateOverdueDesc;
    }

    public void setCertificateOverdueDesc(String certificateOverdueDesc) {
        this.certificateOverdueDesc = certificateOverdueDesc;
    }

    public String getLegalPersonChangeDesc() {
        return legalPersonChangeDesc;
    }

    public void setLegalPersonChangeDesc(String legalPersonChangeDesc) {
        this.legalPersonChangeDesc = legalPersonChangeDesc;
    }

    public Integer getRegistryState() {
        return registryState;
    }

    public void setRegistryState(Integer registryState) {
        this.registryState = registryState;
    }

    public String getRegistryStateDesc() {
        return registryStateDesc;
    }

    public void setRegistryStateDesc(String registryStateDesc) {
        this.registryStateDesc = registryStateDesc;
    }

    public String getFoodCityName() {
        return foodCityName;
    }

    public void setFoodCityName(String foodCityName) {
        this.foodCityName = foodCityName;
    }

    public int getFoodCityAorBiz() {
        return foodCityAorBiz;
    }

    public void setFoodCityAorBiz(int foodCityAorBiz) {
        this.foodCityAorBiz = foodCityAorBiz;
    }

    public int getFoodCityAorId() {
        return foodCityAorId;
    }

    public void setFoodCityAorId(int foodCityAorId) {
        this.foodCityAorId = foodCityAorId;
    }

    public String getFoodCityPic() {
        return foodCityPic;
    }

    public void setFoodCityPic(String foodCityPic) {
        this.foodCityPic = foodCityPic;
    }

    public Integer getMultiplex() {
        return multiplex;
    }

    public void setMultiplex(Integer multiplex) {
        this.multiplex = multiplex;
    }

    public String getMultiplexDesc() {
        return multiplexDesc;
    }

    public void setMultiplexDesc(String multiplexDesc) {
        this.multiplexDesc = multiplexDesc;
    }

    public Long getMultiplexCustomerId() {
        return multiplexCustomerId;
    }

    public void setMultiplexCustomerId(Long multiplexCustomerId) {
        this.multiplexCustomerId = multiplexCustomerId;
    }

    public Long getMultiplexBusinessLineId() {
        return multiplexBusinessLineId;
    }

    public void setMultiplexBusinessLineId(Long multiplexBusinessLineId) {
        this.multiplexBusinessLineId = multiplexBusinessLineId;
    }

    public String getCtimeStr() { return ctimeStr; }

    public void setCtimeStr(String ctimeStr) { this.ctimeStr = ctimeStr; }

    public Integer getBizOrgCode() {
        return bizOrgCode;
    }

    public void setBizOrgCode(Integer bizOrgCode) {
        this.bizOrgCode = bizOrgCode;
    }

    public String getBizOrgCodeDesc() {
        return bizOrgCodeDesc;
    }

    public void setBizOrgCodeDesc(String bizOrgCodeDesc) {
        this.bizOrgCodeDesc = bizOrgCodeDesc;
    }

    public String getFoodCityVideo() {
        return foodCityVideo;
    }

    public void setFoodCityVideo(String foodCityVideo) {
        this.foodCityVideo = foodCityVideo;
    }

    public Integer getFoodCityPoiCount() {
        return foodCityPoiCount;
    }

    public void setFoodCityPoiCount(Integer foodCityPoiCount) {
        this.foodCityPoiCount = foodCityPoiCount;
    }

    public Integer getFoodCityUsePoiCount() {
        return foodCityUsePoiCount;
    }

    public void setFoodCityUsePoiCount(Integer foodCityUsePoiCount) {
        this.foodCityUsePoiCount = foodCityUsePoiCount;
    }

    public CustomerSceneInfoBO getWmSingleCustomerSceneInfo() {
        return wmSingleCustomerSceneInfo;
    }

    public void setWmSingleCustomerSceneInfo(CustomerSceneInfoBO wmSingleCustomerSceneInfo) {
        this.wmSingleCustomerSceneInfo = wmSingleCustomerSceneInfo;
    }

    public Integer getSpecialTaskId() {
        return specialTaskId;
    }

    public void setSpecialTaskId(Integer specialTaskId) {
        this.specialTaskId = specialTaskId;
    }
}

package com.sankuai.meituan.waimai.vo.settle;


import com.sankuai.meituan.waimai.vo.base.WmFieldAuditStatusVo;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 16-3-17.
 */
public class WmSettleBasicVo {

    private int wmSettleId;

    private String partyAFinancePeople;

    private String accName;

    private String accCardno;

    private int wmPoiCount;
    
    private String wmPoiIds;//解绑门店，前端需要

    private WmCardValidationInfoVo cardValidationInfoVo;

    private WmFieldAuditStatusVo diff;


    private int card_valid;
    private String card_invalid_reason;
    private int wallet_open_status;
    private String wallet_open_reason;
    private int realname_status;
    private String realname_reason;
    private int card_binding_status;
    private String card_binding_reason;

    private long wm_wallet_id;
    private String login_name;
    private int open_time;

    private int cardType;

    public String getWmPoiIds() {
        return wmPoiIds;
    }

    public void setWmPoiIds(String wmPoiIds) {
        this.wmPoiIds = wmPoiIds;
    }

    public int getWmSettleId() {
        return wmSettleId;
    }

    public void setWmSettleId(int wmSettleId) {
        this.wmSettleId = wmSettleId;
    }

    public String getPartyAFinancePeople() {
        return partyAFinancePeople;
    }

    public void setPartyAFinancePeople(String partyAFinancePeople) {
        this.partyAFinancePeople = partyAFinancePeople;
    }

    public String getAccName() {
        return accName;
    }

    public void setAccName(String accName) {
        this.accName = accName;
    }

    public String getAccCardno() {
        return accCardno;
    }

    public void setAccCardno(String accCardno) {
        this.accCardno = accCardno;
    }

    public int getWmPoiCount() {
        return wmPoiCount;
    }

    public void setWmPoiCount(int wmPoiCount) {
        this.wmPoiCount = wmPoiCount;
    }

    public WmFieldAuditStatusVo getDiff() {
        return diff;
    }

    public void setDiff(WmFieldAuditStatusVo diff) {
        this.diff = diff;
    }

    public WmCardValidationInfoVo getCardValidationInfoVo() {
        return cardValidationInfoVo;
    }

    public void setCardValidationInfoVo(WmCardValidationInfoVo cardValidationInfoVo) {
        this.cardValidationInfoVo = cardValidationInfoVo;
    }


    public int getCard_valid() {
        return card_valid;
    }

    public void setCard_valid(int card_valid) {
        this.card_valid = card_valid;
    }

    public String getCard_invalid_reason() {
        return card_invalid_reason;
    }

    public void setCard_invalid_reason(String card_invalid_reason) {
        this.card_invalid_reason = card_invalid_reason;
    }

    public int getWallet_open_status() {
        return wallet_open_status;
    }

    public void setWallet_open_status(int wallet_open_status) {
        this.wallet_open_status = wallet_open_status;
    }

    public String getWallet_open_reason() {
        return wallet_open_reason;
    }

    public void setWallet_open_reason(String wallet_open_reason) {
        this.wallet_open_reason = wallet_open_reason;
    }

    public int getRealname_status() {
        return realname_status;
    }

    public void setRealname_status(int realname_status) {
        this.realname_status = realname_status;
    }

    public String getRealname_reason() {
        return realname_reason;
    }

    public void setRealname_reason(String realname_reason) {
        this.realname_reason = realname_reason;
    }

    public int getCard_binding_status() {
        return card_binding_status;
    }

    public void setCard_binding_status(int card_binding_status) {
        this.card_binding_status = card_binding_status;
    }

    public String getCard_binding_reason() {
        return card_binding_reason;
    }

    public void setCard_binding_reason(String card_binding_reason) {
        this.card_binding_reason = card_binding_reason;
    }

    public long getWm_wallet_id() {
        return wm_wallet_id;
    }

    public void setWm_wallet_id(long wm_wallet_id) {
        this.wm_wallet_id = wm_wallet_id;
    }

    public String getLogin_name() {
        return login_name;
    }

    public void setLogin_name(String login_name) {
        this.login_name = login_name;
    }

    public int getOpen_time() {
        return open_time;
    }

    public void setOpen_time(int open_time) {
        this.open_time = open_time;
    }

    public int getCardType() {
        return cardType;
    }

    public void setCardType(int card_type) {
        this.cardType = card_type;
    }
}

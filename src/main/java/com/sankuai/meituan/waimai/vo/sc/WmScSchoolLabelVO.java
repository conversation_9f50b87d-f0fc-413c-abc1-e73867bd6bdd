package com.sankuai.meituan.waimai.vo.sc;

import com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmScSchoolLabelDTO;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 学校标签信息类-用于学校列表和学校详情展示
 * -@author: houjikang
 * -@date: 2023/03/26
 * -@email: <EMAIL>
 */
public class WmScSchoolLabelVO {
    /**
     * 标签ID
     */
    private Long labelId;
    /**
     * 标签名称
     */
    private String labelName;
    /**
     * 标签内容
     */
    private String labelTipsName;

    public Long getLabelId() {
        return labelId;
    }

    public void setLabelId(Long labelId) {
        this.labelId = labelId;
    }

    public String getLabelName() {
        return labelName;
    }

    public void setLabelName(String labelName) {
        this.labelName = labelName;
    }

    public String getLabelTipsName() {
        return labelTipsName;
    }

    public void setLabelTipsName(String labelTipsName) {
        this.labelTipsName = labelTipsName;
    }

    @Override
    public String toString() {
        return "WmScSchoolLabelVO{" +
                "labelId=" + labelId +
                ", labelName='" + labelName + '\'' +
                ", labelTipsName='" + labelTipsName + '\'' +
                '}';
    }

    /**
     * 学校标签列表DTO转VO
     * @param wmScSchoolLabelDTOList 学校标签DTO列表
     * @return 学校标签VO列表
     */
    public static List<WmScSchoolLabelVO> transWmScSchoolLabelDtoToVo(List<WmScSchoolLabelDTO> wmScSchoolLabelDTOList) {
        List<WmScSchoolLabelVO> wmScSchoolLabelVOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(wmScSchoolLabelDTOList)) {
            return wmScSchoolLabelVOList;
        }
        for (WmScSchoolLabelDTO wmScSchoolLabelDTO : wmScSchoolLabelDTOList) {
            WmScSchoolLabelVO wmScSchoolLabelVO = new WmScSchoolLabelVO();
            wmScSchoolLabelVO.setLabelId(wmScSchoolLabelDTO.getLabelId());
            wmScSchoolLabelVO.setLabelName(wmScSchoolLabelDTO.getLabelName());
            wmScSchoolLabelVO.setLabelTipsName(wmScSchoolLabelDTO.getLabelTipsName());
            wmScSchoolLabelVOList.add(wmScSchoolLabelVO);
        }
        return wmScSchoolLabelVOList;
    }
}

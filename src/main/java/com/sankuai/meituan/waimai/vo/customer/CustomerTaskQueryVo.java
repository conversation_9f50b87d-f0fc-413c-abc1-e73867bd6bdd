package com.sankuai.meituan.waimai.vo.customer;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 20230404
 * @desc 客户任务列表查询请求对象
 */
@Data
public class CustomerTaskQueryVo {
    /**
     * 客户ID
     */
    private Integer customerId;

    /**
     * 门店ID
     */
    private Integer wmPoiId;

    /**
     * 任务场景
     */
    private Integer taskScene;

    /**
     * 任务类型
     */
    private Integer taskType;

    /**
     * 任务渠道
     */
    private Integer taskSource;

    /**
     * 创建时间-起
     */
    private Integer queryTimeStart;

    /**
     * 创建时间-止
     */
    private Integer queryTimeEnd;

    /**
     * 页数
     */
    private Integer pageSize;

    /**
     * 页码
     */
    private Integer pageNo;

    /**
     * 任务状态
     */
    private Integer taskStatus;

    /**
     * 关联业务任务ID
     */
    private Integer bizTaskId;
}

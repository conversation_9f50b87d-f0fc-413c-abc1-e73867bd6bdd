package com.sankuai.meituan.waimai.vo.sc;

import lombok.Data;

import java.util.List;

/**
 * 学校范围页面VO
 * <AUTHOR>
 * @date 2023/05/31
 * @email <EMAIL>
 */
@Data
public class WmScSchoolAoiVO {
    /**
     * 学校主键ID
     */
    private Integer schoolPrimaryId;
    /**
     * 学校名称
     */
    private String schoolName;
    /**
     * 学校AOI信息列表
     */
    private List<WmScSchoolAoiDetailVO> schoolAoiInfoList;
    /**
     * 审核状态 4-待录入，2-已生效
     */
    private Integer auditStatus;
    /**
     * 审核状态文案
     */
    private String auditStatusDesc;
    /**
     * 物理城市名称
     */
    private String cityName;
    /**
     * 学校地址
     */
    private String schoolAddress;
    /**
     * 是否是学校和食堂超管
     */
    private Boolean superAdmin;
    /**
     * 聚合订单是否允许配送进校
     */
    private Integer aggreOrderAllowDelivery;
    /**
     * 聚合订单是否允许配送进校描述
     */
    private String aggreOrderAllowDeliveryDesc;
    /**
     * 是否显示范围确认按钮
     * 0-不显示 1-显示
     */
    private Integer confirmAreaButton;
    /**
     * 是否显示反馈范围问题按钮
     * 0-不显示 1-显示
     */
    private Integer feedbackAreaButton;
    /**
     * 反馈跳转URL
     */
    private String feedbackAreaURL;
}

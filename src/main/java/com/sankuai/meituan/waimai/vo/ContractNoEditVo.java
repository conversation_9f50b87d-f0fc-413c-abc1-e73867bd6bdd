package com.sankuai.meituan.waimai.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * Author: 窦旭蒙
 * MIS: douxumeng
 * Date: 2021-11-12 17:56
 * Email: <EMAIL>
 * Desc:
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ContractNoEditVo {

    //客户id
    private Integer customerId;

    //合同id
    private Long contractId;

    //操作类型
    private Integer operateType;

    //合同编号
    private String contractNum;

    //甲方名称
    private String partASignName;

    //甲方ID（美团客户ID）
    private Long partASignMtId;

    //乙方名称
    private String partBSignName;

    //乙方签约人ID
    private Integer partBSignId;

    //丙方名称
    private String partCSignName;

    //丙方签约人ID
    private Integer partCSignId;

    //履约服务费主体
    private String logisticsSubject;

    //合同类型
    private Integer contractType;

    //签约形式
    private Integer cooperateMode;

    //合作模式
    private Integer signType;


    public static ContractNoEditVo convertContractVo2ContractNoEditVo(ContractVo contractVo){
        ContractNoEditVo contractNoEditVo = new ContractNoEditVo();
        contractNoEditVo.setCustomerId(contractVo.getCustomerId());
        contractNoEditVo.setContractId(contractVo.getTempletId().longValue());
        contractNoEditVo.setContractNum(contractVo.getContractNum());
        contractNoEditVo.setPartASignName(contractVo.getPartyA().getSignName());
        contractNoEditVo.setPartASignMtId(contractVo.getPartyA().getMtCustomerId());
        contractNoEditVo.setPartBSignName(contractVo.getPartyB().getSignName());
        contractNoEditVo.setPartBSignId(contractVo.getPartyB().getSignId());
        contractNoEditVo.setLogisticsSubject(contractVo.getLogisticsSubject());
        if(contractVo.getPartyC() != null){
            contractNoEditVo.setPartCSignId(contractVo.getPartyC().getSignId());
            contractNoEditVo.setPartCSignName(contractVo.getPartyC().getSignName());
        }
        return contractNoEditVo;
    }

    public static ContractVo convertContractNoEditVo2ContractVo(ContractNoEditVo contractNoEditVo){
        ContractVo contractVo = new ContractVo();
        contractVo.setCustomerId(contractNoEditVo.getCustomerId());
        contractVo.setTempletId(contractNoEditVo.getContractId().intValue());
        contractVo.setContractNum(contractNoEditVo.getContractNum());
        contractVo.setLogisticsSubject(contractNoEditVo.getPartBSignName());
        contractVo.parseContractType(contractNoEditVo.getContractType());
        contractVo.setCooperateMode(contractNoEditVo.getCooperateMode());
        //parta/partb
        ContractSignerVo partA = new ContractSignerVo();
        ContractSignerVo partB = new ContractSignerVo();
        contractVo.setPartyA(partA);
        contractVo.setPartyB(partB);
        contractVo.getPartyA().setSignName(contractNoEditVo.getPartASignName());
        contractVo.getPartyA().setMtCustomerId(contractNoEditVo.getPartASignMtId());
        contractVo.getPartyB().setSignName(contractNoEditVo.getPartBSignName());
        contractVo.getPartyB().setSignId(contractNoEditVo.getPartBSignId());
        if(StringUtils.isNotEmpty(contractNoEditVo.getPartCSignName())){
            ContractSignerVo partC = new ContractSignerVo();
            partC.setSignName(contractNoEditVo.getPartCSignName());
            partC.setSignId(contractNoEditVo.getPartCSignId());
            contractVo.setPartyC(partC);
        }

        return contractVo;
    }
}

package com.sankuai.meituan.waimai.vo.customerswitch;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SwitchPoiLogInfoVo {
    /**
     * 任务ID
     */
    private Long taskId;
    /**
     * 门店ID
     */
    private Long wmPoiId;
    /**
     * 日志内容
     */
    private String logContent;
    /**
     * 操作名称
     */
    private String opUname;
    /**
     * 创建时间
     */
    private String ctime;
    /**
     * 门店名称
     */
    private String poiName;
    /**
     * TraceID
     */
    private String traceId;
    /**
     * 操作类型
     */
    private String opTypeDesc;
}

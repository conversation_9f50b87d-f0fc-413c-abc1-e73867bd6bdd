package com.sankuai.meituan.waimai.vo.sc;

import com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmScSchoolBasicFeeDTO;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 学校基础费率信息VO
 * <AUTHOR>
 * @date 2023/09/18
 * @email <EMAIL>
 */
@Data
public class WmScSchoolBasicFeeVO {
    /**
     * 二级物理城市描述
     */
    private String cityDesc;
    /**
     * 三级物理城市描述(区县)
     */
    private String locationDesc;
    /**
     * 一级品类描述
     */
    private String firstCategoryDesc;
    /**
     * 二级品类描述
     */
    private String secondCategoryDesc;
    /**
     * 配送方描述
     */
    private String deliveryTypeDesc;
    /**
     * 配送方式描述
     */
    private String logisticsCodeDesc;
    /**
     * 校园食堂商家佣金
     */
    private String serviceFee;

    public static List<WmScSchoolBasicFeeVO> transSchoolBasicFeeDTOListToVOList(List<WmScSchoolBasicFeeDTO> dtoList) {
        List<WmScSchoolBasicFeeVO> voList = new ArrayList<>();
        if (CollectionUtils.isEmpty(dtoList)) {
            return voList;
        }

        for (WmScSchoolBasicFeeDTO dto : dtoList) {
            WmScSchoolBasicFeeVO wmScSchoolBasicFeeVO = new WmScSchoolBasicFeeVO();
            wmScSchoolBasicFeeVO.setServiceFee(dto.getServiceFee());
            wmScSchoolBasicFeeVO.setCityDesc(dto.getCityDesc());
            wmScSchoolBasicFeeVO.setDeliveryTypeDesc(dto.getDeliveryTypeDesc());
            wmScSchoolBasicFeeVO.setFirstCategoryDesc(dto.getFirstCategoryDesc());
            wmScSchoolBasicFeeVO.setLocationDesc(dto.getLocationDesc());
            wmScSchoolBasicFeeVO.setLogisticsCodeDesc(dto.getLogisticsCodeDesc());
            wmScSchoolBasicFeeVO.setSecondCategoryDesc(dto.getSecondCategoryDesc());

            voList.add(wmScSchoolBasicFeeVO);
        }

        return voList;
    }


}


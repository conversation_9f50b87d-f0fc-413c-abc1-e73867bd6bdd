package com.sankuai.meituan.waimai.vo.sc;

import com.sankuai.meituan.auth.util.CollectionUtils;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.dto.WmScSchoolAoiDTO;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 学校范围页面详细信息VO
 * <AUTHOR>
 * @date 2023/05/31
 * @email <EMAIL>
 */
@Data
public class WmScSchoolAoiDetailVO {
    /**
     * AOI ID
     */
    private Long aoiId;
    /**
     * AOI名称
     */
    private String aoiName;
    /**
     * AOI范围
     */
    private String aoiArea;
    /**
     * 即时配是否允许配送进校
     */
    private Integer aoiMode;
    /**
     * 即时配是否允许配送进校描述
     */
    private String aoiModeDesc;

    /**
     * 学校AOI信息VO转DTO
     * @param wmScSchoolAoiDetailVOList wmScSchoolAoiDetailVOList
     * @return List<WmScSchoolAoiDTO>
     */
    public static List<WmScSchoolAoiDTO> transWmScSchoolAoiDetailVoToDto(List<WmScSchoolAoiDetailVO> wmScSchoolAoiDetailVOList) {
        List<WmScSchoolAoiDTO> wmScSchoolAoiDTOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(wmScSchoolAoiDetailVOList)) {
            return wmScSchoolAoiDTOList;
        }

        for (WmScSchoolAoiDetailVO wmScSchoolAoiDetailVO : wmScSchoolAoiDetailVOList) {
            WmScSchoolAoiDTO wmScSchoolAoiDTO = new WmScSchoolAoiDTO();
            wmScSchoolAoiDTO.setAoiId(wmScSchoolAoiDetailVO.getAoiId());
            wmScSchoolAoiDTO.setAoiName(wmScSchoolAoiDetailVO.getAoiName());
            wmScSchoolAoiDTO.setAoiModeDesc(wmScSchoolAoiDetailVO.getAoiModeDesc());
            wmScSchoolAoiDTO.setAoiMode(wmScSchoolAoiDetailVO.getAoiMode());
            wmScSchoolAoiDTO.setAoiArea(wmScSchoolAoiDetailVO.getAoiArea());
            wmScSchoolAoiDTOList.add(wmScSchoolAoiDTO);
        }
        return wmScSchoolAoiDTOList;
    }

    /**
     * 学校AOI信息转DTOVO
     * @param wmScSchoolAoiDTOList wmScSchoolAoiDTOList
     * @return List<WmScSchoolAoiDetailVO>
     */
    public static List<WmScSchoolAoiDetailVO> transWmScSchoolAoiDtoToDetailVo(List<WmScSchoolAoiDTO> wmScSchoolAoiDTOList) {
        List<WmScSchoolAoiDetailVO> wmScSchoolAoiDetailVOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(wmScSchoolAoiDTOList)) {
            return wmScSchoolAoiDetailVOList;
        }

        for (WmScSchoolAoiDTO wmScSchoolAoiDTO : wmScSchoolAoiDTOList) {
            WmScSchoolAoiDetailVO wmScSchoolAoiDetailVO = new WmScSchoolAoiDetailVO();
            wmScSchoolAoiDetailVO.setAoiId(wmScSchoolAoiDTO.getAoiId());
            wmScSchoolAoiDetailVO.setAoiArea(wmScSchoolAoiDTO.getAoiArea());
            wmScSchoolAoiDetailVO.setAoiName(wmScSchoolAoiDTO.getAoiName());
            wmScSchoolAoiDetailVO.setAoiMode(wmScSchoolAoiDTO.getAoiMode());
            wmScSchoolAoiDetailVO.setAoiModeDesc(wmScSchoolAoiDTO.getAoiModeDesc());
            wmScSchoolAoiDetailVOList.add(wmScSchoolAoiDetailVO);
        }
        return wmScSchoolAoiDetailVOList;
    }

}

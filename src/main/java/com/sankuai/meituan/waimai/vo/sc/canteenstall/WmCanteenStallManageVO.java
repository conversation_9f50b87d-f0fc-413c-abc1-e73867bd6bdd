package com.sankuai.meituan.waimai.vo.sc.canteenstall;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.canteenstall.CanteenStallManageTaskTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.canteenstall.CanteenStallManageSubmitStatusEnum;

import lombok.Data;

/**
 * 食堂档口管理信息VO
 * <AUTHOR>
 * @date 2024/05/18
 * @email <EMAIL>
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class WmCanteenStallManageVO {

    /**
     * 档口管理任务编号ID
     */
    private Integer manageId;
    /**
     * 任务类型
     * {@link CanteenStallManageTaskTypeEnum}
     */
    private Integer taskType;
    /**
     * 任务类型描述
     */
    private String taskTypeDesc;
    /**
     * 提交状态
     * {@link CanteenStallManageSubmitStatusEnum}
     */
    private Integer submitStatus;
    /**
     * 提交状态描述
     */
    private String submitStatusDesc;
    /**
     * 学校主键ID
     */
    private Integer schoolPrimaryId;
    /**
     * 学校线索ID
     */
    private String schoolClueId;
    /**
     * 学校名称
     */
    private String schoolName;
    /**
     * 食堂主键ID
     */
    private Integer canteenPrimaryId;
    /**
     * 食堂名称
     */
    private String canteenName;
    /**
     * 创建人UID
     */
    private Integer cuid;
    /**
     * 创建人MIS
     */
    private String cmis;
    /**
     * 创建人姓名
     */
    private String cname;
    /**
     * 创建时间
     */
    private Integer ctime;
    /**
     * 查看详情按钮权限
     * {@link com.sankuai.meituan.waimai.thrift.customer.constant.sc.ScGrayEnum}
     */
    private Integer viewDetailButtonAuth;
    /**
     * 去扎点按钮权限
     * {@link com.sankuai.meituan.waimai.thrift.customer.constant.sc.ScGrayEnum}
     */
    private Integer editCoordinateButtonAuth;
}

package com.sankuai.meituan.waimai.vo.sc;

import com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmScSchoolAreaDTO;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 学校区域信息类-用于学校区域模糊查询时展示
 * -@author: houjikang
 * -@date: 2023/03/26
 * -@email: <EMAIL>
 */
public class WmScSchoolAreaVO {
    /**
     * 区域节点ID
     */
    private Integer areaOrgId;
    /**
     * 区域名称
     */
    private String areaName;

    public Integer getAreaOrgId() {
        return areaOrgId;
    }

    public void setAreaOrgId(Integer areaOrgId) {
        this.areaOrgId = areaOrgId;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    @Override
    public String toString() {
        return "WmScSchoolAreaVO{" +
                "areaOrgId=" + areaOrgId +
                ", areaName='" + areaName + '\'' +
                '}';
    }

    /**
     * 学校区域列表DTO转VO
     * @param wmScSchoolAreaDTOList 学校区域DTO列表
     * @return 学校区域VO列表
     */
    public static List<WmScSchoolAreaVO> transWmScSchoolAreaDtoToVo(List<WmScSchoolAreaDTO> wmScSchoolAreaDTOList) {
        List<WmScSchoolAreaVO> wmScSchoolAreaVOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(wmScSchoolAreaDTOList)) {
            return wmScSchoolAreaVOList;
        }
        for (WmScSchoolAreaDTO wmScSchoolAreaDTO : wmScSchoolAreaDTOList) {
            WmScSchoolAreaVO wmScSchoolAreaVO = new WmScSchoolAreaVO();
            wmScSchoolAreaVO.setAreaOrgId(wmScSchoolAreaDTO.getAreaOrgId());
            wmScSchoolAreaVO.setAreaName(wmScSchoolAreaDTO.getAreaName());
            wmScSchoolAreaVOList.add(wmScSchoolAreaVO);
        }
        return wmScSchoolAreaVOList;
    }
}

package com.sankuai.meituan.waimai.vo.kp;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class WmCustomerKpVo {

    private Integer id;
    private Byte kpType;
    private Byte signerType;
    private Byte certType;
    private String compellation;
    private String certNumber;
    private String phoneNum;
    private Integer bankId;
    private String bankName;
    private String creditCard;
    private String email;
    private List<String> agentAuth;
    private List<String> agentFrontIdcard;
    private List<String> agentBackIdcard;
    private List<String> specialAttachmentList;
    private String brandIds;
    private String visitKPPro;
    private String signTaskType;//KP支持的签约任务类型，运营经理新增属性
    private String relPoiInfo;//绑定的门店id列表，用逗号分割，运营经理新增属性
    private String remark;//kp备注信息，运营经理新增属性
    private Byte state;
    /**
     * 是否有代理人授权书
     */
    private Integer haveAgentAuth;
    /**
     * 法人身份证复印件URL
     */
    private List<String>  legalIdcardCopy;

    /**
     * 法人授权方式-签约代理人KP必须有
     */
    private Integer legalAuthType;
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Byte getKpType() {
        return kpType;
    }

    public void setKpType(Byte kpType) {
        this.kpType = kpType;
    }

    public Byte getSignerType() {
        return signerType;
    }

    public void setSignerType(Byte signerType) {
        this.signerType = signerType;
    }

    public Byte getCertType() {
        return certType;
    }

    public void setCertType(Byte certType) {
        this.certType = certType;
    }

    public String getCompellation() {
        return compellation;
    }

    public void setCompellation(String compellation) {
        this.compellation = compellation;
    }

    public String getCertNumber() {
        return certNumber;
    }

    public void setCertNumber(String certNumber) {
        this.certNumber = certNumber;
    }

    public String getPhoneNum() {
        return phoneNum;
    }

    public void setPhoneNum(String phoneNum) {
        this.phoneNum = phoneNum;
    }

    public String getCreditCard() {
        return creditCard;
    }

    public void setCreditCard(String creditCard) {
        this.creditCard = creditCard;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Integer getBankId() {
        return bankId;
    }

    public void setBankId(Integer bankId) {
        this.bankId = bankId;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public List<String> getSpecialAttachmentList() {
        return specialAttachmentList;
    }

    public void setSpecialAttachmentList(List<String> specialAttachmentList) {
        this.specialAttachmentList = specialAttachmentList;
    }

    public List<String> getAgentAuth() {
        return agentAuth;
    }

    public void setAgentAuth(List<String> agentAuth) {
        this.agentAuth = agentAuth;
    }

    public List<String> getAgentFrontIdcard() {
        return agentFrontIdcard;
    }

    public void setAgentFrontIdcard(List<String> agentFrontIdcard) {
        this.agentFrontIdcard = agentFrontIdcard;
    }

    public List<String> getAgentBackIdcard() {
        return agentBackIdcard;
    }

    public void setAgentBackIdcard(List<String> agentBackIdcard) {
        this.agentBackIdcard = agentBackIdcard;
    }

    public String getBrandIds() {
        return brandIds;
    }

    public void setBrandIds(String brandIds) {
        this.brandIds = brandIds;
    }

    public String getVisitKPPro() {
        return visitKPPro;
    }

    public void setVisitKPPro(String visitKPPro) {
        this.visitKPPro = visitKPPro;
    }

    public String getSignTaskType() {
        return signTaskType;
    }

    public void setSignTaskType(String signTaskType) {
        this.signTaskType = signTaskType;
    }

    public String getRelPoiInfo() {
        return relPoiInfo;
    }

    public void setRelPoiInfo(String relPoiInfo) {
        this.relPoiInfo = relPoiInfo;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Byte getState() {
        return state;
    }

    public void setState(Byte state) {
        this.state = state;
    }

    public Integer getHaveAgentAuth() {
        return haveAgentAuth;
    }

    public void setHaveAgentAuth(Integer haveAgentAuth) {
        this.haveAgentAuth = haveAgentAuth;
    }

    public List<String> getLegalIdcardCopy() {
        return legalIdcardCopy;
    }

    public void setLegalIdcardCopy(List<String> legalIdcardCopy) {
        this.legalIdcardCopy = legalIdcardCopy;
    }

    public Integer getLegalAuthType() {
        return legalAuthType;
    }

    public void setLegalAuthType(Integer legalAuthType) {
        this.legalAuthType = legalAuthType;
    }
}


package com.sankuai.meituan.waimai.vo.customer;


public class WmCustomerFormVo {
    private Long customerId;
    private String customerName;
    private Long wmPoiId;
    private Integer ownerUid;
    private String keyword;
    /**
     * 客户层级，即是否叶子节点：0查询全部；1查询末级客户；2、查询上级客户
     */
    private Integer isLeaf;
    private Integer pageSize;
    private Integer pageNo;
    /**
     * 20205月7日新增查询条件
     */
    private Integer customerRealType;
    /**
     * 业务, 1:蜜蜂获取客户列表；2:蜜蜂关联客户获取列表，这个值只需要蜜蜂传递
     */
    private int biz;
    /**
     * 客户标签名称
     */
    private String labelIds;

    /**
     * 证件类型
     */
    private Integer customerType;

    /**
     * 客户状态
     */
    private Integer effective;

    /**
     * 食堂承包商合作状态
     */
    private Integer wmCoStatus;

    /**
     * 业务品牌id列表
     */
    private String brandIds;
    /**
     * 签约模式 1:纸质 2:电子
     */
    private Integer signMode;
    /**
     * 证件形式（营业执照） 1-纸质,2-电子
     */
    private Integer certificateType;
    /**
     * 统一社会信用代码/证件编号
     */
    private String customerNumber;

    /**
     * 签约类型
     */
    private Integer signerType;

    /**
     * 签约人名字
     */
    private String signerCompellation;

    /**
     * 签约的ID
     */
    private Integer signerId;

    /**
     * 签约人证件类型
     */
    private Integer signerCertType;


    /**
     * 签约人证件号
     */
    private String signerCertNumber;

    /**
     * 签约人手机号
     */
    private String signerPhoneNum;

    /**
     * 客户创建时间的开始时间，时间戳
     */
    private Integer ctimeBegin;

    /**
     * 客户创建时间的结束时间，时间戳
     */
    private Integer ctimeEnd;

    /**
     * 是否来源PC
     */
    private boolean isPC;

    /**
     * 客户复用{@link com.sankuai.meituan.waimai.thrift.customer.constant.customer.CustomerMultiplexEnum}
     */
    private Integer multiplex;

    /**
     * 复用池客户ID
     */
    private Long multiplexCustomerId;

    /**
     * 证件状态（营业执照） 0-无,1-存续,2-注销,3-吊销
     */
    private Integer certificateStatus;

    /**
     * 客户关联门店数-左值
     */
    private Integer poiCountStart;

    /**
     * 客户关联门店数-右值
     */
    private Integer poiCountEnd;

    /**
     * 业务线code{@link com.sankuai.meituan.waimai.poi.constants.PoiOrgEnum}
     */
    private Integer bizOrgCode;

    /**
     * 经营类型
     * 到家/到餐 主要用于区分当前登录人的角色权限
     */
    private Integer business;

    public Long getCustomerId() {
        return customerId==null ? 0 : customerId;
    }

    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public Long getWmPoiId() {
        return wmPoiId;
    }

    public void setWmPoiId(Long wmPoiId) {
        this.wmPoiId = wmPoiId;
    }

    public Integer getOwnerUid() {
        return ownerUid;
    }

    public void setOwnerUid(Integer ownerUid) {
        this.ownerUid = ownerUid;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getPageNo() {
        return pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    public Integer getIsLeaf() {
        return isLeaf==null ? 0 : isLeaf;
    }

    public void setIsLeaf(Integer isLeaf) {
        this.isLeaf = isLeaf;
    }

    public Integer getCustomerRealType() {
        return customerRealType;
    }

    public void setCustomerRealType(Integer customerRealType) {
        this.customerRealType = customerRealType;
    }

    public int getBiz() {
        return biz;
    }

    public void setBiz(int biz) {
        this.biz = biz;
    }

    public String getLabelIds() {
        return labelIds;
    }

    public void setLabelIds(String labelIds) {
        this.labelIds = labelIds;
    }

    public Integer getCustomerType() {
        return customerType;
    }

    public void setCustomerType(Integer customerType) {
        this.customerType = customerType;
    }

    public Integer getEffective() {
        return effective;
    }

    public void setEffective(Integer effective) {
        this.effective = effective;
    }

    public Integer getWmCoStatus() {
        return wmCoStatus;
    }

    public void setWmCoStatus(Integer wmCoStatus) {
        this.wmCoStatus = wmCoStatus;
    }

    public String getBrandIds() {
        return brandIds;
    }

    public void setBrandIds(String brandIds) {
        this.brandIds = brandIds;
    }

    public Integer getSignMode() {
        return signMode;
    }

    public void setSignMode(Integer signMode) {
        this.signMode = signMode;
    }

    public Integer getCertificateType() {
        return certificateType;
    }

    public void setCertificateType(Integer certificateType) {
        this.certificateType = certificateType;
    }

    public String getCustomerNumber() {
        return customerNumber;
    }

    public void setCustomerNumber(String customerNumber) {
        this.customerNumber = customerNumber;
    }

    public Integer getSignerType() {
        return signerType;
    }

    public void setSignerType(Integer signerType) {
        this.signerType = signerType;
    }

    public String getSignerCompellation() {
        return signerCompellation;
    }

    public void setSignerCompellation(String signerCompellation) {
        this.signerCompellation = signerCompellation;
    }

    public Integer getSignerId() {
        return signerId;
    }

    public void setSignerId(Integer signerId) {
        this.signerId = signerId;
    }

    public Integer getSignerCertType() {
        return signerCertType;
    }

    public void setSignerCertType(Integer signerCertType) {
        this.signerCertType = signerCertType;
    }

    public String getSignerCertNumber() {
        return signerCertNumber;
    }

    public void setSignerCertNumber(String signerCertNumber) {
        this.signerCertNumber = signerCertNumber;
    }

    public String getSignerPhoneNum() {
        return signerPhoneNum;
    }

    public void setSignerPhoneNum(String signerPhoneNum) {
        this.signerPhoneNum = signerPhoneNum;
    }

    public Integer getCtimeBegin() {
        return ctimeBegin;
    }

    public void setCtimeBegin(Integer ctimeBegin) {
        this.ctimeBegin = ctimeBegin;
    }

    public Integer getCtimeEnd() {
        return ctimeEnd;
    }

    public void setCtimeEnd(Integer ctimeEnd) {
        this.ctimeEnd = ctimeEnd;
    }

    public boolean isPC() {
        return isPC;
    }

    public void setIsPC(boolean isPC) {
        this.isPC = isPC;
    }

    public Integer getMultiplex() {
        return multiplex;
    }

    public void setMultiplex(Integer multiplex) {
        this.multiplex = multiplex;
    }

    public Long getMultiplexCustomerId() {
        return multiplexCustomerId;
    }

    public void setMultiplexCustomerId(Long multiplexCustomerId) {
        this.multiplexCustomerId = multiplexCustomerId;
    }

    public Integer getCertificateStatus() {
        return certificateStatus;
    }

    public void setCertificateStatus(Integer certificateStatus) {
        this.certificateStatus = certificateStatus;
    }

    public Integer getPoiCountStart() {
        return poiCountStart;
    }

    public void setPoiCountStart(Integer poiCountStart) {
        this.poiCountStart = poiCountStart;
    }

    public Integer getPoiCountEnd() {
        return poiCountEnd;
    }

    public void setPoiCountEnd(Integer poiCountEnd) {
        this.poiCountEnd = poiCountEnd;
    }

    public Integer getBizOrgCode() {
        return bizOrgCode;
    }

    public void setBizOrgCode(Integer bizOrgCode) {
        this.bizOrgCode = bizOrgCode;
    }

    public Integer getBusiness() {
        return business;
    }

    public void setBusiness(Integer business) {
        this.business = business;
    }
}

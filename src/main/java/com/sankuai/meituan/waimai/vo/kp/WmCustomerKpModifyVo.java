package com.sankuai.meituan.waimai.vo.kp;


/**
 * Author: 窦旭蒙
 * MIS: douxumeng
 * Date: 2020-07-01 15:28
 * Email: <EMAIL>
 * Desc: KP升级新增运营经理改版需求，页面操作KP由批量改为非批量
 */
public class WmCustomerKpModifyVo {
    /**
     * 客户ID
     */
    private Integer customerId;
    /**
     * 操作类型
     */
    private Integer operateType;
    /**
     * 待操作的KP对象信息
     */
    private WmCustomerKpVo kp;
    /**
     * 操作来源
     */
    private Integer opSource;

    public Integer getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Integer customerId) {
        this.customerId = customerId;
    }

    public Integer getOperateType() {
        return operateType;
    }

    public void setOperateType(Integer operateType) {
        this.operateType = operateType;
    }

    public WmCustomerKpVo getKp() {
        return kp;
    }

    public void setKp(WmCustomerKpVo kp) {
        this.kp = kp;
    }

    public Integer getOpSource() {
        return opSource;
    }

    public void setOpSource(Integer opSource) {
        this.opSource = opSource;
    }
}

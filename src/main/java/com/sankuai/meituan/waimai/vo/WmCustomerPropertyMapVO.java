package com.sankuai.meituan.waimai.vo;

import com.sankuai.meituan.scmbrand.thrift.domain.servicebrand.ServiceBrandDTO;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;

import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/3/11
 */

public class WmCustomerPropertyMapVO {
	private Map<Integer, WmEmploy> employMap;
	private Map<Integer, ServiceBrandDTO> brandDTOMap;

	public Map<Integer, WmEmploy> getEmployMap() {
		return employMap;
	}

	public void setEmployMap(Map<Integer, WmEmploy> employMap) {
		this.employMap = employMap;
	}

	public Map<Integer, ServiceBrandDTO> getBrandDTOMap() {
		return brandDTOMap;
	}

	public void setBrandDTOMap(Map<Integer, ServiceBrandDTO> brandDTOMap) {
		this.brandDTOMap = brandDTOMap;
	}
}

package com.sankuai.meituan.waimai.validator.util;

import java.util.List;


/**
 * valid检测的结果类
 */
public class ValidResult {

    public static final ValidResult EMPTY_RESULT = new ValidResult();

    private List<String> errorMessages;

    public ValidResult() {}

    public ValidResult(List<String> errorMessages) {
        this.errorMessages = errorMessages;
    }

    public boolean hasError() {
        return errorMessages != null && errorMessages.size() > 0;
    }

    public List<String> getErrorMessages() {
        return errorMessages;
    }

}
package com.sankuai.meituan.waimai.validator.impl;

import com.sankuai.meituan.waimai.validator.annotations.DateTimeFormatNeed;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.text.ParseException;
import java.text.SimpleDateFormat;


public class DateTimeFormatNeedValidator implements ConstraintValidator<DateTimeFormatNeed, String> {

    private String format;

    @Override
    public void initialize(DateTimeFormatNeed constraintAnnotation) {
        format = constraintAnnotation.value();
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {

        try {
            new SimpleDateFormat(format).parse(value);
            return true;
        } catch (ParseException e) {
            return false;
        }
    }

}



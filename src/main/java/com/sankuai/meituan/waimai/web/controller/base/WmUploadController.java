package com.sankuai.meituan.waimai.web.controller.base;

import com.alibaba.fastjson.JSONObject;
import com.meituan.servicecatalog.api.annotations.ExtensionDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.auth.vo.User;
import com.sankuai.meituan.common.security.Base64;
import com.sankuai.meituan.common.time.TimeUtil;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.config.MccConfig;
import com.sankuai.meituan.waimai.kv.groupmbase.client.WmKvTairClient;
import com.sankuai.meituan.waimai.kv.groupmbase.exception.WmKvException;
import com.sankuai.meituan.waimai.s3cloud.util.MtS3CloudFileUtil;
import com.sankuai.meituan.waimai.service.WmImageHandleService;
import com.sankuai.meituan.waimai.service.base.UploadService;
import com.sankuai.meituan.waimai.service.kp.WmCustomerKpService;
import com.sankuai.meituan.waimai.thrift.customer.constant.KpConstants;
import com.sankuai.meituan.waimai.util.MtCloudFileUtil;
import com.sankuai.meituan.waimai.util.base.ImageUtil;
import com.sankuai.meituan.waimai.util.base.WmRestReturnUtil;
import com.sankuai.meituan.waimai.util.contract.HttpUtil;
import com.sankuai.nibcus.inf.customer.client.dto.ContextDTO;
import com.sankuai.nibcus.inf.customer.client.enums.ErrorCodeEnum;
import com.sankuai.nibcus.inf.customer.client.enums.OperatorTypeEnum;
import com.sankuai.nibcus.inf.customer.client.request.FileUrlGetRequest;
import com.sankuai.nibcus.inf.customer.client.response.FileUrlGetResponse;
import com.sankuai.nibcus.inf.customer.client.service.CustomerThriftService;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.io.IOException;

/**
 * Created by lihaowei on 16-12-19.
 */
@Controller
@RequestMapping("/wmcontract/restful/api/upload")
public class WmUploadController {
    private static Logger logger = LoggerFactory.getLogger(WmUploadController.class);

    private static final String KEY_KP_CERT_TEMPLATE = "kp_%s_%s_%s";

    private static final String BUCKET = "wm_imgstore_intern";

    private static final String DOWNLOAD_PRE = "/download/mos";

    @Autowired
    private WmCustomerKpService wmCustomerKpService;

    @Autowired
    private WmImageHandleService wmImageHandleService;

    @Autowired
    private UploadService uploadService;


    @Autowired(required = false)
    @Qualifier("m_WmKvTairClient")
    private WmKvTairClient wmKvTairClient;

    public static final String URL_CUSTOMER_PLATFORM_PREFIX = "/download/mos/customerPlatform/";

    @Autowired
    private CustomerThriftService customerThriftService;

    /**
     * @param source 未压缩 图片源
     * @return {"data":{},"code":0,"msg":"success"}
     */
    @MethodDoc(
            description = "",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "无数据/数据无须鉴权"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @RequestMapping(value = "/w/uploadImg", method = RequestMethod.POST)
    @ResponseBody
    public Object uploadImg(String source) {
        logger.info("source = {}, source.length = {}", (source == null ? "空" : "非空"), (source == null ? 0 : source.length()));

        if (source == null) source = "";
        source = source.replace("data:image/jpeg;base64,", "");
        byte[] fileData = Base64.decode(source);
        if (fileData == null || fileData.length <= 0) {
            return WmRestReturnUtil.fail("上传文件为空");
        }

        if (fileData.length > 10 * 1024 * 1024) {
            return WmRestReturnUtil.fail("文件超过10M请缩小后重新上传");
        }

        logger.info("fileSize = {}", fileData.length);

        String fileSuffix = ".jpg";
        String unique_filename = "contract_file_" + DigestUtils.md5Hex(fileData) + "_" + TimeUtil.unixtime() + fileSuffix;
        String ldapUrlUploadFile = "";
        try {
            ldapUrlUploadFile = MtCloudFileUtil.uploadFileFromBytes(fileData, unique_filename);
            if (StringUtils.isBlank(ldapUrlUploadFile)) {
                return WmRestReturnUtil.fail("上传图片失败");
            }
        } catch (Exception e) {
            logger.warn("#BeeEmployeeController.uploadImg# 上传文件失败.", e);
            return WmRestReturnUtil.fail("上传图片失败");
        }
        logger.info("unique_filename = {}, 上传图片成功 = {}", unique_filename, ldapUrlUploadFile);

        return WmRestReturnUtil.success(ldapUrlUploadFile);
    }

    /**
     * @param url    图片地址
     * @param degree 旋转角度, -270 ~ 270 间隔 90
     */
    @MethodDoc(
            description = "",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "无数据/数据无须鉴权"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @RequestMapping(value = "/w/rotateImg", method = RequestMethod.POST)
    @ResponseBody
    public JSONObject rotateImg(String url, int degree) {
        logger.info("url = {}, degree = {}", url, degree);

        degree = degree % 360;
        if (degree == 0) {
            return WmRestReturnUtil.success(url);
        }

        if (url.contains(URL_CUSTOMER_PLATFORM_PREFIX)) {
            return rotateImgForMtCustomer(url, degree);
        }

        Integer index = url.lastIndexOf("/");
        Integer timeIndex = url.lastIndexOf("?");//兼容部分老美团云图片图片url后有time后缀
        String fileName;
        if (timeIndex > 0) {
            fileName = url.substring(index + 1, timeIndex);
        } else {
            fileName = url.substring(index + 1);
        }

        byte[] fileDataOld = com.sankuai.meituan.waimai.util.contract.HttpUtil.getBytesFromUrl(MtS3CloudFileUtil
                .genInternalTempUrl4Mos(fileName));
        byte[] fileData;
        try {
            fileData = ImageUtil.fileDataDegree(fileDataOld, degree);
        } catch (IOException e) {
            logger.warn("图片旋转失败", e);
            return WmRestReturnUtil.fail("图片旋转失败");
        }

        String newFileName = "file_" + DigestUtils.md5Hex(fileData) + "_" + System.currentTimeMillis() + ".jpg";
        logger.info("newFileName = {}", newFileName);

        String ldapUrlUploadFile = "";
        try {
            ldapUrlUploadFile = MtCloudFileUtil.uploadFileFromBytes(fileData, newFileName);
            if (StringUtils.isBlank(ldapUrlUploadFile)) {
                return WmRestReturnUtil.fail("图片旋转后重新上传美团云失败");
            }
            //图片上传到美团云
            String source = Base64.encodeToString(fileData);
            if (!uploadService.uploadImgToMtCustomer(ldapUrlUploadFile, source)) {
                return WmRestReturnUtil.fail("上传图片失败");
            }
        } catch (Exception e) {
            logger.warn("rotateImg 旋转之后上传文件失败 fileName = " + newFileName, e);
            return WmRestReturnUtil.fail("图片旋转后重新上传美团云失败");
        }

        logger.info("filename = {}, 旋转之后上传图片成功 = {}", newFileName, ldapUrlUploadFile);
        return WmRestReturnUtil.success(ldapUrlUploadFile);
    }


    public JSONObject rotateImgForMtCustomer(String url, int degree) {
        logger.info("rotateImgForMtCustomer url = {}, degree = {}", url, degree);
        if (StringUtils.isBlank(url)) {
            return WmRestReturnUtil.success(url);
        }
        degree = degree % 360;
        if (degree == 0) {
            return WmRestReturnUtil.success(url);
        }
        // 从客户平台拿数据
        Integer index = url.lastIndexOf("/");
        String mtCustomerPicName = url.substring(index + 1);
        String mtCustomerPicUrl = getImgUrlFormMtCustomer(mtCustomerPicName);

        byte[] fileDataOld = HttpUtil.getBytesFromUrl(mtCustomerPicUrl);

        byte[] fileData;
        try {
            fileData = ImageUtil.fileDataDegree(fileDataOld, degree);
        } catch (IOException e) {
            logger.warn("图片旋转失败", e);
            return WmRestReturnUtil.fail("图片旋转失败");
        }

        String newFileName = "file_" + DigestUtils.md5Hex(fileData) + "_" + System.currentTimeMillis() + ".jpg";
        logger.info("newFileName = {}", newFileName);

        String ldapUrlUploadFile = "";
        try {
            ldapUrlUploadFile = MtCloudFileUtil.uploadFileFromBytes(fileData, newFileName);
            if (StringUtils.isBlank(ldapUrlUploadFile)) {
                return WmRestReturnUtil.fail("图片旋转后重新上传美团云失败");
            }
            //图片上传到美团云
            String source = Base64.encodeToString(fileData);
            if (!uploadService.uploadImgToMtCustomer(ldapUrlUploadFile, source)) {
                return WmRestReturnUtil.fail("上传图片失败");
            }
        } catch (Exception e) {
            logger.warn("rotateImg 旋转之后上传文件失败 fileName = " + newFileName, e);
            return WmRestReturnUtil.fail("图片旋转后重新上传美团云失败");
        }

        logger.info("filename = {}, 旋转之后上传图片成功 = {}", newFileName, ldapUrlUploadFile);
        return WmRestReturnUtil.success(ldapUrlUploadFile);
    }

    /**
     * 根据fieldId获取在客户平台的图片地址
     * @param fieldId
     * @return
     */
    private String getImgUrlFormMtCustomer(String fieldId){
        //还原成数据库存储的样式，如：customer/download/cus/mos/p3_contract_file_9a0f941fe6b248318aef869b2ccc33ec_1536048645.jpg
        logger.info("getImgUrlFormMtCustomer, fieldId = {}", fieldId);
        //过期时间
        long mtCloudExpire = ConfigUtilAdapter.getLong("mt_cloud_expire", 300);
        FileUrlGetRequest fileUrlGetRequest = new FileUrlGetRequest();
        fileUrlGetRequest.setFileId(fieldId);
        fileUrlGetRequest.setExpiredSeconds(mtCloudExpire);
        fileUrlGetRequest.setWithWaterMark(false);
        //是否内网域名
        fileUrlGetRequest.setIsIntranet(false);
        ContextDTO contextDTO = new ContextDTO();
        int userId = UserUtils.getUser().getId();
        contextDTO.setOperatorId((long)userId);
        contextDTO.setOperatorType(OperatorTypeEnum.EMPLOYEE);
        fileUrlGetRequest.setContext(contextDTO);
        try {
            FileUrlGetResponse response = customerThriftService.getFileUrl(fileUrlGetRequest);
            logger.info("getImgUrlFormMtCustomer, response={}", JSONObject.toJSONString(response));
            if (response.getCode().getCode() == ErrorCodeEnum.SUCCESS.getCode()) {
                String tempUrl = response.getUrl();
                return tempUrl;
            } else {
                logger.error("请求客户平台获取图片地址异常:fieldId={},response={}", fieldId, response);
            }
        } catch (TException e) {
            logger.error("请求客户平台获取图片地址异常:fieldId={}", fieldId, e);
        }
        return null;
    }

    /**
     * @param url    图片地址
     * @param degree 旋转角度, -270 ~ 270 间隔 90
     */
    @MethodDoc(
            description = "",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "无数据/数据无须鉴权"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @RequestMapping(value = "/w/rotateImgforKp", method = RequestMethod.POST)
    @ResponseBody
    public JSONObject rotateImg(int kpId,String field,String url, int degree) {
        logger.info("kpId = {}, field={},url={},degree = {}", kpId, field, url, degree);
        if(StringUtils.isBlank(url)){
            return WmRestReturnUtil.fail("未找到图片地址");
        }
        User user = UserUtils.getUser();
        boolean isWaterMark = false;

        if (url.contains(BUCKET) && url.contains(MccConfig.getWaterMark())) {
            try {
                String urlCache = wmKvTairClient.get(String.format(KEY_KP_CERT_TEMPLATE, kpId, field, user.getId()));
                if (StringUtils.isNotBlank(urlCache)) {
                    url = urlCache;
                } else {
                    url = wmCustomerKpService.getKpCertSourceUrl(kpId, field);
                }
                isWaterMark = true;
            } catch (WmKvException e) {
                return WmRestReturnUtil.fail("未找到图片地址");
            }
        }

        if(StringUtils.isBlank(url)){
            return WmRestReturnUtil.fail("未找到图片地址");
        }

        degree = degree % 360;
        if (degree == 0) {
            return WmRestReturnUtil.success(url);
        }

        Integer index = url.lastIndexOf("/");
        Integer timeIndex = url.lastIndexOf("?");//兼容部分老美团云图片图片url后有time后缀
        String fileName;
        if (timeIndex > 0) {
            fileName = url.substring(index + 1, timeIndex);
        } else {
            fileName = url.substring(index + 1);
        }

        byte[] fileDataOld = com.sankuai.meituan.waimai.util.contract.HttpUtil.getBytesFromUrl(MtS3CloudFileUtil
                .genInternalTempUrl4Mos(fileName));
        byte[] fileData;
        try {
            fileData = ImageUtil.fileDataDegree(fileDataOld, degree);
        } catch (IOException e) {
            logger.warn("图片旋转失败", e);
            return WmRestReturnUtil.fail("图片旋转失败");
        }

        String newFileName = "file_" + DigestUtils.md5Hex(fileData) + "_" + System.currentTimeMillis() + ".jpg";
        logger.info("newFileName = {}", newFileName);

        String ldapUrlUploadFile = "";
        try {
            ldapUrlUploadFile = MtCloudFileUtil.uploadFileFromBytes(fileData, newFileName);
            if (StringUtils.isBlank(ldapUrlUploadFile)) {
                return WmRestReturnUtil.fail("图片旋转后重新上传美团云失败");
            }

            if (isWaterMark && user != null) {
                wmKvTairClient.set(String.format(KEY_KP_CERT_TEMPLATE, kpId, field, user.getId()), ldapUrlUploadFile, MccConfig.kpCertExpireSeconds());
            }

            if (ldapUrlUploadFile.contains(DOWNLOAD_PRE) && MccConfig.waterMarkSwitch() && isWaterMark) {
                ldapUrlUploadFile = wmImageHandleService.getWaterMarkUrlForMos(ldapUrlUploadFile);
            }
        } catch (Exception e) {
            logger.warn("rotateImg 旋转之后上传文件失败 fileName = " + newFileName, e);
            return WmRestReturnUtil.fail("图片旋转后重新上传美团云失败");
        }

        logger.info("filename = {}, 旋转之后上传图片成功 = {}", newFileName, ldapUrlUploadFile);
        return WmRestReturnUtil.success(ldapUrlUploadFile);
    }
}

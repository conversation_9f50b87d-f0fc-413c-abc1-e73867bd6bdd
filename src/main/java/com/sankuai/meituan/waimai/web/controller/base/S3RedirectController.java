package com.sankuai.meituan.waimai.web.controller.base;

import com.meituan.servicecatalog.api.annotations.ExtensionDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.auth.vo.User;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.s3cloud.util.MtS3CloudFileUtil;
import com.sankuai.meituan.waimai.scm.cloud.service.MtCloudService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;


@Controller
@RequestMapping(value={"/qb/download","/qb/ba/download"})
public class S3RedirectController {

    private Logger logger = LoggerFactory.getLogger(S3RedirectController.class);

    private static final String P3 = "p3_";

    private static final String host = "http://s3plus-img.sankuai.com";

    private static final String old_host = "http://s3-img.meituan.net";

    private static final String SOURCE_INNER = "inner";

    @Resource(name = "mtCloudService")
    private MtCloudService mtCloudService;

    private boolean getMtCloudDownloadSwitch(){
        return ConfigUtilAdapter.getBoolean("mt_cloud_download_switch", true);
    }

    /**
     * 从门店资质洗到客户资质的图片下载
     * @param id
     * @param source
     * @param percentage
     * @param width
     * @param response
     * @throws IOException
     */
    @MethodDoc(
            description = "",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "无数据/数据无须鉴权"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @RequestMapping(value ="mos/{id:.+}", method = RequestMethod.GET)
    public void qbMos(@PathVariable("id") String id, @RequestParam(value = "source", required = false, defaultValue = "outer") String source, Integer percentage, Integer width, HttpServletResponse response) throws IOException {
        logger.info("===mos====id:{},source:{},percentage:{},width:{}=====",id,source,percentage,width);
        String tempUrl = null;
        User user=UserUtils.getUser();
        if(percentage != null || width != null){
            String fileName = id;
            if(percentage != null) {
                fileName = id + "@" + percentage + "p";
            }else if(width != null){
                fileName = id + "@" + width + "w";
            }

            String path="";
            if(getMtCloudDownloadSwitch()){
                path= mtCloudService.getRelativeDownUrl(fileName,0,user.getId(),user.getName());
            }else{
                String md5Name = genMd5Name(id, fileName);
                int MT_CLOUD_EXPIRE = ConfigUtilAdapter.getInt("mt_cloud_expire", 300);
                path = MtS3CloudFileUtil.tempurl("GET", "mt_tenant_2230562", "wm_imgstore_intern", md5Name, (long)MT_CLOUD_EXPIRE, "f9NAUp2N9gVn");
            }

            if(ConfigUtilAdapter.getBoolean("s3_qulification_download_switch")){
                tempUrl = host + path;
            }else {
                if (id.startsWith(P3)) {
                    tempUrl = host + path;
                } else {
                    tempUrl = old_host + path;
                }
            }
        }else{

            if(getMtCloudDownloadSwitch()){
                if (SOURCE_INNER.equalsIgnoreCase(source)) {
                    tempUrl =  mtCloudService.getAbsoluteDownUrl(id,true,0,user.getId(),user.getName());
                } else {
                    tempUrl = mtCloudService.getAbsoluteDownUrl(id,false,0,user.getId(),user.getName());
                }
            }else{
                if (SOURCE_INNER.equalsIgnoreCase(source)) {
                    tempUrl = MtS3CloudFileUtil.genInternalTempUrl4Mos(id);
                } else {
                    tempUrl = MtS3CloudFileUtil.genTempUrl4Mos(id);
                }
            }

        }
        response.sendRedirect(tempUrl);
    }

    private String genMd5Name(String id, String fileName) {
        String md5Name = MtS3CloudFileUtil.getMD5code(id);
        int pos = fileName.lastIndexOf(".");
        if(pos != -1) {
            md5Name = md5Name + fileName.substring(pos);
        }
        return md5Name;
    }
}

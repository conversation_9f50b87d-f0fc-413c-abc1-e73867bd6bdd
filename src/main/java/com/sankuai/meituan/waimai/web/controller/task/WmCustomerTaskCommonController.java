package com.sankuai.meituan.waimai.web.controller.task;

import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.meituan.servicecatalog.api.annotations.ExtensionDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.ParamType;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.auth.vo.User;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.config.MccConfig;
import com.sankuai.meituan.waimai.constant.BatchTaskConstant;
import com.sankuai.meituan.waimai.econtrct.client.constants.EcontractContentTypeEnum;
import com.sankuai.meituan.waimai.econtrct.client.constants.EcontractPdfTypeEnum;
import com.sankuai.meituan.waimai.econtrct.client.service.EcontractBizService;
import com.sankuai.meituan.waimai.mtauth.controlview.keyinfo.KeyField;
import com.sankuai.meituan.waimai.mtauth.controlview.keyinfo.KeyInfoUtils;
import com.sankuai.meituan.waimai.service.WmCustomerCheck;
import com.sankuai.meituan.waimai.service.agreement.WmEcontractSpAreaContentWrapperService;
import com.sankuai.meituan.waimai.service.dc.DcContractAuthService;
import com.sankuai.meituan.waimai.service.metric.ContractMetricService;
import com.sankuai.meituan.waimai.service.task.WmSignTaskService;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.BusinessGroupLineEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.WmEcontractBatchConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.WmTempletContractTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerBasicBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.RetrySmsResponse;
import com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerThriftService;
import com.sankuai.meituan.waimai.thrift.customer.service.WmEcontractSignThriftService;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import com.sankuai.meituan.waimai.util.EncryptUtil;
import com.sankuai.meituan.waimai.util.base.WmRestReturnUtil;
import com.sankuai.meituan.waimai.vo.agreement.SpAreaDataVo;
import com.sankuai.meituan.waimai.vo.agreement.WmShowAreaVo;
import com.sankuai.meituan.waimai.vo.sign.ShortLinkQueryParam;
import com.sankuai.meituan.waimai.vo.task.ManualTaskCommitParam;
import com.sankuai.meituan.waimai.vo.task.SignTaskSearchParam;
import com.sankuai.meituan.waimai.vo.task.SignTaskSearchResult;
import org.apache.commons.collections.MapUtils;
import org.apache.thrift.TException;
import org.mortbay.util.ajax.JSON;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.*;

/**
 * 客户打包任务 PC、APP通用接口
 */
public class WmCustomerTaskCommonController {

    public static final Logger LOGGER = LoggerFactory.getLogger(WmCustomerTaskCommonController.class);

    public static final int READ_OPERATION = 1;

    public static final int WRITE_OPERATION = 2;

    @Autowired
    protected WmSignTaskService       wmSignTaskService;
    @Resource
    private   WmCustomerThriftService wmCustomerThriftService;
    @Autowired
    private   WmCustomerCheck         wmCustomerCheck;

    @Autowired
    private DcContractAuthService dcContractAuthService;

    @Resource
    private ContractMetricService contractMetricService;

    @Resource
    private WmEcontractSignThriftService wmEcontractSignThriftService;

    @Resource
    private EcontractBizService econtractBizService;

    @Resource
    private WmEcontractSpAreaContentWrapperService wmEcontractSpAreaContentWrapperService;

    @MethodDoc(
            displayName = "搜索",
            description = "重发短信，重发短信",
            parameters = {
                    @ParamDoc(
                            name = "name",
                            description = "name",
                            paramType = ParamType.REQUEST_PARAM
                    )
            },
            responseParams = {
                    @ParamDoc(
                            name = "code",
                            description = "调用状态")
            },
            returnValueDescription = "返回returnValueDescription",
            restExampleUrl = "示例restExampleUrl",
            restExampleResponseData = "输出restExampleResponseData",
            restExamplePostData = "示例restExamplePostData", example = "举例example",
            extensions = {
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "数据鉴权逻辑：参数customerId, 只能查询自己有权限修改的客户信息"),
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE_VERIFY", content = "True"),
            })
    @RequestMapping(value = "r/signTask/search", method = RequestMethod.POST)
    @ResponseBody
    public Object search(@RequestBody String input,
                         HttpServletRequest request)  {

        LOGGER.info("WmCustomerTaskCommonController#task#search,input={}", input);

        SignTaskSearchParam signTaskSearchParam = JSONObject.parseObject(input, SignTaskSearchParam.class);

        if (signTaskSearchParam == null) {
            return WmRestReturnUtil.fail("查询参数为空");
        }

        if (signTaskSearchParam.getWmCustomerId() <= 0) {
            return WmRestReturnUtil.fail("参数异常");
        }

        Integer businessGroupLine = signTaskSearchParam.getBusinessGroupLine();
        // 默认为到家业务线
        businessGroupLine = businessGroupLine == null ? BusinessGroupLineEnum.DAOJIA_DELIVERY.getGroupLine() : businessGroupLine;
        if (businessGroupLine.equals(BusinessGroupLineEnum.DAOCAN_SERVICE.getGroupLine()) && signTaskSearchParam.getTaskType() == 0) {
            // 到餐任务类型
            signTaskSearchParam.setTaskType(WmEcontractBatchConstant.ALL_TASK_TYPE_DC);
        }

        try {
            // 读操作，校验权限
            User user = UserUtils.getUser();
            if (businessGroupLine.equals(BusinessGroupLineEnum.DAOCAN_SERVICE.getGroupLine())) {
                dcContractAuthService.authReadDc(user.getId(), signTaskSearchParam.getWmCustomerId());
            } else {
                wmSignTaskService.checkTaskAuth(signTaskSearchParam.getWmCustomerId(), user.getId(), READ_OPERATION);
            }
            // 执行查询
            SignTaskSearchResult searchResult = wmSignTaskService.search(signTaskSearchParam);

            List<KeyField> keyFieldList = Lists.newArrayList(
                    KeyField.of("receiverPhone", KeyField.Type.PHONE)
            );

            return KeyInfoUtils.wrapJson(request, keyFieldList, WmRestReturnUtil.success(searchResult));
        } catch (TException e) {
            LOGGER.info("#task#search,input={} 失败", input, e);
            return WmRestReturnUtil.fail("搜索服务异常");
        } catch (WmCustomerException e) {
            LOGGER.info("#task#search,input={}", input, e);
            return WmRestReturnUtil.fail("搜索失败：" + e.getMsg());
        } catch (WmServerException e) {
            LOGGER.info("#task#search,input={}", input, e);
            return WmRestReturnUtil.fail("搜索失败：" + e.getMsg());
        } catch (Exception e) {
            LOGGER.info("WmCustomerTaskCommonController#task#search, error", e);
            return WmRestReturnUtil.fail("搜索失败");
        }
    }

    @MethodDoc(
            displayName = "重发短信",
            description = "重发短信，重发短信",
            parameters = {
                    @ParamDoc(name = "name", description = "name", paramType = ParamType.REQUEST_PARAM)
            },
            responseParams = {
                    @ParamDoc(name = "code", description = "调用状态")
            },
            returnValueDescription = "返回returnValueDescription",
            restExampleUrl = "示例restExampleUrl",
            restExampleResponseData = "输出restExampleResponseData",
            restExamplePostData = "示例restExamplePostData", example = "举例example",
            extensions = {
                            @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "数据鉴权逻辑：参数customerId, 只能操作自己有权限修改的客户信息"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            })
    @RequestMapping(value = "w/signTask/reSendSMS", method = RequestMethod.POST)
    @ResponseBody
    public Object reSendSMS(int wmCustomerId, int batchId, String batchIdType) throws Exception {
        LOGGER.info("#task#reSendSMS wmCustomerId={} batchId={} batchIdType={}", wmCustomerId, batchId, batchIdType);
        if (!checkOperationInput(wmCustomerId, batchId, batchIdType)) {
            return WmRestReturnUtil.fail("参数异常");
        }
        User user = UserUtils.getUser();
        // 写操作，校验权限
        if (wmSignTaskService.isDcSignTask(batchId, batchIdType)) {
            Long mtCustomerId = wmSignTaskService.getMtCustomerId(batchId, batchIdType);
            dcContractAuthService.authWriteDcWithMtCustomerId(wmCustomerId, mtCustomerId, user.getId());
        } else {
            wmSignTaskService.checkTaskAuth(wmCustomerId, user.getId(), WRITE_OPERATION);
        }
        // 重发短信
        RetrySmsResponse retrySmsResponse = wmSignTaskService.reSendSMS(wmCustomerId, batchId, batchIdType);
        return WmRestReturnUtil.success("success", "重发短信成功。已发送给" + EncryptUtil.encryptMobiles(retrySmsResponse.getMobiles()));
    }

    @MethodDoc(
            displayName = "获取签约链接",
            description = "获取签约链接",
            responseParams = {
                    @ParamDoc(name = "code", description = "调用状态"),
                    @ParamDoc(name = "msg", description = "失败原因"),
                    @ParamDoc(name = "data", description = "签约链接")
            }
    )
    @RequestMapping(value = "w/signTask/getShortLink", method = RequestMethod.GET)
    @ResponseBody
    public Object getShortLink(ShortLinkQueryParam queryParam) throws Exception {
        LOGGER.info("WmCustomerTaskCommonController#getShortLink, queryParam: {}", JSONObject.toJSONString(queryParam));
        if (!checkOperationInput(queryParam.getWmCustomerId(), queryParam.getId(), queryParam.getType())) {
            return WmRestReturnUtil.fail("参数异常");
        }
        // 读操作，校验权限
        User user = UserUtils.getUser();
        Integer businessGroupLine = queryParam.getBusinessGroupLine();
        if (businessGroupLine != null && businessGroupLine.equals(BusinessGroupLineEnum.DAOCAN_SERVICE.getGroupLine())) {
            dcContractAuthService.authReadDc(user.getId(), queryParam.getWmCustomerId());
        } else {
            wmSignTaskService.checkTaskAuth(queryParam.getWmCustomerId(), user.getId(), READ_OPERATION);
        }
        String signLink = wmSignTaskService.getSmsLink(queryParam);
        return WmRestReturnUtil.success(signLink);
    }

    @MethodDoc(
            displayName = "查看先富配送信息下面的配送范围",
            description = "查看先富配送信息下面的配送范围",
            responseParams = {
                    @ParamDoc(name = "code", description = "调用状态"),
                    @ParamDoc(name = "msg", description = "失败原因"),
                    @ParamDoc(name = "data", description = "范围信息")
            }
    )
    @RequestMapping("w/signTask/showArea")
    @ResponseBody
    public JSONObject showArea(WmShowAreaVo wmShowAreaVo) {
        try {
            LOGGER.info("WmCustomerTaskCommonController#showArea, wmShowAreaVo: {}", JSON.toString(wmShowAreaVo));
            if (wmShowAreaVo == null || wmShowAreaVo.getBatchId() <= 0 || StringUtils.isEmpty(wmShowAreaVo.getAreaModule())) {
                return WmRestReturnUtil.fail("数据异常, 请稍后重试!");
            }
            wmSignTaskService.checkTaskAuth(Math.toIntExact(wmShowAreaVo.getCustomerId()), UserUtils.getUser().getId(), READ_OPERATION);
            //获取坐标信息
            String recordKey = wmEcontractSignThriftService.queryRecordIdByBatchId(wmShowAreaVo.getBatchId());
            if (StringUtils.isEmpty(recordKey)) {
                return WmRestReturnUtil.fail("数据异常, 请稍后重试!");
            }
            Map<String, String> spAreaMap = econtractBizService.queryEcontractSpAreaData(recordKey);
            return WmRestReturnUtil.success(analysisSpAreaData(spAreaMap, wmShowAreaVo.getAreaModule()));
        } catch (WmCustomerException exception) {
            LOGGER.warn("WmCustomerTaskCommonController#showArea, warn", exception);
            return WmRestReturnUtil.fail(exception.getMsg());
        } catch (Exception e) {
            LOGGER.error("WmCustomerTaskCommonController#showArea, error", e);
            return WmRestReturnUtil.fail("数据异常, 请稍后重试!");
        }
    }

    @MethodDoc(
            displayName = "取消确认",
            description = "取消确认，取消确认",
            parameters = {
                    @ParamDoc(name = "name", description = "name", paramType = ParamType.REQUEST_PARAM)
            },
            responseParams = {
                    @ParamDoc(name = "code", description = "调用状态")
            },
            returnValueDescription = "返回returnValueDescription",
            restExampleUrl = "示例restExampleUrl",
            restExampleResponseData = "输出restExampleResponseData",
            restExamplePostData = "示例restExamplePostData", example = "举例example",
            extensions = {
                            @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "数据鉴权逻辑：参数customerId, 判断操作人是否有权限修改该客户"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            })
    @RequestMapping(value = "w/signTask/cancelConfirm", method = RequestMethod.POST)
    @ResponseBody
    public Object cancelConfirm(int wmCustomerId, int batchId, String batchIdType) throws Exception {
        LOGGER.info("WmCustomerTaskCommonController#cancelConfirm wmCustomerId={} batchId={} batchIdType={}", wmCustomerId, batchId, batchIdType);
        if (!checkOperationInput(wmCustomerId, batchId, batchIdType)) {
            return WmRestReturnUtil.fail("参数异常");
        }
        // 写操作，校验权限
        User user = UserUtils.getUser();
        if (wmSignTaskService.isDcSignTask(batchId, batchIdType)) {
            Long mtCustomerId = wmSignTaskService.getMtCustomerId(batchId, batchIdType);
            dcContractAuthService.authWriteDcWithMtCustomerId(wmCustomerId, mtCustomerId, user.getId());
        } else {
            wmSignTaskService.checkTaskAuth(wmCustomerId, user.getId(), WRITE_OPERATION);
        }
        // 取消签约
        wmSignTaskService.cancelConfirm(wmCustomerId, batchId, batchIdType);
        return WmRestReturnUtil.success();
    }

    @MethodDoc(
            displayName = "强制解绑", description = "强制解绑，强制解绑",
            parameters = {
                    @ParamDoc(name = "name", description = "name", paramType = ParamType.REQUEST_PARAM)
            },
            responseParams = {
                    @ParamDoc(name = "code", description = "调用状态")
            },
            returnValueDescription = "返回returnValueDescription",
            restExampleUrl = "示例restExampleUrl",
            restExampleResponseData = "输出restExampleResponseData",
            restExamplePostData = "示例restExamplePostData", example = "举例example",
            extensions = {
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "数据鉴权逻辑：参数customerId, 判断操作人是否有权限修改该客户"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            })
    @RequestMapping(value = "w/signTask/forceUnBind", method = RequestMethod.POST)
    @ResponseBody
    public Object forceUnBind(int wmCustomerId, long batchId, String batchIdType) throws Exception {
        LOGGER.info("#task#forceUnBind,wmCustomerId={},batchId={}，batchIdType={}", wmCustomerId, batchId, batchIdType);
        if (!checkOperationInput(wmCustomerId, batchId, batchIdType)) {
            return WmRestReturnUtil.fail("参数异常");
        }
        // 写操作，校验权限
        User user = UserUtils.getUser();
        wmSignTaskService.checkTaskAuth(wmCustomerId, user.getId(), WRITE_OPERATION);
        // 获取用户信息
        Integer userId = UserUtils.getUser().getId();// 用户ID
        String userName = UserUtils.getUser().getName();// 用户名称
        // 强制解绑
        wmSignTaskService.forceUnBind(wmCustomerId, batchId, batchIdType, userId, userName);
        return WmRestReturnUtil.success();
    }

    /**
     * 校验任务列表操作入参
     * 
     * @return
     */
    private boolean checkOperationInput(int wmCustomerId, long batchId, String batchIdType) {
        return wmCustomerId > 0 && batchId > 0 && !StringUtils.isEmpty(batchIdType);
    }

    @MethodDoc(
            displayName = "提交手动待打包任务", description = "提交手动待打包任务",
            parameters = {
                    @ParamDoc(name = "name", description = "name", paramType = ParamType.REQUEST_PARAM)
            },
            responseParams = {
                    @ParamDoc(name = "code", description = "调用状态")
            },
            returnValueDescription = "返回returnValueDescription",
            restExampleUrl = "示例restExampleUrl",
            restExampleResponseData = "输出restExampleResponseData",
            restExamplePostData = "示例restExamplePostData", example = "举例example",
            extensions = {
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "数据鉴权逻辑：参数customerId, 判断操作人是否有权限修改该客户"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            })
    @RequestMapping(value = "w/signTask/commitManualTask", method = RequestMethod.POST)
    @ResponseBody
    public Object commitManualTask(@RequestBody String input) throws Exception {
        try{
            LOGGER.info("#task#commitManualTask,input={}", input);
            ManualTaskCommitParam manualTaskCommitParam = JSONObject.parseObject(input, ManualTaskCommitParam.class);
            manualTaskCommitParam.setCommitUid(UserUtils.getUser().getId());
            // 写操作，校验权限
            User user = UserUtils.getUser();
            Integer businessGroupLine = manualTaskCommitParam.getBusinessGroupLine();
            if (businessGroupLine == null) {
                businessGroupLine = BusinessGroupLineEnum.DAOJIA_DELIVERY.getGroupLine();
            }

            if (!businessGroupLine.equals(BusinessGroupLineEnum.DAOCAN_SERVICE.getGroupLine())) {
                wmSignTaskService.checkTaskAuth(manualTaskCommitParam.getWmCustomerId(), user.getId(), WRITE_OPERATION);
            }
            // 发起打包
            int code = wmSignTaskService.commitManualTask(manualTaskCommitParam);
            if (code == WmContractErrorCodeConstant.BUSINESS_ERROR) {
                int actualTaskLimitNum = ConfigUtilAdapter.getInt("actual_task_limit_num",300);
                return WmRestReturnUtil.fail("提交任务数量过多，本次仅提交成功" + actualTaskLimitNum + "个任务");
            }
            return WmRestReturnUtil.success();
        } catch (WmCustomerException e) {
            LOGGER.warn("WmCustomerTaskCommonController#commitManualTask, 提交待打包任务发起签约失败, warn", e);
            Cat.logMetricForCount("commitManualTask_error");
            return WmRestReturnUtil.fail(e.getMsg());
        } catch (Exception e) {
            LOGGER.error("提交待打包任务发起签约失败,input={}", input, e);
            Cat.logMetricForCount("commitManualTask_error");
            return WmRestReturnUtil.fail("打包发送短信失败");
        }
    }

    @MethodDoc(
            displayName = "获取模块状态", description = "获取模块状态",
            parameters = {
                    @ParamDoc(name = "name", description = "name", paramType = ParamType.REQUEST_PARAM)
            },
            responseParams = {
                    @ParamDoc(name = "code", description = "调用状态")
            },
            returnValueDescription = "返回returnValueDescription",
            restExampleUrl = "示例restExampleUrl",
            restExampleResponseData = "输出restExampleResponseData",
            restExamplePostData = "示例restExamplePostData", example = "举例example",
            extensions = {
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "数据鉴权逻辑：参数customerId, 判断操作人是否有权限修改该客户"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            })
    @RequestMapping(value = "r/signTask/getModuleTaskStatus", method = RequestMethod.POST)
    @ResponseBody
    public Object getModuleTaskStatus(int wmCustomerId) throws Exception {
        LOGGER.info("#task#getModuleTaskStatus, wmCustomerId={}", wmCustomerId);
        // 读操作，校验权限
        User user = UserUtils.getUser();
        wmSignTaskService.checkTaskAuth(wmCustomerId, user.getId(), READ_OPERATION);
        // 是否存在待打包任务
        boolean hasManualTask = wmSignTaskService.hasManualTask(wmCustomerId);
        JSONObject data = new JSONObject();
        data.put("hasManualTask", hasManualTask);
        data.put("hasProcessingTask", false);
        if (!hasManualTask) {
            SignTaskSearchParam searchParam = new SignTaskSearchParam();
            searchParam.setWmCustomerId(wmCustomerId);
            searchParam.setState(WmEcontractBatchConstant.PROCESSING_STATE);
            searchParam.setPageSize(1);
            searchParam.setPageNum(1);
            SignTaskSearchResult search = wmSignTaskService.search(searchParam);
            data.put("hasProcessingTask", search.getInProgressNum() > 0);
        }
        return WmRestReturnUtil.success(data);
    }

    @MethodDoc(
            displayName = "获取任务类型", description = "获取任务类型",
            parameters = {
                    @ParamDoc(name = "name", description = "name", paramType = ParamType.REQUEST_PARAM)
            },
            responseParams = {
                    @ParamDoc(name = "code", description = "调用状态")
            },
            returnValueDescription = "返回returnValueDescription",
            restExampleUrl = "示例restExampleUrl",
            restExampleResponseData = "输出restExampleResponseData",
            restExamplePostData = "示例restExamplePostData", example = "举例example",
            extensions = {
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "无需鉴权"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            })
    @RequestMapping(value = "r/signTask/taskType", method = RequestMethod.GET)
    @ResponseBody
    public Object getTaskType() throws Exception {
        return WmRestReturnUtil.success(wmSignTaskService.getTaskType());
    }

    @MethodDoc(
            displayName = "获取pdf链接", description = "获取pdf链接",
            parameters = {
                    @ParamDoc(name = "name", description = "name", paramType = ParamType.REQUEST_PARAM)
            },
            responseParams = {
                    @ParamDoc(name = "code", description = "调用状态")
            },
            returnValueDescription = "返回returnValueDescription",
            restExampleUrl = "示例restExampleUrl",
            restExampleResponseData = "输出restExampleResponseData",
            restExamplePostData = "示例restExamplePostData", example = "举例example",
            extensions = {
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "数据鉴权逻辑：参数customerId, 判断操作人是否有权限修改该客户"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            })
    @RequestMapping(value = "r/signTask/pdfUrl/get", method = RequestMethod.GET)
    @ResponseBody
    public Object getContractPdfUrl(int customerId, int batchId, String batchIdType) throws Exception {
        LOGGER.info("WmCustomerTaskCommonController#getContractPdfUrl customerId={} batchId={} batchIdType={}", customerId, batchId, batchIdType);
        if (StringUtils.isEmpty(batchIdType)) {
            batchIdType = BatchTaskConstant.BATCH_TYPE;
            contractMetricService.logMetricForNoBatchIdTypeParam();
        }
        if (!checkOperationInput(customerId, batchId, batchIdType)) {
            return WmRestReturnUtil.fail("参数异常");
        }
        // 读操作，校验权限
        User user = UserUtils.getUser();
        if (wmSignTaskService.isDcSignTask(batchId, batchIdType)) {
            dcContractAuthService.authReadDc(user.getId(), customerId);
        } else {
            wmSignTaskService.checkTaskAuth(customerId, user.getId(), READ_OPERATION);
        }
        // 获取PDF链接
        JSONObject data = new JSONObject();
        Map<String, String> pdfUrlMap = wmSignTaskService.getContractMtS3UrlMap(customerId, batchId, batchIdType);
        // 查询签约任务的pdf
        data.put("pdfUrlMap", pdfUrlMap);
        return WmRestReturnUtil.success(data);
    }

    @MethodDoc(
            displayName = "获取客户角色", description = "获取客户角色",
            parameters = {
                    @ParamDoc(name = "name", description = "name", paramType = ParamType.REQUEST_PARAM)
            },
            responseParams = {
                    @ParamDoc(name = "code", description = "调用状态")
            },
            returnValueDescription = "返回returnValueDescription",
            restExampleUrl = "示例restExampleUrl",
            restExampleResponseData = "输出restExampleResponseData",
            restExamplePostData = "示例restExamplePostData", example = "举例example",
            extensions = {
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "数据鉴权逻辑：参数customerId, 判断操作人是否有权限修改该客户"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            })
    @RequestMapping(value = "r/signTask/getUserRole", method = RequestMethod.GET)
    @ResponseBody
    public Object getUserRole(int wmCustomerId) throws Exception {
        LOGGER.info("#getUserRole wmCustomerId={}", wmCustomerId);
        if (wmCustomerId <= 0) {
            return WmRestReturnUtil.fail("参数异常");
        }
        int role = 0;// 默认无权限
        User user = UserUtils.getUser();
        // 降级场景
        if (MccConfig.openHQAuthSplitForCustomer()) {
            role = wmSignTaskService.getUserRole(wmCustomerId, user.getId());
        } else {
            WmCustomerBasicBo wmCustomerBasicBo = wmCustomerThriftService.getCustomerById(wmCustomerId);
            if (wmCustomerBasicBo == null) {
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "客户ID无法查到有效数据");
            }
            role = wmCustomerCheck.hasOwnerAuth(wmCustomerBasicBo, user.getId()) ? 2 : 0;
        }
        JSONObject data = new JSONObject();
        data.put("role", role);
        return WmRestReturnUtil.success(data);
    }

    /**
     * 解析配送范围信息spArea
     */
    private List<SpAreaDataVo> analysisSpAreaData(Map<String, String> spAreaMap, String areaModule) throws IOException {
        List<SpAreaDataVo> result = Lists.newArrayList();
        if (MapUtils.isEmpty(spAreaMap)) {
            return result;
        }
        Set<Map.Entry<String, String>> set = spAreaMap.entrySet();
        for (Map.Entry<String, String> entry : set) {
            String value = entry.getValue();
            String key = entry.getKey();
            if (StringUtils.isEmpty(value)) {
                continue;
            }
            SpAreaDataVo vo = new SpAreaDataVo();
            // 历史数据中默认只有配送协议，所以只要是历史数据默认都是delivery
            vo.setKey("default".equals(key) ? EcontractPdfTypeEnum.DELIVERY.getName() : key);
            if (!vo.getKey().equals(areaModule)) {
                continue;
            }
            vo.setKeyName("default".equals(key) ? EcontractPdfTypeEnum.DELIVERY.getDesc() : getEcontractContentTypeEnumDesc(key));
            vo.setKeyData(wmEcontractSpAreaContentWrapperService.analysisData(value, EcontractContentTypeEnum.SP_AREA.getName()));

            result.add(vo);
        }
        return result;
    }

    /**
     * 获取EcontractPdfTypeEnum对应的desc
     */
    private String getEcontractContentTypeEnumDesc(String name) {
        if (StringUtils.isEmpty(name)) {
            return "";
        }
        for (EcontractPdfTypeEnum item : EcontractPdfTypeEnum.values()) {
            if (item.getName().equals(name)) {
                return item.getDesc();
            }
        }
        return "";
    }

}

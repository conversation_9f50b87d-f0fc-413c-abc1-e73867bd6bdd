package com.sankuai.meituan.waimai.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * -@author: h<PERSON><PERSON><PERSON><PERSON>
 * -@description: 参数为账号id和分页查询，如果获取全部，则 offset和limit设置为-1
 * -@date: 2022/11/18 8:17 PM
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class QueryAccountPoiBO {

    private Long acctId;

    private Integer offset;

    private Integer limit;
}

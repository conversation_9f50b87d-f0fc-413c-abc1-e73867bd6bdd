package com.sankuai.meituan.waimai.util.base;

import com.sankuai.meituan.waimai.thrift.util.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.regex.Pattern;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 15-8-20.
 */
public class DatesUtil {
    public final static String DefaultShortFormat = "yyyy-MM-dd";
    public final static String DefaultLongFormat = "yyyy-MM-dd HH:mm:ss";

    /**
     * @return
     */
    public static Date today() {
        return toDay(new Date());
    }

    public static Date toDay(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

    /**
     * 下周
     * @param date
     * @return
     */
    public static Date getNextWeek(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.WEEK_OF_MONTH, 1);
        return cal.getTime();
    }

    /**
     * 上周
     * @param date
     * @return
     */
    public static Date getPrevWeek(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.WEEK_OF_MONTH, -1);
        return cal.getTime();
    }

    public static Date getPrevMonday(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(getPrevWeek(date));
        cal.set(Calendar.DAY_OF_WEEK,Calendar.MONDAY);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

    /**
     * 上一个周日（周一为一周第一天,周日为一周最后一天）
     * @param date
     * @return
     */
    public static Date getPrevSunday(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(Calendar.DAY_OF_WEEK, Calendar.SUNDAY);
        cal.set(Calendar.HOUR_OF_DAY, 23);
        cal.set(Calendar.MINUTE, 59);
        cal.set(Calendar.SECOND, 59);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

    public static Date getThisMonday(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(Calendar.DAY_OF_WEEK,Calendar.MONDAY);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

    public static Date getThisSunday(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(getNextWeek(date));
        cal.set(Calendar.DAY_OF_WEEK, Calendar.SUNDAY);
        cal.set(Calendar.HOUR_OF_DAY, 23);
        cal.set(Calendar.MINUTE, 59);
        cal.set(Calendar.SECOND, 59);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

    /**
     * the string format must yyyy-MM-dd
     *
     * @param str
     * @return
     * <AUTHOR>
     * <p/>
     * 2011-4-21
     */
    public static Date string2DateDay(String str) {
        SimpleDateFormat formatter = new SimpleDateFormat(DefaultShortFormat);
        String day = str==null?"":str.trim();
        try {
            return formatter.parse(day);
        } catch (ParseException e) {
            Calendar cal = Calendar.getInstance();
            cal.set(Calendar.HOUR_OF_DAY, 0);
            cal.set(Calendar.MINUTE, 0);
            cal.set(Calendar.SECOND, 0);
            cal.set(Calendar.MILLISECOND, 0);
            return cal.getTime();
        }
    }

    /**
     * yyyy-MM-dd
     *
     * @param date
     * @return
     * <AUTHOR>
     * <p/>
     * 2011-4-21
     */
    public static String Date2String(Date date) {
        SimpleDateFormat formatter = new SimpleDateFormat(DefaultShortFormat);
        return formatter.format(date);
    }
    public static String date2yyyymmddhhmmssStr(Date date) {
        SimpleDateFormat formatter = new SimpleDateFormat(DefaultLongFormat);
        return formatter.format(date);
    }

    public static String Date2String(Date date,String str) {
        SimpleDateFormat formatter = new SimpleDateFormat(str);
        return formatter.format(date);
    }

    /**
     * 日期（天）转unixtime
     *
     * @param day
     * @return
     * <AUTHOR>
     * @created 2012-03-15
     */
    public static int day2Unixtime(String day) {
        return (int) (com.sankuai.meituan.waimai.util.DateUtil.string2DateDay(day).getTime() / 1000L);
    }

    /**
     * date转unixtime
     *
     * @param date
     * @return
     * <AUTHOR>
     * @created 2012-06-15
     */
    public static int date2Unixtime(Date date) {
        return (int) (date.getTime() / 1000L);
    }

    public static boolean dateCheck(String str) {
        Pattern pattern = Pattern.compile("[0-9]{4}-[0-9]{2}-[0-9]{2}");
        return pattern.matcher(str).matches();
    }

    /**
     * 秒转日期
     *
     * @param seconds
     * @return
     */
    public static String secondsToString(Integer seconds) {
        return Date2String(fromUnixTime(seconds));
    }

    /**
     * 把表转换为Date
     *
     * @param seconds
     * @return
     */
    public static Date fromUnixTime(Integer seconds) {
        return new Date(seconds * 1000L);
    }


    public static int addMonth(int IssueDateUnixTime,int addMonth) {
        //unixTime to date
        Date date = new Date(IssueDateUnixTime*1000L);
        //date to calendar
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MONTH,addMonth);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 0);
        date = calendar.getTime();
        Long contractValidDateUnixTime = date.getTime();

        return (int)(contractValidDateUnixTime/1000);
    }

    /**
     * 对日期进行天数加减
     * @param dateStr yyyy-MM-dd
     * @param days
     * @return yyyy-MM-dd
     */
    public static String addDaysForDate(String dateStr, int days) {
        Date date = DateUtil.string2DateDay(dateStr);
        DateTime plusDays = new DateTime(date).plusDays(days);
        return DateUtil.Date2String(plusDays.toDate());
    }


    public static boolean checkDateFormat(String day) {
        if (StringUtils.isEmpty(day)) {
            return false;
        }
        Pattern pattern = Pattern.compile("[0-9]{4}-[0-9]{2}-[0-9]{2}");
        return pattern.matcher(day).matches();
    }
}

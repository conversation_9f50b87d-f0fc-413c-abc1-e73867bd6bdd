package com.sankuai.meituan.waimai.util.base;

import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;

import org.apache.commons.codec.digest.DigestUtils;

import java.lang.reflect.Method;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 17/6/5.
 */
public class RepeatSubmissionTokenUtil {

    public static String generateToken(Method method, String seed) throws WmServerException {
        if (method == null) {
            throw new WmServerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "method不允许为空");
        }

        String className = method.getDeclaringClass().getSimpleName();
        String methodName = method.getName();
        String uniqueMarker = DigestUtils.md5Hex(seed);

        String token = className + methodName + uniqueMarker;
        return token.toUpperCase();
    }

}

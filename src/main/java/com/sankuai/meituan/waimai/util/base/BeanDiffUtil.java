package com.sankuai.meituan.waimai.util.base;

import com.google.common.base.Function;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sankuai.meituan.waimai.vo.base.WmFieldAuditStatusVo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;

import java.beans.PropertyDescriptor;
import java.util.*;

public class BeanDiffUtil {

    private static final Logger log = LoggerFactory.getLogger(BeanDiffUtil.class);

    private static String convertField(String fieldName, Map<String, PropertyDesc> propertyDescMap) {
        PropertyDesc propertyDesc = propertyDescMap.get(fieldName);
        if (propertyDesc != null) {
            return propertyDesc.getDesc();
        }
        return fieldName;

    }

    private static String convertValue(Object value, String fieldName, Map<String, PropertyDesc> propertyDescMap) {
        String valueDesc = null;
        PropertyDesc propertyDesc = propertyDescMap.get(fieldName);
        if (propertyDesc != null) {
            Map<Object, String> valueDescMap = propertyDesc.getValueDescMap();
            if (valueDescMap == null) {
                if (propertyDesc.getValueFmt() != null) {
                    return propertyDesc.getValueFmt().format(value);
                }
                return String.valueOf(value);
            }
            if (value instanceof Collection) {
                valueDesc = convertList(value, valueDescMap);
            } else {
                valueDesc = valueDescMap.get(value);
            }
        }
        return valueDesc == null ? "无" : valueDesc;
    }

    private static String convertList(Object value, Map<Object, String> valueDescMap) {
        List<String> descList = null;
        if (value instanceof Collection) {
            descList = Lists.newArrayList();
            List<Object> objectList = (ArrayList<Object>) value;
            for (Object object : objectList) {
                descList.add(valueDescMap.get(object));
            }
        }
        return StringUtils.join(descList, ",");
    }

    public static <T> Map<String, WmFieldAuditStatusVo> diffContentByMap(T before, T after, String ctime, Map<String, PropertyDesc> propertyDescMap) {
        Map<String, WmFieldAuditStatusVo> statusVoMap = Maps.newHashMap();
        if (before == null && after == null) {
            return statusVoMap;
        }
        BeanWrapper beforeWrapper = null;
        BeanWrapper afterWrapper = null;
        PropertyDescriptor[] propertyDescriptors = null;
        int diffType = 1;
        if (before != null) {
            beforeWrapper = new BeanWrapperImpl(before);
            propertyDescriptors = beforeWrapper.getPropertyDescriptors();
            diffType = 2;
        }
        if (after != null) {
            afterWrapper = new BeanWrapperImpl(after);
            propertyDescriptors = afterWrapper.getPropertyDescriptors();
        }

        for (PropertyDescriptor one : propertyDescriptors) {
            if (!propertyDescMap.containsKey(one.getDisplayName())) {
                continue;
            }
            if (one.getReadMethod() == null || one.getWriteMethod() == null) {
                continue;
            }
            Object beforeValue = beforeWrapper != null ? beforeWrapper.getPropertyValue(one.getName()) : null;
            Object afterValue = afterWrapper != null ? afterWrapper.getPropertyValue(one.getName()) : null;
            if (Objects.equals(beforeValue, afterValue)) {
                continue;
            }
//            String propNameDesc = convertField(one.getName(), propertyDescMap);
            String beforeValueDesc = convertValue(beforeValue, one.getName(), propertyDescMap);
            String afterValueDesc = convertValue(afterValue, one.getName(), propertyDescMap);
            if (Objects.equals(beforeValueDesc, afterValueDesc)) {
                continue;
            }
            WmFieldAuditStatusVo auditStatusVo = new WmFieldAuditStatusVo(diffType, ctime);
            auditStatusVo.setModifiedValue(Lists.newArrayList(afterValueDesc));
            statusVoMap.put(one.getName(), auditStatusVo);
        }
        return statusVoMap;
    }

    public interface PropertyValueFormatter {
        String format(Object origin);
    }

    public static class PropertyDesc {
        // global chain
        private Class<?> clazz;
        private List<PropertyDesc> chain;
        // this prop
        private String name;
        private String desc;
        private String defaultValue;
        private Map<Object, String> valueDescMap;
        /**
         * 值描述格式，比如时间由数字转为文本格式<br>
         * time = 时间
         */
        private PropertyValueFormatter valueFmt;

        private PropertyDesc(Class<?> clazz) {
            this.clazz = clazz;
            this.chain = new ArrayList<>();
        }

        private PropertyDesc(Class<?> clazz, List<PropertyDesc> chain, String name) {
            this.clazz = clazz;
            this.chain = chain;
            this.chain.add(this);
            this.name = name;
        }

        public static PropertyDesc clazz(Class<?> clazz) {
            return new PropertyDesc(clazz);
        }

        public PropertyDesc name(String name) {
            return new PropertyDesc(this.clazz, this.chain, name);
        }

        public PropertyDesc desc(String desc) {
            this.desc = desc;
            return this;
        }

        public PropertyDesc defaultValue(String defaultValue) {
            this.defaultValue = defaultValue;
            return this;
        }

        public PropertyDesc valueDescMap(Map<Object, String> valueDescMap) {
            this.valueDescMap = valueDescMap;
            return this;
        }

        public PropertyDesc valueDesc(Object value, String desc) {
            if (valueDescMap == null) {
                valueDescMap = new HashMap<Object, String>();
            }
            valueDescMap.put(value, desc);
            return this;
        }

        public PropertyDesc valueFmt(PropertyValueFormatter valueFmt) {
            this.valueFmt = valueFmt;
            return this;
        }

        public Class<?> getClazz() {
            return clazz;
        }

        public List<PropertyDesc> getChain() {
            return chain;
        }

        public String getName() {
            return name;
        }

        public String getDesc() {
            return desc;
        }

        public String getDefaltValue() {
            return defaultValue;
        }

        public Map<Object, String> getValueDescMap() {
            return valueDescMap;
        }

        public PropertyValueFormatter getValueFmt() {
            return valueFmt;
        }

        public Map<String, PropertyDesc> getChainMap() {
            if (chain == null) {
                return Maps.newHashMap();
            }
            return Maps.uniqueIndex(chain, new Function<PropertyDesc, String>() {
                @Override
                public String apply(PropertyDesc input) {
                    return input.getName();
                }
            });
        }
    }

    /**
     * insertList减storeList
     *
     * @param insertList
     * @param storeList
     * @return
     */
    public static List<Integer> genAddList(List<Integer> insertList, List<Integer> storeList) {
        List<Integer> addList = Lists.newArrayList(insertList);
        addList.removeAll(storeList);
        return addList;
    }

    public static List<Integer> genCommonList(List<Integer> insertList, List<Integer> storeList) {
        List<Integer> commonList = Lists.newArrayList(insertList);
        commonList.retainAll(storeList);
        return commonList;
    }

    /**
     * 求storeList减去insert的差集
     *
     * @param insertList
     * @param storeList
     * @return
     */
    public static List<Integer> genDeleteList(List<Integer> insertList, List<Integer> storeList) {
        List<Integer> deleteList = Lists.newArrayList(storeList);
        deleteList.removeAll(insertList);
        return deleteList;
    }

    public static <T> Map<String, String> diffContentForMap(T before, T after, Map<String,PropertyDesc> propertyDescMap) {
        if (before == null && after == null) {
            return new HashMap<>();
        }
        Map<String, String> map = new HashMap<>();
        BeanWrapper beforeWrapper = null;
        BeanWrapper afterWrapper = null;
        PropertyDescriptor[] propertyDescriptors = null;
        if (before != null) {
            beforeWrapper = new BeanWrapperImpl(before);
            propertyDescriptors = beforeWrapper.getPropertyDescriptors();
        }
        if (after != null) {
            afterWrapper = new BeanWrapperImpl(after);
            propertyDescriptors = afterWrapper.getPropertyDescriptors();
        }

        for (PropertyDescriptor one : propertyDescriptors) {
            if(!propertyDescMap.containsKey(one.getDisplayName())) {
                continue;
            }
            if (checkExclude(one)) {
                continue;
            }
            if (one.getReadMethod() == null || one.getWriteMethod() == null) {
                continue;
            }
            Object beforeValue = beforeWrapper != null ? beforeWrapper.getPropertyValue(one.getName()) : null;
            Object afterValue = afterWrapper != null ? afterWrapper.getPropertyValue(one.getName()) : null;
            if (ObjectUtil.equals2Obj(beforeValue, afterValue)) {
                continue;
            } else {
                String beforeValueDesc = null;
                PropertyDesc propertyDesc = propertyDescMap.get(one.getName());
                if (propertyDesc != null) {
                    Map<Object, String> valueDescMap = propertyDesc.getValueDescMap();
                    if (valueDescMap != null) {
                        beforeValueDesc = valueDescMap.get(beforeValue);
                    }
                }
                if (beforeValueDesc == null) {
                    beforeValueDesc = beforeValue.toString();
                }

                if(beforeValueDesc.equals(propertyDesc.getDefaltValue())){
                    beforeValueDesc = "无";
                }

                map.put(one.getName(), beforeValueDesc);
            }
        }
        return map;
    }

    private static boolean checkExclude(PropertyDescriptor propertyDescriptor) {
        Set<String> defaultExcludeSet = Sets.newHashSet("utime", "muid");
        return defaultExcludeSet.contains(propertyDescriptor.getName());
    }


}

package com.sankuai.meituan.waimai.util.transform.base;

import org.apache.commons.lang.StringUtils;

public class StringCompareUtil {
    public static boolean isEqual(String a, String b) {
        if (StringUtils.isEmpty(a) && StringUtils.isEmpty(b)) {
            return true;
        }
        if (StringUtils.isEmpty(a) || StringUtils.isEmpty(b)) {
            return false;
        }
        return a.equals(b);
    }
}

package com.sankuai.meituan.waimai.util.base;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 15/8/11.
 */
public class DateCompareUtil {

    private static Logger LOG = LoggerFactory.getLogger(DateCompareUtil.class);
    /**
     * 获取两个两时间之间的时间差
     * @param start
     *        开始时：比如9:30
     * @param end
     *        结束时间:比如:18:30
     * @return
     */
    public static Long getTimeInterval(String start,String end){
        Long timeInterval =  0L ;
        SimpleDateFormat sdf = new SimpleDateFormat( "HH:mm" );

        try {
            Date startDate = sdf.parse(start);
            Date endDate = sdf.parse(end);
            timeInterval = endDate.getTime()  - startDate.getTime();
            LOG.debug("timeInterval="+timeInterval);
        } catch (ParseException e) {
            LOG.error("解析时间发生异常!",e);

        }

        return timeInterval;
    }

    /**
     * 获取两时间之间的时间差是否大于（或者小于或者等于指定的时长）
     * @param start
     *        开始时间
     * @param end
     *        结束时间
     * @param timeInterval
     *        要比较的时长
     * @param compare
     *        要进行的比较：大于(compare==1)or等于(compare==0)or小于(compare=-1)
     *        大于等于（compare=10） or小于等于（compare=-10）
     * @return
     */
    public static boolean checkTimeInteral(String start,String end,Long timeInterval,int compare) {
        boolean compareResult = false;//默认为false　

        /**
         * compare==1表示要进行的比较操作是：两个时间时间的差值是否大于指定的时长；
         * compare ==0表示要进行的比较操作是：两个时间时间的差值是否等于指定的时长；
         * compare==-1表示要进行的比较操作是：两个时间时间的差值是否小于指定的时长；
         * compare==10表示要进行的比较是：两个时间时间的差值是否大于等于指定的时长；
         * compare=-10表示要进行的比较是：两个时间时间的差值是否小于等于指定的时长；
         */
        //获取两时间之间的时间差
        Long timeIntervalResult = getTimeInterval(start, end);
        if (compare == 1 && timeIntervalResult > timeInterval)
            compareResult =  true;
        else if (compare == 0 && timeIntervalResult ==timeInterval)
            compareResult  = true;
        else if (compare ==-1 && timeIntervalResult < timeInterval)
            compareResult =  true;
        else if (compare == 10 && timeIntervalResult >= timeInterval)
            compareResult = true;
        else if (compare == -10 && timeIntervalResult <= timeInterval)
            compareResult = true;

        return  compareResult;
    }




}

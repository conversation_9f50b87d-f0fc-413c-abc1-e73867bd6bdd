package com.sankuai.meituan.waimai.util;

import com.amazonaws.*;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3KmsClient;
import com.amazonaws.services.s3.S3ClientOptions;
import com.amazonaws.services.s3.exceptions.GetS3CredentialFailedAfterRetryException;
import com.amazonaws.services.s3.model.GeneratePresignedUrlRequest;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayInputStream;
import java.net.URL;
import java.util.Calendar;
import java.util.Date;


/**
 * <AUTHOR>
 * @date 20231017
 * @desc S3工具类型-上传视频使用
 */
@Slf4j
public class MSSFileUploadUtil {
    public static final String APP_KEY = "com.sankuai.waimai.e.customerweb";

    private static volatile AmazonS3 s3Client = null;
    private static final Object lockObject = new Object();

    private static final String HTML_CONTENT_TYPE = "text/html";

    /**
     * 初始化S3client
     */
    private static void initS3Client() {
        if (s3Client != null) {
            return;
        }
        synchronized (lockObject) {
            if (s3Client != null) {
                return;
            }
            s3Client = CreateAmazonS3Conn(APP_KEY, getHostname());
            //配置云存储服务地址
            s3Client.setEndpoint(getHostname());
            //设置客户端生成的http请求hos格式，目前只支持path type的格式，不支持bucket域名的格式
            S3ClientOptions s3ClientOptions = new S3ClientOptions();
            s3ClientOptions.setPathStyleAccess(true);
            s3Client.setS3ClientOptions(s3ClientOptions);
        }
    }

    /**
     * 创建S3链接
     *
     * @param appKey
     * @param endpoint
     * @return
     */
    private static AmazonS3 CreateAmazonS3Conn(String appKey, String endpoint) {
        ClientConfiguration configuration = new ClientConfiguration();
        // 默认协议为HTTPS。将这个值设置为Protocol.HTTP，则使用的是HTTP协议
        configuration.setProtocol(Protocol.HTTPS);

        //设置客户端生成的http请求hos格式，目前只支持path type的格式，不支持bucket域名的格式
        S3ClientOptions s3ClientOptions = new S3ClientOptions();
        // 目前s3只支持path style,下面选项需要设置为true
        s3ClientOptions.setPathStyleAccess(true);

        //生成云存储api client
        try {
            return new AmazonS3KmsClient(endpoint, appKey, configuration, s3ClientOptions);
        } catch (GetS3CredentialFailedAfterRetryException e) {
            throw new RuntimeException(e);
        }
    }


    /**
     * @param bucketName 要上传到bucket名称
     * @param objectName 要上传的对象名称
     * @param content    要上传的数据字节数组
     * @return 上传得到的url
     */
    public static String uploadFileFromBytes(String bucketName, String objectName, byte[] content) {
        try {
            initS3Client();
        } catch (Exception e) {
            log.error("initS3Client exception objectName={}", objectName, e);
            s3Client = null;
        }
        try {
            ObjectMetadata objectMetadata = new ObjectMetadata();
            objectMetadata.setContentType("video/mp4");
            s3Client.putObject(bucketName, objectName, new ByteArrayInputStream(content), objectMetadata);
            return getRedirectUrl(objectName, bucketName);
        } catch (AmazonServiceException ase) {
            //存储服务端处理异常
            log.error("uploadFileFromBytes AmazonServiceException message={},statusCode={},errorCode={},errorType={}," +
                    "requestId={},bucketName={},objectName={}", ase.getMessage(), ase.getStatusCode(), ase.getErrorCode(), ase.getErrorType(), ase.getRequestId(), bucketName, objectName, ase);
        } catch (AmazonClientException ace) {
            log.error("createBucketIfNotExistExample AmazonServiceException message={},bucketName={},objectName={}", ace.getMessage(), bucketName, objectName, ace);
        }
        return "";
    }

    private static String getHostname() {
        return ConfigUtilAdapter.getString("mss_hostname_key", "s3plus.sankuai.com");
    }

    /**
     * 根据文件名称和bucket获取可访问地址
     *
     * @param fileName
     * @param bucket
     * @return
     */
    public static String getRedirectUrl(String fileName, String bucket) {
        GeneratePresignedUrlRequest generatePresignedUrlRequest = new GeneratePresignedUrlRequest(bucket, fileName);
        generatePresignedUrlRequest.setMethod(HttpMethod.GET);

        //过期时间
        Date currentDate = new Date();
        Calendar expireTime = Calendar.getInstance();
        expireTime.setTime(currentDate);
        expireTime.add(Calendar.HOUR, 1);
        generatePresignedUrlRequest.setExpiration(expireTime.getTime());
        try {
            initS3Client();
        } catch (Exception e) {
            log.error("initS3Client exception objectName={}", bucket, e);
            s3Client = null;
        }
        URL url = s3Client.generatePresignedUrl(generatePresignedUrlRequest);
        return url.toString();
    }

    /**
     * 上传Html文件
     * @param bucketName
     * @param objectName
     * @param content
     */
    public static String uploadHtmlFile(String bucketName, String objectName, String content){
        try{
            initS3Client();
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentType(HTML_CONTENT_TYPE);
            metadata.setContentLength(content.length());
            metadata.setHeader("Content-Disposition", "inline");
            s3Client.putObject(bucketName,objectName, new ByteArrayInputStream(content.getBytes()),metadata);
            return getRedirectUrl(objectName, bucketName);
        }catch (AmazonServiceException ase) {
            log.error("uploadHtmlFile AmazonServiceException message={},statusCode={},errorCode={},errorType={}," +
                    "requestId={},bucketName={},objectName={}", ase.getMessage(), ase.getStatusCode(), ase.getErrorCode(), ase.getErrorType(), ase.getRequestId(), bucketName, objectName, ase);
        }catch (AmazonClientException ace) {
            //客户端处理异常
            log.error("uploadHtmlFile AmazonServiceException message={},bucketName={},objectName={}", ace.getMessage(), bucketName, objectName, ace);
        }

        return "";
    }
}

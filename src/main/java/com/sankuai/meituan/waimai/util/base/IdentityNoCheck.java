package com.sankuai.meituan.waimai.util.base;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.regex.Pattern;

/**
 * @title IdentityNoCheck
 * @description  身份证号校验工具类
 */
public class IdentityNoCheck {

    /** 身份证号码的正则表达式 */
    private static final String ID_NO_REGEXP = "^((\\d{17}|\\d{14})(\\d|x|X))$";

    /** 身份证前2位。 */
    private static final String[] IDNO_CITY = new String[] {"11", "12", "13", "14", "15", "21",
            "22", "23", "31", "32", "33", "34", "35", "36", "37", "41", "42", "43", "44", "45",
            "46", "50", "51", "52", "53", "54", "61", "62", "63", "64", "65", "71", "81", "82",
            "91"};

    /** 18位身份证号的最后一位 */
    private static final int[] IDNO_IWEIGHT = new int[] {7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10,
            5, 8, 4, 2, 1};

    private static final String[] IDNO_CCHECK = new String[] {"1", "0", "X", "9", "8", "7", "6",
            "5", "4", "3", "2"};

    /**
     * 校验身份证号码。
     * 
     * @param identityNo 待校验的身份证号码。
     * @return true:是身份证号。 false:不是身份证号。
     */
    public static boolean isIdentityNo(String identityNo) {

        if (!match(identityNo, ID_NO_REGEXP)) {
            return false;
        }

        int length = identityNo.length();
        if (length != 15 && length != 18) {
            return false;
        }

        // 校验身份证号码的前2位。
        String city = identityNo.substring(0, 2);
        if (!Arrays.asList(IDNO_CITY).contains(city)) {
            return false;
        }
        if (length == 18) {
            // 校验身份证号码的生日部分。
            String birth = identityNo.substring(6, 14);
            Date date = formatToDate(birth, "yyMMdd");
            if (date == null)
                return false;
            int total = 0;
            for (int i = 0; i < 17; i++) {
                total += Integer.valueOf(identityNo.substring(i, i + 1)) * IDNO_IWEIGHT[i];
            }
            int mo = total % 11;
            String lastOne = IDNO_CCHECK[mo];
            return identityNo.substring(17).equalsIgnoreCase(lastOne);
        } else {
            // 校验身份证号码的生日部分。
            String birth = identityNo.substring(6, 12);
            Date date = formatToDate(birth, "yyMMdd");
            if (date != null)
                return true;
            else
                return false;
        }

    }

    private static boolean match(String str, String regexp) {
        if (isEmpty(str)) {
            return false;
        }
        return Pattern.matches(regexp, str);
    }

    private static boolean isEmpty(String str) {
        return str == null || str.trim().isEmpty();
    }

    private static Date formatToDate(String date, String format) {
        try {
            if (null == date || "".equalsIgnoreCase(date)) {
                return null;
            }

            SimpleDateFormat sorceFmt = new SimpleDateFormat(format);
            return new Date(sorceFmt.parse(date).getTime());
        } catch (ParseException e) {
            return null;
        }
    }
}

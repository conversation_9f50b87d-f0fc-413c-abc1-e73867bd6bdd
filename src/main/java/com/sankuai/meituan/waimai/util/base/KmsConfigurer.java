package com.sankuai.meituan.waimai.util.base;

import org.springframework.beans.factory.config.PropertyPlaceholderConfigurer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.io.IOException;
import java.util.Properties;

/**
 * Created by ji<PERSON><PERSON> on 15/8/12.
 */

public class KmsConfigurer extends PropertyPlaceholderConfigurer {
    private final  static Logger log = LoggerFactory.getLogger(KmsConfigurer.class);
    @Override
    protected void loadProperties(Properties props) throws IOException {
        super.loadProperties(props);
        //database password from kms
        props.put("database.dbOrderWrite.password",KmsUtil.getString("database.dbOrderWrite.password"));
        props.put("database.dbOrderRead.password",KmsUtil.getString("database.dbOrderRead.password"));
        props.put("database.dbOrderReadSlow.password",KmsUtil.getString("database.dbOrderReadSlow.password"));
        props.put("database.dbPoiWrite.password",KmsUtil.getString("database.dbPoiWrite.password"));
        props.put("database.dbPoiRead.password",KmsUtil.getString("database.dbPoiRead.password"));
        props.put("database.dbPoiReadSlow.password",KmsUtil.getString("database.dbPoiReadSlow.password"));
        props.put("database.dbManageWrite.password",KmsUtil.getString("database.dbManageWrite.password"));
        props.put("database.dbManageRead.password",KmsUtil.getString("database.dbManageRead.password"));
        props.put("database.dbBusinessRead.password",KmsUtil.getString("database.dbBusinessRead.password"));
        props.put("database.dbPromotion.password",KmsUtil.getString("database.dbPromotion.password"));
        props.put("database.dbApi.password",KmsUtil.getString("database.dbApi.password"));
        props.put("database.dbMpublic.password",KmsUtil.getString("database.dbMpublic.password"));
        //mail password from kms
        props.put("mail.password",KmsUtil.getString("mail.password"));
        //upmAuthService.secret from kms
        props.put("upmAuthService.secret", KmsUtil.getString("upmAuthService.secret"));
        
        props.put("remotePoiService.secret", KmsUtil.getString("remotePoiService.secret"));

        //for test:
//        log.info(String.format("upmAuthService.secret-%s",props.get("upmAuthService.secret")));
    }
}
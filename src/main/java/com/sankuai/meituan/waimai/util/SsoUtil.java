package com.sankuai.meituan.waimai.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.meituan.mtrace.Tracer;
import com.sankuai.meituan.auth.vo.User;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * Author: 窦旭蒙
 * MIS: douxumeng
 * Date: 2021-12-16 12:56
 * Email: <EMAIL>
 * Desc:
 */
@Slf4j
public class SsoUtil {

    public static SsoUser getUser() {
        String ssoJson = Tracer.getContext("sso.user");
        if (StringUtils.isBlank(ssoJson)) {
            return null;
        }
        log.debug("SsoUtil#getUser,ssoJson:{}", ssoJson);
        return JSONObject.parseObject(sso<PERSON>son, SsoUser.class);
    }

    public static User transSsoUser2User(SsoUser ssoUser) {
        log.debug("SsoUtil#transSsoUser2User,ssoUser:{}", JSON.toJSONString(ssoUser));
        User user = new User();
        user.setId(new Long(ssoUser.getId()).intValue());
        user.setLogin(ssoUser.getLogin());
        user.setName(ssoUser.getName());
        log.debug("SsoUtil#transSsoUser2User,result:{}", JSON.toJSONString(user));
        return user;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SsoUser {
        private long id;
        private String login;
        private String name;
    }
}

package com.sankuai.meituan.waimai.util.base;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StopWatch;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;


public class ImageCompress {
    private static Logger LOG = LoggerFactory.getLogger(ImageCompress.class);

    public static final int WIDTH = 1200;
    public static final int HEIGHT = 1600;
    public static final String TMP_FILE_PREFIX = "/tmp/";
    private String targetFilePath;
    private String sourceFileName;

    public ImageCompress(String uniqueStr) {
        this.targetFilePath = TMP_FILE_PREFIX + uniqueStr + System.currentTimeMillis() + "_target.jpg";
        this.sourceFileName = TMP_FILE_PREFIX + uniqueStr + System.currentTimeMillis() + "_temp.jpg";
    }

    public byte[] compressImage(byte[] fileData) throws Exception {
        StopWatch sw = new StopWatch("压缩过程");
        sw.start("byte to File写磁盘");
        File sourceFile = this.getFileFromBytes(fileData, sourceFileName);
        File targetFile = new File(targetFilePath);
        FileOutputStream fileOutputStream = null;
        sw.stop();
        try {
            sw.start("File to Image");
            if (sourceFile == null || targetFilePath == null) {
                throw new Exception("file is null");
            }

            fileOutputStream = new FileOutputStream(targetFile);
            Image image = ImageIO.read(sourceFile);
            if (image == null) {
                throw new Exception("文件为空");
            }
            if (image.getWidth(null) == -1) {
                throw new Exception("file width is empty");
            }
            sw.stop();

            sw.start("bufferedImage 图片压缩");
            int newWidth = 0;
            int newHeight = 0;
            if (image.getWidth(null) > WIDTH || image.getHeight(null) > HEIGHT) {
                int rate1 = image.getWidth(null) / WIDTH;
                int rate2 = image.getHeight(null) / HEIGHT;
                int rate = rate1 > rate2 ? rate1 : rate2;
                newWidth = image.getWidth(null) / rate;
                newHeight = image.getHeight(null) / rate;
            } else {
                newWidth = image.getWidth(null);
                newHeight = image.getHeight(null);
            }
            LOG.info(" compress pic res,with={},height={},newWidth={},newHeight={}", image.getWidth(null), image.getHeight(null), newWidth, newHeight);
            BufferedImage bufferedImage = new BufferedImage(newWidth,
                    newHeight, BufferedImage.TYPE_INT_RGB);
            bufferedImage.getGraphics().drawImage(
                    image.getScaledInstance(newWidth, newHeight,
                            image.SCALE_SMOOTH), 0, 0, null);
            sw.stop();
            sw.start("Image to File");
            ImageIO.write(bufferedImage, "jpg", fileOutputStream);
//            fileOutputStream.close();
            sw.stop();

        } finally {
            if (fileOutputStream != null) {
                try {
                    fileOutputStream.close();
                } catch (Exception e) {
                    LOG.error("fileOutputStream close error", e);
                }
            }
        }
        sw.start("File to byte");
        byte[] res = this.getBytesFromFile(targetFile);
        sw.stop();
        LOG.info("图片压缩时间:");
        LOG.info(sw.prettyPrint());
        return res;
    }

    /**
     * 把字节数组保存为一个文件
     *
     * @param b
     * @param outputFile
     * @return
     */
    public File getFileFromBytes(byte[] b, String outputFile) throws IOException {
        File ret = null;
        BufferedOutputStream stream = null;
        FileOutputStream fstream = null;
        try {
            ret = new File(outputFile);
            fstream = new FileOutputStream(ret);
            stream = new BufferedOutputStream(fstream);
            stream.write(b);
        } finally {
            if (stream != null) {
                stream.close();

            }
            if(fstream!=null){
                fstream.close();
            }
        }
        return ret;
    }

    /**
     * 文件转化为字节数组
     *
     * @param file
     * @return
     */
    public byte[] getBytesFromFile(File file) throws IOException {
        byte[] ret = null;
        if (file == null) {
            // log.error("helper:the file is null!");
            return null;
        }
        FileInputStream in = new FileInputStream(file);
        ByteArrayOutputStream out = new ByteArrayOutputStream(4096);
        try {
            byte[] b = new byte[4096];
            int n;
            while ((n = in.read(b)) != -1) {
                out.write(b, 0, n);
            }
            ret = out.toByteArray();
        } finally {
            in.close();
            out.close();
        }
        return ret;
    }

    public void clear() {
        try {
            File target = new File(targetFilePath);
            if (target.exists()) {
                target.delete();
            }

            File source = new File(sourceFileName);
            if (source.exists()) {
                source.delete();
            }
        } catch (Exception e) {
            LOG.error("clear error", e);
        }
    }
}

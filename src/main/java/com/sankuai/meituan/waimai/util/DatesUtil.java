package com.sankuai.meituan.waimai.util;

import com.sankuai.meituan.waimai.thrift.util.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;
import java.util.TimeZone;
import java.util.regex.Pattern;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 15-8-20.
 */
public class DatesUtil {
    private static Logger logger = LoggerFactory.getLogger(DatesUtil.class);
    public final static String DefaultShortFormat = "yyyy-MM-dd";
    public final static String DefaultLongFormat = "yyyy-MM-dd HH:mm:ss";

    /**
     * @return
     */
    public static Date today() {
        return toDay(new Date());
    }

    public static Date toDay(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

    /**
     * 下周
     * @param date
     * @return
     */
    public static Date getNextWeek(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.WEEK_OF_MONTH, 1);
        return cal.getTime();
    }

    /**
     * 上周
     * @param date
     * @return
     */
    public static Date getPrevWeek(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.WEEK_OF_MONTH, -1);
        return cal.getTime();
    }

    public static Date getPrevMonday(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(getPrevWeek(date));
        cal.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

    /**
     * 上一个周日（周一为一周第一天,周日为一周最后一天）
     * @param date
     * @return
     */
    public static Date getPrevSunday(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(Calendar.DAY_OF_WEEK, Calendar.SUNDAY);
        cal.set(Calendar.HOUR_OF_DAY, 23);
        cal.set(Calendar.MINUTE, 59);
        cal.set(Calendar.SECOND, 59);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

    public static Date getThisMonday(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(Calendar.DAY_OF_WEEK,Calendar.MONDAY);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

    public static Date getThisSunday(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(getNextWeek(date));
        cal.set(Calendar.DAY_OF_WEEK, Calendar.SUNDAY);
        cal.set(Calendar.HOUR_OF_DAY, 23);
        cal.set(Calendar.MINUTE, 59);
        cal.set(Calendar.SECOND, 59);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

    /**
     * the string format must yyyy-MM-dd
     *
     * @param str
     * @return
     * <AUTHOR>
     * <p/>
     * 2011-4-21
     */
    public static Date string2DateDay(String str) {
        SimpleDateFormat formatter = new SimpleDateFormat(DefaultShortFormat);
        String day = str==null?"":str.trim();
        try {
            return formatter.parse(day);
        } catch (ParseException e) {
            Calendar cal = Calendar.getInstance();
            cal.set(Calendar.HOUR_OF_DAY, 0);
            cal.set(Calendar.MINUTE, 0);
            cal.set(Calendar.SECOND, 0);
            cal.set(Calendar.MILLISECOND, 0);
            return cal.getTime();
        }
    }

    /**
     * yyyy-MM-dd
     *
     * @param date
     * @return
     * <AUTHOR>
     * <p/>
     * 2011-4-21
     */
    public static String Date2String(Date date) {
        SimpleDateFormat formatter = new SimpleDateFormat(DefaultShortFormat);
        return formatter.format(date);
    }
    public static String date2yyyymmddhhmmssStr(Date date) {
        SimpleDateFormat formatter = new SimpleDateFormat(DefaultLongFormat);
        return formatter.format(date);
    }

    public static String Date2String(Date date,String str) {
        SimpleDateFormat formatter = new SimpleDateFormat(str);
        return formatter.format(date);
    }

    /**
     * 日期（天）转unixtime
     *
     * @param day
     * @return
     * <AUTHOR>
     * @created 2012-03-15
     */
    public static int day2Unixtime(String day) {
        return (int) (DateUtil.string2DateDay(day).getTime() / 1000L);
    }

    /**
     * date转unixtime
     *
     * @param date
     * @return
     * <AUTHOR>
     * @created 2012-06-15
     */
    public static int date2Unixtime(Date date) {
        return (int) (date.getTime() / 1000L);
    }

    public static boolean dateCheck(String str) {
        Pattern pattern = Pattern.compile("[0-9]{4}-[0-9]{2}-[0-9]{2}");
        return pattern.matcher(str).matches();
    }

    /**
     * 秒转日期
     *
     * @param seconds
     * @return
     */
    public static String secondsToString(Integer seconds) {
        return Date2String(fromUnixTime(seconds));
    }

    /**
     * 秒转完整日期
     *
     * @param seconds
     * @return
     */
    public static String secondsToLongString(Integer seconds) {
        return Date2String(fromUnixTime(seconds), DefaultLongFormat);
    }

    /**
     * 把表转换为Date
     *
     * @param seconds
     * @return
     */
    public static Date fromUnixTime(Integer seconds) {
        return new Date(seconds * 1000L);
    }


    public static int AddMonth(int IssueDateUnixTime,int addMonth) {
        //unixTime to date
        Date date = new Date(IssueDateUnixTime*1000L);
        //date to calendar
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MONTH,addMonth);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 0);
        date = calendar.getTime();
        Long contractValidDateUnixTime = date.getTime();

        return (int)(contractValidDateUnixTime/1000);
    }

    public static boolean checkDateFormat(String day) {
        Pattern pattern = Pattern.compile("[0-9]{4}-[0-9]{2}-[0-9]{2}");
        return pattern.matcher(day).matches();
    }

    public static Date toDate(String date) throws ParseException {
        DateFormat df = new SimpleDateFormat("EEE, d MMM yyyy HH:mm:ss z",
                Locale.ENGLISH);
        df.setTimeZone(TimeZone.getTimeZone("GMT"));
        return df.parse(date);
    }

    /**
     * 最强 yyyy-MM-dd格式校验,包括平年和闰年
     * @param str 日期字符串
     * @return 是否符合要求
     */
    public static boolean checkDateFull(String str){
        Pattern pattern = Pattern.compile("(([0-9]{3}[1-9]|[0-9]{2}[1-9][0-9]{1}|[0-9]{1}[1-9][0-9]{2}|[1-9][0-9]{3})-(((0[13578]|1[02])-(0[1-9]|[12][0-9]|3[01]))|((0[469]|11)-(0[1-9]|[12][0-9]|30))|(02-(0[1-9]|[1][0-9]|2[0-8]))))|((([0-9]{2})(0[48]|[2468][048]|[13579][26])|((0[48]|[2468][048]|[3579][26])00))-02-29)");
        return pattern.matcher(str).matches();
    }

    /**
     * 日期加减
     * @param _option
     * @param _date
     * @return
     */
    public static String addDate(int _option, String _date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar cl = Calendar.getInstance();
        Date date = null;

        try {
            date = (Date) sdf.parse(_date);
        } catch (ParseException e) {
            logger.error("addDate error:{}", e);
        }
        cl.setTime(date);
        cl.add(Calendar.DAY_OF_MONTH,_option);
        date = cl.getTime();
        return sdf.format(date);
    }

    /**
     * 带时分秒的日期转unixtime
     *
     * @param day
     * @return
     * <AUTHOR>
     * @created 2012-03-15
     */
    public static int day2AllUnixtime(String day) {
        SimpleDateFormat formatter = new SimpleDateFormat(DefaultLongFormat);

        String newDay = StringUtil.null2Trim(day);
        try {
            return (int)(formatter.parse(newDay).getTime() / 1000L);
        } catch (ParseException var4) {
            Calendar cal = Calendar.getInstance();
            cal.set(11, 0);
            cal.set(12, 0);
            cal.set(13, 0);
            cal.set(14, 0);
            return (int)(cal.getTime().getTime() / 1000L);
        }
    }

}

package com.sankuai.meituan.waimai.util.transform.signtemplet;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.PdfTemplet;
import java.util.List;

import com.sankuai.meituan.waimai.thrift.customer.domain.sign.SignVersionDetailBo;
import com.sankuai.meituan.waimai.vo.sign.SignVersionDetail;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

public class SignTempletTransUtils {

    public static List<SignVersionDetail> transDetailBoListToDetailList(List<SignVersionDetailBo> signVersionTemplet) {
        List<SignVersionDetail> result = Lists.newArrayList();
        if(CollectionUtils.isEmpty(signVersionTemplet)){
            return result;
        }
        for(SignVersionDetailBo temp : signVersionTemplet){
            result.add(transDetailBoToDetail(temp));
        }
        return result;
    }

    public static SignVersionDetail transDetailBoToDetail(SignVersionDetailBo temp) {
        if(temp == null){
            return null;
        }
        SignVersionDetail result = new SignVersionDetail();
        result.setBizType(temp.getBizType());
        String signVersionDetailBoVersionNum = temp.getVersionNum();
        //e.g 20191118A
        String versionNum = signVersionDetailBoVersionNum.substring(0, 8);
        String versionType = signVersionDetailBoVersionNum.substring(8);
        result.setVersionNum(versionNum);
        result.setVersionType(versionType);
        result.setDocument(temp.getDocument());
        if(!StringUtils.isEmpty(temp.getPdfTemplet())){
            result.setPdfTemplet(JSONObject.parseObject(temp.getPdfTemplet(),PdfTemplet.class));
        }
        result.setStatus(temp.getStatus());
        result.setCtime(temp.getCtime());
        result.setUtime(temp.getUtime());
        return result;
    }
}

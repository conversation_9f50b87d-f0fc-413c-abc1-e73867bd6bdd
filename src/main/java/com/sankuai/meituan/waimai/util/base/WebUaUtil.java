package com.sankuai.meituan.waimai.util.base;

import com.sankuai.meituan.waimai.poitrace.domain.DeviceType;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public final class WebUaUtil {

    private static Logger logger = LoggerFactory.getLogger(WebUaUtil.class);

    private static final ThreadLocal<String> USER_AGENT = new ThreadLocal<>();

    public static DeviceType getDeviceType() {
        String userAgent = USER_AGENT.get();
        if (StringUtils.isNotBlank(userAgent) && userAgent.contains("fromBee")) {
            return DeviceType.APP;
        }
        return DeviceType.PC;
    }

    public static void clear() {
        USER_AGENT.remove();
    }

    public static void init(String fromBee) {
        logger.info("fromBee = {}", fromBee);
        USER_AGENT.set(fromBee);
    }
}

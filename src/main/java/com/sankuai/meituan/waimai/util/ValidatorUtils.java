package com.sankuai.meituan.waimai.util;

import com.dianping.cat.util.StringUtils;
import com.sankuai.meituan.waimai.armor.validator.StringValidator;
import com.sankuai.meituan.waimai.armor.validator.result.StringValidateCodeEnum;
import com.sankuai.meituan.waimai.armor.validator.result.StringValidateResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class ValidatorUtils {


    /**
     * 判断门店ID是否数值
     *
     * @param wmPoiIdStr
     * @return
     */
    public boolean checkWmPoiIdValidate(String wmPoiIdStr) {
        if (StringUtils.isBlank(wmPoiIdStr)) {
            return false;
        }
        try {
            StringValidateResult result = StringValidator.validateNumber(wmPoiIdStr);
            if (result.getCode() != StringValidateCodeEnum.SUCCESS.getCode()) {
                log.info("StringValidator.checkWmPoiIdValidate,参数校验未通过,wmPoiIdStr={}", wmPoiIdStr);
                return false;
            }
        } catch (Exception e) {
            log.error("StringValidator.checkWmPoiIdValidate,判断门店ID是否为数字发生异常,wmPoiIdStr={}", wmPoiIdStr, e);
        }
        return true;
    }
}

package com.sankuai.meituan.waimai.service.handler;


/**
 * <AUTHOR>
 * @version 2021/8/30
 */
public interface IResultCode {

    /**
     * 异常code规则：
     * 1. 平台层code在10000以下、业务层code在10000以上
     * 2. 异常分为应用异常、校验异常、领域逻辑异常、基础组件异常等
     * 3. 应用层异常：以5开头
     *    校验异常：以4开头  如拜访任务不存在  40001
     *    领域逻辑异常：以2开头
     *    基础组件异常：以1开头
     *
     * @return code值
     */
    Integer getCode();

    /**
     * 异常描述
     *
     * @return
     */
    String getDesc();

}


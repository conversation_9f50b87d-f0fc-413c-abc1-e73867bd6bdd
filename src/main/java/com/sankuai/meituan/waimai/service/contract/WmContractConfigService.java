package com.sankuai.meituan.waimai.service.contract;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.econtrct.client.constants.config.FrameContractConfigStatusEnum;
import com.sankuai.meituan.waimai.service.adaptor.WmFrameContractConfigThriftServiceAdapter;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerDeviceType;
import com.sankuai.meituan.waimai.thrift.customer.domain.config.*;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.vo.config.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @description:
 * @author: liuyunjie05
 * @create: 2024/5/21 14:07
 */
@Slf4j
@Service
public class WmContractConfigService {

    @Resource
    private WmFrameContractConfigThriftServiceAdapter wmFrameContractConfigThriftServiceAdapter;

    private static WmFrameContractConfigThriftServiceAdapter wmFrameContractConfigThriftServiceAdapterStatic;

    @PostConstruct
    public void init() {
        wmFrameContractConfigThriftServiceAdapterStatic = wmFrameContractConfigThriftServiceAdapter;
    }

    public static boolean isConfigContract(Integer contractId) {
        try {
            ContractConfigInfo configInfo = wmFrameContractConfigThriftServiceAdapterStatic.queryContractConfigInfo(contractId);
            return configInfo != null;
        } catch (WmCustomerException e) {
            log.warn("WmContractConfigService#isConfigContract, contractId: {}, error", contractId, e);
            return false;
        }
    }

    public static Boolean isEnabledConfigContract(Integer contractId) {
        try {
            ContractConfigInfo configInfo = wmFrameContractConfigThriftServiceAdapterStatic.queryContractConfigInfo(contractId);
            return configInfo != null && configInfo.getStatus() == FrameContractConfigStatusEnum.ENABLE.getCode();
        } catch (Exception e) {
            log.warn("WmContractConfigService#isEnabledConfigContract, contractId: {}, error", contractId, e);
            return false;
        }
    }

    public static ContractConfigAllInfoVo getConfigContractByContractId(Integer contractId) throws WmCustomerException {
        ContractConfigInfo configInfo = wmFrameContractConfigThriftServiceAdapterStatic.queryContractConfigInfo(contractId);
        return convertConfigToVo(configInfo);
    }

    private static ContractConfigAllInfoVo convertConfigToVo(ContractConfigInfo configInfo) {
        if (configInfo == null) {
            return null;
        }
        try {
            ContractConfigAllInfoVo allInfoVo = new ContractConfigAllInfoVo();
            allInfoVo.setContractId(configInfo.getContractId());
            allInfoVo.setContractName(configInfo.getContractName());
            allInfoVo.setContractCode(configInfo.getContractCode());
            allInfoVo.setConfigPropertyVo(convertContractPropertyToVo(configInfo.getContractProperty()));
            allInfoVo.setStatus(configInfo.getStatus());
            allInfoVo.setOperationVo(convertToOperationVo(configInfo.getSupportOpInfo()));
            allInfoVo.setSourceAuthInfoVo(convertToSourceAuthInfoVo(configInfo.getSourceAuthInfo()));
            return allInfoVo;
        } catch (Exception e) {
            log.warn("WmContractConfigService#convertConfigToVo, configInfo: {}, error", JSON.toJSONString(configInfo), e);
            return null;
        }
    }

    private static SourceAuthInfoVo convertToSourceAuthInfoVo(SourceAuthInfo sourceAuthInfo) {
        SourceAuthInfoVo targetVo = new SourceAuthInfoVo();
        targetVo.setCanSaveInBee(sourceAuthInfo.getCanSaveInBee());
        targetVo.setCanDisplayInBee(sourceAuthInfo.getCanDisplayInBee());
        targetVo.setCanDisplayInSingleView(sourceAuthInfo.getCanDisplayInSingleView());
        targetVo.setCanDisplayInMultiView(sourceAuthInfo.getCanDisplayInMultiView());
        return targetVo;
    }

    private static ConfigContractOperationVo convertToOperationVo(OperationSupportInfo supportOpInfo) {
        ConfigContractOperationVo operationVo = new ConfigContractOperationVo();
        operationVo.setCanInvalidContract(supportOpInfo.getCanAbolishContract());
        return operationVo;
    }

    private static ContractConfigPropertyVo convertContractPropertyToVo(ContractProperty contractProperty) {
        ContractConfigPropertyVo configPropertyVo = new ContractConfigPropertyVo();
        configPropertyVo.setPartPropertyVoList(convertContractPartPropertyToVo(contractProperty.getPartPropertyList()));
        configPropertyVo.setValidityVo(convertContractValidityPropertyToVo(contractProperty.getContractValidityProperty()));
        configPropertyVo.setEffectiveTimeVo(convertContractEffectiveTimePropertyToVo(contractProperty.getContractEffectiveTimeProperty()));
        return configPropertyVo;
    }

    private static List<ContractConfigPartPropertyVo> convertContractPartPropertyToVo(List<ContractPartProperty> partPropertyList) {
        List<ContractConfigPartPropertyVo> voList = new ArrayList<>();
        for (ContractPartProperty sourcePartProperty : partPropertyList) {
            ContractConfigPartPropertyVo targetPartProperty = new ContractConfigPartPropertyVo();
            BeanUtils.copyProperties(sourcePartProperty, targetPartProperty);
            voList.add(targetPartProperty);
        }
        return voList;
    }


    private static ContractValidityPropertyVo convertContractValidityPropertyToVo(ContractValidityInfo validityInfo) {
        ContractValidityPropertyVo targetValidityPropertyVo = new ContractValidityPropertyVo();
        BeanUtils.copyProperties(validityInfo, targetValidityPropertyVo);

        targetValidityPropertyVo.setLeftInterval(convertToTimePropertyVo(validityInfo.getLeftInterval()));
        targetValidityPropertyVo.setRightInterval(convertToTimePropertyVo(validityInfo.getRightInterval()));

        return targetValidityPropertyVo;
    }

    private static TimePropertyVo convertToTimePropertyVo(ContractTimeInfo sourceTimeInfo) {
        if (sourceTimeInfo == null) {
            return null;
        }
        TimePropertyVo targetVo = new TimePropertyVo();
        BeanUtils.copyProperties(sourceTimeInfo, targetVo);
        return targetVo;
    }

    private static ContractEffectiveTimePropertyVo convertContractEffectiveTimePropertyToVo(ContractEffectiveTimeInfo effectiveTimeInfo) {
        ContractEffectiveTimePropertyVo effectiveTimePropertyVo = new ContractEffectiveTimePropertyVo();
        BeanUtils.copyProperties(effectiveTimeInfo, effectiveTimePropertyVo);

        effectiveTimePropertyVo.setLeftInterval(convertToTimePropertyVo(effectiveTimeInfo.getLeftInterval()));
        effectiveTimePropertyVo.setRightInterval(convertToTimePropertyVo((effectiveTimeInfo.getRightInterval())));
        return effectiveTimePropertyVo;
    }

    public static List<ContractConfigAllInfoVo> queryConfigFrameContract(Integer uid, Integer wmCustomerId, CustomerDeviceType deviceType) {
        List<ContractConfigInfo> configInfoList = wmFrameContractConfigThriftServiceAdapterStatic.queryConfigFrameContract(uid, wmCustomerId, deviceType);
        List<ContractConfigAllInfoVo> allConfigInfoVoList = new ArrayList<>();
        for (ContractConfigInfo configInfo : configInfoList) {
            ContractConfigAllInfoVo allInfoVo = convertConfigToVo(configInfo);
            if (allInfoVo == null) {
                continue;
            }
            allConfigInfoVoList.add(allInfoVo);
        }
        return allConfigInfoVoList;
    }

    public static List<ContractConfigAllInfoVo> allConfigFrameContract() {
        List<ContractConfigAllInfoVo> allConfigInfoVoList = new ArrayList<>();

        List<ContractConfigInfo> configInfoList = wmFrameContractConfigThriftServiceAdapterStatic.allConfigFrameContract();
        for (ContractConfigInfo configInfo : configInfoList) {
            ContractConfigAllInfoVo allInfoVo = convertConfigToVo(configInfo);
            if (allInfoVo == null) {
                continue;
            }
            allConfigInfoVoList.add(allInfoVo);
        }
        return allConfigInfoVoList;
    }

}

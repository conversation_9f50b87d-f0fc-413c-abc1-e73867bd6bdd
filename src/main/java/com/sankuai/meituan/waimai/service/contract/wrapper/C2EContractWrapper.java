package com.sankuai.meituan.waimai.service.contract.wrapper;

import com.sankuai.meituan.auth.vo.User;
import com.sankuai.meituan.waimai.thrift.agent.domain.WmCommercialAgentInfo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.vo.ContractVo;
import com.sankuai.meituan.waimai.vo.WmContractAgentInfo;
import com.sankuai.meituan.waimai.vo.base.MultiFileJsonVo;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 */
public class C2EContractWrapper extends AbstractContractWrapper {

    private static Logger logger = LoggerFactory.getLogger(C2EContractWrapper.class);

    public C2EContractWrapper(ContractVo contractVo, User user) {
        super(contractVo, user);
    }

    public ContractVo wrap() throws WmCustomerException, TException {
        contractVo.setContractScan(new MultiFileJsonVo());
        WmContractAgentInfo agentById = wmContractAgentService.getAgentById(contractVo.getPartyB().getSignId());
        if (agentById != null) {
            contractVo.getPartyB().setSignPeople(agentById.getLegalPerson());
            contractVo.getPartyB().setSignName(agentById.getName());
        }
        initPartyA();

        setSignTime();
        contractVo.getPartyB().setSignPhone("");

        if (isSave()) {
            contractVo.setContractNum("电子合同保存后自动生成编号");
        }
        return contractVo;
    }

}

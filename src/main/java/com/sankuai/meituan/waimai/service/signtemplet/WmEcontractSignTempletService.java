package com.sankuai.meituan.waimai.service.signtemplet;

import java.util.List;

import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractSignTempletConstant;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.BooleanResult;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.SignVersionDetailBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.SignVersionQueryThriftParam;
import com.sankuai.meituan.waimai.thrift.customer.service.WmEcontractSignThriftService;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.util.transform.signtemplet.SignTempletTransUtils;
import com.sankuai.meituan.waimai.vo.sign.SignTempletQueryParam;
import com.sankuai.meituan.waimai.vo.sign.SignVersionDetail;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class WmEcontractSignTempletService {

    @Autowired
    private WmEcontractSignThriftService wmEcontractSignThriftService;

    public List<SignVersionDetail> querySignVersion(SignTempletQueryParam queryParam) throws Exception {
        if (queryParam == null) {
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "参数异常");
        }
        if (StringUtils.isEmpty(queryParam.getBizType())) {
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "参数异常");
        }
        SignVersionQueryThriftParam thriftParam = new SignVersionQueryThriftParam();
        thriftParam.setBizType(queryParam.getBizType());
        if (!StringUtils.isEmpty(queryParam.getVersionNum()) && !StringUtils.isEmpty(queryParam.getVersionType())) {
            thriftParam.setVersionNum(queryParam.getVersionNum() + queryParam.getVersionType());
        }
        thriftParam.setPageNum(queryParam.getPageNum());
        thriftParam.setPageSize(queryParam.getPageSize());
        List<SignVersionDetailBo> signVersionTemplet = wmEcontractSignThriftService.getSignVersionTemplet(thriftParam);
        return SignTempletTransUtils.transDetailBoListToDetailList(signVersionTemplet);
    }

    public SignVersionDetail editSignVersion(SignTempletQueryParam queryParam) throws Exception {
        if (queryParam == null) {
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "参数异常");
        }
        if (StringUtils.isEmpty(queryParam.getBizType()) || StringUtils.isEmpty(queryParam.getVersionNum())
                || StringUtils.isEmpty(queryParam.getVersionType())) {
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "参数异常");
        }
        SignVersionQueryThriftParam param = new SignVersionQueryThriftParam();
        param.setBizType(queryParam.getBizType());
        param.setVersionNum(queryParam.getVersionNum() + queryParam.getVersionType());
        List<SignVersionDetailBo> signVersionTemplet = wmEcontractSignThriftService.getSignVersionTemplet(param);
        if (CollectionUtils.isEmpty(signVersionTemplet)) {
            return null;
        }
        return SignTempletTransUtils.transDetailBoToDetail(signVersionTemplet.get(0));
    }

    public boolean saveSignVersion(SignVersionDetail signVersionDetail) throws Exception {
        if (StringUtils.isEmpty(signVersionDetail.getBizType()) || StringUtils.isEmpty(signVersionDetail.getOperation())
                || StringUtils.isEmpty(signVersionDetail.getVersionNum()) || StringUtils.isEmpty(signVersionDetail.getVersionType())) {
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "参数异常");
        }
        String basicVersionNum = signVersionDetail.getVersionNum();
        if (!NumberUtils.isNumber(basicVersionNum) || basicVersionNum.length() != 8) {
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "版本号必须为8位数字!");
        }
        SignVersionDetailBo detail = new SignVersionDetailBo();
        detail.setBizType(signVersionDetail.getBizType());
        detail.setVersionNum(signVersionDetail.getVersionNum() + signVersionDetail.getVersionType());
        detail.setPdfTemplet(JSONObject.toJSONString(signVersionDetail.getPdfTemplet()));
        detail.setDocument(signVersionDetail.getDocument());
        if (EcontractSignTempletConstant.OPERATION_CREATE.equals(signVersionDetail.getOperation())) {
            if (wmEcontractSignThriftService.haveExistingTemplet(detail).isRes()) {
                throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "该模板版本号下已有相同版本类型");
            }
        }
        BooleanResult booleanResult = wmEcontractSignThriftService.saveSignVersionTemplet(detail);
        return booleanResult.isRes();
    }

    public boolean setStatus(SignTempletQueryParam queryParam) throws Exception {
        if (StringUtils.isEmpty(queryParam.getBizType()) || StringUtils.isEmpty(queryParam.getVersionNum())
                || StringUtils.isEmpty(queryParam.getVersionType()) || StringUtils.isEmpty(queryParam.getStatus())) {
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "参数异常");
        }
        SignVersionQueryThriftParam param = new SignVersionQueryThriftParam();
        param.setBizType(queryParam.getBizType());
        param.setVersionNum(queryParam.getVersionNum() + queryParam.getVersionType());
        param.setStatus(queryParam.getStatus());
        BooleanResult booleanResult = wmEcontractSignThriftService.setSignVersionTempletStatus(param);
        return booleanResult.isRes();
    }
}

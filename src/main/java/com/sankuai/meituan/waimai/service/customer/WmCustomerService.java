package com.sankuai.meituan.waimai.service.customer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.aspect.TairLock;
import com.sankuai.meituan.waimai.channel.beacon.lib.enums.ModuleEnum;
import com.sankuai.meituan.waimai.channel.beacon.lib.statemap.DefaultProgressStatusUtil;
import com.sankuai.meituan.waimai.channel.beacon.lib.statemap.ProgressStatus;
import com.sankuai.meituan.waimai.config.MccConfig;
import com.sankuai.meituan.waimai.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.constant.TairLockGroup;
import com.sankuai.meituan.waimai.constant.WmCustomerConstant;
import com.sankuai.meituan.waimai.constant.customer.CustomerSSRFCheckEnum;
import com.sankuai.meituan.waimai.infra.constants.WmOrgConstant;
import com.sankuai.meituan.waimai.infra.constants.WmVirtualOrgRecursiveTypeEnum;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import com.sankuai.meituan.waimai.infra.service.WmEmployService;
import com.sankuai.meituan.waimai.mtauth.util.WmEmployUtils;
import com.sankuai.meituan.waimai.poi.constants.attribute.WmPoiIsDeleteEnum;
import com.sankuai.meituan.waimai.poibrand.domain.WmPoiGroupBrand;
import com.sankuai.meituan.waimai.poibrand.service.WmPoiBrandThriftService;
import com.sankuai.meituan.waimai.poilogistics.thrift.domain.CustomerBatchOperatorInfo;
import com.sankuai.meituan.waimai.poilogistics.thrift.domain.OperateInfo;
import com.sankuai.meituan.waimai.poilogistics.thrift.exception.WmPoiLogisticsException;
import com.sankuai.meituan.waimai.poilogistics.thrift.service.WmPoiLogisticsSubmitThriftService;
import com.sankuai.meituan.waimai.poimanager.constant.WmPoiFullStatusConstant;
import com.sankuai.meituan.waimai.poimanager.domain.WmPoiWorkflowModuleInfo;
import com.sankuai.meituan.waimai.service.WmCustomerCheck;
import com.sankuai.meituan.waimai.service.adaptor.WmVirtualOrgServiceAdaptor;
import com.sankuai.meituan.waimai.service.sc.WmScCheckService;
import com.sankuai.meituan.waimai.service.sc.WmScContractorService;
 import com.sankuai.meituan.waimai.thrift.customer.constant.*;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.CustomerPoiListSearchTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.WmCustomerAuthTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.domain.ModuleDetailStatus;
import com.sankuai.meituan.waimai.thrift.customer.domain.OplogSearchPageBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.WmCustomerOplogBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.BaseResponse;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.OplogBoPageData;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.framecontract.DcC2ContractSignerVo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.framecontract.DcContractCustomerVo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.framecontract.DcCustomerInfoRequestDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractCustomerInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.service.*;
import com.sankuai.meituan.waimai.thrift.customer.service.contract.WmCustomerFrameContractThriftService;
import com.sankuai.meituan.waimai.thrift.customer.service.sc.WmCanteenThriftService;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import com.sankuai.meituan.waimai.util.WmCustomerTransUtil;
import com.sankuai.meituan.waimai.util.base.DesensitizeUtil;
import com.sankuai.meituan.waimai.util.base.WmRestReturnUtil;
import com.sankuai.meituan.waimai.vo.WmPoiStatusVo;
import com.sankuai.meituan.waimai.vo.WmPoiWorkFlowInfoVo;
import com.sankuai.meituan.waimai.vo.customer.CustomerInfo;
import com.sankuai.meituan.waimai.vo.customer.WmCustomerLabelTypeVo;
import com.sankuai.meituan.waimai.vo.customer.WmCustomerLabelVo;
import com.sankuai.meituan.waimai.vo.customer.WmCustomerListVo;
import com.sankuai.meituan.waimai.vo.customer.WmCustomerOCRVo;
import com.sankuai.meituan.waimai.vo.customer.WmCustomerPoiListVo;
import com.sankuai.meituan.waimai.vo.customer.WmCustomerUpdateInfo;
import com.sankuai.meituan.waimai.vo.customer.WmCustomerUpdateResult;
import com.sankuai.meituan.waimai.web.controller.WmCustomerController;
import com.sankuai.meituan.wbinf.sg.api.certification.business.BusinessCertification;
import com.sankuai.meituan.wbinf.sg.api.certification.business.QueryBasicInfoRequest;
import com.sankuai.meituan.wbinf.sg.api.certification.business.QueryBasicInfoResponse;
import com.sankuai.meituan.wbinf.sg.api.certification.business.RegisterBasicInfo;
import com.sankuai.nibcus.inf.customer.client.enums.BusinessLineEnum;
import com.sankuai.security.sdk.SecSdk;
import com.sankuai.security.sdk.core.ssrf.SSRFConfig;
import com.sankuai.waimai.crm.authenticate.client.auth.handler.SensitiveResourceView;
import com.sankuai.waimai.crm.authenticate.client.auth.vo.SensitiveResourceDecryptResponseVO;
import com.sankuai.waimai.crm.authenticate.client.constant.DecryptStatusEnum;

import java.util.*;
import javax.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class WmCustomerService {

    private static Logger logger = LoggerFactory.getLogger(WmCustomerController.class);

    @Autowired
    private WmCustomerThriftService wmCustomerThriftService;

    @Autowired
    private WmCustomerCommonThriftService wmCustomerCommonThriftService;

    @Autowired
    private WmCanteenThriftService wmCanteenThriftService;

    @Autowired
    private BusinessCertification.Iface businessIface;
    @Resource
    WmCustomerOplogThriftService wmCustomerOplogThriftService;
    @Autowired
    private WmScContractorService wmScContractorService;
    @Autowired
    private WmScCheckService wmScCheckService;
    @Autowired
    private WmCustomerCheck wmCustomerCheck;

    @Autowired
    private WmCustomerCheckAuthService wmCustomerCheckAuthService;

    @Autowired
    private WmVirtualOrgServiceAdaptor wmVirtualOrgServiceAdaptor;

    @Autowired
    private WmPoiLogisticsSubmitThriftService.Iface wmPoiLogisticsThriftService;

    @Autowired
    private WmCustomerPoiThriftService wmCustomerPoiThriftService;

    @Autowired
    private WmPoiBrandThriftService.Iface wmPoiBrandThriftService;

    @Autowired
    private WmEmployService.Iface wmEmployThriftService;

    @Autowired
    private WmCustomerKpThriftService wmCustomerKpThriftService;

    @Resource
    private WmCustomerFrameContractThriftService wmCustomerFrameContractThriftService;

    private static final int BATCH_NUM_QUERY = 100;

    @TairLock(group = TairLockGroup.CUSTOMER_CREATE, seedExp = "customerInfo.customerNumber+'_'+customerInfo.customerRealType+'_'+customerInfo.registryState")
    public Object createCustomer(CustomerInfo customerInfo, Boolean force) throws WmCustomerException, TException, WmServerException {

        // 用户ID
        Integer userId = UserUtils.getUser().getId();
        // 是否新建
        boolean isNew = (customerInfo.getId() == null || customerInfo.getId() <= 0) ? true : false;
        // 新增上级客户 默认设置为电子签约
        if (isNew && Integer.valueOf(CustomerConstants.CUSTOMER_IS_LEAF_NO).equals(customerInfo.getIsLeaf()) && MccConfig.isSuperCustomerElectronic()) {
            customerInfo.setSignMode(CustomerSignMode.ELECTTRONIC.getCode());
        }
        // 校验签约模式权限
        wmCustomerCheck.checkSignModeEditAuth(customerInfo);
        String userName = UserUtils.getUser().getName();//用户名称
        ValidateResultBo validateResultBo = save(customerInfo, force, userId, userName);
        logger.info("保存方法调用成功:{}", JSONObject.toJSONString(validateResultBo));
        JSONObject data = new JSONObject();
        if (validateResultBo.getValidateFieldList() != null) {
            data.put("list", validateResultBo.getValidateFieldList());
        }
        if (validateResultBo.getCustomerId() != 0) {
            data.put("id", validateResultBo.getCustomerId());
        }
        return WmRestReturnUtil.constructResponseJSON(validateResultBo.getCode(), validateResultBo.getMsg(), data);

    }

    @TairLock(group = TairLockGroup.CUSTOMER_UPDATE, seedExp = "customerInfo.id")
    public Object updateCustomer(CustomerInfo customerInfo, Boolean force) throws WmCustomerException, TException {
        // 校验签约模式权限
        wmCustomerCheck.checkSignModeEditAuth(customerInfo);

        // 校验用户是否有资质编号修改权限
        wmCustomerCheck.checkCustomerQuaEditAuth(customerInfo);
        // 用户ID
        Integer userId = UserUtils.getUser().getId();
        String userName = UserUtils.getUser().getName();//用户名称
        ValidateResultBo validateResultBo = save(customerInfo, force, userId, userName);
        logger.info("保存方法调用成功:{}", JSONObject.toJSONString(validateResultBo));
        JSONObject data = new JSONObject();
        if (validateResultBo.getValidateFieldList() != null) {
            data.put("list", validateResultBo.getValidateFieldList());
        }
        if (validateResultBo.getCustomerId() != 0) {
            data.put("id", validateResultBo.getCustomerId());
        }
        return WmRestReturnUtil.constructResponseJSON(validateResultBo.getCode(), validateResultBo.getMsg(), data);
    }


    public ValidateResultBo save(CustomerInfo customerInfo, Boolean force, Integer userId, String userName) throws TException, WmCustomerException {
        logger.info("customerInfo= {}", JSONObject.toJSONString(customerInfo));
        WmCustomerBasicBo wmCustomerBasicBo = WmCustomerTransUtil.convertCustomerBasicVo(customerInfo);
        //先富创建的客户为DB上单渠道
        wmCustomerBasicBo.setCustomerSource(CustomerSource.WAIMAI_BD);
        fillUserRole(wmCustomerBasicBo);
        //校验营业执照形式
        wmScCheckService.checkCustomerCustomerType(wmCustomerBasicBo);
        /**
         * 承包商的直接路由
         */
        if (wmCustomerBasicBo.getCustomerRealType() == CustomerRealTypeEnum.CONTRACTOR.getValue()) {
            if (!MccCustomerConfig.contractorSaveOrUpdateSwitch()) {
                throw new WmCustomerException(WmScCodeConstants.BIZ_PARA_ERROR, "服务升级中，食堂承包商暂不允许创建和修改，请稍后重试");
            }
            wmScCheckService.checkContractor(wmCustomerBasicBo);
            return wmScContractorService.saveOrUpdateContractor(wmCustomerBasicBo, force, userId, userName);
        }
        logger.info("保存客户信息参数:{}", JSONObject.toJSONString(wmCustomerBasicBo));
        ValidateResultBo validateResultBo = wmCustomerThriftService.saveOrUpdateCustomer(wmCustomerBasicBo, force, userId, userName);
        return validateResultBo;
    }

    /**
     * 当前用户拥有的角色需要下沉到服务层
     *
     * @param wmCustomerBasicBo
     */
    private void fillUserRole(WmCustomerBasicBo wmCustomerBasicBo) {
        if (wmCustomerCheck.checkMedicineAndNonMedicalSwitchManager(UserUtils.getUser())) {
            wmCustomerBasicBo.setUserRoleList(Lists.newArrayList(UserRoleEnum.MEDICINE_AND_NON_MEDICAL_SWITCH_MANAGER));
        }
    }

    /**
     * 通过编号查营业执照数据
     */
    public void getBusinessLicenceByNumber(String number, WmCustomerOCRVo wmCustomerOCRVo) {
        if (StringUtils.isBlank(number)) {
            return;
        }
        number = number.trim();
        QueryBasicInfoRequest request = new QueryBasicInfoRequest().setLicenseNo(number);
        logger.info("查询营业执照，request = {}", request);
        try {
            QueryBasicInfoResponse response = businessIface.queryBasicInfo(
                    ConfigUtilAdapter.getLong("BusinessCertification.partnerId", 11220000),
                    ConfigUtilAdapter.getString("BusinessCertification.digest", "com.sankuai.waimai.m.queenbee"),
                    request);
            logger.info("查询营业执照,number = {},response = {}", number, response);
            if (response == null || response.getRegisterBasicInfo() == null) {
                return;
            }
            RegisterBasicInfo registerBasicInfo = response.getRegisterBasicInfo();
            wmCustomerOCRVo.setNumber(number);
            wmCustomerOCRVo.setValidateDate(registerBasicInfo.getExpiredDate() == -1 ? 0 : registerBasicInfo.getExpiredDate() / 1000);
            wmCustomerOCRVo.setName(registerBasicInfo.getEnterpriseName());
            wmCustomerOCRVo.setLegalPerson(registerBasicInfo.getLegalPerson());
            wmCustomerOCRVo.setAddress(registerBasicInfo.getAddress());
            wmCustomerOCRVo.setBusinessScope(registerBasicInfo.getBusinessScope());
        } catch (Exception e) {
            logger.warn("查询营业执照失败,number = {}", number, e);
        }
    }

    public ModuleDetailStatus getModuleDetailStatusByCustomerId(Integer customerId) throws TException, WmCustomerException {
        return wmCustomerCommonThriftService.getModuleDetailStatusByCustomerId(customerId);
    }

    public Map<String, Object> getCustomerPoiLastFailReason(Integer customerId, Integer contractId, int opUid, String opUname) {
        OplogSearchPageBo searchPageBo = new OplogSearchPageBo();
        searchPageBo.setCustomerId(customerId);
        searchPageBo.setModuleType(WmCustomerOplogBo.OpModuleType.CUSTOMER_POI.type);
        searchPageBo.setModuleId(contractId);
        searchPageBo.setOpType(WmCustomerOplogBo.OpType.CHANGESTATUS.type);
        searchPageBo.setPageSize(1);
        OplogBoPageData oplogBoPageData;
        try {
            oplogBoPageData = wmCustomerOplogThriftService.search(searchPageBo, opUid, opUname);
        } catch (WmCustomerException | TException e) {
            logger.error(e.getMessage(), e);
            return Maps.newHashMap();
        }
        if (CollectionUtils.isNotEmpty(oplogBoPageData.getList())) {
            WmCustomerOplogBo customerOplogBo = oplogBoPageData.getList().get(0);
            Map<String, Object> res = Maps.newHashMap();
            res.put("utime", customerOplogBo.getUtime());
            String log = customerOplogBo.getLog();
            if (log != null && log.length() >= 3 && log.indexOf("原因：") > 0) {
                res.put("reason", log.substring(log.indexOf("原因：") + 3));
            }
            return res;
        }
        return Maps.newHashMap();
    }

    /**
     * 批量根据承包商ID获取关联食堂数
     */
    public void loadCanteenCountByVoList(List<WmCustomerListVo> wmCustomerListVoList) throws TException, WmSchCantException {
        List<Integer> contractorIds = new ArrayList<>();
        for (WmCustomerListVo wmCustomerListVo : wmCustomerListVoList) {
            if (wmCustomerListVo.getCustomerRealType() == CustomerRealTypeEnum.CONTRACTOR.getValue()) {
                contractorIds.add(wmCustomerListVo.getCustomerId());
            }
        }
        if (contractorIds.isEmpty()) {
            return;
        }
        Map<Integer, Integer> canteenCountMap = wmCanteenThriftService.countCanteenByContractorIds(contractorIds);
        Map<Integer, Integer> canteenPoiCountMap = wmCanteenThriftService.countCanteenPoiByContractorIds(contractorIds);
        for (WmCustomerListVo wmCustomerListVo : wmCustomerListVoList) {
            if (wmCustomerListVo.getCustomerRealType() == CustomerRealTypeEnum.CONTRACTOR.getValue() && canteenCountMap.containsKey(wmCustomerListVo.getCustomerId())) {
                wmCustomerListVo.setCanteenCount(canteenCountMap.get(wmCustomerListVo.getCustomerId()));
            }
            if (wmCustomerListVo.getCustomerRealType() == CustomerRealTypeEnum.CONTRACTOR.getValue() && canteenPoiCountMap.containsKey(wmCustomerListVo.getCustomerId())) {
                wmCustomerListVo.setPoiCount(canteenPoiCountMap.get(wmCustomerListVo.getCustomerId()));
            }
        }
    }

    public List<WmCustomerLabelTypeVo> getCustomerLabelTypeList(Integer opUid) throws TException, WmCustomerException {
        List<WmCustomerLabelTypeBo> wmCustomerLabelTypeBoList = wmCustomerThriftService.getCustomerLabelTypeList(opUid);
        if (CollectionUtils.isEmpty(wmCustomerLabelTypeBoList)) {
            return null;
        }
        return WmCustomerLabelTypeVo.transWmCustomerLabelTypeBoList2VoList(wmCustomerLabelTypeBoList);
    }

    public List<WmCustomerLabelVo> getCustomerLabelList(Long wmCustomerId, Integer opUid) throws TException, WmCustomerException {
        List<WmCustomerLabelBo> wmCustomerLabelBoList = wmCustomerThriftService.getCustomerLabelList(wmCustomerId, opUid);
        List<WmCustomerLabelVo> wmCustomerLabelVoList = new ArrayList<>();
        BeanUtils.copyProperties(wmCustomerLabelBoList, wmCustomerLabelVoList);
        return wmCustomerLabelVoList;
    }

    public WmCustomerDetailBo getCustomerWithAuditById(Integer customerId, Integer userId) throws TException, WmCustomerException {
        WmCustomerDetailBo wmCustomerDetailBo = wmCustomerThriftService.getCustomerWithAuditById(customerId);
        // 客户标签信息封装
        List<WmCustomerLabelBo> labelBoList = wmCustomerThriftService.getCustomerLabelInfo(customerId, userId);
        wmCustomerDetailBo.setLabelNames(labelBoList);
        logger.debug("客户：{}，标签信息：{}", customerId, JSONObject.toJSONString(labelBoList));
        return wmCustomerDetailBo;
    }

    /**
     * 判断用户是否有修改复用客户注册号的权限
     *
     * @param customerInfo
     * @return
     * @throws TException
     * @throws WmCustomerException
     */
    public boolean checkUserUpdateMultiplexCustomerNumber(CustomerInfo customerInfo) throws WmCustomerException, TException {
        if (customerInfo.getMultiplexCustomerId() == null || customerInfo.getMultiplexCustomerId() <= 0) {
            //非复用客户
            return true;
        }
        WmCustomerMultiplexDTO wmCustomerMultiplexDto = wmCustomerThriftService.getMultiplexCustomerById(customerInfo.getMultiplexCustomerId(), customerInfo.getMultiplexBusinessLineId());
        if (wmCustomerMultiplexDto == null) {
            //复用池客户ID不合法或者查询不到复用池客户
            throw new WmCustomerException(WmScCodeConstants.BIZ_PARA_ERROR, "查询不到复用客户（复用池客户ID为" + customerInfo.getMultiplexCustomerId() + "）");
        }
        if (!wmCustomerMultiplexDto.getCustomerNumber().equals(customerInfo.getCustomerNumber())
                && !wmCustomerCheck.checkCustomerQuaEditManager(UserUtils.getUser(), customerInfo.getCustomerRealType())) {
            //前端提交过来的信息中修改了资质编号但是却没有修改编号的权限
            return false;
        }
        return true;
    }

    public WmCustomerDetailBo getElicense(String verificationCoded, Long sequence) throws TException, WmCustomerException {
        return wmCustomerThriftService.getElicenseByVerificationCoded(verificationCoded, sequence, UserUtils.getUser().getId());
    }

    public WmCustomerPageDate getCustomerList(WmCustomerFormBo wmCustomerFormBo, int userId) throws TException, WmCustomerException {
        WmCustomerPageDate wmCustomerPageDate = wmCustomerThriftService.getCustomerList(wmCustomerFormBo);
        List<WmCustomerListBo> pageList = wmCustomerPageDate.getList();
        // 客户标签信息封装
        if (CollectionUtils.isNotEmpty(pageList)) {
            List<Integer> customerIdList = new ArrayList<>();
            Map<Integer, WmCustomerListBo> customerMap = new HashMap<>();
            for (WmCustomerListBo wmCustomerListBo : pageList) {
                customerIdList.add(wmCustomerListBo.getId());
                customerMap.put(wmCustomerListBo.getId(), wmCustomerListBo);
            }
            Map<Integer, List<WmCustomerLabelBo>> labelMap = wmCustomerThriftService.getCustomerLabelInfoBatch(customerIdList, userId);
            for (Map.Entry<Integer, List<WmCustomerLabelBo>> entry : labelMap.entrySet()) {
                if (customerMap.get(entry.getKey()) != null) {
                    customerMap.get(entry.getKey()).setLabelNames(entry.getValue());
                }
            }
        }
        return wmCustomerPageDate;
    }

    public Integer queryCustomerRealType(Long customerId) throws Exception {
        WmCustomerBasicBo wmCustomerBasicBo = wmCustomerThriftService.getCustomerByIdOrMtCustomerId(customerId);
        if (wmCustomerBasicBo == null) {
            return null;
        }
        return wmCustomerBasicBo.getCustomerRealType();
    }

    public JSONObject checkUpdate(WmCustomerUpdateInfo info) {
        try {
            if (info == null || info.getCustomerId() == 0) {
                return WmRestReturnUtil.fail("参数错误");
            }
            WmCustomerUpdateResult result = new WmCustomerUpdateResult();
            result.setCustomerId(info.getCustomerId());

            boolean legalPersonChange = false;
            WmCustomerBasicBo wmCustomerBasicBo = wmCustomerThriftService.getCustomerById(info.getCustomerId());
            if (wmCustomerBasicBo != null && wmCustomerBasicBo.getEffective() == CustomerConstants.EFFECT) {
                boolean isNewForCustomerKp = wmCustomerThriftService.isNewForCustomerKp();
                if (isNewForCustomerKp) {
                    if ((wmCustomerBasicBo.getCustomerType() == CustomerType.CUSTOMER_TYPE_BUSINESS.getCode() || wmCustomerBasicBo.getCustomerType() == CustomerType.CUSTOMER_TYPE_BUSINESS_ABROAD.getCode())
                            && !wmCustomerBasicBo.getLegalPerson().equals(info.getLegalPerson())) {
                        legalPersonChange = true;
                    }
                }
            }
            result.setLegalPersonChange(legalPersonChange);
            return WmRestReturnUtil.success(result);
        } catch (WmCustomerException e) {
            logger.warn("checkUpdate 失败 info={}", JSONObject.toJSONString(info), e);
            return WmRestReturnUtil.fail("客户变更校验失败:" + e.getMsg());
        } catch (Exception e) {
            logger.error("checkUpdate 失败 info={}", JSONObject.toJSONString(info), e);
            return WmRestReturnUtil.fail("客户变更校验失败");
        }
    }

    public WmCustomerPoiListConditionDTO getWmCustomerPoiListConditionDTO(WmCustomerPoiListVo vo) {
        if (vo == null) {
            return null;
        }
        WmCustomerPoiListConditionDTO dto = new WmCustomerPoiListConditionDTO();
        dto.setCustomerId(vo.getCustomerId());
        dto.setKeyWord(vo.getKeyWord());
        dto.setWmPoiId(vo.getWmPoiId());
        if (vo.getKpId() != null && vo.getKpId() > 0) {
            dto.setOpManagerId(vo.getKpId());
        }
        dto.setRelationStatus(vo.getBindStatus());
        dto.setSwitchTaskType(vo.getSwitchCustomerType());
        if (vo.getDeliveryStatus() != null && vo.getDeliveryStatus() > 0) {
            dto.setDeliveryStatus(vo.getDeliveryStatus());
        }
        dto.setPageNo(vo.getPageNo());
        dto.setPageSize(vo.getPageSize());
        dto.setSearchType(CustomerPoiListSearchTypeEnum.VALID_AND_READY.getCode());
        // 查询条件-业务品牌
        if (vo.getBrandId() != null && vo.getBrandId() > 0) {
            dto.setBrandId(vo.getBrandId());
        }
        // 查询条件-门店责任人
        if (vo.getOwnerUid() != null && vo.getOwnerUid() > 0) {
            dto.setOwnerUid(vo.getOwnerUid());
        }
        // 查询条件-商家状态
        if (vo.getPoiStatus() != null && vo.getPoiStatus() >= 0) {
            dto.setPoiStatus(vo.getPoiStatus());
        }
        //查询条件-是否子门店
        if (vo.getChildPoiFlag() != null && vo.getChildPoiFlag() > 0) {
            dto.setChildPoiFlag(vo.getChildPoiFlag());
        }
        return dto;
    }

    public List<WmPoiStatusVo> getWmCustomerPoiList(List<WmCustomerPoiListInfoDTO> list, int authType) throws TException, WmServerException {
        List<WmPoiStatusVo> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(list)) {
            return result;
        }
        for (WmCustomerPoiListInfoDTO infoDTO : list) {
            WmPoiStatusVo wmPoiStatusVo = new WmPoiStatusVo();
            wmPoiStatusVo.setWmPoiId(infoDTO.getWmPoiId());
            wmPoiStatusVo.setWmPoiName(infoDTO.getShopName());
            wmPoiStatusVo.setKpId(infoDTO.getOpManagerId());
            wmPoiStatusVo.setKpName(infoDTO.getOpManagerName());
            if (infoDTO.getSwitchTaskId() != null && infoDTO.getSwitchTaskId() > 0) {
                wmPoiStatusVo.setSwitchCustomerType(SwitchCustomerTypeEnum.YES.getCode());
            } else {
                wmPoiStatusVo.setSwitchCustomerType(SwitchCustomerTypeEnum.NO.getCode());
            }
            wmPoiStatusVo.setSwitchTaskId(infoDTO.getSwitchTaskId());
            wmPoiStatusVo.setBindStatus(infoDTO.getRelationStatus());
            wmPoiStatusVo.setChildPoiFlag(infoDTO.getChildPoiFlag());
            WmPoiWorkFlowInfoVo wmPoiWorkFlowInfoVo = new WmPoiWorkFlowInfoVo();
            buildPoiStatus(wmPoiWorkFlowInfoVo, infoDTO.getPoiStatus(), infoDTO.getIsDelete());

            Map<String, WmPoiWorkflowModuleInfo> modules = Maps.newHashMap();
            modules.put(WmCustomerConstant.BASE, getModuleState(ModuleEnum.BASE, infoDTO.getBaseStatus()));
            modules.put(WmCustomerConstant.QUA, getModuleState(ModuleEnum.QUALIFICATION, infoDTO.getQuaStatus()));
            modules.put(WmCustomerConstant.SERVICE, getModuleState(ModuleEnum.SERVICE, infoDTO.getServiceStatus()));
            modules.put(WmCustomerConstant.DELIVERY, getModuleState(ModuleEnum.LOGISTICS, infoDTO.getDeliveryStatus()));
            modules.put(WmCustomerConstant.PRODUCT, getModuleState(ModuleEnum.PRODUCT, infoDTO.getProductStatus()));
            modules.put(WmCustomerConstant.SETTLEMENT, getModuleState(ModuleEnum.SETTLE, infoDTO.getSettleStatus()));
            wmPoiWorkFlowInfoVo.setModules(modules);
            wmPoiStatusVo.setWmPoiWorkFlowInfoVo(wmPoiWorkFlowInfoVo);
            wmPoiStatusVo.setAuthInfo(wmCustomerCheckAuthService.getCustomerPoiListDetailAuth(authType));
            // 业务品牌
            if (infoDTO.getBrandId() != null && infoDTO.getBrandId() > 0) {
                wmPoiStatusVo.setBrandId(infoDTO.getBrandId());
                WmPoiGroupBrand wmPoiGroupBrand = wmPoiBrandThriftService.getByBrandId(infoDTO.getBrandId());
                if (wmPoiGroupBrand == null) {
                    wmPoiStatusVo.setBrandName("-");
                } else {
                    wmPoiStatusVo.setBrandName(wmPoiGroupBrand.getBranch_name());
                }
            } else {
                wmPoiStatusVo.setBrandName("-");
            }

            // 门店责任人
            if (infoDTO.getOwnerUid() != null && infoDTO.getOwnerUid() > 0) {
                wmPoiStatusVo.setOwnerUid(infoDTO.getOwnerUid());
                WmEmploy wmEmploy = wmEmployThriftService.getById(infoDTO.getOwnerUid());
                if (wmEmploy == null) {
                    wmPoiStatusVo.setOwnerName("-");
                } else {
                    wmPoiStatusVo.setOwnerName(wmEmploy.getName() + "(" + wmEmploy.getMisId() + ")");
                }
            } else {
                wmPoiStatusVo.setOwnerName("-");
            }
            result.add(wmPoiStatusVo);
        }
        return result;
    }

    private WmPoiWorkflowModuleInfo getModuleState(ModuleEnum module, Integer beaconStateCode) {
        WmPoiWorkflowModuleInfo info = new WmPoiWorkflowModuleInfo();
        info.setStatus(-1);
        info.setName("未知状态");
        ProgressStatus progressStatus = DefaultProgressStatusUtil.getProgressStatus(module, beaconStateCode);
        if (progressStatus != null) {
            info.setStatus(progressStatus.getCode());
            info.setName(progressStatus.getDesc());
        }
        return info;
    }

    private void buildPoiStatus(WmPoiWorkFlowInfoVo vo, int valid, int isDelete) {
        int status = 0;
        String name = "";
        if (valid == 0) {
            if (isDelete == WmPoiIsDeleteEnum.IS_DELETE.getValue()) {
                status = WmPoiFullStatusConstant.PoiStatus.DELETE;
                name = "已删除";
            } else {
                status = WmPoiFullStatusConstant.PoiStatus.OFFLINE;
                name = "下线中";
            }
        } else if (valid == 1) {
            status = WmPoiFullStatusConstant.PoiStatus.ONLINE;
            name = "上线中";
        } else if (valid == 2) {
            status = WmPoiFullStatusConstant.PoiStatus.CREATING;
            name = "上单中";

        } else if (valid == 3) {
            status = WmPoiFullStatusConstant.PoiStatus.READY;
            name = "待上线";
        }
        vo.setStatus(status);
        vo.setName(name);
    }


    /**
     * 加工客户列表明细权限信息
     * 20240814 WD项目新增逻辑
     * 对于到餐客户，不允许编辑/分配责任人/查看操作日志
     *
     * @param list
     * @param userId
     */
    public void buildWmCustomerListAuthInfo(List<WmCustomerListVo> list, int userId) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        List<Integer> hqWriteCustomerRealTypes = wmCustomerCheckAuthService.getAuthCustomerRealType(userId, WmCustomerAuthTypeEnum.WRITE_AND_READ.getCode());
        List<Integer> downUids = wmVirtualOrgServiceAdaptor.getUidsByUid(userId, WmOrgConstant.QUERY_ALL_SOURCE, WmVirtualOrgRecursiveTypeEnum.NONE.getType());
        boolean isAuthOplog = wmCustomerCheck.checkAuthQueryCustomerOplog(UserUtils.getUser());
        boolean isCanDeleteCustomer = WmEmployUtils.isHQ(userId) && wmCustomerCheck.checkCustomerDeleteManager(UserUtils.getUser());
        // 获取当前用户下级
        for (WmCustomerListVo vo : list) {
            boolean isWrite;
            if (CustomerRealTypeEnum.DAOCAN.getValue() == vo.getCustomerRealType()) {
                isWrite = false;
                isAuthOplog = false;
            }else {
                isWrite = hqWriteCustomerRealTypes.contains(vo.getCustomerRealType()) || downUids.contains(vo.getOwnerUid()) || vo.getOwnerUid().intValue() == userId;
                isAuthOplog = true;
            }
            // 获取权限信息
            Map<String, Boolean> authInfo = wmCustomerCheckAuthService.getCustomerListDetailAuth(isWrite, isAuthOplog, isCanDeleteCustomer);
            vo.setAuthInfo(authInfo);
        }
    }

    public void batchConfirm(Integer customerId, List<Integer> confirmPoiList) throws TException, WmPoiLogisticsException {
        Integer userId = UserUtils.getUser().getId();// 用户ID
        String userName = UserUtils.getUser().getName();// 用户名称
        OperateInfo operateInfo = new OperateInfo();
        operateInfo.setOpId(userId);
        operateInfo.setOpName(userName);
        operateInfo.setModifyReason("批量确认配送信息");
        operateInfo.setModifyRemark("批量确认配送信息");

        CustomerBatchOperatorInfo customerBatchOperatorInfo = new CustomerBatchOperatorInfo();
        customerBatchOperatorInfo.setCustomerId(customerId);
        customerBatchOperatorInfo.setOperateInfo(operateInfo);
        customerBatchOperatorInfo.setWmPoiIdList(confirmPoiList);
        wmPoiLogisticsThriftService.batchApplyConfirm(customerBatchOperatorInfo);
    }

    public Map<Long, WmCustomerPoiListInfoDTO> getWmPoiInfoMap(List<Long> wmPoiIdList, Integer customerId, int maxBatchNum) throws TException, WmCustomerException {
        WmCustomerPoiListConditionDTO condition = new WmCustomerPoiListConditionDTO();
        condition.setWmPoiIdList(wmPoiIdList);
        condition.setCustomerId(customerId);
        condition.setPageNo(1);
        condition.setPageSize(maxBatchNum);
        condition.setSearchType(CustomerPoiListSearchTypeEnum.ONLY_VALID.getCode());
        Map<Long, WmCustomerPoiListInfoDTO> poiInfoMap = Maps.newHashMap();
        List<WmCustomerPoiListInfoDTO> list = wmCustomerPoiThriftService.getCustomerPoiList(condition);
        if (CollectionUtils.isEmpty(list)) {
            return poiInfoMap;
        }
        for (WmCustomerPoiListInfoDTO poiInfo : list) {
            poiInfoMap.put(poiInfo.getWmPoiId(), poiInfo);
        }
        return poiInfoMap;
    }

    public List<Long> getWaitConfirmWmPoiIdListForDelivery(List<Long> wmPoiIdList, Integer customerId) throws TException, WmCustomerException {
        List<WmCustomerPoiListInfoDTO> waitFormList = Lists.newArrayList();
        WmCustomerPoiListConditionDTO condition = new WmCustomerPoiListConditionDTO();
        condition.setCustomerId(customerId);
        condition.setDeliveryStatus(ProgressStatus.WAIT_FOR_CONFIRM.getCode());
        condition.setSearchType(CustomerPoiListSearchTypeEnum.ONLY_VALID.getCode());
        condition.setPageNo(1);
        condition.setPageSize(BATCH_NUM_QUERY);

        List<List<Long>> list = Lists.partition(wmPoiIdList, BATCH_NUM_QUERY);

        for (List<Long> wmPoiIds : list) {
            condition.setWmPoiIdList(wmPoiIds);
            List<WmCustomerPoiListInfoDTO> infoDTOS = wmCustomerPoiThriftService.getCustomerPoiList(condition);
            if (CollectionUtils.isEmpty(infoDTOS)) {
                continue;
            }
            waitFormList.addAll(infoDTOS);
        }

        List<Long> waitConfirmWmPoiIdList = Lists.newArrayList();
        for (WmCustomerPoiListInfoDTO infoDTO : waitFormList) {
            waitConfirmWmPoiIdList.add(infoDTO.getWmPoiId());
        }

        return waitConfirmWmPoiIdList;
    }

    /**
     * 加工普通客户列表明细权限信息
     *
     * @param list
     * @param authType
     */
    public void buildSubCustomerListAuthInfo(List<WmCustomerListVo> list, int authType) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        // 获取当前用户下级
        for (WmCustomerListVo vo : list) {
            // 获取权限信息
            Map<String, Boolean> authInfo = wmCustomerCheckAuthService.getSubCustomerListDetailAuth(authType);
            vo.setAuthInfo(authInfo);
        }
    }

    /**
     * 校验Url是否为可访问地址及是否在域名白名单中
     *
     * @param url    资质图片url地址
     * @param source 数据来源。0：picUrl，1：agentAuth/agentBackIdCard/agentFrontIdCard
     * @return 校验通过则返回false, 否则返回true
     */
    public boolean checkUrlIsInWhiteList(String url, int source) {
        logger.info("[checkUrlIsInWhiteList] 入参url = {}, source = {}", url, source);
        if (!ConfigUtilAdapter.getBoolean("open_ssrf_check_switch", false)) {
            return false;
        }
        if (url == null || StringUtils.isBlank(url)) {
            return false;
        }
        List<String> commonStr = JSONObject.parseArray(MccCustomerConfig.getSSRFCheckCommonStr(), String.class);
        if (CollectionUtils.isNotEmpty(commonStr)) {
            for (String str : commonStr) {
                if (url.contains(str)) {
                    return false;
                }
            }
        }

        List<String> acceptedDomains = new ArrayList<>();
        String errorMsg = null;
        if (source == CustomerSSRFCheckEnum.CUSTOMER_INFO.getCode()) {
            // 客户信息页面的picUrl校验
            errorMsg = CustomerSSRFCheckEnum.CUSTOMER_INFO.getDesc();
            acceptedDomains = JSONObject.parseArray(ConfigUtilAdapter.getString("picurl_ssrf_accepted_whitelist",
                "[\"*.meituan.net\"]"), String.class);
        } else if (source == CustomerSSRFCheckEnum.SIGNER_KP_INFO.getCode()) {
            // kp信息页面的agentAuth/agentBackIdCard/agentFrontIdCard校验
            errorMsg = CustomerSSRFCheckEnum.SIGNER_KP_INFO.getDesc();
            acceptedDomains = JSONObject.parseArray(ConfigUtilAdapter.getString("agent_auth_ssrf_accepted_whitelist",
                "[\"*.meituan.net\"]"), String.class);
        }
        SSRFConfig ssrfConfig = new SSRFConfig();
        ssrfConfig.addAcceptedDomains(acceptedDomains);
        // 仅做白名单匹配，显式关闭域名IP检查(默认白名单不通过会继续检查IP）
        ssrfConfig.setAddressCheckIfNotMatchDomains(false);
        String env = System.getProperty("environment");
        errorMsg = "[" + env + "] ";
        logger.info("[checkUrlIsInWhiteList] 白名单acceptedDomains={}", JSONObject.toJSONString(acceptedDomains));
        if (!SecSdk.checkSSRF(url, ssrfConfig)) {
            // 不通过安全检查，抛异常或者返回异常结果
            logger.error("[checkUrlIsInWhiteList] url={}, source={}未通过ssrf安全检查", url, source);
            return true;
        }
        logger.info("[checkUrlIsInWhiteList] url={}通过ssrf安全检查", url);
        return false;
    }

    /**
     * 获取美食城客户的已占用档口数信息-页面展示使用，异常不影响流程
     *
     * @param customerId
     * @return
     */
    public CustomerMscUsedPoiDTO getMscUsedPoiDTO(Integer customerId) {
        if (customerId == null || customerId <= 0) {
            return null;
        }
        try {
            CustomerMscUsedPoiDTO customerMscUsedPoiDTO = wmCustomerThriftService.getMscUsedPoiDTOByCustomerId(customerId);
            logger.info("getMscUsedPoiDTO,customerId={},customerMscUsedPoiDTO={}", customerId, JSON.toJSONString(customerMscUsedPoiDTO));
            return customerMscUsedPoiDTO;
        } catch (Exception e) {
            logger.error("getMscUsedPoiDTO,根据客户ID查询已占用档口数信息发生异常,customerId={}", customerId, e);
        }
        return null;
    }

    /**
     * 获取美食城客户的已占用档口数信息V2-页面展示使用，异常不影响流程
     *
     * @param customerId
     * @return
     */
    public CustomerMscUserPoiInfoResponse getMscUsedPoiDTOV2(Integer customerId) {
        if (customerId == null || customerId <= 0) {
            return null;
        }
        try {
            CustomerMscUserPoiInfoResponse customerMscUsedPoiResponse = wmCustomerThriftService.getMscUsedPoiInfoByCustomerIdV2(customerId);
            logger.info("getMscUsedPoiDTOV2,customerId={},resp={}", customerId, JSON.toJSONString(customerMscUsedPoiResponse));
            return customerMscUsedPoiResponse;
        } catch (Exception e) {
            logger.error("getMscUsedPoiDTOV2,根据客户ID查询已占用档口数信息V2发生异常,customerId={}", customerId, e);
        }
        return null;
    }


    public List<DcC2ContractSignerVo> getDcC2ContractSignerVoList(Integer wmCustomerId) throws TException, WmCustomerException {
        CustomerSignerQueryDTO queryDTO = new CustomerSignerQueryDTO();
        queryDTO.setWmCustomerId(wmCustomerId);
        queryDTO.setBusinessTypeEnum(BusinessTypeEnum.DAOCAN);
        // 查询到餐客户kp信息
        List<WmCustomerKp> customerKpList = wmCustomerKpThriftService.getCustomerSignerEffectiveByBusiness(queryDTO);
        return transToDcC2ContractSignerVoList(customerKpList, wmCustomerId);
    }

    private List<DcC2ContractSignerVo> transToDcC2ContractSignerVoList(List<WmCustomerKp> customerKpList, Integer customerId) throws TException, WmCustomerException {
        if (CollectionUtils.isEmpty(customerKpList)) {
            return Collections.emptyList();
        }

        List<DcC2ContractSignerVo> dcC2ContractSignerVoList = new ArrayList<>();
        for (WmCustomerKp wmCustomerKp : customerKpList) {
            DcC2ContractSignerVo dcC2ContractSignerVo = new DcC2ContractSignerVo();
            dcC2ContractSignerVo.setSignerId(wmCustomerKp.getId());
            dcC2ContractSignerVo.setSignPhone(DesensitizeUtil.desensitizePhone(wmCustomerKp.getPhoneNum()));
            dcC2ContractSignerVo.setSignName(wmCustomerKp.getCompellation());
            dcC2ContractSignerVo.setMtCustomerId(wmCustomerKp.getMtCustomerId());

            String customerName = queryDcCustomerName(wmCustomerKp.getMtCustomerId());
            dcC2ContractSignerVo.setCustomerName(customerName);

            dcC2ContractSignerVoList.add(dcC2ContractSignerVo);
        }
        logger.info("transToDcC2ContractSignerVoList, {}",JSON.toJSONString(dcC2ContractSignerVoList));
        return dcC2ContractSignerVoList;
    }

    /**
     * 查询到餐客户名称
     * @param mtCustomerId
     * @return
     */
    private String queryDcCustomerName(Long mtCustomerId) {
        if (mtCustomerId == null || mtCustomerId <= 0) {
            return null;
        }
        try {
            DcCustomerInfoRequestDTO requestDTO = new DcCustomerInfoRequestDTO();
            requestDTO.setMtCustomerId(mtCustomerId);
            BaseResponse<DcContractCustomerVo> resp = wmCustomerFrameContractThriftService.queryDcCustomerInfo(requestDTO);
            logger.info("queryDcCustomerName resp:{}",JSON.toJSON(resp));
            if (resp != null && resp.getData() != null) {
                DcContractCustomerVo customerVo = resp.getData();
                return customerVo.getCustomerName();
            }
        } catch (WmCustomerException e) {
            logger.error("queryDcCustomerName,查询到餐客户名称发生异常,mtCustomerId={}", mtCustomerId, e);
        }

        return null;
    }

    public Map<String, String> decryptSensitiveResource(List<String> decryptKeys, Integer uid) throws TException, WmCustomerException {
        com.sankuai.waimai.crm.authenticate.client.auth.handler.SensitiveResourceView sensitiveResourceView =
                com.sankuai.waimai.crm.authenticate.client.auth.service.CrmAuth.newSensitiveResourceView(null);
        List<SensitiveResourceDecryptResponseVO> sensitiveResourceDecryptResponseVOS = sensitiveResourceView.viewSensitiveResource(decryptKeys, uid);
        //logger.info("decryptSensitiveResource 解密结果, sensitiveResourceDecryptResponseVOS:{}",
         //       JSON.toJSONString(sensitiveResourceDecryptResponseVOS));
        Map<String, String> result = new HashMap<>();
        for (SensitiveResourceDecryptResponseVO sensitiveResourceDecryptResponseVO : sensitiveResourceDecryptResponseVOS) {
            if (!sensitiveResourceDecryptResponseVO.getDecryptStatus().equals(DecryptStatusEnum.SUCCESS)) {
                throw new WmCustomerException(-1, sensitiveResourceDecryptResponseVO.getDecryptStatus().desc());
            }
            result.put(sensitiveResourceDecryptResponseVO.getDecryptKey(), sensitiveResourceDecryptResponseVO.getValue());
        }
        return result;
    }

    public void decryptCustomerInfo(CustomerInfo customerInfo, Integer uid) throws TException, WmCustomerException {
        if (customerInfo == null) {
            return;
        }
        List<String> decryptKeys = new ArrayList<>();
        if (Objects.nonNull(customerInfo.getCustomerNameEncrypt()) && customerInfo.getCustomerNameEncrypt()) {
            decryptKeys.add(customerInfo.getCustomerName_sensitive_view_key_info());
        }
        if (Objects.nonNull(customerInfo.getCustomerNumberEncrypt()) && customerInfo.getCustomerNumberEncrypt()) {
            decryptKeys.add(customerInfo.getCustomerNumber_sensitive_view_key_info());
        }
        if (Objects.nonNull(customerInfo.getPicUrlEncrypt()) && customerInfo.getPicUrlEncrypt()) {
            decryptKeys.add(customerInfo.getPicUrl_sensitive_view_key_info());
        }
        if (Objects.nonNull(customerInfo.getLegalPersonEncrypt()) && customerInfo.getLegalPersonEncrypt()) {
            decryptKeys.add(customerInfo.getLegalPerson_sensitive_view_key_info());
        }
        if (Objects.nonNull(customerInfo.getAddressEncrypt()) && customerInfo.getAddressEncrypt()) {
            decryptKeys.add(customerInfo.getAddress_sensitive_view_key_info());
        }
        if (CollectionUtils.isEmpty(decryptKeys)) {
            return;
        }
        logger.info("decryptCustomerInfo 开始解密, decryptKeys:{}, uid:{}", decryptKeys, uid);
        Map<String, String> stringStringMap = decryptSensitiveResource(decryptKeys, uid);
        logger.info("decryptCustomerInfo 解密结果, stringStringMap:{}", stringStringMap);
        if (Objects.nonNull(customerInfo.getCustomerNameEncrypt()) && customerInfo.getCustomerNameEncrypt()) {
            customerInfo.setCustomerName(stringStringMap.get(customerInfo.getCustomerName_sensitive_view_key_info()));
        }
        if (Objects.nonNull(customerInfo.getCustomerNumberEncrypt()) && customerInfo.getCustomerNumberEncrypt()) {
            customerInfo.setCustomerNumber(stringStringMap.get(customerInfo.getCustomerNumber_sensitive_view_key_info()));
        }
        if (Objects.nonNull(customerInfo.getPicUrlEncrypt()) && customerInfo.getPicUrlEncrypt()) {
            customerInfo.setPicUrl(stringStringMap.get(customerInfo.getPicUrl_sensitive_view_key_info()));
        }
        if (Objects.nonNull(customerInfo.getLegalPersonEncrypt()) && customerInfo.getLegalPersonEncrypt()) {
            customerInfo.setLegalPerson(stringStringMap.get(customerInfo.getLegalPerson_sensitive_view_key_info()));
        }
        if (Objects.nonNull(customerInfo.getAddressEncrypt()) && customerInfo.getAddressEncrypt()) {
            customerInfo.setAddress(stringStringMap.get(customerInfo.getAddress_sensitive_view_key_info()));
        }
    }
}

package com.sankuai.meituan.waimai.service.poi;

import com.google.common.collect.Lists;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.waimai.alarm.constant.AlarmPriority;
import com.sankuai.meituan.waimai.poiaudit.thrift.constants.WmPoiAuditConstants;
import com.sankuai.meituan.waimai.poiaudit.thrift.constants.WmPoiProduceSection;
import com.sankuai.meituan.waimai.poiaudit.thrift.constants.WmPoiProduceStatus;
import com.sankuai.meituan.waimai.poiaudit.thrift.domain.WmPoiProduceOplog;
import com.sankuai.meituan.waimai.poiaudit.thrift.exception.WmPoiAuditException;
import com.sankuai.meituan.waimai.poiaudit.thrift.service.WmPoiProduceOplogThriftService;
import com.sankuai.meituan.waimai.poitrace.domain.DeviceType;
import com.sankuai.meituan.waimai.util.CrmEmsAlarmUtil;
import com.sankuai.meituan.waimai.util.base.WebUaUtil;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ProduceNotifyService {

    private static Logger logger = LoggerFactory.getLogger(ProduceNotifyService.class);

    @Autowired
    private WmPoiProduceOplogThriftService.Iface wmPoiProduceOplogThriftService;

    public static final int BASE_INFO = 1;
    public static final int QUALIFICATION = 2;
    public static final int CONTRACT = 3;
    public static final int SERVICE_INFO = 4;
    public static final int SHIPPING_INFO = 5;
    public static final int PRODUCT = 6;

    public static final int INSERT = 0;
    public static final int COMMIT_AUDIT = 1;
    public static final int PASS = 2;
    public static final int REJECT = 3;
    public static final int DELETE = 4;

    public static final List<String> MESSAGE_TO = Lists.newArrayList("<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>");

    private static final int BASE_CODE = 10;

    public void notifyInsert(long wmPoiId, int type, byte source) {
        int opCode = type * BASE_CODE + INSERT;
        oplogNotify(wmPoiId, opCode, "", source);
    }

    public void notifyInsert(long wmPoiId, int type) {
        notifyInsert(wmPoiId, type, getSource(wmPoiId));
    }

    public void notifyCommitAudit(long wmPoiId, int type) {
        int opCode = type * BASE_CODE + COMMIT_AUDIT;
        oplogNotify(wmPoiId, opCode, "");
    }

    public void notifyPass(long wmPoiId, int type) {
        int opCode = type * BASE_CODE + PASS;
        oplogNotify(wmPoiId, opCode, "");
    }

    public void notifyDelete(long wmPoiId, int type) {
        int opCode = type * BASE_CODE + DELETE;
        oplogNotify(wmPoiId, opCode, "");
    }

    public void notifyReject(long wmPoiId, int type) {
        int opCode = type * BASE_CODE + REJECT;
        oplogNotify(wmPoiId, opCode, "");
    }

    public void notifyReject(long wmPoiId, int type, String remark) {
        int opCode = type * BASE_CODE + REJECT;
        oplogNotify(wmPoiId, opCode, remark);
    }

    private void oplogNotify(long wmPoiId, int opCode, String remark) {
        oplogNotify(wmPoiId, opCode, remark, getSource(wmPoiId));
    }

    private void oplogNotify(long wmPoiId, int opCode, String remark, byte source) {
        logger.info("发送流程数据 wmPoiId={}, opCode={}", wmPoiId, opCode);
        WmPoiProduceOplog wmPoiProduceOplog = new WmPoiProduceOplog();
        wmPoiProduceOplog.setWm_poi_id((int) wmPoiId);
        wmPoiProduceOplog.setOp_type(opCode);
        wmPoiProduceOplog.setOp_uid(UserUtils.getUser().getId());
        wmPoiProduceOplog.setOp_uname(UserUtils.getUser().getName());
        wmPoiProduceOplog.setRemark(remark);
        wmPoiProduceOplog.setSource(source);

        boolean success = false;
        for (int cnt = 0; cnt < 10; ++cnt) {
            if (success) {
                break;
            } else {
                try {
                    wmPoiProduceOplogThriftService.recordOplog(wmPoiProduceOplog);
                    success = true;
                } catch (WmPoiAuditException e) {
                    logger.warn("该异常为正常现象", e);
                    success = true;
                } catch (TException e) {
                    logger.warn("服务化链接异常", e);
                }
            }
        }

        if (!success) {
            String message = "wmPoiId = " + wmPoiId + ", opCode = " + opCode + " 写入produce异常";
//            DaxiangUtil.push("<EMAIL>", message, MESSAGE_TO);
            //EMS告警中心
            CrmEmsAlarmUtil.sendAlarmMessage(AlarmPriority.P2, CrmEmsAlarmUtil.CRM_POI_QUEENBEE_PRODUCE_WRITE_ERROR, message);
        }
    }

    public void notifyReject(long wmPoiId, int type, String remark, int opUid, String opUname) {
        int opCode = type * BASE_CODE + REJECT;
        oplogNotifyWithOpUidAndRemark(wmPoiId, opCode, remark, opUid, opUname);
    }

    private void oplogNotifyWithOpUidAndRemark(long wmPoiId, int opCode, String remark, int opUid, String opUname) {
        logger.info("发送流程数据 wmPoiId={}, opCode={}", wmPoiId, opCode);
        WmPoiProduceOplog wmPoiProduceOplog = new WmPoiProduceOplog();
        wmPoiProduceOplog.setWm_poi_id((int) wmPoiId);
        wmPoiProduceOplog.setOp_type(opCode);
        wmPoiProduceOplog.setOp_uid(opUid);
        wmPoiProduceOplog.setOp_uname(opUname);
        wmPoiProduceOplog.setRemark(remark);

        boolean success = false;
        for (int cnt = 0; cnt < 10; ++cnt) {
            if (success) {
                break;
            } else {
                try {
                    wmPoiProduceOplogThriftService.recordOplog(wmPoiProduceOplog);
                    success = true;
                } catch (WmPoiAuditException e) {
                    logger.warn("该异常为正常现象", e);
                    success = true;
                } catch (TException e) {
                    logger.warn("服务化链接异常", e);
                }
            }
        }

        if (!success) {
            String message = "wmPoiId = " + wmPoiId + ", opCode = " + opCode + " 写入produce异常";
//            DaxiangUtil.push("<EMAIL>", message, MESSAGE_TO);
            //EMS告警中心
            CrmEmsAlarmUtil.sendAlarmMessage(AlarmPriority.P2, CrmEmsAlarmUtil.CRM_POI_QUEENBEE_PRODUCE_WRITE_ERROR, message);
        }
    }


    private byte getSource(long wmPoiId) {

        byte source = -1;
        DeviceType deviceType = WebUaUtil.getDeviceType();

        if(deviceType == DeviceType.PC) {
            source = WmPoiAuditConstants.SOURCE_SUPPLY_CHAIN_PC;
        } else if(deviceType == DeviceType.APP) {
            source = WmPoiAuditConstants.SOURCE_SUPPLY_CHAIN_APP;
        }

        return source;
    }

    private void oplogNotifyWithRemark(long wmPoiId,int section,int opType,int opUid, String opUname, String remark) {
        logger.info("发送流程数据 wmPoiId={}, section={},opType={}", wmPoiId, section,opType);
        WmPoiProduceOplog wmPoiProduceOplog = new WmPoiProduceOplog();
        wmPoiProduceOplog.setWm_poi_id((int) wmPoiId);
        wmPoiProduceOplog.setSection(section);
        wmPoiProduceOplog.setOp_type(opType);
        wmPoiProduceOplog.setOp_uid(opUid);
        wmPoiProduceOplog.setOp_uname(opUname);
        wmPoiProduceOplog.setRemark(remark);

        boolean success = false;
        for (int cnt = 0; cnt < 10; ++cnt) {
            if (success) {
                break;
            } else {
                try {
                    wmPoiProduceOplogThriftService.recordOplog(wmPoiProduceOplog);
                    success = true;
                } catch (WmPoiAuditException e) {
                    logger.warn("该异常为正常现象 code={}, message = {}", e.getCode(), e.getMsg());
                    logger.warn("", e);
                    success = true;
                } catch (TException e) {
                    logger.warn("服务化链接异常", e);
                }
            }
        }
    }

    public void notifyInsertWithRemark(long wmPoiId,int section,int opUid, String opUname, String remark){
        oplogNotifyWithRemark(wmPoiId,section,WmPoiProduceStatus.INPUT,opUid,opUname,remark);
    }

    public void notifyToConfirmingWithRemark(long wmPoiId,int section,int opUid, String opUname, String remark){
        oplogNotifyWithRemark(wmPoiId,section,WmPoiProduceStatus.CONFIRMING,opUid,opUname,remark);
    }

    public void notifyEffectWithRemark(long wmPoiId,int section,int opUid, String opUname, String remark){
        oplogNotifyWithRemark(wmPoiId,section,WmPoiProduceStatus.CONFIRM_OK,opUid,opUname,remark);
    }

    public void notifyCancelWithRemark(long wmPoiId,int section,int opUid, String opUname, String remark){
        oplogNotifyWithRemark(wmPoiId,section,WmPoiProduceStatus.CONFIRM_NO,opUid,opUname,remark);
    }

    public void notifyDeleteWithRemark(long wmPoiId,int section,int opUid, String opUname, String remark){
        oplogNotifyWithRemark(wmPoiId,section,WmPoiProduceStatus.DELETED,opUid,opUname,remark);
    }

    public void fixSettleOplog(List<Integer> wmPoiIdList) throws Exception {
        WmPoiProduceOplog latestRecordByPoint = null;
        for (Integer temp : wmPoiIdList) {
            latestRecordByPoint = wmPoiProduceOplogThriftService
                    .getLatestRecordByPoint(temp.longValue(), WmPoiProduceSection.SETTLEMENT);


            if (latestRecordByPoint != null && latestRecordByPoint.getOp_type() == 73) {
                //4
                notifyDeleteWithRemark(temp,WmPoiProduceSection.SETTLEMENT,0,"","fix");
                //0
                notifyInsertWithRemark(temp,WmPoiProduceSection.SETTLEMENT,0,"","fix");
                //1
                notifyToConfirmingWithRemark(temp,WmPoiProduceSection.SETTLEMENT,0,"","fix");
                //3
                notifyCancelWithRemark(temp,WmPoiProduceSection.SETTLEMENT,0,"","fix");
            }

            if(latestRecordByPoint != null && latestRecordByPoint.getOp_type() == 72){
                    //4
                    notifyDeleteWithRemark(temp,WmPoiProduceSection.SETTLEMENT,0,"","fix");
                    //0
                    notifyInsertWithRemark(temp,WmPoiProduceSection.SETTLEMENT,0,"","fix");
                    //1
                    notifyToConfirmingWithRemark(temp,WmPoiProduceSection.SETTLEMENT,0,"","fix");
                    //2
                    notifyEffectWithRemark(temp,WmPoiProduceSection.SETTLEMENT,0,"","fix");
            }

            if(latestRecordByPoint != null && latestRecordByPoint.getOp_type() == 71){
                //4
                notifyDeleteWithRemark(temp,WmPoiProduceSection.SETTLEMENT,0,"","fix");
                //0
                notifyInsertWithRemark(temp,WmPoiProduceSection.SETTLEMENT,0,"","fix");
                //1
                notifyToConfirmingWithRemark(temp,WmPoiProduceSection.SETTLEMENT,0,"","fix");
            }

//            if (latestRecordByPoint == null) {
//                //插入70,71
//                notifyInsertWithRemark(temp,WmPoiProduceSection.SETTLEMENT,0,"","fix");
//                notifyToConfirmingWithRemark(temp,WmPoiProduceSection.SETTLEMENT,0,"","fix");
//            } else {
//                if(latestRecordByPoint.getOp_type() == 72){
//                    notifyDeleteWithRemark(temp,WmPoiProduceSection.SETTLEMENT,0,"","fix");
//                    notifyInsertWithRemark(temp,WmPoiProduceSection.SETTLEMENT,0,"","fix");
//                    notifyToConfirmingWithRemark(temp,WmPoiProduceSection.SETTLEMENT,0,"","fix");
//                    notifyEffectWithRemark(temp,WmPoiProduceSection.SETTLEMENT,0,"","fix");
//                }
//            }
        }
    }


}

package com.sankuai.meituan.waimai.service;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.org.opensdk.model.domain.Emp;
import com.sankuai.meituan.org.opensdk.model.domain.items.EmpItems;
import com.sankuai.meituan.org.opensdk.service.EmpService;
import com.sankuai.meituan.org.queryservice.domain.base.Paging;
import com.sankuai.meituan.org.queryservice.exception.MDMThriftException;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import com.sankuai.meituan.waimai.infra.service.WmEmployService;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class EmpServiceAdaptor {

    static Logger logger = LoggerFactory.getLogger(EmpServiceAdaptor.class);

    @Autowired
    private EmpService empService;

    @Resource
    private WmEmployService.Iface wmEmployService;

    public String getPhone(String misId) {
        String mobile = null;
        try {
            Emp emp = empService.queryByMis(misId, null);
            mobile = emp == null ? "" : empService.empDataDecode(emp.getEmpId(), "mobile");
        } catch (MDMThriftException e) {
            logger.error("查询手机号失败", e);
        }
        return mobile;
    }

    public String getPhone(int uid) {
        String mobile = null;
        try {
            mobile = empService.empDataDecode(String.valueOf(uid), "mobile");
        } catch (MDMThriftException e) {
            logger.error("查询手机号失败", e);
        }
        return mobile;
    }

    public List<String> queryMisIdsByOrgId(String orgId) {
        List<String> misIds = new ArrayList<>();
        try {
            EmpItems empItems = empService.queryEmp(orgId, 0, null, new Paging(0, Paging.MAX_SIZE));
            if (empItems != null && CollectionUtils.isNotEmpty(empItems.getItems())) {
                for (Emp emp : empItems.getItems()) {
                    misIds.add(emp.getMis());
                }
            }
        } catch (Exception e) {
            logger.error("根据组织id查询组织下mis号列表失败 orgId:{}", orgId, e);
        }
        return misIds;
    }

    /**
     * 根据UID查询用户信息
     * @param uid UID
     * @return 用户信息
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public WmEmploy getWmEmployByUid(Integer uid) throws WmSchCantException {
        try {
            return wmEmployService.getById(uid);
        } catch (WmServerException e) {
            logger.error("[EmpServiceAdaptor.getWmEmployByUid] WmServerException. uid = {}", uid, e);
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "查询用户信息异常");
        } catch (Exception e) {
            logger.error("[EmpServiceAdaptor.getWmEmployByUid] Exception. uid = {}", uid, e);
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "查询用户信息异常");
        }
    }

    /**
     * 根据UID列表批量查询用户信息Map. key->uid val->WmEmploy
     * @param uids UID列表
     * @return 用户信息列表
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public Map<Integer, WmEmploy> getEmployMapByUids(List<Integer> uids) throws WmSchCantException {
        try {
            List<WmEmploy> employList =  wmEmployService.mgetByUids(uids);
            Map<Integer, WmEmploy> resMap = new HashMap<>();
            for (WmEmploy employ : employList) {
                resMap.put(employ.getUid(), employ);
            }

            return resMap;
        } catch (WmServerException e) {
            logger.error("[EmpServiceAdaptor.getEmployMapByUids] WmServerException. uids = {}", JSONObject.toJSONString(uids), e);
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "查询用户信息异常");
        } catch (Exception e) {
            logger.error("[EmpServiceAdaptor.getEmployMapByUids] Exception. uids = {}", JSONObject.toJSONString(uids), e);
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "查询用户信息异常");
        }
    }

}

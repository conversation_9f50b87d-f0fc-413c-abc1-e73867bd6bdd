package com.sankuai.meituan.waimai.service.adaptor;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.infra.constants.WmUniAorType;
import com.sankuai.meituan.waimai.infra.domain.*;
import com.sankuai.meituan.waimai.infra.service.WmOpenCityService;
import com.sankuai.meituan.waimai.infra.service.WmUniAorService;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import com.sankuai.meituan.waimai.vo.customer.AorVo;
import com.sankuai.meituan.waimai.vo.customer.SearchAorVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Iterator;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class AorInfoAdaptor {
    @Autowired
    private WmUniAorService.Iface wmUniAorService;
    @Autowired
    private WmOpenCityService.Iface wmOpenCityService;

    public SearchAorVo searchAorIdByName(int aorBiz, String aorName){
        log.info("#searchAorIdByName,aorBiz={},aorName={}",aorBiz,aorName);
        WmUniAorFindParam param = new WmUniAorFindParam();
        if (StringUtils.isNumeric(aorName)) {
            param.setIds(Lists.newArrayList(Integer.valueOf(aorName)));
        } else {
            param.setFuzzyName(aorName);
        }
        WmUniAorSearchOptions options = new WmUniAorSearchOptions();
        options.setWithWmUniAorType(true);
        options.setWmUniAorType(aorBiz);
        param.setOptions(options);
        List<WmUniAor> searchResult = Lists.newArrayList();
        try {
            searchResult = wmUniAorService.findAor(param);
            log.info("#searchAorIdByName,searchResult={}", JSONObject.toJSONString(searchResult));
        } catch (WmServerException | TException e) {
            log.error("findAor exception",e);
        }
        Iterator<WmUniAor> iterator = searchResult.iterator();
        WmUniAor temp;
        while(iterator.hasNext()){
            temp = iterator.next();
            if(temp.getType() != aorBiz){
                iterator.remove();
            }
        }
        return transToSearchAorVo(searchResult);
    }

    public AorVo getAorInfoByAorIdV2(int aorBiz, String aorId) {
        log.info("#getAorInfoByAorIdV2,aorBiz={},aorId={}",aorBiz,aorId);
        int aorIdInt = Integer.parseInt(aorId);
        WmUniAor aorById = null;
        try {
            aorById = wmUniAorService.getAorById(aorIdInt);
            log.info("#getAorById={}", JSONObject.toJSONString(aorById));
        } catch (WmServerException | TException e) {
            log.error("findAor exception",e);
        }
        if(aorById == null || aorById.getType() != aorBiz){
            return null;
        }

        return transToAorVo(aorBiz,Lists.newArrayList(aorById));
    }

    private AorVo transToAorVo(int aorBiz,List<WmUniAor> searchResult) {
        if(CollectionUtils.isEmpty(searchResult)){
            return null;
        }
        AorVo result = new AorVo();
        WmUniAor wmUniAor = searchResult.get(0);
        WmUniAorLocationInfo locationInfos = wmUniAor.getLocationInfos();
        List<Integer> cityIds = null;


        if(locationInfos == null){
            cityIds = null;
        }
        else if(aorBiz == WmUniAorType.ZY.code()){
            cityIds = locationInfos.getLevel2CityIds();
        }
        else if(aorBiz == WmUniAorType.DLS.code()){
            cityIds = locationInfos.getLevel3CityIds();
        }
        if(CollectionUtils.isNotEmpty(cityIds)){
            try {
                WmOpenCity byCityId = wmOpenCityService.getByCityId(cityIds.get(0));
                log.info("#transToAorVo,byCityId={}",JSONObject.toJSONString(byCityId));
                result.setOriginCityName(byCityId.getCityName());
            } catch (WmServerException | TException e) {
                log.error("getByCityId exception",e);
            }
        }
        result.setAorId(wmUniAor.getId());
        result.setAorName(wmUniAor.getName());
        // 默认false
        result.setLevel2CityIsBeiJing(false);
        if (locationInfos != null) {
            List<Integer> level2CityIds = locationInfos.getLevel2CityIds();
            if (CollectionUtils.isNotEmpty(level2CityIds)) {
                result.setLevel2CityIsBeiJing(Objects.equals(level2CityIds.get(0), MccCustomerConfig.getBeijingAorId()));
            }
        }

        return result;
    }

    private SearchAorVo transToSearchAorVo(List<WmUniAor> searchResult) {
        SearchAorVo result = new SearchAorVo();
        List<AorVo> aorVoList = result.getAorList();
        for(WmUniAor temp : searchResult){
            aorVoList.add(new AorVo(temp.getId(),temp.getName()));
        }
        return result;
    }


}

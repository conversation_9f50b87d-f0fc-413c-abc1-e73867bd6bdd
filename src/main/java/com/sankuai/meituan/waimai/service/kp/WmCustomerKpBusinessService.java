package com.sankuai.meituan.waimai.service.kp;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.auth.vo.User;
import com.sankuai.meituan.waimai.config.MccConfig;
import com.sankuai.meituan.waimai.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.constant.LegalAuthOperateTypeEnum;
import com.sankuai.meituan.waimai.mtauth.controlview.keyinfo.KeyField;
import com.sankuai.meituan.waimai.mtauth.controlview.keyinfo.KeyInfoUtils;
import com.sankuai.meituan.waimai.service.agreement.AggrementAuthService;
import com.sankuai.meituan.waimai.service.customer.WmCustomerCheckAuthService;
import com.sankuai.meituan.waimai.thrift.customer.constant.*;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.KpVersionEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.CustomerKpLegalAuthBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.CustomerKpLegalAuthRequest;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKpBusinessBO;
import com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerKpThriftService;
import com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerThriftService;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.util.base.DesensitizeUtil;
import com.sankuai.meituan.waimai.util.base.WmRestReturnUtil;
import com.sankuai.meituan.waimai.vo.customer.WmCustomerAuditEditInfo;
import com.sankuai.meituan.waimai.vo.kp.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: xhwang
 * @Date: 2022/8/25
 * @Description: 处理商家端kp相关请求
 **/
@Slf4j
@Service
public class WmCustomerKpBusinessService {

    @Autowired
    private WmCustomerKpService wmCustomerKpService;


    @Autowired
    private AggrementAuthService aggrementAuthService;

    @Autowired
    private WmCustomerKpThriftService wmCustomerKpThriftService;

    @Autowired
    private WmCustomerThriftService wmCustomerThriftService;

    @Autowired
    private HttpServletRequest request;

    @Autowired
    private WmCustomerCheckAuthService wmCustomerCheckAuthService;

    private static final List<KeyField> keyFields;

    static {
        keyFields = ImmutableList.of(
                KeyField.of("legalKpPhoneNum", KeyField.Type.PHONE)
        );
    }


    /**
     * 根据当前用户及门店查询用户签约人页签权限
     *
     * @param wmPoiId
     * @return
     */
    public JSONObject getSignAuth(Long wmPoiId) {
        try {
            return WmRestReturnUtil.success(isUserAuth(wmPoiId));
        } catch (Exception e) {
            log.warn("查询当前页签权限异常");
            return WmRestReturnUtil.fail(e.getMessage());
        }
    }

    /**
     * 通过cookie获取登录账号
     *
     * @return
     */
    public int getLoginAcctId() {
        String acctId = aggrementAuthService.getAcctIdInCookie(request);
        if (StringUtils.isBlank(acctId)) {
            return 0;
        }
        return Integer.parseInt(acctId);
    }

    /**
     * 通过Cookie获取当前用户门店ID
     *
     * @return
     */
    public int getLoginWmPoiId() {
        String poiId = aggrementAuthService.getWmPoiIdInCookie(request);
        if (StringUtils.isBlank(poiId)) {
            return 0;
        }
        return Integer.parseInt(poiId);
    }


    /**
     * 校验商家端有权限操作的客户可KP信息
     * 客户需要为单店类型，KP为已生效签约人类型
     *
     * @return
     */
    public WmCustomerKpBusinessBO isUserAuth(Long wmPoiId) {
        int poiId = getLoginWmPoiId();
        int acctId = getLoginAcctId();
        WmCustomerKpBusinessBO kpBusinessBO = new WmCustomerKpBusinessBO();
        kpBusinessBO.setAuth(false);
        kpBusinessBO.setLegalKpShow(false);
        kpBusinessBO.setLegalKpId(null);
        kpBusinessBO.setAcctId(acctId);
        kpBusinessBO.setAcctName("商家修改_PC端_账号" + acctId);
        if (wmPoiId == null || wmPoiId == 0) {
            wmPoiId = (long) poiId;
        }
        log.info("是否可以查询签约人页签: wmPoiId:{}, acctId:{}", wmPoiId, acctId);
        if (wmPoiId == null || wmPoiId < 1) {
            kpBusinessBO.setMess("当前无权限查看签约人信息");
            return kpBusinessBO;
        }
        //鉴权
        if (!aggrementAuthService.checkPoiAggrementAuthByWmPoiId(wmPoiId, request)) {
            kpBusinessBO.setMess("当前无权限查看签约人信息");
            return kpBusinessBO;
        }
        //查询当前用户对应的客户和kp信息,客户为单店类型，KP为已生效
        kpBusinessBO = getKpIdByWmPoiId(wmPoiId);
        if (kpBusinessBO == null || kpBusinessBO.getKpId() == null || kpBusinessBO.getKpId() < 1
                || kpBusinessBO.getCustomerId() == null || kpBusinessBO.getCustomerId() < 1) {
            kpBusinessBO.setMess("未查询到有效签约人信息");
            return kpBusinessBO;
        }
        kpBusinessBO.setAuth(true);
        kpBusinessBO.setAcctId(acctId);
        kpBusinessBO.setAcctName("商家修改_PC端_账号" + acctId);
        kpBusinessBO.setWmPoiId(wmPoiId.intValue());
        kpBusinessBO.setLegalKpShow(false);

        CustomerKpLegalAuthRequest kpLegalAuthRequest = new CustomerKpLegalAuthRequest();
        kpLegalAuthRequest.setOpSource(CustomerDeviceType.BUSINESS_PC.getCode());
        kpLegalAuthRequest.setWmPoiId(wmPoiId.intValue());
        kpLegalAuthRequest.setCustomerId(kpBusinessBO.getCustomerId());

        try {
            //添加KP类型为法人的数据展示
            Integer legalKpId = getLegalKpId(kpBusinessBO.getCustomerId());
            if (kpBusinessBO.isAuth() && legalKpId != null) {
                kpBusinessBO.setLegalKpShow(true);
                kpBusinessBO.setLegalKpId(legalKpId);
            }
        } catch (Exception e) {
            log.error("商家端查询KP类型法人的数据发生异常，不暂时KP类型法人的tab,customerId={}", kpBusinessBO.getCustomerId(), e);
        }
        return kpBusinessBO;
    }

    /**
     * 判断当前商家端用户是否有kp操作权限
     * 仅限于单店类型客户
     *
     * @param kpId
     * @return
     */
    public boolean hasBusinessAuth(Integer kpId) {
        if (kpId == null || kpId < 1) {
            return false;
        }
        String poiId = aggrementAuthService.getWmPoiIdInCookie(request);
        Long wmPoiId = 0L;
        if (StringUtils.isNotBlank(poiId) && StringUtils.isNumeric(poiId)) {
            wmPoiId = Long.parseLong(poiId);
        }
        if (wmPoiId < 1) {
            return false;
        }
        WmCustomerKpBusinessBO kpBusinessBO = getKpIdByWmPoiId(wmPoiId);
        if (kpBusinessBO == null || kpBusinessBO.getCustomerId() == null || kpBusinessBO.getCustomerId() < 1
                || kpBusinessBO.getKpId() == null || kpBusinessBO.getKpId() < 1) {
            return false;
        }
        return (kpBusinessBO.getKpId().equals(kpId) || kpBusinessBO.getLegalKpId().equals(kpId));
    }


    /**
     * 根据wmPoiId查询Kp和客户ID信息
     * 不能保证都有签约人KP信息
     * 底层有门店城市 灰度逻辑
     *
     * @return
     */
    public WmCustomerKpBusinessBO getKpIdByWmPoiId(Long wmPoiId) {
        if (wmPoiId == null || wmPoiId < 1) {
            return null;
        }
        try {
            WmCustomerKpBusinessBO businessBO = wmCustomerKpThriftService.getSignerAuth(wmPoiId);
            log.info("通过wmPoi:{}查询关联客户和KP信息完成, businessBO:{}", wmPoiId, JSON.toJSONString(businessBO));
            return businessBO;
        } catch (Exception e) {
            log.error("根据KP信息查询客户和Kp信息异常, wmPoiId:{}", wmPoiId);
            return null;
        }
    }

    /**
     * 查询法人kpId
     *
     * @param customerId
     * @return
     */
    public Integer getLegalKpId(Integer customerId) {
        if (customerId == null || customerId < 1) {
            return null;
        }
        try {
            WmCustomerKp legalKp = wmCustomerKpThriftService.getLatestLegalKp(customerId);
            if (legalKp == null) {
                return null;
            }
            log.info("getLegalKpId，查询客户的KP类型为法人ID数据, legalKp:{},customerId={}", JSON.toJSONString(legalKp), customerId);
            return legalKp.getId();
        } catch (Exception e) {
            log.error("getLegalKpId，查询客户的KP类型为法人ID数据异常, customerId:{}", customerId, e);
            return null;
        }
    }


    /**
     * 商家端查看签约人信息
     * 单店类型
     *
     * @param kpId
     * @return
     */
    public JSONObject getSignerKpInfo(Integer kpId, Integer opSource) {
        if (!hasBusinessAuth(kpId)) {
            return WmRestReturnUtil.fail("当前无权限查看签约人信息");
        }
        try {
            WmCustomerKp wmCustomerKp = wmCustomerKpThriftService.getCustomerKpInfo(kpId);
            log.info("WmCustomerKpBusinessService::getSignerKpInfo#kp查询结果:{},opSource={},kpId={}", JSON.toJSONString(wmCustomerKp), opSource, kpId);
            if (wmCustomerKp == null) {
                return WmRestReturnUtil.fail("未查询到有效KP信息");
            }
            // model转vo
            WmCustomerKpDetailVo wmCustomerKpDetailVo = wmCustomerKpService.transformByWmCustomerKp(wmCustomerKp);
            //脱敏
            if (CertTypeEnum.getOtherDesensitizeMap().containsKey(wmCustomerKpDetailVo.getCertType())) {
                wmCustomerKpDetailVo.setDesenCertNumber(DesensitizeUtil.desensitizeOtherCode(wmCustomerKpDetailVo.getCertNumber()));
            } else {
                wmCustomerKpDetailVo.setDesenCertNumber(DesensitizeUtil.desensitizeCode(wmCustomerKpDetailVo.getCertNumber()));
            }
            wmCustomerKpDetailVo.setDesenCreditCard(DesensitizeUtil.desensitizeBankNum(wmCustomerKpDetailVo.getCreditCard()));
            wmCustomerKpDetailVo.setDesenPhoneNum(DesensitizeUtil.desensitizePhone(wmCustomerKpDetailVo.getPhoneNum()));

            wmCustomerKpDetailVo.setAuthInfo(wmCustomerCheckAuthService.getBusinessCustomerKpListDetailAuth());
            return WmRestReturnUtil.success(wmCustomerKpDetailVo);
        } catch (WmCustomerException e) {
            log.warn("获取签约人KP信息异常, kpId={}", kpId, e);
            return WmRestReturnUtil.fail("获取签约人KP信息异常:" + e.getMsg());
        } catch (Exception e) {
            log.error("获取签约人KP信息异常, kpId={}", kpId, e);
            return WmRestReturnUtil.fail("获取签约人KP信息异常");
        }
    }

    /**
     * 商家端修改签约人信息
     *
     * @param customerKpModifyVo
     * @return
     */
    public JSONObject businessModifyKpInfo(WmCustomerKpModifyVo customerKpModifyVo) {
        log.info("WmCustomerKpBusinessService::modifyKpInfo：{},", JSON.toJSONString(customerKpModifyVo));
        //校验权限
        WmCustomerKpBusinessBO businessBO = isUserAuth(0L);
        if (!businessBO.isAuth()) {
            return WmRestReturnUtil.fail(businessBO.getMess());
        }
        WmCustomerKpVo wmCustomerKpVo = customerKpModifyVo.getKp();
        if (null == wmCustomerKpVo) {
            return WmRestReturnUtil.fail("待操作KP数据不能为空");
        }

        //校验参数
        WmCustomerKpValidateVO validateVO = validateKpUpdateInfo(customerKpModifyVo, businessBO);
        if (!validateVO.isValid()) {
            return WmRestReturnUtil.fail(validateVO.getMsg());
        }

        int wmPoiId = getLoginWmPoiId();
        if (wmPoiId <= 0) {
            return WmRestReturnUtil.fail("未查询到有效的门店ID");
        }

        // ssrf参数校验
        String ssrfCheckRes = wmCustomerKpService.checkAgentAuthAndIdCardWithSSRF(wmCustomerKpVo);
        if (StringUtils.isNotBlank(ssrfCheckRes)) {
            return WmRestReturnUtil.fail(ssrfCheckRes);
        }

        Integer customerId = businessBO.getCustomerId();
        // 对象转化
        WmCustomerKp wmCustomerKp = wmCustomerKpService.transformByCustomerKpVoForUpdate(customerId, wmCustomerKpVo);
        wmCustomerKp.setOperateSource(customerKpModifyVo.getOpSource());

        log.info("modifyKpInfo#转化后的kp对象信息:{},", JSON.toJSONString(wmCustomerKp));

        try {
            log.info("商家端修改KP信息，设置来源");
            wmCustomerKp.setOperateSource(CustomerDeviceType.BUSINESS_PC.getCode());
            wmCustomerKp.setVersion(KpVersionEnum.V3.getCode());
            wmCustomerKpThriftService.businessModifyKpInfo(customerId, wmCustomerKp, customerKpModifyVo.getOperateType(), businessBO.getAcctId(), businessBO.getAcctName());
        } catch (WmCustomerException e) {
            log.warn("更新KP信息异常，code={}, msg={}, customerKpModifyVo={}", e.getCode(), e.getMsg(), JSON.toJSONString(wmCustomerKpVo), e);
            if (e.getCode() == 501) {
                return WmRestReturnUtil.success("", e.getMsg());
            } else {
                return WmRestReturnUtil.fail(e.getMsg());
            }
        } catch (Exception e) {
            log.error("更新KP信息异常，customerKpModifyVo={}", JSON.toJSONString(wmCustomerKpVo), e);
            return WmRestReturnUtil.fail("更新KP信息异常");
        }
        return WmRestReturnUtil.success();
    }

    /**
     * 更新签约人KP类型，参数校验
     *
     * @return
     */
    public WmCustomerKpValidateVO validateKpUpdateInfo(WmCustomerKpModifyVo customerKpModifyVo, WmCustomerKpBusinessBO businessBO) {
        WmCustomerKpValidateVO validateVO = new WmCustomerKpValidateVO();
        validateVO.setValid(false);

        if (customerKpModifyVo == null || customerKpModifyVo.getCustomerId() == null
                || customerKpModifyVo.getCustomerId() < 1) {
            validateVO.setMsg("客户信息无效，请核对后重试");
            return validateVO;
        }
        //匹配当前客户
        if (!customerKpModifyVo.getCustomerId().equals(businessBO.getCustomerId())) {
            validateVO.setValid(false);
            validateVO.setMsg("当前用户无权限修改此客户信息");
            return validateVO;
        }

        //签约人KP
        if (customerKpModifyVo.getKp().getKpType() == KpTypeEnum.SIGNER.getType()) {
            validateVO = validateSignerKp(customerKpModifyVo, businessBO);
        } else if (customerKpModifyVo.getKp().getKpType() == KpTypeEnum.LEGAL.getType()) {
            validateVO = validateLegalKpInfo(customerKpModifyVo, businessBO);
        }

        return validateVO;
    }


    /**
     * KP类型法人的校验
     *
     * @param customerKpModifyVo
     * @param businessBO
     * @return
     */
    private WmCustomerKpValidateVO validateLegalKpInfo(WmCustomerKpModifyVo customerKpModifyVo, WmCustomerKpBusinessBO businessBO) {
        WmCustomerKpValidateVO validateVO = new WmCustomerKpValidateVO();
        validateVO.setValid(false);
        if (customerKpModifyVo.getKp() == null
                || customerKpModifyVo.getOperateType() == null || KpOperationTypeConstant.DELETE == customerKpModifyVo.getOperateType()) {
            validateVO.setValid(false);
            validateVO.setMsg("商家端不能操作删除");
            return validateVO;
        }
        WmCustomerKpVo customerKp = customerKpModifyVo.getKp();
        if (customerKp.getKpType() == null || KpTypeEnum.LEGAL.getType() != customerKp.getKpType()) {
            validateVO.setValid(false);
            validateVO.setMsg("只能修改法人KP信息");
            return validateVO;
        }
        if (customerKpModifyVo.getOperateType() == KpOperationTypeConstant.UPDATE
                && (customerKp.getId() == null || customerKp.getId() < 1)) {
            validateVO.setValid(false);
            validateVO.setMsg("修改法人KP信息参数kpId不合法");
            return validateVO;
        }
        //匹配当前KP
        if (customerKpModifyVo.getOperateType() == KpOperationTypeConstant.UPDATE
                && (businessBO.getLegalKpId() == null || !customerKp.getId().equals(businessBO.getLegalKpId()))) {
            validateVO.setValid(false);
            validateVO.setMsg("当前用户无权限修改此KP信息");
            return validateVO;
        }

        validateVO.setValid(true);
        return validateVO;
    }

    /**
     * 校验签约人KP信息
     *
     * @param customerKpModifyVo
     * @param businessBO
     * @return
     */
    private WmCustomerKpValidateVO validateSignerKp(WmCustomerKpModifyVo customerKpModifyVo, WmCustomerKpBusinessBO businessBO) {
        WmCustomerKpValidateVO validateVO = new WmCustomerKpValidateVO();
        //客户类型需要为单店
//		try {
//			WmCustomerBasicBo customerBasicBo = wmCustomerThriftService.getCustomerById(businessBO.getCustomerId());
//			if(customerBasicBo == null || CustomerRealTypeEnum.DANDIAN.getValue() != customerBasicBo.getCustomerRealType()){
//				validateVO.setValid(false);
//				validateVO.setMsg("查询客户信息异常");
//				return validateVO;
//			}
//		} catch (Exception e) {
//			log.error("查询客户信息异常, customerId:{}", businessBO.getCustomerId());
//			validateVO.setValid(false);
//			validateVO.setMsg("查询客户信息异常");
//			return validateVO;
//		}

        if (customerKpModifyVo.getKp() == null
                || customerKpModifyVo.getOperateType() == null || KpOperationTypeConstant.UPDATE != customerKpModifyVo.getOperateType()) {
            validateVO.setValid(false);
            validateVO.setMsg("商家端只能进行修改操作");
            return validateVO;
        }
        WmCustomerKpVo updateKp = customerKpModifyVo.getKp();
        if (updateKp.getId() == null || updateKp.getId() < 1 || KpTypeEnum.SIGNER.getType() != updateKp.getKpType()) {
            validateVO.setValid(false);
            validateVO.setMsg("只能修改签约人KP信息");
            return validateVO;
        }
        //匹配当前KP
        if (!updateKp.getId().equals(businessBO.getKpId())) {
            validateVO.setValid(false);
            validateVO.setMsg("当前用户无权限修改此KP信息");
            return validateVO;
        }

        //签约类型为代理人是，必须上传代理人授权书
        if (updateKp.getSignerType() == KpSignerTypeEnum.AGENT.getType()
                && (updateKp.getLegalAuthType() == null || updateKp.getLegalAuthType() != LegalAuthTypeEnum.MESSAGE_AUTH.getCode())
                && (CollectionUtils.isEmpty(updateKp.getAgentAuth()) || StringUtils.isEmpty(updateKp.getAgentAuth().get(0)))) {
            validateVO.setValid(false);
            validateVO.setMsg("修改代理人必须上传授权书");
            return validateVO;
        }

        //签约人KP状态为生效，不能在流程中
        try {
            WmCustomerKp customerKpInfo = wmCustomerKpThriftService.getCustomerKpInfo(updateKp.getId());
            if (customerKpInfo.getState() != KpSignerStateMachine.EFFECT.getState()) {
                validateVO.setValid(false);
                validateVO.setMsg("签约人KP在正在流程中，无法修改");
                return validateVO;
            }
        } catch (WmCustomerException e) {
            log.warn("获取签约人KP信息异常, kpId={}", updateKp.getId(), e);
            validateVO.setValid(false);
            validateVO.setMsg("获取签约人KP信息异常");
            return validateVO;
        } catch (Exception e) {
            log.error("获取签约人KP信息异常, kpId={}", updateKp.getId(), e);
            validateVO.setValid(false);
            validateVO.setMsg("获取签约人KP信息异常");
            return validateVO;
        }

        validateVO.setValid(true);
        return validateVO;
    }

    /**
     * 商家端取消授权
     *
     * @param kpId
     * @return
     */
    public JSONObject cancelChangeSigner(Integer kpId) {
        if (!hasBusinessAuth(kpId)) {
            return WmRestReturnUtil.fail("当前用户无权限修改此KP信息");
        }
        try {
            WmCustomerKp wmCustomerKp = wmCustomerKpThriftService.getCustomerKpInfo(kpId);
            if (wmCustomerKp == null || wmCustomerKp.getCustomerId() <= 0) {
                return WmRestReturnUtil.fail("找不到对应的KP信息");
            }
            wmCustomerKpThriftService.cancelAuth(kpId, getLoginAcctId(), "");
            return WmRestReturnUtil.success();
        } catch (WmCustomerException e) {
            log.warn("商家端取消授权异常kpId={}，code={}, msg={}", kpId, e.getCode(), e.getMsg(), e);
            return WmRestReturnUtil.fail(e.getMsg());
        } catch (Exception e) {
            log.error("商家端取消授权异常kpId={}", kpId, e);
            return WmRestReturnUtil.fail("取消授权异常");
        }
    }

    public JSONObject reSendAuthSms(Integer kpId) {
        if (!hasBusinessAuth(kpId)) {
            return WmRestReturnUtil.fail("当前用户无权限修改此KP信息");
        }
        try {
            WmCustomerKp wmCustomerKp = wmCustomerKpThriftService.getCustomerKpInfo(kpId);
            if (wmCustomerKp == null || wmCustomerKp.getCustomerId() <= 0) {
                return WmRestReturnUtil.fail("找不到对应的KP信息");
            }
            wmCustomerKpThriftService.reSendAuthSms(kpId, getLoginAcctId(), "");
            return WmRestReturnUtil.success();
        } catch (WmCustomerException e) {
            log.warn("重发短信异常kpId={}，code={}, msg={}", kpId, e.getCode(), e.getMsg(), e);
            return WmRestReturnUtil.fail(e.getMsg());
        } catch (Exception e) {
            log.error("重发短信异常kpId={}", kpId, e);
            return WmRestReturnUtil.fail("重发短信异常");
        }
    }

    /**
     * 商家端获取客户详细信息
     *
     * @param customerId
     * @return
     */
    public JSONObject getCustomerDetail(Integer customerId) {
        WmCustomerKpBusinessBO businessBO = isUserAuth(0L);
        if (businessBO == null || businessBO.getCustomerId() == null || !businessBO.getCustomerId().equals(customerId)) {
            return WmRestReturnUtil.fail("当前无权限查看客户信息");
        }
        try {
            WmCustomerBasicBo wmCustomerBasicBo = wmCustomerThriftService.getCustomerById(customerId);
            WmCustomerAuditEditInfo detail = buildEditInfo(wmCustomerBasicBo);
            detail.setHasEffectSignerHistory(wmCustomerKpThriftService.existEffectiveSignerKpIncludeHistory(customerId));
            return WmRestReturnUtil.success(detail);
        } catch (WmCustomerException wmCustomerException) {
            log.warn("商家端获取客户信息异常, customerId:{}", customerId);
            return WmRestReturnUtil.fail(wmCustomerException.getMsg());
        } catch (Exception e) {
            log.error("商家端获取客户信息异常, customerId:{}", customerId, e);
            return WmRestReturnUtil.fail("获取客户信息异常");
        }
    }


    public JSONObject getKpSignerProgress(WmCustomerKpSignerParamVo paramVo) {
        log.info("getKpSignerProgress paramVo = {}", JSONObject.toJSONString(paramVo));
        if (paramVo == null || paramVo.getCustomerId() == null || paramVo.getKpType() == null) {
            return WmRestReturnUtil.fail("参数不合法");
        }

        if (paramVo.getKpType() != KpTypeEnum.SIGNER.getType()) {
            WmCustomerKpSignerProgressResult result = new WmCustomerKpSignerProgressResult();
            result.setProgressList(new ArrayList<WmCustomerKpSignerNodeBo>());
            return WmRestReturnUtil.success(result);
        }

        if ((paramVo.getSignerType() == null || paramVo.getCertType() == null) && paramVo.getKpId() == null) {
            return WmRestReturnUtil.fail("参数不合法");
        }

        try {
            // 读权限校验
            WmCustomerKpSignerProgressParam progressParam = new WmCustomerKpSignerProgressParam();
            progressParam.setKpId(paramVo.getKpId());
            progressParam.setSignerType(paramVo.getSignerType());
            progressParam.setCertType(paramVo.getCertType());
            progressParam.setHaveAgentAuth(paramVo.getHaveAgentAuth());
            progressParam.setCertNumber(paramVo.getCertNumber());
            progressParam.setCustomerId(paramVo.getCustomerId());
            progressParam.setLegalAuthType(paramVo.getLegalAuthType());
            WmCustomerKpSignerProgressResult result = wmCustomerKpThriftService.getKpSignerProgress(progressParam);
            log.info("getKpSignerProgress result = {}", JSONObject.toJSONString(result));
            return WmRestReturnUtil.success(result);
        } catch (WmCustomerException e) {
            log.error("商家端获取客户kp签约人流程异常", e);
            return WmRestReturnUtil.fail(e.getMsg());
        } catch (Exception e) {
            log.error("商家端获取客户kp签约人流程异常", e);
            return WmRestReturnUtil.fail("商家端获取客户kp签约人流程异常");
        }
    }

    private WmCustomerAuditEditInfo buildEditInfo(WmCustomerBasicBo wmCustomerBasicBo) {
        WmCustomerAuditEditInfo info = new WmCustomerAuditEditInfo();
        info.setId(wmCustomerBasicBo.getId());
        info.setWdcClueId(wmCustomerBasicBo.getWdcClueId());
        info.setWdcClueIdStr(wmCustomerBasicBo.getWdcClueIdStr());
        info.setMtCustomerId(wmCustomerBasicBo.getMtCustomerId());
        info.setSignMode(wmCustomerBasicBo.getSignMode());
        info.setCustomerRealType(wmCustomerBasicBo.getCustomerRealType());
        info.setCustomerRealTypeStr(wmCustomerBasicBo.getCustomerRealTypeStr());
        info.setCustomerType(wmCustomerBasicBo.getCustomerType());
        info.setIsLeaf(wmCustomerBasicBo.getIsLeaf());
        info.setLegalPerson(wmCustomerBasicBo.getCustomerType() == CustomerType.CUSTOMER_TYPE_IDCARD.getCode() ? wmCustomerBasicBo.getCustomerName() : wmCustomerBasicBo.getLegalPerson());
        info.setCustomerName(wmCustomerBasicBo.getCustomerName());
        info.setEffective(wmCustomerBasicBo.getEffective());
        info.setSuperCustomerId(wmCustomerBasicBo.getSuperCustomerId());
        info.setMultiplexCustomerId(wmCustomerBasicBo.getMultiplexCustomerId());
        info.setMultiplex(wmCustomerBasicBo.getMultiplex());
        return info;
    }

    /**
     * 获取客户KP的流程状态
     *
     * @param paramVo
     * @return
     */
    public JSONObject getKpProgress(WmCustomerKpSignerParamVo paramVo) {
        log.info("getKpProgress paramVo = {}", JSONObject.toJSONString(paramVo));
        if (paramVo == null || paramVo.getCustomerId() == null || paramVo.getKpType() == null) {
            return WmRestReturnUtil.fail("参数不合法");
        }

        if (paramVo.getKpType() != KpTypeEnum.SIGNER.getType()
                && paramVo.getKpType() != KpTypeEnum.LEGAL.getType()) {
            WmCustomerKpSignerProgressResult result = new WmCustomerKpSignerProgressResult();
            result.setProgressList(Lists.<WmCustomerKpSignerNodeBo>newArrayList());
            return WmRestReturnUtil.success(result);
        }

        if (paramVo.getKpType() == KpTypeEnum.SIGNER.getType()
                && ((paramVo.getSignerType() == null || paramVo.getCertType() == null) && paramVo.getKpId() == null)) {
            return WmRestReturnUtil.fail("参数不合法");
        }

        try {
            // 读权限校验
            WmCustomerKpProgressReq kpProgressReq = new WmCustomerKpProgressReq();
            kpProgressReq.setKpId(paramVo.getKpId());
            kpProgressReq.setSignerType(paramVo.getSignerType());
            kpProgressReq.setCertType(paramVo.getCertType());
            kpProgressReq.setHaveAgentAuth(paramVo.getHaveAgentAuth());
            kpProgressReq.setCertNumber(paramVo.getCertNumber());
            kpProgressReq.setCustomerId(paramVo.getCustomerId());
            kpProgressReq.setKpType(paramVo.getKpType());
            kpProgressReq.setLegalAuthType(paramVo.getLegalAuthType());
            WmCustomerKpProgressResult result = wmCustomerKpThriftService.getKpProgress(kpProgressReq);
            log.info("getKpProgress result = {}", JSONObject.toJSONString(result));
            JSONObject jsonObject = WmRestReturnUtil.success();
            jsonObject.put("data", result);
            jsonObject = KeyInfoUtils.wrapJson(request, keyFields, jsonObject);

            return jsonObject;
        } catch (WmCustomerException e) {
            log.error("商家端获取客户kp流程进度异常", e);
            return WmRestReturnUtil.fail(e.getMsg());
        } catch (Exception e) {
            log.error("商家端获取客户kp流程进度异常", e);
            return WmRestReturnUtil.fail("获取客户kp流程进度异常");
        }
    }

    /**
     * 获取签约人KP-代理人授权相关信息
     *
     * @param kpCommonReq
     * @return
     */
    public JSONObject getLegalAgentAuthInfo(WmCustomerKpCommonReq kpCommonReq) {
        if (kpCommonReq == null || kpCommonReq.getCustomerId() == null
                || kpCommonReq.getCustomerId() <= 0 ) {
            return WmRestReturnUtil.fail("参数不合法");
        }

        int wmPoiId = getLoginWmPoiId();
        if (wmPoiId < 0) {
            return WmRestReturnUtil.fail("门店ID不合法");
        }
        int acctId = getLoginAcctId();

        try {
            CustomerKpLegalAuthRequest kpLegalAuthRequest = new CustomerKpLegalAuthRequest();
            kpLegalAuthRequest.setCustomerId(kpCommonReq.getCustomerId());
            kpLegalAuthRequest.setOpUId(acctId);
            kpLegalAuthRequest.setWmPoiId(wmPoiId);
            kpLegalAuthRequest.setKpId(kpCommonReq.getKpId());
            kpLegalAuthRequest.setOpSource(kpCommonReq.getOpSource());
            CustomerKpLegalAuthBo kpLegalAuthBo = wmCustomerKpThriftService.getLegalAgentAuthInfo(kpLegalAuthRequest);
            log.info("getLegalAgentAuthInfo,kpLegalAuthBo = {}", JSONObject.toJSONString(kpLegalAuthBo));
            return WmRestReturnUtil.success(kpLegalAuthBo);
        } catch (WmCustomerException e) {
            log.error("商家端获取签约代理人KP的授权信息异常", e);
            return WmRestReturnUtil.fail(e.getMsg());
        } catch (Exception e) {
            log.error("商家端获取签约代理人KP的授权信息异常", e);
            return WmRestReturnUtil.fail("获取签约代理人KP的授权信息异常");
        }
    }

    /**
     * 法人授权相关操作（强制授权、取消授权、重发短信）
     *
     * @param req
     * @return
     */
    public JSONObject legalAuthAgentOperate(LegalAuthAgentOperateRequest req) {
        try {
            if (req == null) {
                return WmRestReturnUtil.fail("请求参数不能为空");
            }
            if (req.getKpId() <= 0) {
                return WmRestReturnUtil.fail("KPID不能为空");
            }
            if (req.getCustomerId() <= 0) {
                return WmRestReturnUtil.fail("客户ID不能为空");
            }
            if (req.getOpSource() <= 0) {
                return WmRestReturnUtil.fail("操作来源必填");
            }
            int kpId = req.getKpId();
            if (!hasBusinessAuth(kpId)) {
                return WmRestReturnUtil.fail("当前用户无权限修改此KP信息");
            }
            WmCustomerKp wmCustomerKp = wmCustomerKpThriftService.getCustomerKpInfo(kpId);
            if (wmCustomerKp == null || wmCustomerKp.getCustomerId() <= 0) {
                return WmRestReturnUtil.fail("找不到对应的KP信息");
            }
            int accountId = getLoginAcctId();
            if (LegalAuthOperateTypeEnum.CANCEL_AUTH.getType() == req.getOpType()) {
                // 取消授权
                return cancelAuthForLegalKp(kpId, accountId, req.getOpSource());
            } else if (LegalAuthOperateTypeEnum.RESEND_MSG.getType() == req.getOpType()) {
                // 重发短信
                return reSendAuthSmsForLegalKp(kpId, accountId, req.getOpSource());
            } else {
                //未识别
                return WmRestReturnUtil.fail("未识别到操作类型");
            }
        } catch (WmCustomerException e) {
            log.warn("[商家端]legalAuthAgentOperate fail. req={}，code={}, msg={}", JSONObject.toJSONString(req), e.getCode(), e.getMsg(), e);
            return WmRestReturnUtil.fail(e.getMsg());
        } catch (Exception e) {
            log.warn("[商家端]legalAuthAgentOperate fail. req={}，code={}, msg={}", JSONObject.toJSONString(req), e);
            return WmRestReturnUtil.fail("系统异常");
        }
    }

    /**
     * 取消授权-法人授权
     *
     * @param kpId
     * @param accountId
     * @return
     */
    private JSONObject cancelAuthForLegalKp(int kpId, int accountId, int deviceType) {
        try {
            log.info("[商家端] cancelAuthForLegalKp kpId:{} opUid:{} deviceType:{}", kpId, accountId, deviceType);
            wmCustomerKpThriftService.cancelAuthForLegalKpForBiz(kpId, accountId, deviceType);
            return WmRestReturnUtil.success();
        } catch (WmCustomerException e) {
            log.warn("[商家端] 取消授权异常kpId={}，code={}, msg={}", kpId, e.getCode(), e.getMsg(), e);
            return WmRestReturnUtil.fail(e.getMsg());
        } catch (Exception e) {
            log.error("[商家端] 取消授权异常kpId={}", kpId, e);
            return WmRestReturnUtil.fail("取消授权异常");
        }
    }

    /**
     * 重发短信-法人授权
     *
     * @param kpId
     * @param accountId
     * @return
     */
    private JSONObject reSendAuthSmsForLegalKp(int kpId, int accountId, int deviceType) {
        try {
            log.info("[商家端] reSendAuthSmsForLegalKp kpId:{} accountId:{} deviceType:{}", kpId, accountId, deviceType);
            wmCustomerKpThriftService.reSendAuthSmsForLegalKpForBiz(kpId, accountId, deviceType);
            return WmRestReturnUtil.success();
        } catch (WmCustomerException e) {
            log.warn("[商家端] 重发短信异常kpId={}，code={}, msg={}", kpId, e.getCode(), e.getMsg(), e);
            return WmRestReturnUtil.fail(e.getMsg());
        } catch (Exception e) {
            log.error("[商家端] 重发短信异常kpId={}", kpId, e);
            return WmRestReturnUtil.fail("重发短信异常");
        }
    }
}

package com.sankuai.meituan.waimai.service.sc;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.config.MccScConfig;
import com.sankuai.meituan.waimai.constant.RegexConstant;
import com.sankuai.meituan.waimai.domain.sc.WmScMetadataDeliveryGoalSetBO;
import com.sankuai.meituan.waimai.service.sc.canteenstall.WmScCanteenStallService;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerType;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.CertificateTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.*;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.canteenstall.CanteenStallAuditTaskTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.canteenstall.CanteenStallClueAbnormalReasonEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.delivery.SchoolDeliveryMaterialDemandEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerBasicBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.CanteenBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.delivery.WmSchoolDeliveryAssignmentDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.delivery.WmSchoolDepartmentIntensionDTO;
import com.sankuai.meituan.waimai.thrift.customer.service.sc.WmCanteenThriftService;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import com.sankuai.meituan.waimai.util.DateUtil;
import com.sankuai.meituan.waimai.util.RegexUtil;
import com.sankuai.meituan.waimai.util.StringUtil;
import com.sankuai.meituan.waimai.vo.sc.*;
import com.sankuai.meituan.waimai.vo.sc.CanteenFormVo;
import com.sankuai.meituan.waimai.vo.sc.SchoolFormVo;
import com.sankuai.meituan.waimai.vo.sc.canteenstall.WmCanteenStallAuditSubmitVO;
import com.sankuai.meituan.waimai.vo.sc.canteenstall.WmCanteenStallBindSubmitVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.thrift.TException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.*;

import static com.sankuai.meituan.waimai.service.sc.auth.WmScAuthService.*;
import static com.sankuai.meituan.waimai.service.sc.delivery.WmSchoolDeliveryAssignmentBasicService.DELIVERY_ASSIGNMENT_FIXED_DEPARTMENT_NAME_LIST;
import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants.BIZ_PARA_ERROR;

/**
 * @program: scm
 * @description: 学校食堂校验类
 * @author: jianghuimin02
 * @create: 2020-04-23 10:39
 **/
@Slf4j
@Service
public class WmScCheckService {

    @Autowired
    private WmCanteenThriftService wmCanteenThriftService;

    @Autowired
    private WmScCanteenService wmScCanteenService;

    @Autowired
    private WmScSchoolService wmScSchoolService;


    public static final int MAXNAME = 100;
    public static final int MAXKPNAME = 50;
    public static final int MAXPHONE = 11;
    public static final int MAXSTALL = 4;
    public static final int MAXCODE = 10;

    /**
     * 学校履约管控其他信息-最多字数限制
     */
    public static final int SCHOOL_DELIVERY_OTHER_INFO_MAX_LENGTH = 200;
    /**
     * 自定义校方部门摸排最大数量限制
     */
    public static final int CUSTOM_DEPARTMENT_INTENTION_MAX_SIZE = 100;

    /**
     * 学校合作信息-合同编号/授权编号占用的最大字节数
     */
    public static final int AGREEMENT_CODE_MAX_BYTE_SIZE = 50;

    /**
     * 食堂档口审批任务最大上传图片数量 = 3
     */
    public static final int ALLOW_MAX_CANTEEN_STALL_AUDIT_PIC_NUM = 3;

    /**
     * 校验学校普遍客户关系梳理信息
     * @param contactInfoList 信息列表
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void checkSchoolContactUserInfo(List<WmScMetadataDeliveryGoalSetBO.SchoolDeliveryGoalSetContactInfo> contactInfoList) throws WmSchCantException {
        if (CollectionUtils.isEmpty(contactInfoList)) {
            return;
        }

        // 1-校验手机号是否重复
        HashSet<String> hashSetPhoneNum = new HashSet<>();
        List<String> errorMsgList = new ArrayList<>();
        for (WmScMetadataDeliveryGoalSetBO.SchoolDeliveryGoalSetContactInfo contactInfo : contactInfoList) {
            if (contactInfo == null || JSONObject.toJSONString(contactInfo).equals("{}")) {
                continue;
            }

            if (hashSetPhoneNum.contains(contactInfo.getPhoneNum())) {
                String errMsg = "普遍联系人" + contactInfo.getName() + "（" + contactInfo.getPhoneNum() + "）无法保存，原因：本次提交的普遍客户手机号重复，请调整后再操作";
                errorMsgList.add(errMsg);
            } else {
                hashSetPhoneNum.add(contactInfo.getPhoneNum());
            }
        }

        // 2-校验人员ID是否重复
        HashSet<String> hashSetUserId = new HashSet<>();
        for (WmScMetadataDeliveryGoalSetBO.SchoolDeliveryGoalSetContactInfo contactInfo : contactInfoList) {
            if (contactInfo == null || JSONObject.toJSONString(contactInfo).equals("{}") || StringUtils.isBlank(contactInfo.getId())) {
                continue;
            }

            if (hashSetUserId.contains(contactInfo.getId())) {
                String errMsg = "普遍联系人" + contactInfo.getName() + "（" + contactInfo.getPhoneNum() + "）无法保存，原因：本次提交的普遍客户ID重复，请调整后再操作";
                errorMsgList.add(errMsg);
            } else {
                hashSetUserId.add(contactInfo.getId());
            }
        }

        if (CollectionUtils.isNotEmpty(errorMsgList)) {
            throw new WmSchCantException(BIZ_PARA_ERROR, StringUtils.join(errorMsgList, "；"));
        }
    }

    /**
     * 校验交付人员指定 - 部门名称是否重复
     * @param assignmentDTO assignmentDTO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void checkSchoolDeliveryDepartmentNameRepeat(WmSchoolDeliveryAssignmentDTO assignmentDTO) throws WmSchCantException {
        List<WmSchoolDepartmentIntensionDTO> departmentIntensionDTOList = assignmentDTO.getDepartmentIntensionDTOList();
        Map<String, Integer> hashMap = new HashMap<>();
        List<String> repeatNameList = new ArrayList<>();
        log.info("[WmScCheckService.checkSchoolDeliveryDepartmentNameRepeat] departmentIntensionDTOList = {}", JSONObject.toJSONString(departmentIntensionDTOList));

        int customerDepartmentNum = 0;
        // 自定义的是否重复
        for (WmSchoolDepartmentIntensionDTO intensionDTO : departmentIntensionDTOList) {
            if (DELIVERY_ASSIGNMENT_FIXED_DEPARTMENT_NAME_LIST.contains(intensionDTO.getDepartmentName())) {
                continue;
            }
            customerDepartmentNum ++;

            if (hashMap.containsKey(intensionDTO.getDepartmentName())) {
                hashMap.put(intensionDTO.getDepartmentName().replace("-costom", ""), hashMap.get(intensionDTO.getDepartmentName()) + 1);
            } else {
                hashMap.put(intensionDTO.getDepartmentName().replace("-costom", ""), 1);
            }

            String name = intensionDTO.getDepartmentName().replace("-custom", "");
            if (DELIVERY_ASSIGNMENT_FIXED_DEPARTMENT_NAME_LIST.contains(name)) {
                hashMap.put(intensionDTO.getDepartmentName().replace("-costom", ""), hashMap.get(intensionDTO.getDepartmentName()) + 1);
            }
        }

        log.info("[WmScCheckService.checkSchoolDeliveryDepartmentNameRepeat] hashMap = {}, customerDepartmentNum = {}", JSONObject.toJSONString(hashMap), customerDepartmentNum);
        if (customerDepartmentNum > CUSTOM_DEPARTMENT_INTENTION_MAX_SIZE) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "自定义校方部门摸排最多录入" + CUSTOM_DEPARTMENT_INTENTION_MAX_SIZE + "项");
        }

        for (Map.Entry<String, Integer> entry : hashMap.entrySet()) {
            if (entry.getValue() > 1) {
                repeatNameList.add(entry.getKey().replace("-custom", ""));
            }
        }

        if (CollectionUtils.isNotEmpty(repeatNameList)) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "【校方部门摸排】与【校方部门摸排-自定义】模块中，部门名称不可重复，请在【校方部门摸排-自定义】修改" + StringUtils.join(repeatNameList, "、") + "名称后再提交");
        }
    }

    public void checkSchoolDeliveryGoalSetInput(WmScMetadataDeliveryGoalSetBO.SchoolDeliveryGoalSetStallOnlineInfo stallOnlineInfo) throws WmSchCantException {
        if (stallOnlineInfo.getDeliverableStallNum() < stallOnlineInfo.getFirstOnlineStallNum() || stallOnlineInfo.getFirstOnlineStallNum() < 0) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "首批上线档口数：需填写不大于可交付档口数的非负整数");
        }
    }

    /**
     * 校验交付人员指定录入信息
     * @param assignmentDTO assignmentDTO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void checkSchoolDeliveryAssignmentInput(WmSchoolDeliveryAssignmentDTO assignmentDTO) throws WmSchCantException {
        // 校验部门名称重复
        checkSchoolDeliveryDepartmentNameRepeat(assignmentDTO);

        // 校验特殊处理字段信息
        checkSchoolDeliveryAssignmentSpecialData(assignmentDTO);

        // 校验档口数量
        checkSchoolDeliveryAssignmentStallNum(assignmentDTO);
    }

    private void checkSchoolDeliveryAssignmentStallNum(WmSchoolDeliveryAssignmentDTO assignmentDTO) throws WmSchCantException {
        if (assignmentDTO.getDeliverableStallNum() > assignmentDTO.getPublicStallNum()) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "可交付档口数：请输入不大于公海档口数的非负整数");
        }
    }

    private void checkSchoolDeliveryAssignmentSpecialData(WmSchoolDeliveryAssignmentDTO assignmentDTO) throws WmSchCantException {
        List<Integer> demandList = new ArrayList<>();
        if (StringUtils.isBlank(assignmentDTO.getMaterialDemand())) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "请选择物料需求");
        }

        String[] items = assignmentDTO.getMaterialDemand().split(",");
        for (String item : items) {
            demandList.add(Integer.parseInt(item));
        }
        // 展架数量
        if (demandList.contains((int) SchoolDeliveryMaterialDemandEnum.DISPLAY_RACK.getType())) {
            if (assignmentDTO.getNeedDisplayRackNum() == null) {
                throw new WmSchCantException(BIZ_PARA_ERROR, "请填写需要的展架数量");
            }
            if (assignmentDTO.getNeedDisplayRackNum() < 0) {
                throw new WmSchCantException(BIZ_PARA_ERROR, "需要的展架数量：请输入非负整数");
            }
        }

        // 打印机数量
        if (demandList.contains((int) SchoolDeliveryMaterialDemandEnum.PRINTER.getType())) {
            if (assignmentDTO.getNeedPrinterNum() == null) {
                throw new WmSchCantException(BIZ_PARA_ERROR, "请填写需要的打印机数量");
            }
            if (assignmentDTO.getNeedPrinterNum() < 0) {
                throw new WmSchCantException(BIZ_PARA_ERROR, "需要的打印机数量：请输入非负整数");
            }
        }

        // 遮阳伞数量
        if (demandList.contains((int) SchoolDeliveryMaterialDemandEnum.SUNSHADE.getType())) {
            if (assignmentDTO.getNeedSunshadeNum() == null) {
                throw new WmSchCantException(BIZ_PARA_ERROR, "请填写需要的遮阳伞数量");
            }
            if (assignmentDTO.getNeedSunshadeNum() < 0) {
                throw new WmSchCantException(BIZ_PARA_ERROR, "需要的遮阳伞数量：请输入非负整数");
            }
        }

        // 垃圾桶数量
        if (demandList.contains((int) SchoolDeliveryMaterialDemandEnum.TRASH_CAN.getType())) {
            if (assignmentDTO.getNeedTrashCanNum() == null) {
                throw new WmSchCantException(BIZ_PARA_ERROR, "请填写需要的垃圾桶数量");
            }
            if (assignmentDTO.getNeedTrashCanNum() < 0) {
                throw new WmSchCantException(BIZ_PARA_ERROR, "需要的垃圾桶数量：请输入非负整数");
            }
        }

        // 其他
        if (demandList.contains((int) SchoolDeliveryMaterialDemandEnum.OTHERS.getType())) {
            if (StringUtils.isBlank(assignmentDTO.getOtherMaterialDemand())) {
                throw new WmSchCantException(BIZ_PARA_ERROR, "请填写需要的其他物料与数量");
            }
            if (assignmentDTO.getOtherMaterialDemand().length() > 200) {
                throw new WmSchCantException(BIZ_PARA_ERROR, "其他物料与数量最多200字");
            }
        }
    }

    /**
     * 学校信息保存字段校验
     * @param schoolFormVo schoolFormVo
     * @param authMap authMap
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void checkSchoolInfoEditInput(SchoolFormVo schoolFormVo, Map<String, Boolean> authMap) throws WmSchCantException {
        // 学校基本信息校验
        if (authMap.get(SCHOOL_BASIC_INFO_EDIT_OPERATION_CODE)) {
            checkSchoolBasicInfo(schoolFormVo);
        }
        // 学校合作信息校验
        if (authMap.get(SCHOOL_COOPERATION_INFO_VIEW_OPERATION_CODE) && authMap.get(SCHOOL_COOPERATION_INFO_EDIT_OPERATION_CODE)) {
            checkSchoolCooperateInfo(schoolFormVo);
        }
        // 学校KP信息校验
        if (authMap.get(SCHOOL_KP_INFO_VIEW_OPERATION_CODE) && authMap.get(SCHOOL_KP_INFO_EDIT_OPERATION_CODE)) {
            checkSchoolKpInfo(schoolFormVo);
        }
        // 学校运行信息校验
        if (authMap.get(SCHOOL_OPERATION_INFO_EDIT_OPERATION_CODE)) {
            checkSchoolOperationInfo(schoolFormVo);
        }
        // 学校配置信息校验
        if (authMap.get(SCHOOL_CONFIG_INFO_EDIT_OPERATION_CODE)) {
            checkSchoolConfigInfo(schoolFormVo);
        }
        // 在校师生人数校验
        if (authMap.get(SCHOOL_TEA_STU_NUM_EDIT_OPERATION_CODE)) {
            checkSchoolTeaStuNum(schoolFormVo);
        }
        // 在校学生人数 & 蜂窝内校外学生人数
        if (authMap.get(SCHOOL_AOR_STU_NUM_EDIT_OPERATION_CODE)) {
            checkSchoolAorStuNum(schoolFormVo);
        }
    }

    /**
     * 在校师生人数校验
     * @param schoolFormVo schoolFormVo
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void checkSchoolTeaStuNum(SchoolFormVo schoolFormVo) throws WmSchCantException {
        // 在校师生人数校验
        if (schoolFormVo.getTeaStuNum() == null || schoolFormVo.getTeaStuNum() > MccScConfig.schoolTeaStuNumMax()
                || schoolFormVo.getTeaStuNum() < 0) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "在校师生人数必须小于100000");
        }
    }

    /**
     * 在校学生人数 & 蜂窝内校外学生人数校验
     * @param schoolFormVo schoolFormVo
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void checkSchoolAorStuNum(SchoolFormVo schoolFormVo) throws WmSchCantException {
        if (schoolFormVo.getStudentNum() != null) {
            if (schoolFormVo.getStudentNum() < 0) {
                throw new WmSchCantException(BIZ_PARA_ERROR, "在校学生人数不能为负数");
            }

            if (schoolFormVo.getStudentNum() >= schoolFormVo.getTeaStuNum()) {
                throw new WmSchCantException(BIZ_PARA_ERROR, "在校学生人数要小于在校师生人数");
            }

            if (MccScConfig.getSchoolStudentNumCompareRedisentUserSwitch()
                    && schoolFormVo.getResidentStudentUserNum() != null
                    && schoolFormVo.getStudentNum() < schoolFormVo.getResidentStudentUserNum()) {
                throw new WmSchCantException(BIZ_PARA_ERROR, "在校学生人数应大于等于校内常住学生用户数");
            }
        }

        // 蜂窝内校外学生人数校验
        if (schoolFormVo.getOutsideStudentNum() != null) {
            if (schoolFormVo.getOutsideStudentNum() < 0) {
                throw new WmSchCantException(BIZ_PARA_ERROR, "蜂窝内校外学生人数不能为负数");
            }
        }
    }

    /**
     * 校验学校人数信息
     * @param schoolFormVo schoolFormVo
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void checkSchoolPersonNumInfo(SchoolFormVo schoolFormVo) throws WmSchCantException {
        // 在校师生人数校验
        if (schoolFormVo.getTeaStuNum() == null || schoolFormVo.getTeaStuNum() > MccScConfig.schoolTeaStuNumMax()
                || schoolFormVo.getTeaStuNum() < 0) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "在校师生人数必须小于100000");
        }

        if (schoolFormVo.getStudentNum() != null) {
            if (schoolFormVo.getStudentNum() < 0) {
                throw new WmSchCantException(BIZ_PARA_ERROR, "在校学生人数不能为负数");
            }

            if (schoolFormVo.getStudentNum() >= schoolFormVo.getTeaStuNum()) {
                throw new WmSchCantException(BIZ_PARA_ERROR, "在校学生人数要小于在校师生人数");
            }

            if (MccScConfig.getSchoolStudentNumCompareRedisentUserSwitch()
                    && schoolFormVo.getResidentStudentUserNum() != null
                    && schoolFormVo.getStudentNum() < schoolFormVo.getResidentStudentUserNum()) {
                throw new WmSchCantException(BIZ_PARA_ERROR, "在校学生人数应大于等于校内常住学生用户数");
            }
        }

        // 蜂窝内校外学生人数校验
        if (schoolFormVo.getOutsideStudentNum() != null) {
            if (schoolFormVo.getOutsideStudentNum() < 0) {
                throw new WmSchCantException(BIZ_PARA_ERROR, "蜂窝内校外学生人数不能为负数");
            }
        }
    }

    /**
     * 校验学校基本信息
     * @param schoolFormVo schoolFormVo
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void checkSchoolBasicInfo(SchoolFormVo schoolFormVo) throws WmSchCantException {
        if (StringUtil.isBlank(schoolFormVo.getSchoolCode())
                || schoolFormVo.getSchoolCode().length() != MAXCODE
                || !StringUtil.isNumeric(schoolFormVo.getSchoolCode())) {
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "学校标志码必须为" + MAXCODE + "位数字");
        }
        // 学校标识码校验(编辑学校)
        if (schoolFormVo.getSchoolCode().charAt(0) == '0') {
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "请填写正确的学校标识码：非0开头的10位数字");
        }
        // 学校标识码校验(新建学校)
        if (schoolFormVo.getSchoolId() == 0 && schoolFormVo.getSchoolCode().charAt(0) == '0') {
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "请填写正确的学校标识码：非0开头的10位数字");
        }

        if (StringUtil.isBlank(schoolFormVo.getSchoolName())) {
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "学校名称为空");
        }

        if (schoolFormVo.getSchoolName().length() > MAXNAME) {
            throw new WmSchCantException(WmScCodeConstants.SCHOOL_KP_TOOLONG, "学校名称不可超过"+MAXNAME+"个汉字");
        }

        if (schoolFormVo.getSchoolType() == 0) {
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "学校类型为空");
        }

        if (schoolFormVo.getAorId() == 0) {
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "蜂窝ID不存在，不可保存");
        }

        if (schoolFormVo.getSchoolKp() != null && schoolFormVo.getSchoolKp().length() > MAXKPNAME) {
            throw new WmSchCantException(WmScCodeConstants.SCHOOL_KP_TOOLONG, "学校KP不可超过"+MAXKPNAME+"个汉字");
        }

        if (StringUtil.isNotBlank(schoolFormVo.getSchoolKpNum()) && schoolFormVo.getSchoolKpNum().length() != MAXPHONE) {
            throw new WmSchCantException(WmScCodeConstants.SCHOOL_KP_TOOLONG, "联系电话不是"+MAXPHONE+"位，不可保存");
        }

        if (StringUtils.isNotBlank(schoolFormVo.getSchoolAddress()) && schoolFormVo.getSchoolAddress().length() > 200) {
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "学校地址不允许超过200字符");
        }
    }

    /**
     * 校验学校KP信息
     * @param schoolFormVo schoolFormVo
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void checkSchoolKpInfo(SchoolFormVo schoolFormVo) throws WmSchCantException {
        if (StringUtils.isBlank(schoolFormVo.getSchoolKp())) {
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "请输入学校KP");
        }

        if (StringUtils.isBlank(schoolFormVo.getSchoolKpNum())) {
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "请输入学校KP联系电话");
        }
    }

    /**
     * 校验学校运行信息
     * @param schoolFormVo schoolFormVo
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void checkSchoolOperationInfo(SchoolFormVo schoolFormVo) throws WmSchCantException {
        if (schoolFormVo.getSchoolLightOffInfo() == null
                || schoolFormVo.getSchoolLightOffInfo().getLightOffRequirement() == null) {
            return;
        }

        Integer lightOffRequirement = schoolFormVo.getSchoolLightOffInfo().getLightOffRequirement();
        // 没有选择熄灯要求
        if (SchoolLightOffRequirementEnum.getByType(lightOffRequirement) == null) {
            return;
        }
        // 熄灯要求选择统一熄灯时间时, 学校熄灯时间必填
        if (SchoolLightOffRequirementEnum.getByType(lightOffRequirement) == SchoolLightOffRequirementEnum.GLOBAL_LIGHT_OFF_TIME
                && StringUtils.isBlank(schoolFormVo.getSchoolLightOffInfo().getLightOffTime())) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "请填写学校熄灯时间");
        }
    }

    /**
     * 校验学校配置信息
     * @param schoolFormVo schoolFormVo
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void checkSchoolConfigInfo(SchoolFormVo schoolFormVo) throws WmSchCantException {
        // 聚合站点ID
        if (StringUtils.isNotBlank(schoolFormVo.getAggreSiteId())) {
            // 去掉首尾的英文逗号
            schoolFormVo.setAggreSiteId(schoolFormVo.getAggreSiteId().replaceAll("^,+", "").replaceAll(",+$", ""));
            if (!RegexUtil.isRegexMatch(RegexConstant.SCHOOL_AGGRE_SITE_ID_EX, schoolFormVo.getAggreSiteId())) {
                throw new WmSchCantException(BIZ_PARA_ERROR, "多个聚合站点ID请以英文逗号分隔");
            }
        }
    }

    /**
     * 校验学校的输入参数-学校基建升级
     * @param schoolFormVo http接口入参
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void checkSchoolCooperateInfo(SchoolFormVo schoolFormVo) throws WmSchCantException {
        // 合同/授权编号及日期校验
        if (StringUtils.isNotBlank(schoolFormVo.getAgreementCode())) {
            if (SchoolAgreementTypeEnum.getByType(schoolFormVo.getAgreementType()) == null) {
                throw new WmSchCantException(BIZ_PARA_ERROR, "请选择合同/授权编号类型");
            }

            if (!schoolFormVo.getAgreementType().equals((int) SchoolAgreementTypeEnum.AUTH.getType())
                    && !schoolFormVo.getAgreementType().equals((int) SchoolAgreementTypeEnum.CONTRACT.getType())) {
                throw new WmSchCantException(BIZ_PARA_ERROR, "请选择合同/授权编号类型");
            }

            if (schoolFormVo.getAgreementTimeEnd() == null || schoolFormVo.getAgreementTimeEnd() <= 0
                    || schoolFormVo.getAgreementTimeStart() == null || schoolFormVo.getAgreementTimeStart() <= 0) {
                throw new WmSchCantException(BIZ_PARA_ERROR, "请填写合同/授权有效期");
            }

            if (schoolFormVo.getAgreementTimeStart() > schoolFormVo.getAgreementTimeEnd()) {
                throw new WmSchCantException(BIZ_PARA_ERROR, "合同/授权开始时间应小于结束时间");
            }

            if (schoolFormVo.getAgreementType() == SchoolAgreementTypeEnum.CONTRACT.getType()
                    && schoolFormVo.getAgreementCode().length() > AGREEMENT_CODE_MAX_BYTE_SIZE) {
                throw new WmSchCantException(BIZ_PARA_ERROR, "合同编号填写过长，请缩短： 最大可录入50个数字与字母的组合");
            }

            if (schoolFormVo.getAgreementType() == SchoolAgreementTypeEnum.AUTH.getType()
                    && schoolFormVo.getAgreementCode().length() > AGREEMENT_CODE_MAX_BYTE_SIZE) {
                throw new WmSchCantException(BIZ_PARA_ERROR, "授权编号填写过长，请缩短： 最大可录入50个数字与字母的组合");
            }

        } else {
            // 如果合同/授权编号为空 - 合同/授权类型以及有效期时间应该都为空
            if (!schoolFormVo.getAgreementType().equals((int) SchoolAgreementTypeEnum.UNKNOWN.getType())) {
                throw new WmSchCantException(BIZ_PARA_ERROR, "请填写合同/授权编号");
            }

            if (schoolFormVo.getAgreementTimeEnd() != 0 || schoolFormVo.getAgreementTimeStart() != 0) {
                throw new WmSchCantException(BIZ_PARA_ERROR, "请填写合同/授权编号");
            }
        }
        // 学校开发方式校验
        if (SchoolDevTypeEnum.getByType(schoolFormVo.getSchoolDevType()) == null) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "学校开发方式类型错误");
        }

    }

    /**
     * 校验学校的输入参数(新建学校)
     * @param schoolFormVo schoolFormVo
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void checkSchoolFormVoWhenNewSchool(SchoolFormVo schoolFormVo) throws WmSchCantException {
        if (schoolFormVo == null) {
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "参数为空");
        }
        // 校验学校合作信息
        checkSchoolCooperateInfo(schoolFormVo);
        // 校验学校基本信息
        checkSchoolBasicInfo(schoolFormVo);
        // 校验学校KP信息
        checkSchoolKpInfo(schoolFormVo);
        // 校验学校人数信息
        checkSchoolPersonNumInfo(schoolFormVo);
        // 校验学校运行信息
        checkSchoolOperationInfo(schoolFormVo);
        // 校验学校配置信息
        checkSchoolConfigInfo(schoolFormVo);
    }

    /**
     * 校验食堂基本信息
     * @param canteenFormVo canteenFormVo
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    public void checkCanteenFormVo(CanteenFormVo canteenFormVo) throws WmSchCantException, TException {
        if (canteenFormVo == null) {
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "参数为空");
        }
        // 校验食堂档口数量、线下营业档口数量和可上线档口数量
        checkCanteenStallNum(canteenFormVo);

        if (StringUtil.isBlank(canteenFormVo.getCanteenName())) {
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "食堂名称为空");
        }

        if (canteenFormVo.getCanteenName().length() > MAXNAME) {
            throw new WmSchCantException(WmScCodeConstants.SCHOOL_KP_TOOLONG, "食堂名称超过" + MAXNAME + "字");
        }

        if (canteenFormVo.getCanteenType() == 0) {
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "食堂类型为空");
        }

        if (canteenFormVo.getCanteenAttribute() == 0) {
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "食堂属性为空");
        }

        if (canteenFormVo.getSchoolId() <= 0) {
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "学校信息为空");
        }

        if (canteenFormVo.getStallNum().length() > MAXSTALL) {
            throw new WmSchCantException(WmScCodeConstants.SCHOOL_KP_TOOLONG, "档口数量最多可录入" + MAXSTALL + "位数字");
        }

        if (StringUtil.isNotBlank(canteenFormVo.getManager()) && canteenFormVo.getManager().length() > MAXKPNAME) {
            throw new WmSchCantException(WmScCodeConstants.SCHOOL_KP_TOOLONG, "食堂经理姓名不可超过" + MAXKPNAME + "个汉字");
        }

        if (StringUtil.isNotBlank(canteenFormVo.getManagerPhone()) && canteenFormVo.getManagerPhone().length() != MAXPHONE) {
            throw new WmSchCantException(WmScCodeConstants.SCHOOL_KP_TOOLONG, "联系电话不是" + MAXPHONE + "位，不可保存");
        }

        if (canteenFormVo.getCategory() <= 0) {
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "食堂品类为空");
        }
    }

    /**
     * 校验食堂档口数量、线下营业档口数量和可上线档口数量
     * @param canteenFormVo canteenFormVo
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    public void checkCanteenStallNum(CanteenFormVo canteenFormVo) throws WmSchCantException, TException {
        // 在填写窗口期内或新增食堂场景需要进行检验
        if (wmScCanteenService.getStallNumPermission().equals(ScGrayEnum.CAN_EDIT.getCode())
                || canteenFormVo.getId() == 0) {
            if (canteenFormVo.getOfflineBizStallNum() == null) {
                throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "线下营业档口数量为必填项");
            }

            if (canteenFormVo.getPreOnlineStallNum() == null) {
                throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "可上线档口数量为必填项");
            }

            if (wmScSchoolService.isSchoolInCanteenStallAccuracyGrayList(canteenFormVo.getSchoolId())) {
                if (canteenFormVo.getOfflineBizStallNum() < 0) {
                    throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "线下营业档口数量必须是非负整数");
                }

                if (StringUtil.isBlank(canteenFormVo.getStallNum())
                        || canteenFormVo.getStallNum().contains("-")
                        || canteenFormVo.getStallNum().contains(".")) {
                    throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "档口数量需输入非负整数");
                }
            } else {
                if (canteenFormVo.getOfflineBizStallNum() <= 0) {
                    throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "线下营业档口数量必须是大于0的正整数");
                }

                if (StringUtil.isBlank(canteenFormVo.getStallNum())
                        || "0".equals(canteenFormVo.getStallNum())
                        || canteenFormVo.getStallNum().contains("-")
                        || canteenFormVo.getStallNum().contains(".")) {
                    throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "档口的数量需要输入正整数");
                }
            }

            if (canteenFormVo.getOfflineBizStallNum() > Integer.parseInt(canteenFormVo.getStallNum())) {
                throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "线下营业档口数量应小于等于档口数量");
            }

            if (canteenFormVo.getPreOnlineStallNum() < 0) {
                throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "可上线档口数量不能为负数");
            }

            if (canteenFormVo.getPreOnlineStallNum() > canteenFormVo.getOfflineBizStallNum()) {
                throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "可上线档口数量应小于等于线下营业档口数量");
            }
            return;
        }
        // 若不在编辑窗口期, 需校验档口数量、可上线档口数量和线下营业档口数量和库表是否一致
        checkCanteenStallNumIsSameWithDB(canteenFormVo);
    }

    /**
     * 校验档口数量、可上线档口数量和线下营业档口数量和库表是否一致
     * @param canteenFormVo canteenFormVo
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    public void checkCanteenStallNumIsSameWithDB(CanteenFormVo canteenFormVo) throws WmSchCantException, TException {
        // 新增食堂场景不需要校验
        if (canteenFormVo.getId() == 0) {
            log.info("[WmScCheckService.checkCanteenStallNumIsSameWithDB] canteenFormVo = {} save canteen", JSONObject.toJSONString(canteenFormVo));
            return;
        }

        CanteenBo canteenBo = wmCanteenThriftService.getCanteen(canteenFormVo.getId());
        try {
            if (canteenFormVo.getOfflineBizStallNum() == null
                    || !canteenFormVo.getOfflineBizStallNum().equals(canteenBo.getOfflineBizStallNum())
                    || canteenFormVo.getPreOnlineStallNum() == null
                    || !canteenFormVo.getPreOnlineStallNum().equals(canteenBo.getPreOnlineStallNum())
                    || Integer.parseInt(canteenFormVo.getStallNum()) != canteenBo.getStallNum()) {
                throw new WmSchCantException(BIZ_PARA_ERROR, "窗口期外不允许修改档口数量/线下营业档口数量/可上线档口数量");
            }
        } catch (Exception e) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "窗口期外不允许修改档口数量/线下营业档口数量/可上线档口数量");
        }
    }

    /**
     * 校验承包商输入字段
     */
    public void checkContractor(WmCustomerBasicBo wmCustomerBasicBo) throws WmCustomerException, TException {
        //校验营业执照的信息
        if(wmCustomerBasicBo.getCustomerType() == CustomerType.CUSTOMER_TYPE_BUSINESS.getCode()){
            checkContractorLicense(wmCustomerBasicBo);
        }
        //校验身份信息
        if(wmCustomerBasicBo.getCustomerType() == CustomerType.CUSTOMER_TYPE_IDCARD.getCode()){
            checkContractorID(wmCustomerBasicBo);
        }
        //校验总部详细地址
        if (StringUtil.isNotBlank(wmCustomerBasicBo.getHqDetailAddress()) && wmCustomerBasicBo.getHqDetailAddress().length() > 200) {
            throw new WmCustomerException(WmScCodeConstants.BIZ_PARA_ERROR, "总部详细地址长度不允许超过200");
        }
        //校验合同编号 校验格式：WMYW-xxx-xx-xxxxxxxxx
        if (StringUtil.isNotBlank(wmCustomerBasicBo.getContractNum()) && !RegexUtil.isRegexMatch(RegexConstant.CONTRACTOR_CONTRACT_NUM_EX,wmCustomerBasicBo.getContractNum())) {
            throw new WmCustomerException(WmScCodeConstants.BIZ_PARA_ERROR, "合同编号不符合格式要求：WMYW-xxx-xx-xxxxxxxxx");
        }
        //食堂承包商只能是上级客户
        if (!Integer.valueOf(CustomerConstants.CUSTOMER_IS_LEAF_NO).equals(wmCustomerBasicBo.getIsLeaf())) {
            throw new WmCustomerException(WmScCodeConstants.BIZ_PARA_ERROR, "食堂承包商只能是上级客户");
        }
    }

    /**
     * 校验客户证件形式（营业执照）为电子形式时是否可选修改形式及校验有效期
     *
     * @param wmCustomerBasicBo
     * @throws WmCustomerException
     * @throws TException
     */
    public void checkCustomerCustomerType(WmCustomerBasicBo wmCustomerBasicBo) throws WmCustomerException, TException {
        if (wmCustomerBasicBo.getCertificateType() == null) {
            wmCustomerBasicBo.setCertificateType(CertificateTypeEnum.PAPER.getType());
        }
        //校验营业执照是否在枚举类型内
        if (CertificateTypeEnum.getByType(wmCustomerBasicBo.getCertificateType()) == null) {
            throw new WmCustomerException(WmScCodeConstants.BIZ_PARA_ERROR, "请输入正确的营业执照形式");
        }
        //校验
        if (wmCustomerBasicBo.getValidateDate() != 0L
                && wmCustomerBasicBo.getValidateDate() < DateUtil.unixTime()) {
            throw new WmCustomerException(WmScCodeConstants.BIZ_PARA_ERROR, "有效期已过期不可保存");
        }
    }

    /**
     * 校验营业执照的必填参数
     */
    private void checkContractorLicense(WmCustomerBasicBo wmCustomerBasicBo) throws WmCustomerException {
        if(StringUtil.isBlank(wmCustomerBasicBo.getCustomerName())){
            throw new WmCustomerException(WmScCodeConstants.BIZ_PARA_ERROR, "执照名称为空");
        }
    }

    /**
     * 校验身份信息的必填参数
     */
    private void checkContractorID(WmCustomerBasicBo wmCustomerBasicBo) throws WmCustomerException {
        if(StringUtil.isBlank(wmCustomerBasicBo.getCustomerName())){
            throw new WmCustomerException(WmScCodeConstants.BIZ_PARA_ERROR, "姓名为空");
        }
        if(StringUtil.isBlank(wmCustomerBasicBo.getCustomerNumber())){
            throw new WmCustomerException(WmScCodeConstants.BIZ_PARA_ERROR, "证件号码为空");
        }
    }

    /**
     * 校验非分页查询参数
     */
    public void checkQuerySchoolWithoutPage(String schoolName) throws WmCustomerException {
        if (StringUtil.isBlank(schoolName)) {
            throw new WmCustomerException(WmScCodeConstants.BIZ_PARA_ERROR, "必须输出参数才能查询");
        }
    }

    /**
     * 校验学校履约管控信息入参V2
     * @param wmScSchoolPerformanceVO wmScSchoolPerformanceVO
     * @throws Exception java.lang.Exception
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void checkSchoolPerformanceInfoInput(WmScSchoolPerformanceVO wmScSchoolPerformanceVO, Map<String, Boolean> authMap)
            throws Exception, WmSchCantException {
        if (wmScSchoolPerformanceVO == null
                || wmScSchoolPerformanceVO.getSchoolPrimaryId() == null
                || wmScSchoolPerformanceVO.getSchoolPrimaryId() <= 0) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "学校履约管控信息为空");
        }
        // 学校信息模块校验
        if (authMap.get(SCHOOL_ALLOW_DELIVERY_EDIT_OPERATION_CODE)) {
            // 校验校方是否允许配送进校信息
            checkSchoolAllowPerformanceInfo(wmScSchoolPerformanceVO);
        }

        // 履约信息模块校验
        if (authMap.get(SCHOOL_DELIVERY_INFO_EDIT_OPERATION_CODE)) {
            List<WmScSchoolPerformanceUnitVO> wmScSchoolPerformanceUnitVOList = wmScSchoolPerformanceVO.getSchoolPerformanceUnitList();
            // 履约信息模块最少一条，最多三条
            if (CollectionUtils.isEmpty(wmScSchoolPerformanceUnitVOList)) {
                throw new WmSchCantException(BIZ_PARA_ERROR, "请补全履约信息模块后再提交");
            }
            if (wmScSchoolPerformanceUnitVOList.size() > 3) {
                throw new WmSchCantException(BIZ_PARA_ERROR, "履约信息模块不能超过三条");
            }
            // 当履约信息模块第一条校外送校内的配送方式 = 选项4. 没有校外供给时，不能有其他条履约信息模块
            if (wmScSchoolPerformanceUnitVOList.get(0).getSchoolInDeliveryType().equals((int) SchoolInDeliveryTypeEnum.NO_SUPPLY.getType())) {
                if (wmScSchoolPerformanceUnitVOList.size() > 1) {
                    throw new WmSchCantException(BIZ_PARA_ERROR, "履约信息模块校外送校内的配送方式不合法");
                }
            }
            Set<Integer> deliveryTypeSet = new HashSet<>();
            deliveryTypeSet.add(wmScSchoolPerformanceUnitVOList.get(0).getSchoolInDeliveryType());
            // 履约信息模块除了第一条外，校外送校内的配送方式 不能为 选项4. 没有校外供给 && 不能重复
            for (int i = 1; i < wmScSchoolPerformanceUnitVOList.size(); i++) {
                if (deliveryTypeSet.contains(wmScSchoolPerformanceUnitVOList.get(i).getSchoolInDeliveryType())) {
                    throw new WmSchCantException(BIZ_PARA_ERROR, "履约信息模块校外送校内的配送方式不合法");
                }
                if (wmScSchoolPerformanceUnitVOList.get(i).getSchoolInDeliveryType().equals((int) SchoolInDeliveryTypeEnum.NO_SUPPLY.getType())) {
                    throw new WmSchCantException(BIZ_PARA_ERROR, "履约信息模块校外送校内的配送方式不合法");
                }
                deliveryTypeSet.add(wmScSchoolPerformanceUnitVOList.get(i).getSchoolInDeliveryType());
            }
            for (WmScSchoolPerformanceUnitVO wmScSchoolPerformanceUnitVO : wmScSchoolPerformanceUnitVOList) {
                if (wmScSchoolPerformanceUnitVO == null) {
                    throw new WmSchCantException(BIZ_PARA_ERROR, "请补全校外送校内的配送方式后再提交");
                }
                // 校外送校内的配送方式
                if (wmScSchoolPerformanceUnitVO.getSchoolInDeliveryType() == null
                        || SchoolInDeliveryTypeEnum.getByType(wmScSchoolPerformanceUnitVO.getSchoolInDeliveryType()) == null) {
                    throw new WmSchCantException(BIZ_PARA_ERROR, "请补全校外送校内的配送方式后再提交");
                }

                // 校外送校内的配送方式 = 选项4没有校外供给 -> 其他选项隐藏不校验
                if (!wmScSchoolPerformanceUnitVO.getSchoolInDeliveryType().equals((int) SchoolInDeliveryTypeEnum.NO_SUPPLY.getType())) {
                    // 校外送校内的配送方式 = 选项3商家自配送 -> 校验具体使用的自配方式与其他信息
                    if (wmScSchoolPerformanceUnitVO.getSchoolInDeliveryType().equals((int) SchoolInDeliveryTypeEnum.ZI_PEI.getType())) {
                        checkPoiSelfDeliveryTypeAndInfoV2(wmScSchoolPerformanceUnitVO);
                    }

                    // 该配送方式可将餐品送到的具体位置
                    if (wmScSchoolPerformanceUnitVO.getDeliverySpecificLocation() == null
                            || SchoolDeliverySpecificLocationEnum.getByType(wmScSchoolPerformanceUnitVO.getDeliverySpecificLocation()) == null) {
                        throw new WmSchCantException(BIZ_PARA_ERROR, "请补全该配送方式可将餐品送到的具体位置后再提交");
                    }

                    // 该配送方式可将餐品送到的具体位置 = 选项1距离学校门口/围栏较远的地方，学生需要出校取餐 -> 校验不能送到校门口原因及其他信息
                    if (wmScSchoolPerformanceUnitVO.getDeliverySpecificLocation().equals((int) SchoolDeliverySpecificLocationEnum.FAR_FROM_SCHOOL_GATE.getType())) {
                        checkDeliveryNotGateReasonAndInfoV2(wmScSchoolPerformanceUnitVO);
                    }

                    // 该配送方式可将餐品送到的具体位置 = 选项2 & 校外送校内的配送方式 = 选项2 -> 校验不能进校原因及其他信息
                    if (wmScSchoolPerformanceUnitVO.getDeliverySpecificLocation().equals((int) SchoolDeliverySpecificLocationEnum.DELIVERY_TO_SCHOOL_GATE.getType())
                            && wmScSchoolPerformanceUnitVO.getSchoolInDeliveryType().equals((int) SchoolInDeliveryTypeEnum.JU_HE_PEI.getType())) {
                        checkDeliveryNotEnterReasonAndInfoV2(wmScSchoolPerformanceUnitVO);
                    }

                    // 该配送方式可将餐品送到的具体位置 = 选项3 & 校外送校内的配送方式 = 选项1/3 -> 校验可以进校的原因及其他信息
                    if (wmScSchoolPerformanceUnitVO.getDeliverySpecificLocation().equals((int) SchoolDeliverySpecificLocationEnum.DELIVERY_TO_FIXED_POINT.getType())
                            && (wmScSchoolPerformanceUnitVO.getSchoolInDeliveryType().equals((int) SchoolInDeliveryTypeEnum.JI_SHI_PEI.getType())
                            || wmScSchoolPerformanceUnitVO.getSchoolInDeliveryType().equals((int) SchoolInDeliveryTypeEnum.ZI_PEI.getType()))) {
                        checkDeliveryEnterReasonAndInfoV2(wmScSchoolPerformanceUnitVO);
                    }

                    // 该配送方式可将餐品送到的具体位置 = 选项4 -> 校验可送餐上楼原因及其他信息
                    if (wmScSchoolPerformanceUnitVO.getDeliverySpecificLocation().equals((int) SchoolDeliverySpecificLocationEnum.DELIVERY_TO_DORMITORY.getType())) {
                        checkDeliveryUpstairsReasonAndInfoV2(wmScSchoolPerformanceUnitVO);
                    }
                }
            }
        }
    }

    /**
     * 校验可送餐上楼原因及其他信息V2
     *
     * @param wmScSchoolPerformanceUnitVO wmScSchoolPerformanceUnitVO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void checkDeliveryUpstairsReasonAndInfoV2(WmScSchoolPerformanceUnitVO wmScSchoolPerformanceUnitVO) throws WmSchCantException {
        if (wmScSchoolPerformanceUnitVO.getDeliveryUpstairsReason() == null
                || SchoolDeliveryUpstairsReasonEnum.getByType(wmScSchoolPerformanceUnitVO.getDeliveryUpstairsReason()) == null) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "请补全可送餐上楼原因后再提交");
        }

        // 可送餐上楼原因 = 选项4其他原因
        if (wmScSchoolPerformanceUnitVO.getDeliveryUpstairsReason().equals((int) SchoolDeliveryUpstairsReasonEnum.OTHERS.getType())) {
            if (StringUtils.isBlank(wmScSchoolPerformanceUnitVO.getDeliveryUpstairsInfo())) {
                throw new WmSchCantException(BIZ_PARA_ERROR, "请补全可送餐上楼其他原因后再提交");
            }

            if (wmScSchoolPerformanceUnitVO.getDeliveryUpstairsInfo().length() > SCHOOL_DELIVERY_OTHER_INFO_MAX_LENGTH) {
                throw new WmSchCantException(BIZ_PARA_ERROR, "请录入" + SCHOOL_DELIVERY_OTHER_INFO_MAX_LENGTH + "字以内的可送餐上楼其他原因");
            }
        }
    }


    /**
     * 校验可以进校的原因及其他信息V2
     *
     * @param wmScSchoolPerformanceUnitVO wmScSchoolPerformanceUnitVO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void checkDeliveryEnterReasonAndInfoV2(WmScSchoolPerformanceUnitVO wmScSchoolPerformanceUnitVO) throws WmSchCantException {
        if (wmScSchoolPerformanceUnitVO.getDeliveryEnterReason() == null
                || SchoolDeliveryEnterReasonEnum.getByType(wmScSchoolPerformanceUnitVO.getDeliveryEnterReason()) == null) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "请补全可以进校的原因后再提交");
        }

        // 可以进校的原因 = 选项3其他原因
        if (wmScSchoolPerformanceUnitVO.getDeliveryEnterReason().equals((int) SchoolDeliveryEnterReasonEnum.OTHERS.getType())) {
            if (StringUtils.isBlank(wmScSchoolPerformanceUnitVO.getDeliveryEnterInfo())) {
                throw new WmSchCantException(BIZ_PARA_ERROR, "请补全可以进校的其他原因后再提交");
            }

            if (wmScSchoolPerformanceUnitVO.getDeliveryEnterInfo().length() > SCHOOL_DELIVERY_OTHER_INFO_MAX_LENGTH) {
                throw new WmSchCantException(BIZ_PARA_ERROR, "请录入" + SCHOOL_DELIVERY_OTHER_INFO_MAX_LENGTH + "字以内的可以进校其他原因");
            }
        }
    }

    /**
     * 校验不能进校原因及其他信息V2
     *
     * @param wmScSchoolPerformanceUnitVO wmScSchoolPerformanceUnitVO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void checkDeliveryNotEnterReasonAndInfoV2(WmScSchoolPerformanceUnitVO wmScSchoolPerformanceUnitVO) throws WmSchCantException {
        if (wmScSchoolPerformanceUnitVO.getDeliveryNotEnterReason() == null
                || SchoolDeliveryNotEnterReasonEnum.getByType(wmScSchoolPerformanceUnitVO.getDeliveryNotEnterReason()) == null) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "请补全不能进校原因后再提交");
        }

        // 不能进校原因 = 选项2其他原因
        if (wmScSchoolPerformanceUnitVO.getDeliveryNotEnterReason().equals((int) SchoolDeliveryNotEnterReasonEnum.OTHERS.getType())) {
            if (StringUtils.isBlank(wmScSchoolPerformanceUnitVO.getDeliveryNotEnterInfo())) {
                throw new WmSchCantException(BIZ_PARA_ERROR, "请补全不能进校其他原因后再提交");
            }

            if (wmScSchoolPerformanceUnitVO.getDeliveryNotEnterInfo().length() > SCHOOL_DELIVERY_OTHER_INFO_MAX_LENGTH) {
                throw new WmSchCantException(BIZ_PARA_ERROR, "请录入" + SCHOOL_DELIVERY_OTHER_INFO_MAX_LENGTH + "字以内的不能进校其他原因");
            }
        }
    }

    /**
     * 校验不能送到校门口原因及其他信息V2
     *
     * @param wmScSchoolPerformanceUnitVO wmScSchoolPerformanceUnitVO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void checkDeliveryNotGateReasonAndInfoV2(WmScSchoolPerformanceUnitVO wmScSchoolPerformanceUnitVO) throws WmSchCantException {
        if (wmScSchoolPerformanceUnitVO.getDeliveryNotGateReason() == null
                || SchoolDeliveryNotGateReasonEnum.getByType(wmScSchoolPerformanceUnitVO.getDeliveryNotGateReason()) == null) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "请补全不能送到校门口原因后再提交");
        }

        // 不能送到校门口原因 = 选项3其他原因
        if (wmScSchoolPerformanceUnitVO.getDeliveryNotGateReason().equals((int) SchoolDeliveryNotGateReasonEnum.OTHERS.getType())) {
            if (StringUtils.isBlank(wmScSchoolPerformanceUnitVO.getDeliveryNotGateInfo())) {
                throw new WmSchCantException(BIZ_PARA_ERROR, "请补全不能送到校门口其他原因后再提交");
            }

            if (wmScSchoolPerformanceUnitVO.getDeliveryNotGateInfo().length() > SCHOOL_DELIVERY_OTHER_INFO_MAX_LENGTH) {
                throw new WmSchCantException(BIZ_PARA_ERROR, "请录入" + SCHOOL_DELIVERY_OTHER_INFO_MAX_LENGTH + "字以内的不能送到校门口其他原因");
            }
        }
    }

    /**
     * 校验具体使用的自配方式与其他信息V2
     *
     * @param wmScSchoolPerformanceUnitVO wmScSchoolPerformanceUnitVO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void checkPoiSelfDeliveryTypeAndInfoV2(WmScSchoolPerformanceUnitVO wmScSchoolPerformanceUnitVO) throws WmSchCantException {
        if (wmScSchoolPerformanceUnitVO.getPoiSelfDeliveryType() == null
                || SchoolPoiSelfDeliveryTypeEnum.getByType(wmScSchoolPerformanceUnitVO.getPoiSelfDeliveryType()) == null) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "请补全具体使用的自配方式后再提交");
        }
        // 具体使用的自配方式 = 其他
        if (wmScSchoolPerformanceUnitVO.getPoiSelfDeliveryType().equals((int) SchoolPoiSelfDeliveryTypeEnum.OTHERS.getType())) {
            if (StringUtils.isBlank(wmScSchoolPerformanceUnitVO.getPoiSelfDeliveryInfo())) {
                throw new WmSchCantException(BIZ_PARA_ERROR, "请补全具体使用的自配方式其他信息后再提交");
            }

            if (wmScSchoolPerformanceUnitVO.getPoiSelfDeliveryInfo().length() > SCHOOL_DELIVERY_OTHER_INFO_MAX_LENGTH) {
                throw new WmSchCantException(BIZ_PARA_ERROR, "请录入" + SCHOOL_DELIVERY_OTHER_INFO_MAX_LENGTH + "字以内的具体使用的自配方式其他信息");
            }
        }
    }

    /**
     * 校验校方是否允许配送进校信息V2
     *
     * @param wmScSchoolPerformanceVO wmScSchoolDeliveryVO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void checkSchoolAllowPerformanceInfo(WmScSchoolPerformanceVO wmScSchoolPerformanceVO) throws WmSchCantException {
        // 校方是否允许配送进校 = 其他
        if (wmScSchoolPerformanceVO.getSchoolAllowDelivery() != null
                && wmScSchoolPerformanceVO.getSchoolAllowDelivery().equals((int) SchoolAllowDeliveryV2Enum.OTHERS.getType())) {
            if (StringUtils.isBlank(wmScSchoolPerformanceVO.getSchoolAllowDeliveryInfo())) {
                throw new WmSchCantException(BIZ_PARA_ERROR, "请补全校方是否允许配送进校其他信息后再提交");
            }

            if (wmScSchoolPerformanceVO.getSchoolAllowDeliveryInfo().length() > SCHOOL_DELIVERY_OTHER_INFO_MAX_LENGTH) {
                throw new WmSchCantException(BIZ_PARA_ERROR, "请录入" + SCHOOL_DELIVERY_OTHER_INFO_MAX_LENGTH + "字以内的校方是否允许配送进校其他信息");
            }
        }
    }

    /**
     * 食堂档口批量创建线索-校验线索Excel
     * @param dataFile 上传文件
     * @param canteenPrimaryId 食堂主键ID
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws IOException java.io.IOException
     */
    public void checkCanteenStallUploadClueExcel(MultipartFile dataFile, Integer canteenPrimaryId) throws WmSchCantException, IOException, TException {
        if (canteenPrimaryId == null || canteenPrimaryId <= 0) {
            log.warn("[WmScCanteenStallService.uploadClueExcel] canteenPrimaryId is null, return. canteenPrimaryId = {}", canteenPrimaryId);
            throw new WmSchCantException(BIZ_PARA_ERROR, "请补全食堂后再保存");
        }

        if (dataFile == null || dataFile.getInputStream() == null) {
            log.warn("[WmScCanteenStallService.uploadClueExcel] dataFile.getInputStream is null. inputStream = {}", JSONObject.toJSONString(dataFile));
            throw new WmSchCantException(BIZ_PARA_ERROR, "请补全门店文件后再保存");
        }

        if (dataFile.getSize() > MccScConfig.getCanteenStallClueFileMaxSize() * 1024) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "上传文件过大，文件大小不能超过" + MccScConfig.getCanteenStallClueFileMaxSize() + "KB");
        }

        if (!FilenameUtils.isExtension(dataFile.getOriginalFilename().toLowerCase(), new String[]{"xlsx", "xls"})) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "文件格式不支持");
        }

        if (!MccScConfig.getSupportExcelContentTypes().contains(dataFile.getContentType())) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "非法的excel文件类型：" + dataFile.getContentType());
        }
    }

    /**
     * 校验食堂档口线索状态提审信息
     * @param submitVO submitVO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void checkCanteenStallClueSubmitVO(WmCanteenStallAuditSubmitVO submitVO) throws WmSchCantException {
        // 标记异常提审
        if (submitVO.getAuditTaskType().equals((int) CanteenStallAuditTaskTypeEnum.STALL_BIND_CLUE_ABNORMAL.getType())) {
            if (submitVO.getAbnormalReason() == null
                    || CanteenStallClueAbnormalReasonEnum.getByType(submitVO.getAbnormalReason()) == null) {
                throw new WmSchCantException(BIZ_PARA_ERROR, "请选择标记异常原因");
            }

            if (submitVO.getProofPicture().size() > ALLOW_MAX_CANTEEN_STALL_AUDIT_PIC_NUM) {
                throw new WmSchCantException(BIZ_PARA_ERROR, "标记证明最多上传三张");
            }
        }
    }


    /**
     * 公海绑定提交基本信息校验
     * @param submitVO submitVO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void checkCanteenStallSubmitByWdcBind(WmCanteenStallBindSubmitVO submitVO) throws WmSchCantException, TException {
        if (submitVO == null || submitVO.getCanteenPrimaryId() == null || CollectionUtils.isEmpty(submitVO.getWdcClueIdList())) {
            log.error("[WmScCanteenStallService.checkCanteenStallSubmitByWdcBind] param invalid. submitVO = {}", JSONObject.toJSONString(submitVO));
            throw new WmSchCantException(BIZ_PARA_ERROR, "入参为空");
        }

        if (submitVO.getWdcClueIdList().size() > MccScConfig.getCanteenStallWdcClueSubmitMaxSize()) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "线索编号最多输入" + MccScConfig.getCanteenStallWdcClueSubmitMaxSize() + "个");
        }
    }

    /**
     * 外卖门店绑定提交基本信息校验
     * @param submitVO submitVO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void checkCanteeStallSubmitByWmPoiBind(WmCanteenStallBindSubmitVO submitVO) throws WmSchCantException, TException {
        if (submitVO == null || submitVO.getCanteenPrimaryId() == null || CollectionUtils.isEmpty(submitVO.getWmPoiIdList())) {
            log.error("[checkCanteeStallSubmitByWmPoiBind.checkCanteeStallSubmitByWmPoiBind] param invalid. submitVO = {}", JSONObject.toJSONString(submitVO));
            throw new WmSchCantException(BIZ_PARA_ERROR, "入参为空");
        }

        if (submitVO.getWmPoiIdList().size() > MccScConfig.getCanteenStallWmPoiSubmitMaxSize()) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "外卖门店编号最多输入" + MccScConfig.getCanteenStallWmPoiSubmitMaxSize() + "个");
        }
    }

}

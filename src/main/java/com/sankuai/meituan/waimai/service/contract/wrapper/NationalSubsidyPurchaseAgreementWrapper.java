package com.sankuai.meituan.waimai.service.contract.wrapper;

import com.sankuai.meituan.auth.vo.User;
import com.sankuai.meituan.waimai.constant.contract.ContractSceneEnum;
import com.sankuai.meituan.waimai.service.adaptor.WmCustomerFrameContractThriftServiceAdapter;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.framecontract.response.ContractSignSubjectInfo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerBasicBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.vo.ContractVo;
import com.sankuai.meituan.waimai.vo.base.MultiFileJsonVo;
import com.sankuai.meituan.waimai.vo.sign.SignSubjectInfoVo;
import org.apache.logging.log4j.util.Strings;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 国补采销合作协议专用Wrapper
 */
public class NationalSubsidyPurchaseAgreementWrapper extends AbstractContractWrapper {

    private static final Logger logger = LoggerFactory.getLogger(NationalSubsidyPurchaseAgreementWrapper.class);

    @Resource
    private WmCustomerFrameContractThriftServiceAdapter wmCustomerFrameContractThriftServiceAdapter;

    public NationalSubsidyPurchaseAgreementWrapper(ContractVo contractVo, User user) {
        super(contractVo, user);
    }

    @Override
    public ContractVo wrap() throws WmCustomerException, TException {
        initPartyA();
        initPartyB();
        setSignTime();
        contractVo.setContractScan(new MultiFileJsonVo());
        if (isSave()) {
            contractVo.setContractNum("电子合同保存后自动生成编号");
        }
        if (ContractSceneEnum.isGetSimpleInfo(contractVo.getCurrentContractScene())) {
            setPartyBSignSubjectInfoVo();
        }
        return contractVo;
    }

    @Override
    protected void initPartyB() throws TException, WmCustomerException {
        if (!ContractSceneEnum.isSave(contractVo.getCurrentContractScene())) {
            return;
        }
        if (contractVo.getPartyB() == null || contractVo.getPartyB().getSignId() == null || contractVo.getPartyB().getSignId() <= 0) {
            throw new WmCustomerException(CustomerErrorCodeConstants.PARAM_ERROR, "当前客户未绑定集合店，请绑定后重新操作");
        }

        if (Strings.isEmpty(contractVo.getPartyB().getSignName())) {
            WmCustomerBasicBo customer = wmCustomerThriftService.getCustomerById(contractVo.getPartyB().getSignId());
            if (customer == null) {
                throw new WmCustomerException(CustomerErrorCodeConstants.BUSINESS_ERROR, "未查询到集合店主体信息，请重新操作");
            }
            contractVo.getPartyB().setSignName(customer.getCustomerName());
        }

        WmCustomerKp customerKpOfSigner = wmCustomerKpThriftService.getCustomerSignerKp(contractVo.getPartyB().getSignId());
        if (customerKpOfSigner == null) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BUSINESS_ERROR, "未查询到集合店签约人信息，请重新操作");
        }
        contractVo.getPartyB().setSignPeople(customerKpOfSigner.getCompellation());
        contractVo.getPartyB().setSignPhone(customerKpOfSigner.getPhoneNum());
    }

    /**
     * 设置ContractVo中乙方签约主体信息
     */
    private void setPartyBSignSubjectInfoVo() throws WmCustomerException {
        if (contractVo.getCustomerId() == null) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BUSINESS_ERROR, "客户id为空");
        }

        try {
            List<ContractSignSubjectInfo> signSubjectInfoList = wmCustomerFrameContractThriftServiceAdapter.queryContractSignSubjectInfo(contractVo.getCustomerId());
            if (CollectionUtils.isEmpty(signSubjectInfoList)) {
                logger.info("NationalSubsidyPurchaseAgreementWrapper#setPartyBSignSubjectInfoVo, 客户没有关联的签约主体信息, customerId: {}", contractVo.getCustomerId());
                return;
            }

            List<SignSubjectInfoVo> subjectInfoVoList = signSubjectInfoList.stream()
                    .map(this::convertToSignSubjectInfoVo)
                    .collect(Collectors.toList());
            contractVo.setPartBSignSubjectInfoList(subjectInfoVoList);
        } catch (WmCustomerException e) {
            logger.warn("NationalSubsidyPurchaseAgreementWrapper#setPartyBSignSubjectInfoVo, warn", e);
            throw e;
        } catch (Exception e) {
            logger.error("NationalSubsidyPurchaseAgreementWrapper#setPartyBSignSubjectInfoVo, error", e);
            throw new WmCustomerException(CustomerErrorCodeConstants.BUSINESS_ERROR, "系统异常，当前暂无可选择集合店对象，请稍后重试");
        }
    }

    /**
     * 转换签约主体信息
     */
    private SignSubjectInfoVo convertToSignSubjectInfoVo(ContractSignSubjectInfo subjectInfo) {
        SignSubjectInfoVo signSubjectInfoVo = new SignSubjectInfoVo();
        signSubjectInfoVo.setSignSubjectId(subjectInfo.getSignSubjectId());
        signSubjectInfoVo.setSignSubjectName(subjectInfo.getSignSubjectName());
        return signSubjectInfoVo;
    }


} 
package com.sankuai.meituan.waimai.service.base;

import com.sankuai.meituan.waimai.kv.groupm.service.TairLockHandler;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 17/8/29.
 */
@Service
public class WmContractLockService {

    private static Logger log = LoggerFactory.getLogger(WmContractLockService.class);

    private static final int MAX_RETRY_TIMES = 5;

    @Autowired
    private TairLockHandler tairLockHandler;

    public boolean lock(String key) {
        log.info("WmContractLockService lock key = " + key);
        for (int retryTime = 0; retryTime < MAX_RETRY_TIMES; retryTime++) {
            if (tairLockHandler.tryLock(key)) {
                return true;
            }
        }
        return false;
    }

    public void unlock(String key) {
        log.info("WmContractLockService lock key = " + key);
        tairLockHandler.unLock(key);
    }
}

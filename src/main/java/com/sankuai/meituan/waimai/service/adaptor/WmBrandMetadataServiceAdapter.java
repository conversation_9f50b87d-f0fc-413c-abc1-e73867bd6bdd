package com.sankuai.meituan.waimai.service.adaptor;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.scmbrand.thrift.domain.apply.BrandApplyDomainDataDTO;
import com.sankuai.meituan.scmbrand.thrift.domain.apply.BrandApplyMetadataTabResultDTO;
import com.sankuai.meituan.scmbrand.thrift.domain.common.MetadataFieldElResultDTO;
import com.sankuai.meituan.scmbrand.thrift.domain.metadataplatform.MetadataCheckRequestDTO;
import com.sankuai.meituan.scmbrand.thrift.domain.metadataplatform.MetadataTabQueryDTO;
import com.sankuai.meituan.scmbrand.thrift.exception.BrandServiceException;
import com.sankuai.meituan.scmbrand.thrift.service.metadataplatform.BrandMetadataOuterService;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 品牌元数据服务适配器
 * <AUTHOR>
 * @date 2024/02/27
 * @email <EMAIL>
 */
@Service
@Slf4j
public class WmBrandMetadataServiceAdapter {

    @Autowired
    private BrandMetadataOuterService brandMetadataOuterService;

    /**
     * 元数据平台使用业务方. 1-校园食堂
     */
    public static final int SCHOOL_METADATA_BUSINESS_ID = 1;

    /**
     * 通过业务参数查询元数据DTO
     * @param queryDTO 业务参数, 包含JSONString和自定义元素属性
     * @return 元数据DTO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public List<BrandApplyMetadataTabResultDTO> getMetadataTabWithoutDomainData(MetadataTabQueryDTO queryDTO) throws WmSchCantException {
        log.info("[WmBrandMetadataServiceAdapter.getMetadataTabWithoutDomainData] input param: queryDTO = {}", JSONObject.toJSONString(queryDTO));
        try {
            List<BrandApplyMetadataTabResultDTO> resultDTOList = brandMetadataOuterService.queryMetadataTabWithoutDomainData(queryDTO);
            log.info("[WmBrandMetadataServiceAdapter.getMetadataTabWithoutDomainData] resultDTOList = {}", JSONObject.toJSONString(resultDTOList));
            return resultDTOList;
        } catch (Exception e) {
            log.error("[WmBrandMetadataServiceAdapter.getMetadataTabWithoutDomainData] Exception. queryDTO = {}", JSONObject.toJSONString(queryDTO), e);
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "查询元数据平台异常");
        }
    }

    /**
     * 元数据信息校验
     * @param requestDTO 元数据业务数据
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void metadataCheck(MetadataCheckRequestDTO requestDTO) throws WmSchCantException {
        try {
            brandMetadataOuterService.metadataCheck(requestDTO);
        } catch (BrandServiceException e) {
            log.error("[WmBrandMetadataServiceAdapter.metadataCheck] Exception. requestDTO = {}", JSONObject.toJSONString(requestDTO), e);
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "信息校验不通过，原因为：" + e.getMsg());
        }
    }

    public List<MetadataFieldElResultDTO> validateRules(String multiId) throws WmSchCantException {
        try {
            if (StringUtils.isBlank(multiId)) {
                return new ArrayList<>();
            }
            return brandMetadataOuterService.selectFieldFrontRule(multiId, SCHOOL_METADATA_BUSINESS_ID);
        } catch (BrandServiceException e) {
            log.error("[WmBrandMetadataServiceAdapter.validateRules] BrandServiceException. multiId = {}", multiId, e);
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "数据校验异常");
        }
    }


}

package com.sankuai.meituan.waimai.service.handler.school;

import com.alibaba.excel.EasyExcelFactory;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.meituan.mtrace.thread.pool.TraceExecutors;
import com.sankuai.meituan.waimai.exception.BusinessException;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import com.sankuai.meituan.waimai.mtauth.util.WmEmployUtils;
import com.sankuai.meituan.waimai.service.handler.ResultCode;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.SchoolCategoryUploadEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.SchoolBo;
import com.sankuai.meituan.waimai.thrift.customer.service.sc.WmSchoolThriftService;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import com.sankuai.meituan.waimai.util.base.WmRestReturnUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants.BIZ_PARA_ERROR;

@Service
@Slf4j
public class SchoolExcelBatchApplication {
    @Autowired
    private WmSchoolThriftService schoolThriftService;

    private final ExecutorService executor = TraceExecutors.getTraceExecutorService(
            new ThreadPoolExecutor(20, 50, 5, TimeUnit.MINUTES,
                    new LinkedBlockingQueue<>(1000), new ThreadFactoryBuilder().setNameFormat("export-lead-index-%d").build(),
                    new ThreadPoolExecutor.CallerRunsPolicy())
    );

    private long maxLine = 1000;

    public Object batchAssign(byte[] bytes) throws WmSchCantException {
        // 1.校验文件
        try (ByteArrayInputStream bais = new ByteArrayInputStream(bytes)) {
            SchoolBatchAssignExcelLineCheckHandler handler = new SchoolBatchAssignExcelLineCheckHandler(maxLine);
            EasyExcelFactory.read(bais, SchoolBatchAssignExcelTemplate.class, handler).headRowNumber(1).doReadAll();
            if (StringUtils.isNotEmpty(handler.getMessage())) {
                throw new BusinessException(ResultCode.BATCH_INSERT_DATA_ERROR, handler.getMessage());
            }
        } catch (IOException e) {
            log.error("文件内容前置检测失败", e);
            throw new BusinessException("批量分配的文件内容检测失败", e);
        }

        WmEmploy currentEmploy = WmEmployUtils.getWmEmploy();
        if (currentEmploy == null) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "查询用户信息失败");
        }

        // 2.处理文件
        try {
            executor.execute(() -> {
                try (ByteArrayInputStream bais = new ByteArrayInputStream(bytes)) {
                    // EasyExcelFactor数据处理
                    SchoolBatchAssignHandler batchAssignHandler = new SchoolBatchAssignHandler();

                    // 读取数据
                    EasyExcelFactory.read(bais, SchoolBatchAssignExcelTemplate.class, batchAssignHandler).headRowNumber(1).doReadAll();

                    // 转换数据
                    List<SchoolBatchAssignExcelTemplate> list = batchAssignHandler.getList();
                    List<SchoolBo> schoolBos = new ArrayList<>();
                    if (list.size() != 0){
                        for (SchoolBatchAssignExcelTemplate schoolBatchAssignExcelTemplate : list) {
                            SchoolBo schoolBo  = new SchoolBo();
                            // BatchAssignHandler数据已校验
                            if (schoolBatchAssignExcelTemplate.getSchoolId().contains("."))
                                schoolBatchAssignExcelTemplate.setSchoolId(schoolBatchAssignExcelTemplate.getSchoolId().split("\\.")[0]);
                            schoolBo.setSchoolId(Integer.parseInt(schoolBatchAssignExcelTemplate.getSchoolId()));
                            SchoolCategoryUploadEnum byName = SchoolCategoryUploadEnum.getByName(schoolBatchAssignExcelTemplate.getSchoolCategoryDesc());
                            schoolBo.setSchoolCategory(Integer.valueOf(byName == null ? null : byName.getType()));
                            schoolBos.add(schoolBo);
                        }
                    }
                    log.info("uploadSchoolInfoList.doAfterAllAnalysed] schoolBos = {}", schoolBos);

                    // 调用业务逻辑处理
                    if (schoolBos.size() != 0){
                        schoolThriftService.schoolBoExcelUpLoad(schoolBos, currentEmploy.getUid());
                    }
                } catch (Exception e) {
                    log.error("读取分配文件失败", e);
                    throw new BusinessException(ResultCode.BATCH_INSERT_DATA_ERROR, "读取分配文件失败");
                }
            });
        } catch (Exception e) {
            log.error("uploadCanteenInfoList,上传文件发生异常", e);
            throw new BusinessException(ResultCode.BATCH_INSERT_DATA_ERROR, "上传文件发生异常");
        }
        return WmRestReturnUtil.success(null);
    }
}

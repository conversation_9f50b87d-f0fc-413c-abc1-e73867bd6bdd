package com.sankuai.meituan.waimai.service.contract.wrapper;

import com.sankuai.meituan.auth.vo.User;
import com.sankuai.meituan.waimai.thrift.agent.domain.WmCommercialAgentInfo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.vo.ContractVo;
import com.sankuai.meituan.waimai.vo.WmContractAgentInfo;
import com.sankuai.meituan.waimai.vo.base.MultiFileJsonVo;
import org.apache.thrift.TException;

/**
 * @description: 到餐C2合同
 * @author: zhangyuanhao02
 * @create: 2024/8/7 16:40
 */
public class DcC2EContractWrapper extends AbstractContractWrapper {

    public DcC2EContractWrapper(ContractVo contractVo, User user) {
        super(contractVo, user);
    }

    @Override
    public ContractVo wrap() throws WmCustomerException, TException {
        contractVo.setContractScan(new MultiFileJsonVo());
        WmContractAgentInfo agentById = wmContractAgentService.getAgentById(contractVo.getPartyB().getSignId());

        if (agentById != null) {
            contractVo.getPartyB().setSignPeople(agentById.getLegalPerson());
            contractVo.getPartyB().setSignName(agentById.getName());
        }
        initPartyADcC2();
        setSignTime();
        contractVo.getPartyB().setSignPhone("");

        if (isSave()) {
            contractVo.setContractNum("电子合同保存后自动生成编号");
        }
        return contractVo;
    }
}

package com.sankuai.meituan.waimai.service.customer;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.poibrand.constants.WmPoiBrandConstants;
import com.sankuai.meituan.waimai.poibrand.domain.WmPoiServiceBrand;
import com.sankuai.meituan.waimai.poibrand.service.WmPoiBrandQueryThriftService;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerBrandListBo;
import com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerBrandThriftService;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import com.sankuai.meituan.waimai.vo.customer.WmCustomerBrandListVo;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;

@Service
public class WmCustomerBrandService {

    private static Logger logger = LoggerFactory.getLogger(WmCustomerBrandService.class);

    @Autowired
    private WmPoiBrandQueryThriftService.Iface wmPoiBrandQueryThriftService;

    @Autowired
    private WmCustomerBrandThriftService wmCustomerBrandThriftService;

    @Autowired
    private WmCustomerCheckAuthService wmCustomerCheckAuthService;

    public Map<Integer, String> checkCustomerBrandRel(Integer customerId, List<Integer> brandIdList) {
        try {
            List<Integer> existBrandIdList = wmCustomerBrandThriftService.selectBrandIdListByCustomerId(customerId);
            List<WmPoiServiceBrand> wmPoiServiceBrandList = wmPoiBrandQueryThriftService.mgetWmPoiServiceBrand(existBrandIdList);
            Map<Integer, String> brandIdNameMap = Maps.newHashMap();
            for (WmPoiServiceBrand wmPoiServiceBrand : wmPoiServiceBrandList) {
                if (!brandIdList.contains(wmPoiServiceBrand.getId())) {
                    continue;
                }
                brandIdNameMap.put(wmPoiServiceBrand.getId(), wmPoiServiceBrand.getBranch_name());
            }
            return brandIdNameMap;
        } catch (WmCustomerException e) {
            logger.error("校验客户品牌关联异常 code = {}, msg = {}", e.getCode(), e.getMsg(), e);
        } catch (WmServerException e) {
            logger.error("校验客户品牌关联异常 code = {}, msg = {}", e.getCode(), e.getMsg(), e);
        } catch (TException e) {
            logger.error("校验客户品牌关联异常", e);
        }
        return Maps.newHashMap();
    }


    public WmCustomerBrandListVo customerBrandListBo2Vo(WmCustomerBrandListBo wmCustomerBrandListBo) {
        WmCustomerBrandListVo wmCustomerBrandListVo = new WmCustomerBrandListVo();
        if (wmCustomerBrandListBo == null) {
            return wmCustomerBrandListVo;
        }

        wmCustomerBrandListVo.setCustomerId(wmCustomerBrandListBo.getCustomerId());
        wmCustomerBrandListVo.setBrandId(wmCustomerBrandListBo.getBrandId());
        wmCustomerBrandListVo.setBrandName(wmCustomerBrandListBo.getBrandName());
        wmCustomerBrandListVo.setBrandLogo(wmCustomerBrandListBo.getBrandLogo());
        wmCustomerBrandListVo.setCusBrandPoiNum(wmCustomerBrandListBo.getCusBrandPoiNum());
        wmCustomerBrandListVo.setWmBrandPoiNum(wmCustomerBrandListBo.getWmBrandPoiNum());
        wmCustomerBrandListVo.setPhysicalBrandPoiNum(wmCustomerBrandListBo.getPhysicalBrandPoiNum());

        WmPoiBrandConstants.BrandType brandType = WmPoiBrandConstants.BrandType.of(wmCustomerBrandListBo.getBrandType());
        String brandName = brandType != null ? brandType.getDesc() : "";
        wmCustomerBrandListVo.setBrandTypeName(brandName);

        return wmCustomerBrandListVo;
    }

    public List<WmCustomerBrandListVo> customerBrandListBoList2VoList(List<WmCustomerBrandListBo> wmCustomerBrandListBoList, boolean isAuth) {
        if (CollectionUtils.isEmpty(wmCustomerBrandListBoList)) {
            return Lists.newArrayList();
        }

        List<WmCustomerBrandListVo> wmCustomerBrandListVoList = Lists.newArrayList();
        for (WmCustomerBrandListBo wmCustomerBrandListBo : wmCustomerBrandListBoList) {
            WmCustomerBrandListVo vo = customerBrandListBo2Vo(wmCustomerBrandListBo);
            vo.setAuthInfo(wmCustomerCheckAuthService.getCustomerBrandListDetailAuth(isAuth));
            wmCustomerBrandListVoList.add(vo);
        }
        return wmCustomerBrandListVoList;
    }

}

package com.sankuai.meituan.waimai.service.sc.canteenstall;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONArray;
import com.meituan.kafka.utils.Json;
import com.meituan.payment.bankinfo.thrift.idl.BankInfo;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.auth.vo.User;
import com.sankuai.meituan.waimai.config.MccConfig;
import com.sankuai.meituan.waimai.config.MccScConfig;
import com.sankuai.meituan.waimai.constant.sc.WmScGrayPlatformConstant;
import com.sankuai.meituan.waimai.e.graycenter.sdk.GrayConfigClient;
import com.sankuai.meituan.waimai.e.graycenter.sdk.domain.GrayParams;
import com.sankuai.meituan.waimai.e.graycenter.sdk.exception.InvalidParamGrayException;
import com.sankuai.meituan.waimai.e.graycenter.sdk.exception.KeyNotExistGrayException;
import com.sankuai.meituan.waimai.e.graycenter.sdk.exception.UnexpectedGrayException;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import com.sankuai.meituan.waimai.mtauth.util.WmEmployUtils;
import com.sankuai.meituan.waimai.service.base.AbstractExcelAnalysisService;
import com.sankuai.meituan.waimai.service.base.WmCanteenStallClueExcelAnalysisService;
import com.sankuai.meituan.waimai.service.sc.WmScCheckService;
import com.sankuai.meituan.waimai.service.sc.auth.WmScAuthService;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.*;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.canteenstall.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.CanteenBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.SchoolAreaBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.SchoolBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmCanteenPoiSimpleStreamDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.canteenstall.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.dto.WmScCanteenPoiTaskDTO;
import com.sankuai.meituan.waimai.thrift.customer.service.sc.WmCanteenPoiThriftService;
import com.sankuai.meituan.waimai.thrift.customer.service.sc.WmCanteenStallThriftService;
import com.sankuai.meituan.waimai.thrift.customer.service.sc.WmCanteenThriftService;
import com.sankuai.meituan.waimai.thrift.customer.service.sc.WmSchoolThriftService;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import com.sankuai.meituan.waimai.util.WmScTransUtil;
import com.sankuai.meituan.waimai.util.base.WmRestReturnUtil;
import com.sankuai.meituan.waimai.vo.sc.CanteenListVo;
import com.sankuai.meituan.waimai.vo.sc.SchoolListVo;
import com.sankuai.meituan.waimai.vo.sc.WmCanteenPoiSimpleAuditInfoVO;
import com.sankuai.meituan.waimai.vo.sc.WmUnBindPoiTaskVo;
import com.sankuai.meituan.waimai.vo.sc.canteenstall.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.openxml4j.exceptions.OpenXML4JException;
import org.apache.thrift.TException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.xml.sax.SAXException;

import javax.xml.parsers.ParserConfigurationException;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.util.*;

import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants.*;

/**
 * 食堂档口管理系统Service
 * <AUTHOR>
 * @date 2024/05/10
 * @email <EMAIL>
 */
@Slf4j
@Service
public class WmScCanteenStallService {

    @Autowired
    private WmCanteenThriftService canteenThriftService;

    @Autowired
    private WmCanteenStallThriftService wmCanteenStallThriftService;

    @Autowired
    private WmScCheckService wmScCheckService;

    @Autowired
    private WmSchoolThriftService wmSchoolThriftService;

    @Autowired
    private WmScAuthService wmScAuthService;

    @Autowired
    private WmCanteenStallClueExcelAnalysisService wmCanteenStallClueExcelAnalysisService;

    @Autowired
    private WmCanteenPoiThriftService wmCanteenPoiThriftService;

    /**
     * 模糊搜索操作人拥有数据权限的学校
     * @param content 模糊搜索内容
     * @return List<SchoolListVo>
     * @throws TException org.apache.thrift.TException
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public List<SchoolListVo> getSchoolListByFuzzySearchWithAuth(String content) throws TException, WmSchCantException {
        log.info("[WmScCanteenStallService.getSchoolListByFuzzySearchWithAuth] input param: content = {}", content);
        if (StringUtils.isBlank(content)) {
            log.warn("[WmScCanteenStallService.getSchoolListByFuzzySearchWithAuth] content is blank. content = {}", content);
            throw new WmSchCantException(BIZ_PARA_ERROR, "查询参数为空");
        }

        WmEmploy currentEmploy = WmEmployUtils.getWmEmploy();
        if (currentEmploy == null) {
            log.error("[WmScCanteenStallService.getSchoolListByFuzzySearchWithAuth] get currentEmploy failed. content = {}", content);
            throw new WmSchCantException(BIZ_AUTH_ERROR, "查询用户信息失败");
        }
        List<SchoolBo> schoolBoList = wmSchoolThriftService.getSchoolListByFuzzySearchWithAuth(content, currentEmploy.getUid());
        return WmScTransUtil.schoolBoToVo(schoolBoList);
    }

    /**
     * 模糊搜索操作人拥有数据权限的食堂
     * @param schoolPrimaryId 学校主键ID
     * @return List<CanteenListVo>
     * @throws TException org.apache.thrift.TException
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public List<CanteenListVo> getCanteenListBySchoolPrimaryIdWithAuth(Integer schoolPrimaryId) throws TException, WmSchCantException {
        log.info("[WmScCanteenStallService.getCanteenListByFuzzySearchWithAuth] input param: schoolPrimaryId = {}", schoolPrimaryId);
        if (schoolPrimaryId == null || schoolPrimaryId <= 0) {
            log.info("[WmScCanteenStallService.getCanteenListByFuzzySearchWithAuth] schoolPrimaryId is null. schoolPrimaryId = {}", schoolPrimaryId);
            return new ArrayList<>();
        }

        WmEmploy currentEmploy = WmEmployUtils.getWmEmploy();
        if (currentEmploy == null) {
            log.error("[WmScCanteenStallService.getCanteenListByFuzzySearchWithAuth] get currentEmploy failed. schoolPrimaryId = {}", schoolPrimaryId);
            throw new WmSchCantException(BIZ_AUTH_ERROR, "查询用户信息失败");
        }

        List<CanteenBo> canteenBoList = canteenThriftService.getCanteenListBySchoolPrimaryIdWithAuth(schoolPrimaryId, currentEmploy.getUid());
        return WmScTransUtil.canteenBoToVo(canteenBoList);
    }

    /**
     * 上传并校验线索Excel
     * @param dataFile 线索Excel
     * @param canteenPrimaryId 食堂主键ID
     * @return 错误信息列表
     */
    public WmCanteenStallSubmitResponseVO uploadClueExcel(MultipartFile dataFile, Integer canteenPrimaryId) throws TException, WmSchCantException, IOException, AbstractExcelAnalysisService.ExcelAnalyseException, OpenXML4JException, ParserConfigurationException, InvocationTargetException, SAXException, NoSuchMethodException, InstantiationException, IllegalAccessException {
        log.info("[WmScCanteenStallService.uploadClueExcel] input param: canteenPrimaryId = {}", canteenPrimaryId);
        WmCanteenStallSubmitResponseVO responseVO = new WmCanteenStallSubmitResponseVO();
        WmEmploy currentEmploy = WmEmployUtils.getWmEmploy();
        if (currentEmploy == null) {
            log.error("[WmScCanteenStallService.uploadClueExcel] currentEmploy is null. canteenPrimaryId = {}", canteenPrimaryId);
            throw new WmSchCantException(BIZ_PARA_ERROR, "获取人员信息失败");
        }
        // 1-操作鉴权
        wmScAuthService.checkBatchCreateClueUploadPageAuth(currentEmploy.getUid());

        // 2-校验基本信息
        wmScCheckService.checkCanteenStallUploadClueExcel(dataFile, canteenPrimaryId);

        // 3-解析上传的Excel文件
        List<WmCanteenStallClueExcelVO> clueExcelVOList = new ArrayList<>();
        try {
            clueExcelVOList = wmCanteenStallClueExcelAnalysisService.analysisToClass(dataFile.getInputStream());
            setClueExcelVOListWithLineNum(clueExcelVOList);
            log.info("[WmScCanteenStallService.uploadClueExcel] clueExcelVOList = {}", JSONObject.toJSONString(clueExcelVOList));
        } catch (IllegalArgumentException e) {
            log.warn("[WmScCanteenStallService.uploadClueExcel] IllegalArgumentException. canteenPrimaryId = {}", canteenPrimaryId, e);
            throw new WmSchCantException(BIZ_PARA_ERROR, e.getMessage());
        }

        // 4-校验食堂信息与Excel文件内容
        List<WmCanteenStallClueExcelDTO> clueCheckDTOList = wmCanteenStallThriftService.checkClueExcelDTOList(
                canteenPrimaryId,
                WmScTransUtil.transCanteenStallClueExcelVOsToDTOs(clueExcelVOList)
        );
        log.info("[WmScCanteenStallService.uploadClueExcel] clueCheckDTOList = {}", JSONObject.toJSONString(clueCheckDTOList));

        // 5-如果存在有错误信息的线索则返回
        List<WmCanteenStallClueErrorVO> clueErrorVOList = getClueErrorVOListByCheckDTOList(clueCheckDTOList);
        if (CollectionUtils.isNotEmpty(clueErrorVOList)) {
            responseVO.setClueErrorList(clueErrorVOList);
            return responseVO;
        }

        // 6-保存Excel线索并生成档口管理任务
        Integer manageId = wmCanteenStallThriftService.saveClueExcelDTOList(canteenPrimaryId, clueCheckDTOList, currentEmploy.getUid());
        responseVO.setManageId(manageId);
        return responseVO;
    }


    private void setClueExcelVOListWithLineNum(List<WmCanteenStallClueExcelVO> clueExcelVOList) {
        int startRow = wmCanteenStallClueExcelAnalysisService.startRow() + 1;
        if (CollectionUtils.isEmpty(clueExcelVOList)) {
            return;
        }

        for (WmCanteenStallClueExcelVO excelVO : clueExcelVOList) {
            excelVO.setLineNum(++ startRow);
        }
    }

    /**
     * 根据线索校验结果过滤得到错误VOList
     * @param clueCheckDTOList 线索校验结果
     * @return 错误VOList
     */
    public List<WmCanteenStallClueErrorVO> getClueErrorVOListByCheckDTOList(List<WmCanteenStallClueExcelDTO> clueCheckDTOList) {
        List<WmCanteenStallClueErrorVO> clueErrorVOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(clueCheckDTOList)) {
            return clueErrorVOList;
        }

        for (WmCanteenStallClueExcelDTO clueExcelDTO : clueCheckDTOList) {
            if (CollectionUtils.isEmpty(clueExcelDTO.getErrorReasonList())) {
                continue;
            }
            WmCanteenStallClueErrorVO errorVO = new WmCanteenStallClueErrorVO();
            errorVO.setLineNum(clueExcelDTO.getLineNum());
            errorVO.setCluePoiName(clueExcelDTO.getCluePoiName());
            errorVO.setCluePoiCate(clueExcelDTO.getCluePoiCate());
            errorVO.setSecondCityName(clueExcelDTO.getSecondCityName());
            errorVO.setThirdCityName(clueExcelDTO.getThirdCityName());
            errorVO.setCluePoiAddress(clueExcelDTO.getCluePoiAddress());
            errorVO.setCluePoiPhoneNum(clueExcelDTO.getCluePoiPhoneNum());
            errorVO.setErrorReasonList(WmScTransUtil.transClueErrorReasonListToDesc(clueExcelDTO.getErrorReasonList()));
            clueErrorVOList.add(errorVO);
        }
        return clueErrorVOList;
    }

    /**
     * 根据档口管理任务ID获取线索详情列表
     * @param manageId 档口管理任务ID
     * @return WmCanteenStallClueListVO
     * @throws TException org.apache.thrift.TException
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public WmCanteenStallClueListVO getCanteenStallClueList(Integer manageId) throws TException, WmSchCantException {
        log.info("[WmScCanteenStallService.getCanteenStallClueList] input param: manageId = {}", manageId);
        if (manageId == null || manageId <= 0) {
            log.error("[WmScCanteenStallService.getCanteenStallClueList] manageId is null. manageId = {}", manageId);
            throw new WmSchCantException(BIZ_PARA_ERROR, "入参为空");
        }

        // 1-根据档口管理任务ID查询线索信息
        WmCanteenStallClueListDTO clueListDTO = wmCanteenStallThriftService.getClueListByManageId(manageId);
        log.info("[WmScCanteenStallService.getCanteenStallClueList] clueListDTO = {}", JSONObject.toJSONString(clueListDTO));
        WmCanteenStallClueListVO clueListVO = WmScTransUtil.transCanteenStallClueListDTOToVO(clueListDTO);

        // 2-查询并组装学校范围信息
        SchoolAreaBo schoolAreaBo = wmSchoolThriftService.getSchoolAreaById(clueListDTO.getSchoolPrimaryId(), SchoolInfoTypeEnum.LOOK_OVER.getType());
        clueListVO.setSchoolAreaList(WmScTransUtil.transSchoolAoiDTOListToAreaList(schoolAreaBo.getWmScSchoolAoiDTOList()));

        return clueListVO;
    }


    /**
     * 通过线索主键ID删除单条线索
     * @param cluePrimaryId 线索主键ID
     * @param manageId 档口管理任务ID
     * @throws TException org.apache.thrift.TException
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void deleteClueByCluePrimaryId(Integer cluePrimaryId, Integer manageId) throws TException, WmSchCantException {
        log.info("[WmScCanteenStallService.deleteClueByCluePrimaryId] input param: cluePrimaryId = {}, manageId = {}",
                cluePrimaryId, manageId);
        if (cluePrimaryId == null || cluePrimaryId <= 0 || manageId <= 0) {
            log.error("[WmScCanteenStallService.deleteClueByCluePrimaryId] cluePrimaryId is null. cluePrimaryId = {}", cluePrimaryId);
            throw new WmSchCantException(BIZ_PARA_ERROR, "入参为空");
        }

        WmEmploy currentEmploy = WmEmployUtils.getWmEmploy();
        if (currentEmploy == null) {
            log.error("[WmScCanteenStallService.deleteClueByCluePrimaryId] currentEmploy is null. cluePrimaryId = {}", cluePrimaryId);
            throw new WmSchCantException(BIZ_PARA_ERROR, "查询用户信息失败");
        }

        // 1-操作鉴权
        wmScAuthService.checkBatchCreateClueCoordinatePageAuth(manageId, currentEmploy.getUid());

        // 2-删除单条线索信息
        wmCanteenStallThriftService.deleteClueByCluePrimaryId(cluePrimaryId, currentEmploy.getUid());
    }


    /**
     * 保存食堂档口线索扎点
     * @param clueDetailVO 线索详情VO
     * @throws TException org.apache.thrift.TException
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void saveClueCoordinate(WmCanteenStallClueVO clueDetailVO) throws TException, WmSchCantException {
        log.info("[WmScCanteenStallService.saveClueCoordinate] input param: clueDetailVO = {}", JSONObject.toJSONString(clueDetailVO));
        if (clueDetailVO == null || clueDetailVO.getCanteenPrimaryId() == null
                || clueDetailVO.getId() <= 0 || clueDetailVO.getManageId() == null) {
            log.error("[WmScCanteenStallService.saveClueCoordinate] error. clueDetailVO = {}", JSONObject.toJSONString(clueDetailVO));
            throw new WmSchCantException(BIZ_PARA_ERROR, "入参为空");
        }

        WmEmploy currentEmploy = WmEmployUtils.getWmEmploy();
        if (currentEmploy == null) {
            log.error("[WmScCanteenStallService.saveClueCoordinate] currentEmploy is null. clueDetailVO = {}", JSONObject.toJSONString(clueDetailVO));
            throw new WmSchCantException(BIZ_PARA_ERROR, "查询用户信息失败");
        }

        // 1-操作鉴权
        wmScAuthService.checkBatchCreateClueCoordinatePageAuth(clueDetailVO.getManageId(), currentEmploy.getUid());

        // 2-保存扎点信息
        WmCanteenStallClueDTO clueDetailDTO = new WmCanteenStallClueDTO();
        clueDetailDTO.setId(clueDetailVO.getId());
        clueDetailDTO.setCanteenPrimaryId(clueDetailVO.getCanteenPrimaryId());
        clueDetailDTO.setCluePoiCoordinate(clueDetailVO.getCluePoiCoordinate());
        wmCanteenStallThriftService.saveClueCoordinate(clueDetailDTO, currentEmploy.getUid());
    }


    /**
     * 创建档口绑定任务列表（创建线索）
     * @param manageId 食堂档口管理任务ID
     * @throws TException org.apache.thrift.TException
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void createStallBindListByManageId(Integer manageId) throws TException, WmSchCantException {
        log.info("[WmScCanteenStallService.createStallBindListByManageId] input param: manageId = {}", manageId);
        if (manageId == null || manageId <= 0) {
            log.error("[WmScCanteenStallService.createStallBindListByManageId] manageId is null. manageId = {}", manageId);
            throw new WmSchCantException(BIZ_PARA_ERROR, "入参为空");
        }

        WmEmploy currentEmploy = WmEmployUtils.getWmEmploy();
        if (currentEmploy == null) {
            log.error("[WmScCanteenStallService.createStallBindListByManageId] currentEmploy is null. manageId = {}", manageId);
            throw new WmSchCantException(BIZ_PARA_ERROR, "查询用户信息失败");
        }

        // 1-操作鉴权
        wmScAuthService.checkBatchCreateClueCoordinatePageAuth(manageId, currentEmploy.getUid());

        // 2-创建线索
        wmCanteenStallThriftService.createStallBindListByManageId(manageId, currentEmploy.getUid(), currentEmploy.getName() + "(" + currentEmploy.getMisId() + ")");
    }

    /**
     * 食堂档口管理列表查询
     * @param manageQueryVO 查询参数
     * @return WmCanteenStallManageListVO
     * @throws TException org.apache.thrift.TException
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public WmCanteenStallManageListVO getManageList(WmCanteenStallManageQueryVO manageQueryVO) throws TException, WmSchCantException {
        log.info("[WmScCanteenStallService.getManageList] input param: manageQueryVO = {}", JSONObject.toJSONString(manageQueryVO));
        if (manageQueryVO == null) {
            log.error("[WmScCanteenStallService.getManageList] manageQueryVO is null, return.");
            throw new WmSchCantException(BIZ_PARA_ERROR, "入参为空");
        }

        WmEmploy currentEmploy = WmEmployUtils.getWmEmploy();
        if (currentEmploy == null) {
            log.error("[WmScCanteenStallService.getManageList] currentEmploy is null. manageQueryVO = {}", JSONObject.toJSONString(manageQueryVO));
            throw new WmSchCantException(BIZ_PARA_ERROR, "查询用户信息失败");
        }

        // 1-查询档口管理列表数据
        WmCanteenStallManageQueryDTO queryDTO = WmScTransUtil.transCanteenStallManageQueryVOToDTO(manageQueryVO, currentEmploy.getUid());
        WmCanteenStallManageListDTO manageListDTO = wmCanteenStallThriftService.getManageList(queryDTO);

        // 2-查询固定按钮权限信息
        Map<String, Boolean> authMap = wmScAuthService.getCanteenstallManageListFixedButtonAuth(currentEmploy.getUid());
        WmCanteenStallManageListVO manageListVO =  WmScTransUtil.transCanteenStallManageListDTOToVO(manageListDTO, authMap);

        // 3-按钮权限再次计算
        manageListVO.setManageList(resetManageListButtonAuth(manageListVO.getManageList()));

        return manageListVO;
    }

    /**
     * 根据展示逻辑再计算档口管理列表的按钮展示逻辑
     * @param manageVOList manageVOList
     * @return List<WmCanteenStallManageVO>
     */
    private List<WmCanteenStallManageVO> resetManageListButtonAuth(List<WmCanteenStallManageVO> manageVOList) {
        for (WmCanteenStallManageVO manageVO : manageVOList) {
            // 若食堂被删除, 都不展示
            if (manageVO.getSchoolPrimaryId().equals(0)) {
                manageVO.setViewDetailButtonAuth(ScGrayEnum.NOT_DISPLAY.getCode());
                manageVO.setEditCoordinateButtonAuth(ScGrayEnum.NOT_DISPLAY.getCode());
                continue;
            }

            if (manageVO.getSubmitStatus().equals((int) CanteenStallManageSubmitStatusEnum.SUBMITTED.getType())) {
                // 提交状态=已提交时展示"查看详情"
                manageVO.setEditCoordinateButtonAuth(ScGrayEnum.NOT_DISPLAY.getCode());
            } else {
                // 提交状态=未提交时展示"去扎点"
                manageVO.setViewDetailButtonAuth(ScGrayEnum.NOT_DISPLAY.getCode());
            }
        }
        return manageVOList;
    }


    /**
     * 食堂档口绑定列表查询(食堂详情页面)
     * @param bindQueryVO 查询参数
     * @return WmCanteenStallBindListVO
     * @throws TException org.apache.thrift.TException
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public WmCanteenStallBindListVO getBindListWithCanteenPrimaryId(WmCanteenStallBindQueryVO bindQueryVO) throws TException, WmSchCantException {
        log.info("[WmScCanteenStallService.getBindListWithCanteenPrimaryId] input param: bindQueryVO = {}", JSONObject.toJSONString(bindQueryVO));
        if (bindQueryVO == null || bindQueryVO.getCanteenPrimaryId() == null) {
            log.error("[WmScCanteenStallService.getBindListWithCanteenPrimaryId] bindQueryVO is null, return.");
            throw new WmSchCantException(BIZ_PARA_ERROR, "入参为空");
        }

        WmEmploy currentEmploy = WmEmployUtils.getWmEmploy();
        if (currentEmploy == null) {
            log.error("[WmScCanteenStallService.getBindListWithCanteenPrimaryId] currentEmploy is null. bindQueryVO = {}", JSONObject.toJSONString(bindQueryVO));
            throw new WmSchCantException(BIZ_PARA_ERROR, "查询用户信息失败");
        }

        // 1-查询档口绑定列表数据
        WmCanteenStallBindQueryDTO queryDTO = WmScTransUtil.transCanteenStallBindQueryVOToDTO(bindQueryVO, currentEmploy.getUid());
        WmCanteenStallBindListDTO bindListDTO = wmCanteenStallThriftService.getBindListWithCanteenPrimaryId(queryDTO);

        // 2-按钮权限再次计算
        WmCanteenStallBindListVO bindListVO = WmScTransUtil.transCanteenStallBindListDTOToVO(bindListDTO);
        bindListVO.setBindList(resetBindListButtonAuth(bindListVO.getBindList()));


        // 3- 判断是否走新解绑流程
        if(MccScConfig.getIsNewUnbindFlowSwitch()){
            bindListVO.getBindList().stream().forEach(bindVO -> bindVO.setIsUnbindSwitchUpdate(true));
        }


        return bindListVO;
    }

    /**
     * 食堂档口绑定列表查询(档口管理详情页面)
     * @param bindQueryVO 查询参数
     * @return WmCanteenStallBindListVO
     * @throws TException org.apache.thrift.TException
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public WmCanteenStallBindListVO getBindListWithManageId(WmCanteenStallBindQueryVO bindQueryVO) throws TException, WmSchCantException {
        log.info("[WmScCanteenStallService.getBindListWithManageId] input param: bindQueryVO = {}", JSONObject.toJSONString(bindQueryVO));
        if (bindQueryVO == null || bindQueryVO.getManageId() == null) {
            log.error("[WmScCanteenStallService.getBindListWithManageId] bindQueryVO is null, return.");
            throw new WmSchCantException(BIZ_PARA_ERROR, "入参为空");
        }

        WmEmploy currentEmploy = WmEmployUtils.getWmEmploy();
        if (currentEmploy == null) {
            log.error("[WmScCanteenStallService.getBindListWithManageId] currentEmploy is null. bindQueryVO = {}", JSONObject.toJSONString(bindQueryVO));
            throw new WmSchCantException(BIZ_PARA_ERROR, "查询用户信息失败");
        }

        // 1-查询档口绑定列表数据
        WmCanteenStallBindQueryDTO queryDTO = WmScTransUtil.transCanteenStallBindQueryVOToDTO(bindQueryVO, currentEmploy.getUid());
        WmCanteenStallBindListDTO bindListDTO = wmCanteenStallThriftService.getBindListWithManageId(queryDTO);

        // 2-按钮权限再次计算
        WmCanteenStallBindListVO bindListVO = WmScTransUtil.transCanteenStallBindListDTOToVO(bindListDTO);
        bindListVO.setBindList(resetBindListButtonAuth(bindListVO.getBindList()));

        // 3- 判断是否走新解绑流程
        if(MccScConfig.getIsNewUnbindFlowSwitch()){
            bindListVO.getBindList().stream().forEach(bindVO -> bindVO.setIsUnbindSwitchUpdate(true));
        }

        return bindListVO;
    }


    /**
     * 根据展示逻辑再计算档口绑定列表的按钮展示逻辑
     * @param bindVOList bindVOList
     * @return List<WmCanteenStallBindVO>
     */
    private List<WmCanteenStallBindVO> resetBindListButtonAuth(List<WmCanteenStallBindVO> bindVOList) {
        for (WmCanteenStallBindVO bindVO : bindVOList) {

            // 标记正常按钮和标记异常按钮展示逻辑
            if (bindVO.getClueGenerateStatus().equals((int) CanteenStallClueGenerateStatusEnum.GENERATING.getType())
                    || bindVO.getAuditStatus().equals((int) CanteenStallAuditStatusEnum.AUDITING.getType())) {
                bindVO.setNormalButtonAuth(ScGrayEnum.NOT_DISPLAY.getCode());
                bindVO.setAbnormalButtonAuth(ScGrayEnum.NOT_DISPLAY.getCode());
            } else {
                if (bindVO.getClueFollowUpStatus().equals((int) CanteenStallClueFollowUpStatusEnum.NORMAL.getType())) {
                    bindVO.setNormalButtonAuth(ScGrayEnum.NOT_DISPLAY.getCode());
                } else {
                    bindVO.setAbnormalButtonAuth(ScGrayEnum.NOT_DISPLAY.getCode());
                }
            }

            // 跟进状态审批中按钮展示逻辑
            if (!bindVO.getAuditStatus().equals((int) CanteenStallAuditStatusEnum.AUDITING.getType())) {
                bindVO.setAuditingButtonAuth(ScGrayEnum.NOT_DISPLAY.getCode());
            }

            // 去上单按钮展示逻辑
            if (!bindVO.getClueBindStatus().equals((int) CanteenStallClueBindStatusEnum.BIND_SUCCESS.getType())) {
                bindVO.setPlaceOrderButtonAuth(ScGrayEnum.NOT_DISPLAY.getCode());
            }
        }

        return bindVOList;
    }

    /**
     * 档口绑定线索状态标记提审(档口详情)
     * @param submitVO submitVO
     * @throws TException org.apache.thrift.TException
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void submitClueFollowUpStatusWithManageId(WmCanteenStallAuditSubmitVO submitVO) throws TException, WmSchCantException {
        log.info("[WmScCanteenStallService.submitClueFollowUpStatusWithManageId] input param: submitVO = {}", JSONObject.toJSONString(submitVO));
        if (submitVO == null || CollectionUtils.isEmpty(submitVO.getBindIdList())
                || submitVO.getAuditTaskType() == null
                || submitVO.getManageId() == null
                || submitVO.getCanteenPrimaryId() == null) {
            log.error("[WmScCanteenStallService.submitClueFollowUpStatusWithManageId] input param: submitVO is null. submitVO = {}", JSONObject.toJSONString(submitVO));
            throw new WmSchCantException(BIZ_PARA_ERROR, "入参为空");
        }

        WmEmploy currentEmploy = WmEmployUtils.getWmEmploy();
        if (currentEmploy == null) {
            log.error("[WmScCanteenStallService.submitClueFollowUpStatusWithManageId] currentEmploy is null. submitVO = {}", JSONObject.toJSONString(submitVO));
            throw new WmSchCantException(BIZ_PARA_ERROR, "查询用户信息失败");
        }

        // 1-操作鉴权
        wmScAuthService.checkClueFollowUpStatusSubmitAuthWithManageId(submitVO.getAuditTaskType(), submitVO.getManageId(), currentEmploy.getUid());

        // 2-提审信息校验
        wmScCheckService.checkCanteenStallClueSubmitVO(submitVO);

        // 3-数据转换并调用接口
        WmCanteenStallAuditSubmitDTO submitDTO = WmScTransUtil.transCanteenStallAuditSubmitVOToDTO(submitVO, currentEmploy);
        wmCanteenStallThriftService.submitClueAuditTask(submitDTO);
    }


    /**
     * 档口绑定线索状态标记提审(食堂详情)
     * @param submitVO submitVO
     * @throws TException org.apache.thrift.TException
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void submitClueFollowUpStatusWithCanteenPrimaryId(WmCanteenStallAuditSubmitVO submitVO) throws TException, WmSchCantException {
        log.info("[WmScCanteenStallService.submitClueFollowUpStatusWithCanteenPrimaryId] input param: submitVO = {}", JSONObject.toJSONString(submitVO));
        if (submitVO == null || CollectionUtils.isEmpty(submitVO.getBindIdList())
                || submitVO.getAuditTaskType() == null
                || submitVO.getCanteenPrimaryId() == null) {
            log.error("[WmScCanteenStallService.submitClueFollowUpStatusWithCanteenPrimaryId] input param: submitVO is null. submitVO = {}", JSONObject.toJSONString(submitVO));
            throw new WmSchCantException(BIZ_PARA_ERROR, "入参为空");
        }

        WmEmploy currentEmploy = WmEmployUtils.getWmEmploy();
        if (currentEmploy == null) {
            log.error("[WmScCanteenStallService.submitClueFollowUpStatusWithCanteenPrimaryId] currentEmploy is null. submitVO = {}", JSONObject.toJSONString(submitVO));
            throw new WmSchCantException(BIZ_PARA_ERROR, "查询用户信息失败");
        }

        // 1-操作鉴权
        wmScAuthService.checkClueFollowUpStatusSubmitAuthWithCanteenPrimaryId(submitVO.getAuditTaskType(), submitVO.getCanteenPrimaryId(), currentEmploy.getUid());

        // 2-提审信息校验
        wmScCheckService.checkCanteenStallClueSubmitVO(submitVO);

        // 3-数据转换并调用接口
        WmCanteenStallAuditSubmitDTO submitDTO = WmScTransUtil.transCanteenStallAuditSubmitVOToDTO(submitVO, currentEmploy);
        wmCanteenStallThriftService.submitClueAuditTask(submitDTO);
    }

    /**
     * 根据档口绑定任务ID查询审批页面信息
     * @param bindId 档口绑定任务ID
     * @param auditSystemId 任务系统任务ID
     * @return 审批页面信息
     * @throws TException org.apache.thrift.TException
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public WmCanteenStallAuditInfoVO getClueFollowUpStatusAuditInfo(Integer bindId, String auditSystemId) throws TException, WmSchCantException {
        log.info("[WmScCanteenStallService.getClueFollowUpStatusAuditInfo] input param: bindId = {}, auditSystemId= {}", bindId, auditSystemId);
        if ((bindId == null || bindId <= 0) && StringUtils.isBlank(auditSystemId)) {
            log.error("[WmScCanteenStallService.getClueFollowUpStatusAuditInfo] bindId is null. bindId = {}", bindId);
            throw new WmSchCantException(BIZ_PARA_ERROR, "入参为空");
        }

        // 1-如果任务系统任务ID是空, 则根据档口绑定任务ID查询
        if (StringUtils.isBlank(auditSystemId)) {
            auditSystemId = wmCanteenStallThriftService.getAuditSystemIdByAuditingBindId(bindId);
        }

        // 2-查询审批页面信息
        WmCanteenStallBindListDTO bindListDTO = wmCanteenStallThriftService.getAuditBindListByAuditSystemId(auditSystemId);

        // 3-查询审批流信息
        WmCanteenStallAuditStreamDTO auditStreamDTO = wmCanteenStallThriftService.getAuditStreamByAuditSystemId(auditSystemId);

        // 4-查询审批任务信息
        WmCanteenStallAuditTaskDTO auditTaskDTO = wmCanteenStallThriftService.getAuditTaskByAuditSystemId(auditSystemId);

        WmCanteenStallAuditInfoVO auditInfoVO = new WmCanteenStallAuditInfoVO();
        auditInfoVO.setAuditInfo(WmScTransUtil.transCanteenStallBindListDTOToVO(bindListDTO));
        auditInfoVO.setAuditStreamInfo(WmScTransUtil.transCanteenStallAuditStreamDTOToVO(auditStreamDTO));
        auditInfoVO.setAuditTask(WmScTransUtil.transCanteenStallAuditTaskDTOToVO(auditTaskDTO));

        return auditInfoVO;
    }

    /**
     * 根据任务ID查询门店更新绑定食堂的审批信息
     * @return 审批信息
     * @throws TException org.apache.thrift.TException
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public WmCanteenPoiSimpleAuditInfoVO getPoiUpdateBindCanteenSimpleAudit(Integer ticketId, String auditTaskType) throws TException, WmSchCantException {
        log.info("[WmScCanteenStallService.getPoiUpdateBindCanteenSimpleAudit] input param: ticketId = {}, auditTaskType= {}", ticketId, auditTaskType);
        if ((ticketId == null || ticketId <= 0) && StringUtils.isBlank(auditTaskType)) {
            log.error("[WmScCanteenStallService.getPoiUpdateBindCanteenAudit] taskId is null. taskId = {}", ticketId);
            throw new WmSchCantException(BIZ_PARA_ERROR, "入参为空");
        }

        // 3-查询审批流信息
        WmCanteenPoiSimpleStreamDTO auditStreamDTO = wmCanteenPoiThriftService.getSimpleAuditStreamByAuditSystemId(String.valueOf(ticketId));


        WmCanteenPoiSimpleAuditInfoVO auditInfoVO = new WmCanteenPoiSimpleAuditInfoVO();
        auditInfoVO.setAuditStreamInfo(WmScTransUtil.transCanteenPoiSimpleProgressDTOToVO(auditStreamDTO));

        return auditInfoVO;
    }

    /**
     * 根据任务ID查询门店更新绑定食堂的审批信息
     * @return 审批信息
     * @throws TException org.apache.thrift.TException
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public JSONObject getSwitchEnums() throws TException, WmSchCantException {
        JSONObject result = new JSONObject();
        JSONArray canteenPoiTaskTypeEnumArray = new JSONArray();
        for (CanteenPoiTaskTypeEnum enumValue : CanteenPoiTaskTypeEnum.values()) {
            JSONObject enumObject = new JSONObject();
            enumObject.put("code", enumValue.getCode());
            enumObject.put("name", enumValue.getName());
            canteenPoiTaskTypeEnumArray.add(enumObject);
        }
        result.put("CanteenPoiTaskTypeEnum", canteenPoiTaskTypeEnumArray);

        JSONArray unBindReasonTypeEnumArray = new JSONArray();
        for (UnBindReasonTypeEnum enumValue : UnBindReasonTypeEnum.values()) {
            JSONObject enumObject = new JSONObject();
            enumObject.put("code", enumValue.getCode());
            enumObject.put("description", enumValue.getDescription());
            unBindReasonTypeEnumArray.add(enumObject);
        }
        result.put("UnBindReasonTypeEnum", unBindReasonTypeEnumArray);

        JSONArray switchTaskReasonTypeEnumArray = new JSONArray();
        for (SwitchTaskReasonTypeEnum enumValue : SwitchTaskReasonTypeEnum.values()) {
            JSONObject enumObject = new JSONObject();
            enumObject.put("code", enumValue.getCode());
            enumObject.put("description", enumValue.getDescription());
            switchTaskReasonTypeEnumArray.add(enumObject);
        }
        result.put("SwitchTaskReasonTypeEnum", switchTaskReasonTypeEnumArray);
        return result;
        //return result.toJSONString();
    }



    /**
     * 公海绑定提交
     * @param submitVO submitVO
     * @return WmCanteenStallSubmitResponseVO
     * @throws TException org.apache.thrift.TException
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public WmCanteenStallSubmitResponseVO submitByWdcClueIds(WmCanteenStallBindSubmitVO submitVO) throws TException, WmSchCantException {
        log.info("[WmScCanteenStallService.submitByWdcClueIds] input param: submitVO = {}", JSONObject.toJSONString(submitVO));
        WmCanteenStallSubmitResponseVO responseVO = new WmCanteenStallSubmitResponseVO();
        WmEmploy currentEmploy = WmEmployUtils.getWmEmploy();
        if (currentEmploy == null) {
            log.error("[WmScCanteenStallService.submitByWdcClueIds] currentEmploy is null. submitVO = {}", JSONObject.toJSONString(submitVO));
            throw new WmSchCantException(BIZ_PARA_ERROR, "查询用户信息失败");
        }

        // 1-操作鉴权
        wmScAuthService.checkWdcBindSubmitPageAuth(currentEmploy.getUid());

        // 2-基本信息校验
        wmScCheckService.checkCanteenStallSubmitByWdcBind(submitVO);

        // 3-公海绑定前置校验
        List<WmCanteenStallCheckFailDTO> checkFailDTOList = wmCanteenStallThriftService.checkSubmitByWdcClueBind(
                WmScTransUtil.transCanteenStallBindSubmitVOToDTO(submitVO));

        // 4-如果存在校验不通过的则直接返回
        if (CollectionUtils.isNotEmpty(checkFailDTOList)) {
            log.info("[WmScCanteenStallService.submitByWdcClueIds] checkFailDTOList = {}", JSONObject.toJSONString(checkFailDTOList));
            responseVO.setWdcErrorList(WmScTransUtil.transCanteenStallCheckFailDTOsTOWdcErrorVOs(checkFailDTOList));
            return responseVO;
        }

        // 5-提交公海档口绑定
        WmCanteenStallBindSubmitDTO submitDTO = new WmCanteenStallBindSubmitDTO();
        submitDTO.setCanteenPrimaryId(submitVO.getCanteenPrimaryId());
        submitDTO.setWdcClueIdList(submitVO.getWdcClueIdList());
        submitDTO.setUserId(currentEmploy.getUid());
        submitDTO.setUserName(currentEmploy.getName() + "(" + currentEmploy.getMisId() + ")");
        Integer manageId = wmCanteenStallThriftService.submitByWdcClueBind(submitDTO);

        responseVO.setManageId(manageId);
        log.info("[WmScCanteenStallService.submitByWdcClueIds] manageId = {}", manageId);
        return responseVO;
    }


    /**
     * 外卖门店绑定提交
     * @param submitVO submitVO
     * @return WmCanteenStallSubmitResponseVO
     * @throws TException org.apache.thrift.TException
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public WmCanteenStallSubmitResponseVO submitByWmPoiIds(WmCanteenStallBindSubmitVO submitVO) throws TException, WmSchCantException {
        log.info("[WmScCanteenStallService.submitByWmPoiIds] input param: submitVO = {}", JSONObject.toJSONString(submitVO));
        WmCanteenStallSubmitResponseVO responseVO = new WmCanteenStallSubmitResponseVO();
        WmEmploy currentEmploy = WmEmployUtils.getWmEmploy();
        if (currentEmploy == null) {
            log.error("[WmScCanteenStallService.submitByWmPoiIds] currentEmploy is null. submitVO = {}", JSONObject.toJSONString(submitVO));
            throw new WmSchCantException(BIZ_PARA_ERROR, "查询用户信息失败");
        }

        // 1-操作鉴权  (检查用户是否有外卖门店绑定页面的访问权限)
        wmScAuthService.checkWmPoiBindSubmitPageAuth(currentEmploy.getUid());

        // 2-基本信息校验  (检查提交参数的合法性(食堂ID、门店列表不为空且数量不超过限制))
        wmScCheckService.checkCanteeStallSubmitByWmPoiBind(submitVO);

        // 3-外卖门店绑定前置校验 (检查每个门店是否可以绑定)
        List<WmCanteenStallCheckFailDTO> checkFailDTOList = wmCanteenStallThriftService.checkSubmitByWmPoiBind(
                WmScTransUtil.transCanteenStallBindSubmitVOToDTO(submitVO));
        // 4-如果存在校验不通过的则直接返回
        if (CollectionUtils.isNotEmpty(checkFailDTOList)) {
            log.info("[WmScCanteenStallService.submitByWmPoiIds] checkFailDTOList = {}", JSONObject.toJSONString(checkFailDTOList));
            responseVO.setWmPoiErrorList(WmScTransUtil.transCanteenStallCheckFailDTOListToWmPoiErrorVOList(checkFailDTOList));
            return responseVO;
        }

        // 5-提交外卖门店档口绑定(构建参数并调用绑定服务进行绑定操作)
        WmCanteenStallBindSubmitDTO submitDTO = new WmCanteenStallBindSubmitDTO();
        submitDTO.setCanteenPrimaryId(submitVO.getCanteenPrimaryId());
        submitDTO.setWmPoiIdList(submitVO.getWmPoiIdList());
        submitDTO.setUserId(currentEmploy.getUid());
        submitDTO.setUserName(currentEmploy.getName() + "(" + currentEmploy.getMisId() + ")");
        Integer manageId = wmCanteenStallThriftService.submitByWmPoiBind(submitDTO);

        responseVO.setManageId(manageId);
        log.info("[WmScCanteenStallService.submitByWmPoiIds] manageId = {}", manageId);
        return responseVO;
    }


    /**
     * 档口绑定任务解绑(食堂详情)
     * @param bindId 档口绑定ID
     * @throws TException org.apache.thrift.TException
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void unbindClueAndWmPoiWithCanteenPrimaryId(Integer bindId, Integer canteenPrimaryId) throws TException, WmSchCantException {
        log.info("[WmScCanteenStallService.unbindClueAndWmPoiWithCanteenPrimaryId] input param: bindId = {}, canteenPrimaryId = {}",
                bindId, canteenPrimaryId);
        if (bindId == null || bindId <= 0 || canteenPrimaryId == null || canteenPrimaryId <= 0) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "入参为空");
        }

        WmEmploy currentEmploy = WmEmployUtils.getWmEmploy();
        if (currentEmploy == null) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "查询用户信息失败");
        }
        // 1-用户操作鉴权
        wmScAuthService.checkUnbindClueAndWmPoiAuthWithCanteenPrimaryId(canteenPrimaryId, currentEmploy.getUid());

        // 2-执行解绑动作
        wmCanteenStallThriftService.unbindClueAndWmPoiByBindId(bindId, currentEmploy.getUid(), currentEmploy.getName() + "(" + currentEmploy.getMisId() + ")");
    }




    /**
     * 档口绑定任务解绑(食堂详情)
     * @throws TException org.apache.thrift.TException
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void unbindClueAndWmPoiWithCanteenPrimaryIdV2(WmUnBindPoiTaskVo taskVo) throws TException, WmSchCantException {
        log.info("[WmScCanteenStallService.unbindClueAndWmPoiWithCanteenPrimaryIdV2] input param: taskVo = {}", JSONObject.toJSONString(taskVo));
        if (taskVo == null) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "入参为空");
        }
        taskVo.valid();

        WmEmploy currentEmploy = WmEmployUtils.getWmEmploy();
        if (currentEmploy == null) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "查询用户信息失败");
        }
        // 1-用户操作鉴权  todo:调用提供的鉴权结果
        //wmScAuthService.checkUnbindClueAndWmPoiAuthWithCanteenPrimaryId(canteenPrimaryId, currentEmploy.getUid());

        WmScCanteenPoiTaskDTO wmScCanteenPoiTaskDTO = new WmScCanteenPoiTaskDTO();
        BeanUtils.copyProperties(taskVo, wmScCanteenPoiTaskDTO);
        User user = UserUtils.getUser();
        wmScCanteenPoiTaskDTO.setWmPoiIdList(taskVo.getWmPoiIds());
        wmScCanteenPoiTaskDTO.setBindIdList(taskVo.getBindIds());
        wmScCanteenPoiTaskDTO.setCanteenIdFrom(taskVo.getCanteenPrimaryId());
        wmScCanteenPoiTaskDTO.setTaskReasonType(taskVo.getUnBindTaskReasonType());
        wmScCanteenPoiTaskDTO.setTaskReason(taskVo.getUnBindTaskReason());
        wmScCanteenPoiTaskDTO.setProofMaterialImage(taskVo.getProofMaterialImage());


        wmScCanteenPoiTaskDTO.setAuditNodeType(CanteenAuditProgressEnum.UNBIND_PROGRESS.getCode());
        wmScCanteenPoiTaskDTO.setUserName(taskVo.getUserName());
        wmScCanteenPoiTaskDTO.setUserId(user == null ? 0 : user.getId());
        wmScCanteenPoiTaskDTO.setTaskType(CanteenPoiTaskTypeEnum.UNBIND.getCode());
        wmCanteenPoiThriftService.submitTaskSimple(wmScCanteenPoiTaskDTO);
    }



    /**
     * 档口绑定任务解绑(档口详情)
     * @param bindId 档口绑定ID
     * @throws TException org.apache.thrift.TException
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void unbindClueAndWmPoiWithManageId(Integer bindId, Integer manageId) throws TException, WmSchCantException {
        log.info("[WmScCanteenStallService.unbindClueAndWmPoiWithManageId] input param: bindId = {}, manageId = {}",
                bindId, manageId);
        WmEmploy currentEmploy = WmEmployUtils.getWmEmploy();
        if (currentEmploy == null) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "查询用户信息失败");
        }

        if (manageId == null || manageId <= 0 || bindId == null || bindId <= 0) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "入参为空");
        }
        // 1-用户操作鉴权
        wmScAuthService.checkUnbindClueAndWmPoiAuthWithManageId(manageId, currentEmploy.getUid());

        // 2-执行解绑动作
        wmCanteenStallThriftService.unbindClueAndWmPoiByBindId(bindId, currentEmploy.getUid(), currentEmploy.getName() + "(" + currentEmploy.getMisId() + ")");
    }

    public String getUnbindTransferApprovalTaskLink(String auditSystemId) throws WmSchCantException, TException{
        if(auditSystemId == null){
            return null;
        }
       return  wmCanteenPoiThriftService.getUnbindTransferApprovalTaskLink(auditSystemId);
    }


}

package com.sankuai.meituan.waimai.service.agreement;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import java.util.List;
import org.apache.commons.collections.CollectionUtils;

/**
 * 抽象数据解析方法，
 * 提供通用的数据解析能力
 * 和转换为接口目标数据结构的能力
 * 并不关心其真正解析为什么样的目标对象
 */
public abstract class AbstractDataAnalysisService {

    /**
     * 数据解析为目标List对象
     */
    protected <T> List<T> analysisList(String data, String type) {
        List<T> tList = JSON.parseObject(data, new TypeReference<List<T>>(){});
        if (CollectionUtils.isEmpty(tList)) {
            return Lists.newArrayList();
        }
        return tList;
    }

    /**
     * 数据对象转换为统一对外的对象
     */
    protected <T> List<Object> toObjectList(List<T> tList) {
        if (CollectionUtils.isEmpty(tList)) {
            return Lists.newArrayList();
        }

        List<Object> objectList = Lists.newArrayList();
        for (T t:tList) {
            CollectionUtils.addIgnoreNull(objectList, t);
        }
        return objectList;
    }

    /**
     * 数据解析为目标List对象（通过.class）
     */
    protected <T> List<T> analysisListByClass(String data, Class<T> clazz) {
        List<T> tList = JSON.parseArray(data, clazz);
        if (CollectionUtils.isEmpty(tList)) {
            return Lists.newArrayList();
        }
        return tList;
    }

}

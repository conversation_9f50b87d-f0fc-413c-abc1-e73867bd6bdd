package com.sankuai.meituan.waimai.service.contract.wrapper;

import com.sankuai.meituan.auth.vo.User;
import com.sankuai.meituan.waimai.thrift.customer.constant.SignSubjectEnum;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.vo.ContractVo;
import com.sankuai.meituan.waimai.vo.base.MultiFileJsonVo;
import org.apache.thrift.TException;

/**
 * @description:
 * @author: liuyunjie05
 * @create: 2023/10/31 10:17
 */
public class SpeedyDeliveryCooperationWrapper extends AbstractContractWrapper {
    public SpeedyDeliveryCooperationWrapper(ContractVo contractVo, User user) {
        super(contractVo, user);
    }

    @Override
    public ContractVo wrap() throws WmCustomerException, TException {
        initPartyA();
        initPartyB();
        setSignTime();
        contractVo.setContractScan(new MultiFileJsonVo());
        if (isSave()) {
            contractVo.setContractNum("电子合同保存后自动生成编号");
        }
        return contractVo;
    }

    @Override
    protected void initPartyB() throws TException, WmCustomerException {
        super.initPartyB();
        contractVo.getPartyB().setSignName(SignSubjectEnum.BJ_SANKUAI.getDesc());
    }

}

package com.sankuai.meituan.waimai.service.customerswitch;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.meituan.mtrace.thread.TraceRunnable;
import com.meituan.service.mobile.util.StringUtil;
import com.sankuai.meituan.auth.vo.User;
import com.sankuai.meituan.waimai.config.MccConfig;
import com.sankuai.meituan.waimai.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.domain.SwitchPoiQueryVO;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import com.sankuai.meituan.waimai.infra.service.WmEmployService;
import com.sankuai.meituan.waimai.label.constants.LabelSubjectTypeEnum;
import com.sankuai.meituan.waimai.poibizflow.constant.WmPoiBizFlowErrorCodeConstant;
import com.sankuai.meituan.waimai.poibizflow.constant.customerSwitch.CustomerSwitchVesionEnum;
import com.sankuai.meituan.waimai.poibizflow.thrift.exception.WmPoiBizException;
import com.sankuai.meituan.waimai.poibizflow.thrift.switcher.WmPoiSwitchThriftService;
import com.sankuai.meituan.waimai.poibizflow.thrift.switcher.bo.SwitchTaskBo;
import com.sankuai.meituan.waimai.service.adaptor.WmPoiFlowlineLabelThriftServiceAdapter;
import com.sankuai.meituan.waimai.thrift.constants.WmPoiFieldQueryConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerContractStatus;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerBasicBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerForceUnbindUnconfirmDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerListBo;
import com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerPoiThriftService;
import com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerThriftService;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiLabelRel;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import com.sankuai.meituan.waimai.thrift.service.WmPoiQueryThriftService;
import com.sankuai.meituan.waimai.util.DaxiangUtil;
import com.sankuai.meituan.waimai.util.base.WmRestReturnUtil;
import com.sankuai.meituan.waimai.vo.ContractVo;
import com.sankuai.meituan.waimai.vo.customerswitch.CustomerSwitchPoiInfo;
import com.sankuai.meituan.waimai.vo.customerswitch.WmPoiInfo;
import com.sankuai.meituan.waimai.vo.customerswitch.WmPoiInfoQueryResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;

@Slf4j
@Service
public class WmCustomerSwitchService {

    private static final String XIANFU = "<EMAIL>";

    @Autowired
    private WmCustomerPoiThriftService wmCustomerPoiThriftService;

    @Autowired
    private WmCustomerSwitchCheck wmCustomerSwitchCheck;

    @Autowired
    private WmPoiSwitchThriftService wmPoiSwitchThriftService;

    @Autowired
    private WmPoiQueryThriftService.Iface wmPoiQueryThriftService;


    @Autowired
    private WmEmployService.Iface wmEmployService;

    @Autowired
    private WmPoiFlowlineLabelThriftServiceAdapter wmPoiFlowlineLabelThriftServiceAdapter;

    @Autowired
    private WmCustomerThriftService wmCustomerThriftService;


    private static final Set<String> WM_POI_FIELDS = Sets.newHashSet(WmPoiFieldQueryConstant.WM_POI_FIELD_WM_POI_ID, WmPoiFieldQueryConstant.WM_POI_FIELD_NAME, WmPoiFieldQueryConstant.WM_POI_FIELD_LABEL_IDS);

    public Collection<CustomerSwitchPoiInfo> getPoiInfosByCustomerId(long customerId) throws WmCustomerException, TException, WmServerException {
        List<Long> poiIds = wmCustomerPoiThriftService.selectWmPoiIdsByCustomerId((int) customerId);
        if (CollectionUtils.isEmpty(poiIds)) {
            return Lists.newArrayList();
        }
        int maxNum = MccConfig.getCustomerSwitchMaxCount();
        if (poiIds.size() > maxNum) {
            throw new WmCustomerException(WmPoiBizFlowErrorCodeConstant.BUSINESS_ERROR, String.format("全部门店大于%s个，超过上限，请在%s创建任务。", maxNum, MccConfig.getCustomerSwitchLinkToBatchTool()));
        }
        List<WmPoiAggre> wmPoiAggreList = wmPoiQueryThriftService.mgetWmPoiAggreByWmPoiIdWithSpecificField(poiIds, WM_POI_FIELDS);
        Map<Long, CustomerSwitchPoiInfo> map = new HashMap<>();
        for (WmPoiAggre aggre : wmPoiAggreList) {
            if (StringUtil.isBlank(aggre.getName())) {
                continue;
            }
            map.put(aggre.getWm_poi_id(), new CustomerSwitchPoiInfo(aggre.getWm_poi_id(), aggre.getName()));
        }
        return map.values();
    }


    public void batchForceUnbindUncofirm(final List<Long> taskIdList, final User user) throws WmPoiBizException, TException {
        final Map<Long, WmCustomerForceUnbindUnconfirmDTO> map = wmCustomerSwitchCheck.checkAuthBatchForceUnbindUnconfirm(taskIdList, user);
        new Thread(new TraceRunnable(new Runnable() {
            @Override
            public void run() {
                WmEmploy wmEmploy = getWmEmploy(user.getId());
                List<Long> errList = Lists.newArrayList();
                try {
                    /** 1.任务校验 **/
                    for (Map.Entry<Long, WmCustomerForceUnbindUnconfirmDTO> entry : map.entrySet()) {
                        try {
                            /** 2.强制解绑 **/
                            wmCustomerPoiThriftService.forceUnbindUnconfirm(entry.getValue());
                        } catch (WmCustomerException e) {
                            errList.add(entry.getKey());
                            log.error("batchForceUnbindUncofirm 失败 taskId={},param={}", entry.getKey(), JSONObject.toJSONString(entry.getValue()), e);
                        } catch (Exception e) {
                            errList.add(entry.getKey());
                            log.error("batchForceUnbindUncofirm 失败 taskId={},param={}", entry.getKey(), JSONObject.toJSONString(entry.getValue()), e);
                        }
                    }
                } catch (Exception e) {
                    errList.addAll(taskIdList);
                    log.error("batchForceUnbindUncofirm 失败 taskIdList={},param={}", JSONObject.toJSONString(taskIdList), e);
                }

                if (wmEmploy != null) {
                    int sum = taskIdList.size();
                    int errSize = errList.size();
                    StringBuilder errMsg = new StringBuilder("");
                    if (!CollectionUtils.isEmpty(errList)) {
                        errMsg.append("（任务id:");
                        for (int i = 0; i < errSize; i++) {
                            errMsg.append(errList.get(i));
                            if (i == (errSize - 1)) {
                                errMsg.append("）");
                            } else {
                                errMsg.append("/");
                            }
                        }
                    }
                    DaxiangUtil.push(XIANFU, String.format("本次提交批量强制确认解绑共%s个任务，执行成功%s个，未执行成功%s个%s",
                            sum, (sum - errSize), errSize, errMsg.toString()), String.format("%<EMAIL>", wmEmploy.getMisId()));
                }
            }
        })).start();
    }


    public void batchCancelSwitch(final List<Long> taskIdList, final User user) throws WmPoiBizException, TException {
        /** 1.任务校验 **/
        wmCustomerSwitchCheck.checkAuthBatchCancelSwitch(taskIdList, user);
        new Thread(new TraceRunnable(new Runnable() {
            @Override
            public void run() {
                WmEmploy wmEmploy = getWmEmploy(user.getId());
                List<Long> errList = Lists.newArrayList();
                try {
                    for (Long taskId : taskIdList) {
                        try {
                            /** 2.任务取消 **/
                            wmPoiSwitchThriftService.cancelSwitch(taskId, user.getId(), user.getName());
                        } catch (WmPoiBizException e) {
                            errList.add(taskId);
                            log.warn("batchCancelSwitch 失败 taskId={},errMsg={}", taskId, e.getMsg(), e);
                        } catch (Exception e) {
                            errList.add(taskId);
                            log.error("batchCancelSwitch 失败 taskId={}", taskId, e);
                        }
                    }
                } catch (Exception e) {
                    errList.addAll(taskIdList);
                    log.error("batchCancelSwitch 失败 taskIdList={},param={}", JSONObject.toJSONString(taskIdList), e);
                }
                if (wmEmploy != null) {
                    int sum = taskIdList.size();
                    int errSize = errList.size();
                    StringBuilder errMsg = new StringBuilder("");
                    if (!CollectionUtils.isEmpty(errList)) {
                        errMsg.append("（任务id:");
                        for (int i = 0; i < errSize; i++) {
                            errMsg.append(errList.get(i));
                            if (i == (errSize - 1)) {
                                errMsg.append("）");
                            } else {
                                errMsg.append("/");
                            }
                        }
                    }
                    DaxiangUtil.push(XIANFU, String.format("本次提交批量取消切换共%s个任务，执行成功%s个，未执行成功%s个%s",
                            sum, (sum - errSize), errSize, errMsg.toString()), String.format("%<EMAIL>", wmEmploy.getMisId()));
                }
            }
        })).start();
    }

    public JSONObject getWmPoiInfoList(String wmPoiIdsStr) throws TException, WmServerException {
        WmPoiInfoQueryResult result = new WmPoiInfoQueryResult();
        String[] wmPoiIds = wmPoiIdsStr.split(",");
        List<Long> wmPoiIdsList = Lists.newArrayList();
        for (String wmPoiIdStr : wmPoiIds) {
            wmPoiIdsList.add(Long.parseLong(wmPoiIdStr));
        }
        int maxNum = MccConfig.getCustomerSwitchMaxCount();
        if (wmPoiIdsList.size() > maxNum) {
            throw new WmServerException(WmPoiBizFlowErrorCodeConstant.BUSINESS_ERROR, "门店个数大于" + maxNum + "个，请删除部分门店。");
        }

        List<Long> errWmPoiIdList = Lists.newArrayList();
        List<WmPoiInfo> list = Lists.newArrayList();
        List<WmPoiAggre> wmPoiAggreList = wmPoiQueryThriftService.mgetWmPoiAggreByWmPoiIdWithSpecificField(wmPoiIdsList, WM_POI_FIELDS);
        if (CollectionUtils.isEmpty(wmPoiAggreList)) {
            errWmPoiIdList.addAll(wmPoiIdsList);
        } else {
            for (WmPoiAggre aggre : wmPoiAggreList) {
                if (StringUtil.isBlank(aggre.getName())) {
                    errWmPoiIdList.add(aggre.getWm_poi_id());
                    continue;
                }
                list.add(new WmPoiInfo(aggre.getWm_poi_id(), aggre.getName()));
            }
        }
        result.setRightWmPoiInfoList(list);
        result.setErrWmPoiIdList(errWmPoiIdList);
        if (CollectionUtils.isEmpty(errWmPoiIdList)) {
            return WmRestReturnUtil.success(result);
        } else {
            return WmRestReturnUtil.fail(result, "查询不到门店，无法添加");
        }
    }


    private WmEmploy getWmEmploy(int uid) {
        try {
            return wmEmployService.getById(uid);
        } catch (WmServerException e) {
            log.warn("getWmEmploy 失败 uid={}", uid, e);
        } catch (Exception e) {
            log.warn("getWmEmploy 失败 uid={}", uid, e);
        }
        return null;
    }

    /**
     * 自动选项是否展示结算
     *
     * @param fromCustomerId
     * @param toCustomerId
     * @return
     */
    public boolean isShowSettle(Long fromCustomerId, Long toCustomerId, Long switchTaskId) {
        if (switchTaskId == null || switchTaskId == 0l) {
            if (fromCustomerId == null || fromCustomerId == 0l || toCustomerId == null || fromCustomerId == 0l) {
                return false;
            }

            try {
                WmCustomerBasicBo fromBo = wmCustomerThriftService.getCustomerById(fromCustomerId.intValue());
                WmCustomerBasicBo toBo = wmCustomerThriftService.getCustomerById(toCustomerId.intValue());
                if (fromBo == null || toBo == null) {
                    return false;
                }
                long newSettleForCustomerTag = MccConfig.getNewSettleForCustomerTag();

                WmPoiLabelRel fromCustomerTag = wmPoiFlowlineLabelThriftServiceAdapter.selectLabelRelCache(fromBo.getMtCustomerId(), newSettleForCustomerTag, LabelSubjectTypeEnum.CUSTOMER.getCode());

                WmPoiLabelRel toCustomerTag = wmPoiFlowlineLabelThriftServiceAdapter.selectLabelRelCache(toBo.getMtCustomerId(), newSettleForCustomerTag, LabelSubjectTypeEnum.CUSTOMER.getCode());

                boolean fromIsOldSettle = fromCustomerTag == null || (fromCustomerTag != null && fromCustomerTag.getId() <= 0l);
                boolean toIsOldSettle = toCustomerTag == null || (toCustomerTag != null && toCustomerTag.getId() <= 0l);

                if (fromIsOldSettle && toIsOldSettle) {
                    return true;
                }
            } catch (WmCustomerException e) {
                log.warn("isShowSettle getCustomerById失败 fromCustomerId={},toCustomerId={}", fromCustomerId, toCustomerId, e);
            } catch (Exception e) {
                log.error("isShowSettle getCustomerById失败 fromCustomerId={},toCustomerId={}", fromCustomerId, toCustomerId, e);
            }

        } else {
            try {
                SwitchTaskBo switchTaskBo = wmPoiSwitchThriftService.getSwitchTaskByTaskId(switchTaskId, 0, "");
                if (switchTaskBo != null && switchTaskBo.getVersion() != null && switchTaskBo.getVersion() == CustomerSwitchVesionEnum.V2.getCode()) {
                    return true;
                }
            } catch (WmPoiBizException e) {
                log.info("isShowSettle 获取切换任务信息失败switchTaskId={}", switchTaskId, e);
            } catch (Exception e) {
                log.error("isShowSettle 获取切换任务信息失败switchTaskId={}", switchTaskId, e);
            }
        }
        return false;
    }


    /**
     * 查询切换门店列表
     *
     * @param wmPoiIdList
     * @return
     */
    public List<SwitchPoiQueryVO> listSwitchPoi(List<Long> wmPoiIdList) {
        List<SwitchPoiQueryVO> list = Lists.newArrayList();
        List<List<Long>> partitionList = com.google.common.collect.Lists.partition(wmPoiIdList, 50);
        for (List<Long> partition : partitionList) {
            try {
                List<WmPoiAggre> wmPoiAggreList = wmPoiQueryThriftService.mgetWmPoiAggreByWmPoiIdWithSpecificField(
                        partition, WM_POI_FIELDS);
                for (WmPoiAggre wmPoiAggre : wmPoiAggreList) {
                    SwitchPoiQueryVO switchPoiQueryVO = new SwitchPoiQueryVO();
                    switchPoiQueryVO.setWmPoiId(wmPoiAggre.getWm_poi_id());
                    switchPoiQueryVO.setWmPoiName(wmPoiAggre.getName());
                    switchPoiQueryVO.setChildPoiFlag(checkChildPoiByTagId(wmPoiAggre.getLabel_ids()));
                    list.add(switchPoiQueryVO);
                }
            } catch (WmServerException e) {
                log.error("listSwitchPoi,获取门店信息失败，wmPoiIdList = {},", JSON.toJSONString(wmPoiIdList), e);
            } catch (TException e) {
                log.error("listSwitchPoi,获取门店信息超时失败，wmPoiIdList = {}", JSON.toJSONString(wmPoiIdList), e);
            }
        }
        //按照是否子门店排序
        if (!CollectionUtils.isEmpty(list)) {
            Collections.sort(list, getPoiChildFlagComparator());
        }

        return list;
    }

    /**
     * 根据门店当前标签情况判断是否子门店
     *
     * @param poiLabels
     * @return
     */
    public Boolean checkChildPoiByTagId(String poiLabels) {
        if (StringUtils.isEmpty(poiLabels)) {
            return false;
        }
        List<Integer> poiLabelList = new ArrayList<>();
        String[] labels = poiLabels.split(",");
        for (String num : labels) {
            poiLabelList.add(Integer.parseInt(num));
        }
        //如果存在交集则是子门店
        if (!Collections.disjoint(poiLabelList, MccCustomerConfig.getSubPoiTagId())) {
            return true;
        }
        return false;
    }


    /**
     * 按照是否子门店排序
     *
     * @return
     */
    private Comparator<SwitchPoiQueryVO> getPoiChildFlagComparator() {
        return new Comparator<SwitchPoiQueryVO>() {
            @Override
            public int compare(SwitchPoiQueryVO o1, SwitchPoiQueryVO o2) {
                return o1.getChildPoiFlag().compareTo(o2.getChildPoiFlag());
            }
        };
    }



}

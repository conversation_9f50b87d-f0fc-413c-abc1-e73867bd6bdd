package com.sankuai.meituan.waimai.customer.service.sign.applytask.context;

import java.util.List;
import java.util.Set;

import javax.annotation.Resource;

import com.sankuai.meituan.waimai.thrift.customer.domain.sign.*;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.mchange.lang.IntegerUtils;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiService;
import com.sankuai.meituan.waimai.customer.util.base.AssertUtil;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyBizTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;

/**
 * 电子合同任务上下文拼装
 * 根据customerId拼装wmPoiIdList
 * <AUTHOR>
 */
@Service
public class WmEcontractCustomerWrapService implements IWmEcontractWrapService {

    private static final Set<EcontractTaskApplyTypeEnum> NOT_WRAP_WMPOIID_APPLYTYPE = Sets.newHashSet();

    static {
        NOT_WRAP_WMPOIID_APPLYTYPE.add(EcontractTaskApplyTypeEnum.BATCH_POI_GENERATE_PDF);
        NOT_WRAP_WMPOIID_APPLYTYPE.add(EcontractTaskApplyTypeEnum.ADDEDSERVICEDISCOUNT);
        NOT_WRAP_WMPOIID_APPLYTYPE.add(EcontractTaskApplyTypeEnum.SHANGOU_REBATE);
        NOT_WRAP_WMPOIID_APPLYTYPE.add(EcontractTaskApplyTypeEnum.OPERATION_MANAGER_KP_CONFIRM);
        NOT_WRAP_WMPOIID_APPLYTYPE.add(EcontractTaskApplyTypeEnum.DELIVERY_SERVICE_CONTRACT);
        NOT_WRAP_WMPOIID_APPLYTYPE.add(EcontractTaskApplyTypeEnum.DELIVERY_SITE_CONTRACT);
        NOT_WRAP_WMPOIID_APPLYTYPE.add(EcontractTaskApplyTypeEnum.AD_ANNUAL_FRAMEWORK_CONTRACT);
        NOT_WRAP_WMPOIID_APPLYTYPE.add(EcontractTaskApplyTypeEnum.BRAND_AD_CONTRACT);
        NOT_WRAP_WMPOIID_APPLYTYPE.add(EcontractTaskApplyTypeEnum.AD_ORDER);
        NOT_WRAP_WMPOIID_APPLYTYPE.add(EcontractTaskApplyTypeEnum.BUSINESS_CUSTOMER_E_CONTRACT);
        NOT_WRAP_WMPOIID_APPLYTYPE.add(EcontractTaskApplyTypeEnum.WM_POI_BASE_TAG_SIGN);
    }

    @Resource
    private WmCustomerPoiService wmCustomerPoiService;

    @Override
    public EcontractTaskContextBo wrap(EcontractTaskApplyBo applyBo,EcontractTaskContextBo contextBo) throws WmCustomerException {
        AssertUtil.assertObjectNotNull(contextBo, "申请信息");
        AssertUtil.assertEqual(contextBo.getBizTypeEnum().getType(), EcontractTaskApplyBizTypeEnum.CUSTOMER_ID.getType(), "申请类型必须为客户");

        if (EcontractTaskApplyTypeEnum.SETTLE.equals(contextBo.getApplyTypeEnum())) {
            return wrapSettle(contextBo);
        }

        if(EcontractTaskApplyTypeEnum.CUSTOMER.equals(contextBo.getApplyTypeEnum())){
            return wrapUnBindWmPoi(contextBo);
        }

        if(EcontractTaskApplyTypeEnum.FOODCITY_POI_TABLE.equals(contextBo.getApplyTypeEnum())){
            return wrapFoodPoiTable(contextBo);
        }

        if(EcontractTaskApplyTypeEnum.AGENT_SQS_STANDARD.equals(contextBo.getApplyTypeEnum())) {
            //根据客户查询门店
            contextBo.setCustomerId(applyBo.getWmCustomerId());
            contextBo.setWmPoiIdList(Lists.<Long>newArrayList());
            return contextBo;
        }

        if(NOT_WRAP_WMPOIID_APPLYTYPE.contains(contextBo.getApplyTypeEnum())){
            //根据客户查询门店
            contextBo.setCustomerId(IntegerUtils.parseInt(contextBo.getBizId(), 0));
            contextBo.setWmPoiIdList(Lists.<Long>newArrayList());
            return contextBo;
        }

        //根据客户查询门店
        contextBo.setCustomerId(IntegerUtils.parseInt(contextBo.getBizId(), 0));
        List<Long> wmPoiIdList = wmCustomerPoiService.selectWmPoiIdsByCustomerIdFromDBAndSwitchCentre(contextBo.getCustomerId());
        contextBo.setWmPoiIdList(wmPoiIdList);
        return contextBo;
    }

    private EcontractTaskContextBo wrapUnBindWmPoi(EcontractTaskContextBo contextBo) throws WmCustomerException {
        contextBo.setCustomerId(IntegerUtils.parseInt(contextBo.getBizId(), 0));
        EcontractCancelAuthInfoBo econtractCancelAuthInfoBo = JSONObject
                .parseObject(contextBo.getApplyInfoBo(), EcontractCancelAuthInfoBo.class);
        contextBo.setWmPoiIdList(econtractCancelAuthInfoBo.getWmPoiIdList());
        return contextBo;
    }

    private EcontractTaskContextBo wrapSettle(EcontractTaskContextBo contextBo) throws WmCustomerException {
        AssertUtil.assertObjectNotNull(contextBo, "申请信息");
        AssertUtil.assertEqual(contextBo.getBizTypeEnum().getType(), EcontractTaskApplyBizTypeEnum.CUSTOMER_ID.getType(), "申请类型必须为客户");

        contextBo.setCustomerId(IntegerUtils.parseInt(contextBo.getBizId(), 0));
        List<EcontractSettleInfoBo> settleInfoBoList = JSON.parseArray(contextBo.getApplyInfoBo(), EcontractSettleInfoBo.class);
        List<Long> wmPoiIdlist = Lists.newArrayList();
        for (EcontractSettleInfoBo settleInfoBo:settleInfoBoList) {
            if (settleInfoBo == null || CollectionUtils.isEmpty(settleInfoBo.getPoiInfoBoList())) {
                continue;
            }

            for (EcontractPoiInfoBo poiInfoBo:settleInfoBo.getPoiInfoBoList()) {
                wmPoiIdlist.add(poiInfoBo.getWmPoiId().longValue());
            }
        }
        contextBo.setWmPoiIdList(wmPoiIdlist);
        return contextBo;
    }

    private EcontractTaskContextBo wrapFoodPoiTable(EcontractTaskContextBo contextBo) throws WmCustomerException {
        contextBo.setCustomerId(IntegerUtils.parseInt(contextBo.getBizId(), 0));
        EcontractFoodcityPoiTableBo econtractFoodcityPoiTableBo = JSONObject.parseObject(contextBo.getApplyInfoBo(), EcontractFoodcityPoiTableBo.class);
        contextBo.setWmPoiIdList(econtractFoodcityPoiTableBo.getWmPoiIdList());
        return contextBo;
    }
}

package com.sankuai.meituan.waimai.customer.dao;

import com.sankuai.meituan.waimai.customer.domain.WmCustomerAuditDB;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerAuditVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public interface WmCustomerAuditDBMapper {

    int deleteCustomerAudit(Integer id);

    int insertCustomerAudit(WmCustomerAuditDB wmCustomerAuditDB);

    WmCustomerAuditDB selectCustomerAuditByCustomerId(Integer customerId);

    /**
     * 从主库查询
     *
     * @param customerId
     * @return
     */
    WmCustomerAuditDB selectCustomerAuditByCustomerIdRT(Integer customerId);

    /**
     * 更新提审信息为已打包提审。
     *
     * @param customerId
     * @return
     */
    int updatePackageCommitAudited(Integer customerId);

    WmCustomerAuditDB selectCustomerAuditById(Integer id);

    int updateCustomerAudit(WmCustomerAuditDB wmCustomerAuditDB);

    int invalidCustomerAudit(Integer customerId);

    List<WmCustomerAuditDB> selectCustomerAuditing(WmCustomerAuditVo wmCustomerAuditVo);

    int invalidCustomerAuditById(Integer id);

    /**
     * 根据特批任务ID查询特批中记录
     *
     * @param bizTaskId
     * @return
     */
    List<WmCustomerAuditDB> getSpecialAuditingByBizTaskId(@Param("bizTaskId") Integer bizTaskId);

    /**
     * 更新审核结果：这里会传审核返回的审核时间
     *
     * @param wmCustomerAuditDB
     * @return
     */
    int updateAuditResultById(WmCustomerAuditDB wmCustomerAuditDB);

    /**
     * 根据ID更新状态为特批中，并同步特批任务ID
     *
     * @param bizTaskId
     * @param id
     * @return
     */
    int update2SpecialAuditingById(@Param("bizTaskId") Long bizTaskId, @Param("id") Integer id);
}
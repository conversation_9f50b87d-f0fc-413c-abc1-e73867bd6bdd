package com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper.area.nationalsubsidy;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.customer.constant.WmEcontractConstant;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper.IWmEcontractAreaDataWrapperService;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.customer.util.trans.WmPoiSpAreaBoUtil;
import com.sankuai.meituan.waimai.econtrct.client.domain.biz.EcontractContentBo;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractBatchTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataWrapperEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.*;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * @description:
 * @author: liuyunjie05
 * @create: 2025/5/29 14:43
 */
@Slf4j
public abstract class AbstractNationalSubsidyDeliveryAreaWrapperService implements IWmEcontractAreaDataWrapperService {

    public static final String SUPPORT_MARK = "support";

    protected EcontractTaskApplyTypeEnum transToEcontractTaskApplyTypeEnum(EcontractBatchTypeEnum batchTypeEnum) throws WmCustomerException {
        if (EcontractBatchTypeEnum.NATIONAL_SUBSIDY_DISTRIBUTOR_DELIVERY == batchTypeEnum) {
            return EcontractTaskApplyTypeEnum.NATIONAL_SUBSIDY_DISTRIBUTOR_DELIVERY;
        }
        if (EcontractBatchTypeEnum.NATIONAL_SUBSIDY_HEADQUARTERS_DELIVERY == batchTypeEnum) {
            return EcontractTaskApplyTypeEnum.NATIONAL_SUBSIDY_HEADQUARTERS_DELIVERY;
        }
        throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "任务路由异常");
    }

    /**
     * 处理单个配送信息的包装
     */
//    public List<EcontractContentBo> wrapSingle(EcontractBatchContextBo contextBo) throws WmCustomerException {
//        log.info("AbstractNationalSubsidyDeliveryAreaWrapperService#wrapSingle:{}", JSON.toJSONString(contextBo));
//
//        EcontractTaskBo taskBo = getTaskBo(contextBo);
//        EcontractDeliveryInfoBo deliveryInfoBo = parseDeliveryInfo(taskBo);
//
//        if (!isValidSingleDeliveryInfo(deliveryInfoBo)) {
//            return Lists.newArrayList();
//        }
//
//        EcontractWmPoiSpAreaBo areaBoWithSlaText = processDeliveryArea(deliveryInfoBo);
//        if (areaBoWithSlaText == null) {
//            return Lists.newArrayList();
//        }
//
//        EcontractContentBo contentBo = WmPoiSpAreaBoUtil.transEcontractWmPoiSelfDeliveryAreaBo2EcontractContentBo(areaBoWithSlaText);
//        return Lists.newArrayList(contentBo);
//    }

    /**
     * 处理批量配送信息的包装
     */
    private List<EcontractContentBo> wrapBatch(EcontractBatchContextBo contextBo) throws WmCustomerException {
        log.info("AbstractNationalSubsidyDeliveryAreaWrapperService#wrapBatch, contextBo:{}", JSON.toJSONString(contextBo));

        EcontractTaskBo taskBo = getTaskBo(contextBo);
        EcontractBatchDeliveryInfoBo batchDeliveryInfoBo = parseBatchDeliveryInfo(taskBo);

        if (!isValidBatchDeliveryInfo(batchDeliveryInfoBo)) {
            return Lists.newArrayList();
        }

        return processBatchDeliveryInfoList(batchDeliveryInfoBo.getEcontractDeliveryInfoBoList());
    }

//    /**
//     * 默认的wrap方法，处理单个配送信息
//     */
//    public List<EcontractContentBo> wrap(EcontractBatchContextBo contextBo) throws WmCustomerException {
//        return wrapSingle(contextBo);
//    }

    /**
     * 根据包装类型路由到不同的处理方法
     */
    protected List<EcontractContentBo> wrap(EcontractBatchContextBo contextBo, EcontractDataWrapperEnum wrapperEnum) throws WmCustomerException, IllegalAccessException {
        switch (wrapperEnum) {
            case NATIONAL_SUBSIDY_DISTRIBUTOR_DELIVERY_PERFORMANCE_SERVICE_FEE:
            case NATIONAL_SUBSIDY_HEADQUARTERS_DELIVERY_PERFORMANCE_SERVICE_FEE:
                return wrapBatch(contextBo);
            case NATIONAL_SUBSIDY_DISTRIBUTOR_DELIVERY_WHOLE_CITY:
            case NATIONAL_SUBSIDY_HEADQUARTERS_DELIVERY_WHOLE_CITY:
                return wrapWholeCity(contextBo);
            case NATIONAL_SUBSIDY_DISTRIBUTOR_DELIVERY_LONG_DISTANCE:
            case NATIONAL_SUBSIDY_HEADQUARTERS_DELIVERY_LONG_DISTANCE:
                return wrapLongDistance(contextBo);
            default:
                throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "国补配送任务范围路由异常");
        }
    }

    /**
     * 获取任务信息
     */
    private EcontractTaskBo getTaskBo(EcontractBatchContextBo contextBo) throws WmCustomerException {
        return WmEcontractContextUtil.selectByApplyType(contextBo, transToEcontractTaskApplyTypeEnum(contextBo.getBatchTypeEnum()));
    }

    /**
     * 解析单个配送信息
     */
//    private EcontractDeliveryInfoBo parseDeliveryInfo(EcontractTaskBo taskBo) {
//        try {
//            return JSON.parseObject(taskBo.getApplyContext(), EcontractDeliveryInfoBo.class);
//        } catch (Exception e) {
//            log.warn("单个配送信息数据解析异常", e);
//            return null;
//        }
//    }

    /**
     * 解析批量配送信息
     */
    private EcontractBatchDeliveryInfoBo parseBatchDeliveryInfo(EcontractTaskBo taskBo) {
        try {
            return JSON.parseObject(taskBo.getApplyContext(), EcontractBatchDeliveryInfoBo.class);
        } catch (Exception e) {
            log.warn("批量配送信息数据解析异常", e);
            return null;
        }
    }

    /**
     * 验证单个配送信息是否有效
     */
    private boolean isValidSingleDeliveryInfo(EcontractDeliveryInfoBo deliveryInfoBo) {
        return deliveryInfoBo != null
                && StringUtils.isNotEmpty(deliveryInfoBo.getDeliveryArea())
                && isSupportedDeliveryType(deliveryInfoBo);
    }

    /**
     * 验证批量配送信息是否有效
     */
    private boolean isValidBatchDeliveryInfo(EcontractBatchDeliveryInfoBo batchDeliveryInfoBo) {
        return batchDeliveryInfoBo != null
                && CollectionUtils.isNotEmpty(batchDeliveryInfoBo.getEcontractDeliveryInfoBoList());
    }

    /**
     * 检查是否支持的配送类型
     */
    private boolean isSupportedDeliveryType(EcontractDeliveryInfoBo deliveryInfoBo) {
        return SUPPORT_MARK.equals(deliveryInfoBo.getSupportSelfDelivery())
                || SUPPORT_MARK.equals(deliveryInfoBo.getSupportMTDelivery())
                || SUPPORT_MARK.equals(deliveryInfoBo.getSupportCompanyCustomerDelivery());
    }

    /**
     * 处理配送区域信息并设置SLA文案
     */
    private EcontractWmPoiSpAreaBo processDeliveryArea(EcontractDeliveryInfoBo deliveryInfoBo) {
        EcontractWmPoiSpAreaBo areaBo = parseDeliveryAreaJson(deliveryInfoBo.getDeliveryArea());
        if (areaBo == null) {
            return null;
        }

        setSlaTextIfNeeded(areaBo, deliveryInfoBo);
        log.debug("处理后的配送区域信息:{}", JSON.toJSONString(areaBo));
        return areaBo;
    }

    /**
     * 解析配送区域JSON
     */
    private EcontractWmPoiSpAreaBo parseDeliveryAreaJson(String deliveryAreaJson) {
        try {
            return JSONObject.parseObject(deliveryAreaJson, EcontractWmPoiSpAreaBo.class);
        } catch (Exception e) {
            log.warn("配送区域JSON解析异常", e);
            return null;
        }
    }

    /**
     * 根据配送方式设置SLA文案
     */
    private void setSlaTextIfNeeded(EcontractWmPoiSpAreaBo areaBo, EcontractDeliveryInfoBo deliveryInfoBo) {
        if (SUPPORT_MARK.equals(deliveryInfoBo.getSupportMTDelivery())
                || SUPPORT_MARK.equals(deliveryInfoBo.getSupportCompanyCustomerDelivery())) {
            areaBo.setSlaText(ConfigUtilAdapter.getString("mtdelivery_extra_statement_text", ""));
        }
    }

    /**
     * 处理批量配送信息列表
     */
    private List<EcontractContentBo> processBatchDeliveryInfoList(List<EcontractDeliveryInfoBo> deliveryInfoBoList) {
        List<EcontractContentBo> result = Lists.newArrayList();

        for (EcontractDeliveryInfoBo deliveryInfoBo : deliveryInfoBoList) {
            if (isValidBatchDeliveryItem(deliveryInfoBo)) {
                EcontractWmPoiSpAreaBo areaBo = processBatchDeliveryArea(deliveryInfoBo);
                if (areaBo != null) {
                    EcontractContentBo contentBo = WmPoiSpAreaBoUtil.transEcontractWmPoiSelfDeliveryAreaBo2EcontractContentBo(areaBo);
                    result.add(contentBo);
                }
            }
        }

        return result;
    }

    /**
     * 验证批量配送项是否有效
     */
    private boolean isValidBatchDeliveryItem(EcontractDeliveryInfoBo deliveryInfoBo) {
        return (SUPPORT_MARK.equals(deliveryInfoBo.getSupportSelfDelivery())
                || SUPPORT_MARK.equals(deliveryInfoBo.getSupportMTDelivery()))
                && StringUtils.isNotEmpty(deliveryInfoBo.getDeliveryArea());
    }

    /**
     * 处理批量配送区域信息
     */
    private EcontractWmPoiSpAreaBo processBatchDeliveryArea(EcontractDeliveryInfoBo deliveryInfoBo) {
        EcontractWmPoiSpAreaBo areaBo = parseBatchDeliveryAreaJson(deliveryInfoBo.getDeliveryArea());
        if (areaBo == null) {
            return null;
        }

        setSlaTextIfNeeded(areaBo, deliveryInfoBo);
        return areaBo;
    }

    /**
     * 解析批量配送区域JSON（使用JSONArray.parseObject）
     */
    private EcontractWmPoiSpAreaBo parseBatchDeliveryAreaJson(String deliveryAreaJson) {
        try {
            return JSONArray.parseObject(deliveryAreaJson, EcontractWmPoiSpAreaBo.class);
        } catch (Exception e) {
            log.warn("批量配送区域JSON解析异常", e);
            return null;
        }
    }

    /**
     * 处理全城送配送信息的包装
     */
    public List<EcontractContentBo> wrapWholeCity(EcontractBatchContextBo contextBo) throws IllegalAccessException, WmCustomerException {
        log.info("AbstractNationalSubsidyDeliveryAreaWrapperService#wrapWholeCity:{}", JSON.toJSONString(contextBo));

        EcontractTaskBo taskBo = getBatchPoiFeeTaskBo(contextBo);
        EcontractBatchDeliveryInfoBo batchDeliveryInfoBo = parseBatchDeliveryInfo(taskBo);

        if (!isValidBatchDeliveryInfo(batchDeliveryInfoBo)) {
            return Lists.newArrayList();
        }

        return processWholeCityDeliveryInfoList(batchDeliveryInfoBo.getEcontractDeliveryInfoBoList());
    }

    /**
     * 处理远距离配送信息的包装
     */
    public List<EcontractContentBo> wrapLongDistance(EcontractBatchContextBo contextBo) throws IllegalAccessException, WmCustomerException {
        log.info("AbstractNationalSubsidyDeliveryAreaWrapperService#wrapLongDistance:{}", JSON.toJSONString(contextBo));

        EcontractTaskBo taskBo = getBatchPoiFeeTaskBo(contextBo);
        EcontractBatchDeliveryInfoBo batchDeliveryInfoBo = parseBatchDeliveryInfo(taskBo);

        if (!isValidBatchDeliveryInfo(batchDeliveryInfoBo)) {
            return Lists.newArrayList();
        }

        return processLongDistanceDeliveryInfoList(batchDeliveryInfoBo.getEcontractDeliveryInfoBoList());
    }

    /**
     * 获取批量门店费用任务信息
     */
    private EcontractTaskBo getBatchPoiFeeTaskBo(EcontractBatchContextBo contextBo) throws WmCustomerException {
        return WmEcontractContextUtil.selectByApplyType(contextBo, transToEcontractTaskApplyTypeEnum(contextBo.getBatchTypeEnum()));
    }

    /**
     * 处理全城送配送信息列表
     */
    private List<EcontractContentBo> processWholeCityDeliveryInfoList(List<EcontractDeliveryInfoBo> deliveryInfoBoList) {
        List<EcontractContentBo> result = Lists.newArrayList();

        for (EcontractDeliveryInfoBo deliveryInfoBo : deliveryInfoBoList) {
            EcontractDeliveryWholeCityInfoBo wholeCityInfoBo = deliveryInfoBo.getEcontractDeliveryWholeCityInfoBo();
            if (isValidWholeCityDeliveryItem(wholeCityInfoBo)) {
                EcontractWmPoiSpAreaBo areaBo = parseWholeCityDeliveryAreaJson(wholeCityInfoBo.getDeliveryArea());
                if (areaBo != null) {
                    areaBo.setSlaText(WmEcontractConstant.DEFAULT_DELIVERY_SLA_WHOLE_CITY_DOCUMENT);
                    EcontractContentBo contentBo = WmPoiSpAreaBoUtil.transEcontractWmPoiSelfDeliveryAreaBo2EcontractContentBo(areaBo);
                    result.add(contentBo);
                }
            }
        }

        return result;
    }

    /**
     * 处理远距离配送信息列表
     */
    private List<EcontractContentBo> processLongDistanceDeliveryInfoList(List<EcontractDeliveryInfoBo> deliveryInfoBoList) {
        List<EcontractContentBo> result = Lists.newArrayList();

        for (EcontractDeliveryInfoBo deliveryInfoBo : deliveryInfoBoList) {
            EcontractDeliveryCompanyCustomerLongDistanceInfoBo longDistanceInfoBo = deliveryInfoBo.getEcontractDeliveryCompanyCustomerLongDistanceInfoBo();
            if (isValidLongDistanceDeliveryItem(longDistanceInfoBo)) {
                EcontractWmPoiSpAreaBo areaBo = parseLongDistanceDeliveryAreaJson(longDistanceInfoBo.getDeliveryArea());
                if (areaBo != null) {
                    areaBo.setSlaText(WmEcontractConstant.DEFAULT_DELIVERY_COMPANY_CUSTOMER_LONG_DISTANCE_DOCUMENT);
                    EcontractContentBo contentBo = WmPoiSpAreaBoUtil.transEcontractWmPoiSelfDeliveryAreaBo2EcontractContentBo(areaBo);
                    result.add(contentBo);
                }
            }
        }

        return result;
    }

    /**
     * 验证全城送配送项是否有效
     */
    private boolean isValidWholeCityDeliveryItem(EcontractDeliveryWholeCityInfoBo wholeCityInfoBo) {
        return wholeCityInfoBo != null
                && SUPPORT_MARK.equals(wholeCityInfoBo.getSupportSLA())
                && StringUtils.isNotEmpty(wholeCityInfoBo.getDeliveryArea());
    }

    /**
     * 验证远距离配送项是否有效
     */
    private boolean isValidLongDistanceDeliveryItem(EcontractDeliveryCompanyCustomerLongDistanceInfoBo longDistanceInfoBo) {
        return longDistanceInfoBo != null
                && StringUtils.isNotEmpty(longDistanceInfoBo.getDeliveryArea());
    }

    /**
     * 解析全城送配送区域JSON
     */
    private EcontractWmPoiSpAreaBo parseWholeCityDeliveryAreaJson(String deliveryAreaJson) {
        try {
            return JSONArray.parseObject(deliveryAreaJson, EcontractWmPoiSpAreaBo.class);
        } catch (Exception e) {
            log.warn("全城送配送区域JSON解析异常", e);
            return null;
        }
    }

    /**
     * 解析远距离配送区域JSON
     */
    private EcontractWmPoiSpAreaBo parseLongDistanceDeliveryAreaJson(String deliveryAreaJson) {
        try {
            return JSONArray.parseObject(deliveryAreaJson, EcontractWmPoiSpAreaBo.class);
        } catch (Exception e) {
            log.warn("远距离配送区域JSON解析异常", e);
            return null;
        }
    }

}

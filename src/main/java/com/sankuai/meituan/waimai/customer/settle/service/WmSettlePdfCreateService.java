package com.sankuai.meituan.waimai.customer.settle.service;

import static com.sankuai.meituan.waimai.customer.util.trans.WmSettleTransUtil.PDF_MAPPING;

import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.settle.bo.WmQdbVo;
import com.sankuai.meituan.waimai.customer.settle.bo.WmSettleVo;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyBizTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettle;
import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettleAudited;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractPoiInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractSettleInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskApplyBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.wallet.WmWalletConstant;
import com.sankuai.meituan.waimai.wallet.WmWalletUtil;

@Service
public class WmSettlePdfCreateService {
    private static final Logger LOGGER = LoggerFactory.getLogger(WmSettlePdfCreateService.class);

    private final byte NORMAL_TYPE = (byte)1;
    private final byte MOON_TYPE = (byte)2;
    private final byte MIX_TYPE = (byte)3;

    @Autowired
    private WmSettleService wmSettleService;
    @Autowired
    private WmSettlePdfTransService wmSettlePdfTransService;
    @Autowired
    private WmSettleDiffService wmSettleDiffService;

    public Pair<Boolean, EcontractTaskApplyBo> genSettleTaskApplyBo(int wmCustomerId) throws WmCustomerException {
        List<WmSettle> wmSettleList = wmSettleService.getWmSettleByWmCustomerIdMaster(wmCustomerId);
        List<WmSettleAudited> wmSettleAuditedList = wmSettleService.getWmSettleAuditedByWmCustomerId(wmCustomerId, true);

        LOGGER.info("#genSettleTaskApplyBo,wmSettleList={},wmSettleAuditedList={}", JSONObject.toJSONString(wmSettleList),
                JSONObject.toJSONString(wmSettleAuditedList));

        if (CollectionUtils.isEmpty(wmSettleList)) {
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "没有结算信息");
        }
        boolean supportWallet = checkSettleOfflineIsMoonVersion(wmSettleList);

        EcontractTaskApplyBo applyBo = new EcontractTaskApplyBo();
        applyBo.setBizId(wmCustomerId + "");
        applyBo.setBizTypeEnum(EcontractTaskApplyBizTypeEnum.CUSTOMER_ID);
        applyBo.setApplyTypeEnum(EcontractTaskApplyTypeEnum.SETTLE);

        if (!supportWallet) {
            applyBo.setApplyInfoBo(JSONObject.toJSONString(genSettleNormalContext(wmSettleList, wmSettleAuditedList, true)));
        } else {
            applyBo.setApplyInfoBo(JSONObject.toJSONString(genMixSettleContext(wmSettleList, wmSettleAuditedList)));
        }
        return Pair.of(supportWallet, applyBo);
    }

    private List<EcontractSettleInfoBo> genMixSettleContext(List<WmSettle> wmSettleList,List<WmSettleAudited> wmSettleAuditedList){
        List<WmSettle> normalWmSettleList = Lists.newArrayList();
        List<WmSettle> moonWmSettleList = Lists.newArrayList();
        for(WmSettle temp : wmSettleList){
            if(WmWalletUtil.isUnEffectWallet(temp)){
                moonWmSettleList.add(temp);
            }else{
                normalWmSettleList.add(temp);
            }
        }
        List<EcontractSettleInfoBo> result = Lists.newArrayList();
        if(CollectionUtils.isNotEmpty(normalWmSettleList)) {
            result.addAll(genSettleNormalContext(
                    normalWmSettleList, wmSettleAuditedList, false));
        }
        if(CollectionUtils.isNotEmpty(moonWmSettleList)){
            result.addAll(genSettleMoonContext(
                    moonWmSettleList, wmSettleAuditedList, false));
        }
        genDiff(wmSettleList, wmSettleAuditedList, result);
        return result;
    }

    private List<EcontractSettleInfoBo> genSettleMoonContext(List<WmSettle> wmSettleList,List<WmSettleAudited> wmSettleAuditedList,boolean isGenDiff) {
        List<EcontractSettleInfoBo> result = Lists.newArrayList();
        List<Integer> wmPoiIdList = null;
        WmSettleVo wmSettleVo = null;
        WmQdbVo wmQdbVo = null;

        String accountType = "";
        String accountName = "";
        String accountCardNum = "";
        String province = "";
        String city = "";
        String bank = "";
        String branch = "";
        String financialContact = "";
        String financialContactPhone = "";
        String settleType = "";
        String payPeriod = "";
        String minPayAmount = "";
        String poiNames = "";
        String qdbNumber = "";
        String quaType = "";
        String quaNum = "";
        String reservedPhone = "";
        String partB = "";
        String partBAddress = "";
        String partBPhone = "";
        List<EcontractPoiInfoBo> poiInfoBoList = Lists.newArrayList();

        String legalPerson = "";
        String legalIDCardNum = "";

        EcontractSettleInfoBo infoBo = null;

        for (WmSettle wmSettle : wmSettleList) {
            wmPoiIdList = wmSettle.getWmPoiIdList();

            wmSettleVo = wmSettlePdfTransService.wmSettleBo2Vo(wmSettle);
            wmQdbVo = wmSettlePdfTransService.genQdbVo(wmSettle.getWmCustomerId());

            accountType = wmSettleVo.getAccType() + "";
            accountName = wmSettleVo.getAccName();
            accountCardNum = wmSettleVo.getAccCardNo();
            province = wmSettleVo.getProvince();
            city = wmSettleVo.getCity();
            bank = wmSettleVo.getBank();
            branch = wmSettleVo.getBranchname();
            financialContact = wmSettleVo.getPartyAFinancePeople();
            financialContactPhone = wmSettleVo.getPartyAFinancePhone();
            settleType = PDF_MAPPING.get("settleType"+wmSettleVo.getSettleType());
            payPeriod = wmSettleVo.getPayPeriodStr();
            if(wmSettleVo.getMinPayAmountType() == 4){
                minPayAmount = wmSettleVo.getMinPayAmount();
            }else{
                minPayAmount = PDF_MAPPING.get("minPayAmountType"+wmSettleVo.getMinPayAmountType());
            }

            qdbNumber = wmQdbVo.getQdbNumber();
            WmWalletConstant.CertificateRecord certificateRecord = WmWalletConstant.CertificateRecord.indexOf(wmSettle.getCert_type());
            quaType = certificateRecord == null ? "" : certificateRecord.getCertificateTypeName();

            //对公-取法人资质编号
            if(wmSettle.getAcctype() == 1){
                quaNum =
                    wmSettle.getLegal_cert_num();
            }
            //对私-取个人资质编号
            else if(wmSettle.getAcctype() == 2){
                quaNum =
                    wmSettle.getCert_num();
            }

            reservedPhone = wmSettle.getReserve_phone();
            partB = wmQdbVo.getPartyB();
            partBAddress = wmQdbVo.getPartyBAddress();
            partBPhone = wmQdbVo.getPartyBPhone();

            legalPerson = wmSettle.getLegal_person();
            legalIDCardNum = wmSettle.getLegal_id_card();

            poiInfoBoList = wmSettlePdfTransService.genEcontractPoiInfoBoList(wmPoiIdList);

            infoBo = new EcontractSettleInfoBo.Builder().poiNames(poiNames)
                    .accountType(accountType).accountName(accountName)
                    .accountCardNum(accountCardNum).province(province)
                    .city(city).bank(bank).branch(branch)
                    .financialContact(financialContact).financialContactPhone(financialContactPhone)
                    .settleType(settleType).payPeriod(payPeriod).minPayAmount(minPayAmount)
                    .qdbNumber(qdbNumber).quaType(quaType).reservedPhone(reservedPhone)
                    .partB(partB).partBAddress(partBAddress).partBPhone(partBPhone)
                    .poiInfoBoList(poiInfoBoList).quaNum(quaNum).supportWallet(true)
                    .legalPerson(legalPerson).legalIDCardNum(legalIDCardNum)
                    .build();
            result.add(infoBo);
        }

        if(isGenDiff){
            genDiff(wmSettleList, wmSettleAuditedList, result);
        }

        return result;
    }

    private void genDiff(List<WmSettle> wmSettleList, List<WmSettleAudited> wmSettleAuditedList,
            List<EcontractSettleInfoBo> result) {
        EcontractSettleInfoBo diffInfoBo = wmSettleDiffService
                .genApplyEcontractDiffInfo(wmSettleList, wmSettleAuditedList);
        if (diffInfoBo != null && CollectionUtils.isNotEmpty(result)) {
            result.get(0).setDiffInfo(diffInfoBo.getDiffInfo());
        }
    }

    private List<EcontractSettleInfoBo> genSettleNormalContext(List<WmSettle> wmSettleList,List<WmSettleAudited> wmSettleAuditedList,boolean isGenDiff) {
        List<EcontractSettleInfoBo> result = Lists.newArrayList();
        List<Integer> wmPoiIdList = null;
        List<List<Integer>> partionList = null;
        WmSettleVo wmSettleVo = null;

        String accountType = "";
        String accountName = "";
        String accountCardNum = "";
        String province = "";
        String city = "";
        String bank = "";
        String branch = "";
        String financialContact = "";
        String financialContactPhone = "";
        String settleType = "";
        String payPeriod = "";
        String minPayAmount = "";
        String poiNames = "";

        EcontractSettleInfoBo infoBo = null;

        for (WmSettle wmSettle : wmSettleList) {
            wmPoiIdList = wmSettle.getWmPoiIdList();
            wmSettleVo = wmSettlePdfTransService.wmSettleBo2Vo(wmSettle);
            accountType = wmSettleVo.getAccType() + "";
            accountName = wmSettleVo.getAccName();
            accountCardNum = wmSettleVo.getAccCardNo();
            province = wmSettleVo.getProvince();
            city = wmSettleVo.getCity();
            bank = wmSettleVo.getBank();
            branch = wmSettleVo.getBranchname();
            financialContact = wmSettleVo.getPartyAFinancePeople();
            financialContactPhone = wmSettleVo.getPartyAFinancePhone();
            settleType = PDF_MAPPING.get("settleType"+wmSettleVo.getSettleType());
            payPeriod = wmSettleVo.getPayPeriodStr();
            if(wmSettleVo.getMinPayAmountType() == 4){
                minPayAmount = wmSettleVo.getMinPayAmount();
            }else{
                minPayAmount = PDF_MAPPING.get("minPayAmountType"+wmSettleVo.getMinPayAmountType());
            }

            partionList = Lists.partition(wmPoiIdList, 20);
            List<EcontractPoiInfoBo> poiInfoBoList = null;
            for (List<Integer> tempList : partionList) {
                poiInfoBoList = Lists.newArrayList();
                for(Integer temp : tempList){
                    poiInfoBoList.add(new EcontractPoiInfoBo.Builder().wmPoiId(temp).build());
                }
                poiNames = wmSettlePdfTransService.genWmPoiNamesStr(tempList);
                infoBo = new EcontractSettleInfoBo.Builder().poiNames(poiNames).poiInfoBoList(poiInfoBoList)
                        .accountType(accountType).accountName(accountName)
                        .accountCardNum(accountCardNum).province(province)
                        .city(city).bank(bank).branch(branch)
                        .financialContact(financialContact).financialContactPhone(financialContactPhone)
                        .settleType(settleType).payPeriod(payPeriod).minPayAmount(minPayAmount).build();
                result.add(infoBo);
            }
        }

        if(isGenDiff){
            genDiff(wmSettleList, wmSettleAuditedList, result);
        }

        return result;
    }


    /**
     * 有一个结算属于开钱包-即可开钱包
     * @param wmSettleList
     * @return
     */
    public boolean checkSettleOfflineIsMoonVersion(List<WmSettle> wmSettleList) {
        if (CollectionUtils.isNotEmpty(wmSettleList)) {
            for (WmSettle wmSettle : wmSettleList) {
                if (WmWalletUtil.isUnEffectWallet(wmSettle)) {
                    return true;
                }
            }
            return false;
        }
        return false;
    }
}

package com.sankuai.meituan.waimai.customer.service.sign.base;

import com.sankuai.meituan.waimai.customer.domain.WmEContractSignBaseDB;
import com.sankuai.meituan.waimai.customer.service.sign.base.init.WmEcontractBaseCustomerInitService;
import com.sankuai.meituan.waimai.customer.service.sign.base.init.WmEcontractBaseKPInitService;
import com.sankuai.meituan.waimai.customer.service.sign.base.init.WmEcontractBasePoiInitService;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractBaseBizService;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractBaseApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBaseApplyBo;

import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class WmEcontractBaseInitService {

    @Resource
    private WmEcontractBaseCustomerInitService wmEcontractBaseCustomerInitService;

    @Resource
    private WmEcontractBaseKPInitService wmEcontractBaseKPInitService;

    @Resource
    private WmEcontractBaseBizService wmEcontractBaseBizService;

    @Resource
    private WmEcontractBasePoiInitService wmEcontractBasePoiInitService;

    public WmEContractSignBaseDB init(EcontractBaseApplyBo applyBo) throws WmCustomerException {
        WmEContractSignBaseDB baseDB;
        if (EcontractBaseApplyTypeEnum.CUSTOMER.equals(applyBo.getApplyTypeEnum())) {
            baseDB = wmEcontractBaseCustomerInitService.init(applyBo);
        } else if (EcontractBaseApplyTypeEnum.KP.equals(applyBo.getApplyTypeEnum())) {
            baseDB = wmEcontractBaseKPInitService.init(applyBo);
        } else {
            baseDB = wmEcontractBasePoiInitService.init(applyBo);
        }
        wmEcontractBaseBizService.save(baseDB);
        return baseDB;
    }

}

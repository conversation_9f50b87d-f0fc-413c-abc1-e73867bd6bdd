package com.sankuai.meituan.waimai.customer.service.sign.mafka.producer;

import com.alibaba.fastjson.JSONObject;
import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.ConsumerConstants;
import com.meituan.mafka.client.producer.IProducerProcessor;
import com.meituan.mafka.client.producer.ProducerResult;
import com.meituan.mafka.client.producer.ProducerStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.Properties;

/**
 * Author: 窦旭蒙
 * MIS: douxumeng
 * Date: 2022-04-09 15:20
 * Email: <EMAIL>
 * Desc:
 */
@Component
public class WmEContractUpdatePackStatusProducer {

    private static final Logger LOG = LoggerFactory.getLogger(WmEContractUpdatePackStatusProducer.class);

    private static IProducerProcessor producer;

    @PostConstruct
    public void init() {
        Properties properties = new Properties();
        properties.setProperty(ConsumerConstants.MafkaBGNamespace, "waimai");
        properties.setProperty(ConsumerConstants.MafkaClientAppkey, "com.sankuai.waimai.e.customer");
        try {
            // 创建topic对应的producer对象（注意每次build调用会产生一个新的实例）
            producer = MafkaClient.buildProduceFactory(properties, "econtract.signpack.status.update");
        } catch (Exception e) {
            LOG.error("WmEContractUpdatePackStatusProducer.init buildProduceFactory", e);
        }
    }

    @PreDestroy
    public void destroy() {
        try {
            producer.close();
        } catch (Exception e) {
            LOG.error("WmEContractUpdatePackStatusProducer.destroy", e);
        }
    }

    public boolean sendMsg(JSONObject jsonObject, Long packId) {
        try {
            String msg = jsonObject.toJSONString();
            ProducerResult result = producer.sendMessage(msg,packId);
            if (result.getProducerStatus() == ProducerStatus.SEND_FAILURE) { // 失败时重试一次
                result = producer.sendMessage(msg,packId);
            }
            if (result != null && result.getProducerStatus() == ProducerStatus.SEND_OK) {
                LOG.info("WmEContractUpdatePackStatusProducer.sendMsg success view={}", jsonObject.toJSONString());
            }
            return true;
        } catch (Exception e) {
            LOG.error("WmEContractUpdatePackStatusProducer.sendMsg jsonObject:{}", jsonObject, e);
        }
        return false;
    }
}

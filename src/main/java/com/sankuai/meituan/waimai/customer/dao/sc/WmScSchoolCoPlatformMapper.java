package com.sankuai.meituan.waimai.customer.dao.sc;

import com.sankuai.meituan.waimai.customer.domain.sc.WmScSchoolCoPlatformDO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 学校平台合作信息Mapper V2
 * <AUTHOR>
 * @date 2024/06/14
 * @email <EMAIL>
 **/
@Component
public interface WmScSchoolCoPlatformMapper {

    /**
     * 根据主键ID查询学校平台合作信息
     * @param id 主键ID
     * @return 学校平台合作信息
     */
    WmScSchoolCoPlatformDO selectByPrimaryKey(@Param("id") Long id);

    /**
     * 根据学校主键ID查询学校平台合作信息
     * @param schoolPrimaryId 学校主键ID
     * @return 学校平台合作信息列表
     */
    List<WmScSchoolCoPlatformDO> selectBySchoolPrimaryId(@Param("schoolPrimaryId") Integer schoolPrimaryId);

    /**
     * 根据学校主键ID和合作平台查询 (同一个学校可以有多个"其他"类型的合作平台)
     * @param schoolPrimaryId 学校主键ID
     * @param cooperationPlatform 合作平台枚举值
     * @return 学校平台合作信息列表
     */
    List<WmScSchoolCoPlatformDO> selectBySchoolPrimaryIdAndCooperationPlatform(@Param("schoolPrimaryId") Integer schoolPrimaryId,
                                                                             @Param("cooperationPlatform") Integer cooperationPlatform);

    /**
     * 新增学校平台合作信息(单条)
     * @param wmScSchoolCoPlatformDO 学校平台合作信息
     * @return 更新行数
     */
    int insertSelective(WmScSchoolCoPlatformDO wmScSchoolCoPlatformDO);

    /**
     * 根据主键ID进行更新
     * @param wmScSchoolCoPlatformDO wmScSchoolCoPlatformDO
     * @return 更新行数
     */
    int updateByPrimaryKeySelective(WmScSchoolCoPlatformDO wmScSchoolCoPlatformDO);

    /**
     * 根据主键ID进行逻辑删除
     * @param id 主键ID
     * @param muid 操作人ID
     * @return 更新行数
     */
    int invalidByPrimaryKey(@Param("id") Long id, @Param("muid") Long muid);

}

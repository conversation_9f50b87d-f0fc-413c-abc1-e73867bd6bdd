package com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.customer.aspect.AreaDataWrapper;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.customer.util.trans.WmPoiSpAreaBoUtil;
import com.sankuai.meituan.waimai.econtrct.client.domain.biz.EcontractContentBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataWrapperEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchDeliveryInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractWmPoiSpAreaBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@AreaDataWrapper(wrapperEnum = EcontractDataWrapperEnum.BATCH_DELIVERY)
public class WmEcontractBatchDeliverySelfDeliveryAreaWrapperService implements IWmEcontractAreaDataWrapperService {

    public static final String SUPPORT_MARK = "support";

    @Override
    public List<EcontractContentBo> wrap(EcontractBatchContextBo contextBo) throws WmCustomerException {

        List<EcontractContentBo> result = Lists.newArrayList();

        EcontractTaskBo taskBo = WmEcontractContextUtil.selectByApplyType(contextBo, EcontractTaskApplyTypeEnum.BATCHPOIFEE);

        EcontractBatchDeliveryInfoBo econtractBatchDeliveryInfoBo = null;

        try {
            econtractBatchDeliveryInfoBo = JSON.parseObject(taskBo.getApplyContext(), EcontractBatchDeliveryInfoBo.class);
        } catch (Exception e) {
            log.warn("数据解析异常", e);
            return result;
        }

        if (econtractBatchDeliveryInfoBo == null) {
            return result;
        }

        List<EcontractDeliveryInfoBo> econtractDeliveryInfoBoList = econtractBatchDeliveryInfoBo.getEcontractDeliveryInfoBoList();

        if (CollectionUtils.isEmpty(econtractDeliveryInfoBoList)) {
            return result;
        }

        EcontractWmPoiSpAreaBo econtractWmPoiSpAreaBoTemp = null;
        for (EcontractDeliveryInfoBo temp : econtractDeliveryInfoBoList) {
            // 支持（美配or自配）&& 配送范围不为空时
            if ((SUPPORT_MARK.equals(temp.getSupportSelfDelivery()) || SUPPORT_MARK.equals(temp.getSupportMTDelivery()))
                    && StringUtils.isNotEmpty(temp.getDeliveryArea())) {
                econtractWmPoiSpAreaBoTemp = JSONArray.parseObject(temp.getDeliveryArea(), EcontractWmPoiSpAreaBo.class);
                if (econtractWmPoiSpAreaBoTemp == null) {
                    continue;
                }
                // 根据配送方式封装特别声明文案（美配 or 企客）
                if (SUPPORT_MARK.equals(temp.getSupportMTDelivery())
                        || SUPPORT_MARK.equals(temp.getSupportCompanyCustomerDelivery())) {
                    econtractWmPoiSpAreaBoTemp.setSlaText(ConfigUtilAdapter.getString("mtdelivery_extra_statement_text", ""));
                }
                // 主配配送范围和主配sla配送范围都解析SelfDeliveryPlanBoList字段
                EcontractContentBo econtractContentBo = WmPoiSpAreaBoUtil.transEcontractWmPoiSelfDeliveryAreaBo2EcontractContentBo(econtractWmPoiSpAreaBoTemp);
                result.add(econtractContentBo);
            }
        }
        return result;
    }
}

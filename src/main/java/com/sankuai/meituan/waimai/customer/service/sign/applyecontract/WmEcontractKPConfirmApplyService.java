package com.sankuai.meituan.waimai.customer.service.sign.applyecontract;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper.WmEcontractDateWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.sms.WmEcontractKPSmsWrapperService;
import com.sankuai.meituan.waimai.econtrct.client.constants.TaskConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.EcontractBatchBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageBatchInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataWrapperEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;

import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

@Service
public class WmEcontractKPConfirmApplyService extends AbstractWmEcontractApplyAdapterService {

    private static final String FLOW_KP_CONFIRM = "kp_confirm";

    private static List<String> flowList = Lists.newArrayList();

    private static Map<String, EcontractDataWrapperEnum> dataWrapperMap = Maps.newHashMap();

    static {
        flowList.add(FLOW_KP_CONFIRM);

        dataWrapperMap.put(FLOW_KP_CONFIRM, EcontractDataWrapperEnum.KP_CONFIRM);
    }

    @Resource
    private WmEcontractDateWrapperService wmEcontractDateWrapperService;

    @Resource
    private WmEcontractKPSmsWrapperService wmEcontractKPSmsWrapperService;

    @Override
    public EcontractBatchBo wrapEcontractBo(EcontractBatchContextBo batchContextBo)
        throws TException, IllegalAccessException, WmCustomerException {
        List<StageBatchInfoBo> batchInfoBoList = Lists.newArrayList();
        batchInfoBoList.add(wmEcontractDateWrapperService.wrap(batchContextBo, dataWrapperMap));
        batchInfoBoList.add(wmEcontractKPSmsWrapperService.wrap(batchContextBo));

        return new EcontractBatchBo.Builder()
            .token(getToken())
            .econtractBizId(getBizId(batchContextBo))
            .econtractType(TaskConstant.TYPE_WM_CUSTOMER_KP_CONFIRM)
            .stageInfoBoList(batchInfoBoList)
            .flowList(flowList)
            .callBackUrl(getCallbackUrl()).econtractBatchSource(getSource(batchContextBo))
            .build();
    }
}

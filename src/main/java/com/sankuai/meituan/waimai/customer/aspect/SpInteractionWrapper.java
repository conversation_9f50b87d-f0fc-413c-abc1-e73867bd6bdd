package com.sankuai.meituan.waimai.customer.aspect;

import java.lang.annotation.ElementType;
import java.lang.annotation.Inherited;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataWrapperEnum;

/**
* H5特殊交互数据组装
*/
@Inherited
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface SpInteractionWrapper {

    EcontractDataWrapperEnum wrapperEnum();

}

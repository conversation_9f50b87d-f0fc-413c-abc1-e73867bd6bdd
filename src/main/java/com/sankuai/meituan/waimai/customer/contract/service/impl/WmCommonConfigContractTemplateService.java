package com.sankuai.meituan.waimai.customer.contract.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.base.MoreObjects;
import com.sankuai.meituan.util.DateUtil;
import com.sankuai.meituan.waimai.customer.contract.domain.WmContractVersionDB;
import com.sankuai.meituan.waimai.customer.contract.service.impl.check.ContractCheckFilter;
import com.sankuai.meituan.waimai.customer.ddd.contract.domain.aggre.WmContractSignAggre;
import com.sankuai.meituan.waimai.customer.util.ContractNumberUtil;
import com.sankuai.meituan.waimai.thrift.customer.constant.*;
import com.sankuai.meituan.waimai.thrift.customer.constant.config.ContractSourceEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.config.ContractConfigInfo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractBasicBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractSignBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskApplyBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskConfigBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.ManualTaskApplyBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.ManualTaskSettleContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.config.*;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.UUID;

/**
 * @description: 配置化合同发起签约类
 * @author: liuyunjie05
 * @create: 2024/5/21 15:16
 */
@Slf4j
@Service
public class WmCommonConfigContractTemplateService extends AbstractWmEContractTempletService {

    @Override
    public Integer startSign(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException, TException {
        log.info("WmCommonConfigContractTemplateService#startSign, contractBo: {}", JSON.toJSONString(contractBo));
        // 如果是发起待打包
        if (contractBo.getPackWay() == SignPackWay.WAIT_HAND_PACK.getCode()) {
            try {
                // 前置校验
                ContractCheckFilter.getConfigContractPackSignFilter().filter(contractBo, opUid, opName);
                // 获取当前数据
                WmCustomerContractBo oldBo = wmContractService
                        .getWmCustomerContractBoById(contractBo.getBasicBo().getTempletContractId(), false, opUid, opName);
                // 更新or插入
                Integer contractId = insertOrUpdate(contractBo, opUid, opName);
                // 记录变更状态
                int status = MoreObjects.firstNonNull(oldBo.getBasicBo(), new WmTempletContractBasicBo()).getStatus();
                if (status != contractBo.getBasicBo().getStatus()) {
                    contractLogService.logStatusChange(
                            MoreObjects.firstNonNull(oldBo.getBasicBo(), new WmTempletContractBasicBo()).getStatus(),
                            contractBo.getBasicBo(), opUid, opName);
                }
                contractLogService.logUpdate(oldBo, contractBo, opUid, opName);
                // 正式发起待签约任务
                toNextContractStatus(contractId, CustomerContractStatus.WAITING_SIGN.getCode(), opName);
                contractBo.getBasicBo().setStatus(CustomerContractStatus.WAITING_SIGN.getCode());
                contractLogService.logStatusChange(MoreObjects.firstNonNull(oldBo.getBasicBo(), new WmTempletContractBasicBo()).getStatus(),
                        contractBo.getBasicBo(), opUid, opName);
                ManualTaskApplyBo manualTaskApplyBo = buildManualTaskApplyBo(contractBo.getBasicBo(), contractId, opUid);
                wmEcontractSignBzService.applyManualTask(manualTaskApplyBo);
                return contractId;
            } catch (WmCustomerException ec) {
                log.error("WmCommonConfigContractTemplateService#startSign, WmCustomerException, 发起待打包合同任务失败 contractBo: {}, msg: {}", JSON.toJSONString(contractBo), ec.getMsg());
                throw ec;
            } catch (Exception e) {
                log.error("WmCommonConfigContractTemplateService#startSign, error, contractBo: {}", JSON.toJSONString(contractBo), e);
                throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "发起待签约合同异常");
            }
        } else {
            return startSignConfigContract(contractBo, opUid, opName);
        }
    }

    private ManualTaskApplyBo buildManualTaskApplyBo(WmTempletContractBasicBo basicBo, long contractId, int opUid) {
        return new ManualTaskApplyBo.Builder()
                .commitUid(opUid)
                .customerId(basicBo.getParentId())
                .applyTypeEnum(EcontractTaskApplyTypeEnum.COMMON_CONFIG_FRAME_CONTRACT_AGREEMENT)
                .bizId(contractId)
                .applyContext(JSON.toJSONString(buildManualTaskSettleContextBo(basicBo)))
                .module(basicBo.getConfigContractInfo() != null ? basicBo.getConfigContractInfo().getContractCode() : EcontractTaskApplyTypeEnum.COMMON_CONFIG_FRAME_CONTRACT_AGREEMENT.getName())
                .build();
    }

    private ManualTaskSettleContextBo buildManualTaskSettleContextBo(WmTempletContractBasicBo basicBo) {
        ManualTaskSettleContextBo manualTaskSettleContextBo = new ManualTaskSettleContextBo();
        manualTaskSettleContextBo.setContractSource(ContractSourceEnum.CONFIG.getCode());
        manualTaskSettleContextBo.setConfigContractId(basicBo.getConfigContractInfo() != null ? basicBo.getConfigContractInfo().getContractId() : 0);
        manualTaskSettleContextBo.setConfigContractCode(basicBo.getConfigContractInfo() != null ? basicBo.getConfigContractInfo().getContractCode() : EcontractTaskApplyTypeEnum.COMMON_CONFIG_FRAME_CONTRACT_AGREEMENT.getName());
        return manualTaskSettleContextBo;
    }

    private Integer startSignConfigContract(WmCustomerContractBo contractBo, int opUid, String opName) throws TException, WmCustomerException {
        ContractCheckFilter.configContractSignFilter().filter(contractBo, opUid, opName);
        WmCustomerContractBo oldBo = wmContractService
                .getWmCustomerContractBoById(contractBo.getBasicBo().getTempletContractId(), false, opUid, opName);
        Integer contractId = insertOrUpdate(contractBo, opUid, opName);

        //新建并发起签约时，同步暂存状态
        contractLogService.logStatusChange(MoreObjects.firstNonNull(oldBo.getBasicBo(), new WmTempletContractBasicBo()).getStatus(),
                contractBo.getBasicBo(), opUid, opName);

        WmContractVersionDB versionDB = saveVersion(opUid, opName, contractId, CustomerContractStatus.SIGNING.getCode());
        contractLogService.logUpdate(oldBo, contractBo, opUid, opName);
        toNextContractStatus(contractId, CustomerContractStatus.SIGNING.getCode(), opName);
        contractBo.getBasicBo().setStatus(CustomerContractStatus.SIGNING.getCode());
        contractLogService.logStatusChange(MoreObjects.firstNonNull(oldBo.getBasicBo(), new WmTempletContractBasicBo()).getStatus(),
                contractBo.getBasicBo(), opUid, opName);
        applySign(contractBo, versionDB, opUid, opName);
        return contractId;
    }

    @Override
    EcontractTaskApplyBo buildEcontractTaskApplyBo(WmCustomerContractBo contractBo, WmContractVersionDB versionDB) throws WmCustomerException {
        EcontractTaskApplyBo applyBo = new EcontractTaskApplyBo();
        applyBo.setRecordId(versionDB.getVersion_number());
        applyBo.setBizId(String.valueOf(contractBo.getBasicBo().getParentId()));
        applyBo.setBizTypeEnum(EcontractTaskApplyBizTypeEnum.CUSTOMER_ID);
        applyBo.setApplyTypeEnum(EcontractTaskApplyTypeEnum.COMMON_CONFIG_FRAME_CONTRACT_AGREEMENT);
        ContractConfigInfo configContractInfo = contractBo.getBasicBo().getConfigContractInfo();
        applyBo.setConfigContractContext(initConfigContractContext(configContractInfo));

        CommonConfigContractInfoBo configContractInfoBo = new CommonConfigContractInfoBo();
        WmTempletContractSignBo partyASignerBo = WmContractSignAggre.Factory.makeWithSignBo(contractBo.getSignBoList()).getPartyASignerBo();
        WmTempletContractSignBo partyBSignerBo = WmContractSignAggre.Factory.makeWithSignBo(contractBo.getSignBoList()).getPartyBSignerBo();
        configContractInfoBo.setPartAStampName(partyASignerBo.getSignName());
        configContractInfoBo.setPartBStampName(partyBSignerBo.getSignName());
        configContractInfoBo.setContractNum(contractBo.getBasicBo().getContractNum());
        if (contractBo.getBasicBo().getDueDate() == 0L) {
            configContractInfoBo.setDueDate("长期有效");
        } else {
            configContractInfoBo.setDueDate(DateUtil.secondsToString((int) contractBo.getBasicBo().getDueDate()));
        }
        if (contractBo.getBasicBo().getExpectEffectiveDate() == 0) {
            configContractInfoBo.setExpectEffectiveDate("立即生效");
        } else {
            configContractInfoBo.setExpectEffectiveDate(DateUtil.secondsToString(contractBo.getBasicBo().getExpectEffectiveDate()));
        }
        configContractInfoBo.setConfigContractId(configContractInfo.getContractId());
        configContractInfoBo.setConfigContractCode(configContractInfo.getContractCode());
        configContractInfoBo.setConfigContractName(configContractInfo.getContractName());


        applyBo.setConfigBo(new EcontractTaskConfigBo());
        applyBo.setApplyInfoBo(JSON.toJSONString(configContractInfoBo));
        log.info("WmCommonConfigContractTemplateService#buildEcontractTaskApplyBo: {}", JSON.toJSONString(applyBo));
        return applyBo;
    }

    private ConfigContractContext initConfigContractContext(ContractConfigInfo configContractInfo) {
        ConfigContractContext contractContext = new ConfigContractContext();
        BeanUtils.copyProperties(configContractInfo, contractContext);

        ContractContentTemplateContext contentTemplateContext = new ContractContentTemplateContext();
        BeanUtils.copyProperties(configContractInfo.getContentTemplateInfo(), contentTemplateContext);
        contractContext.setContentTemplateContext(contentTemplateContext);

        EcontractUserContext econtractUserContext = new EcontractUserContext();
        BeanUtils.copyProperties(configContractInfo.getEcontractUserInfo(), econtractUserContext);
        contractContext.setEcontractUserContext(econtractUserContext);

        if (configContractInfo.getSignFlowTemplateInfo() != null) {
            SignFlowTemplateContext signFlowTemplateContext = new SignFlowTemplateContext();
            BeanUtils.copyProperties(configContractInfo.getSignFlowTemplateInfo(), signFlowTemplateContext);
            contractContext.setSignFlowTemplateContext(signFlowTemplateContext);
        }
        return contractContext;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer save(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException, TException {
        contractBo.getBasicBo().setContractNum(UUID.randomUUID().toString());
        Integer insertId = super.save(contractBo, opUid, opName);
        String configContractCooperationEnum = ContractNumberUtil.genConfigContractCooperationAgreementEnum(String.valueOf(contractBo.getBasicBo().getType()), insertId);
        contractBo.getBasicBo().setContractNum(configContractCooperationEnum);
        wmTempletContractDBMapper.updateNumberById(insertId.longValue(), configContractCooperationEnum);
        log.info("WmCommonConfigContractTemplateService#save, contractId: {}, contractNumber: {}", insertId, configContractCooperationEnum);
        return insertId;
    }


    @Override
    public boolean invalid(int contractId, int opUid, String opUname) throws WmCustomerException, TException {
        log.info("WmCommonConfigContractTemplateService#invalid, contractId: {}, opUid: {}, opUname: {}", contractId, opUid, opUname);
        WmCustomerContractBo wmCustomerContractBo = new WmCustomerContractBo();
        wmCustomerContractBo.setBasicBo(new WmTempletContractBasicBo());
        wmCustomerContractBo.getBasicBo().setTempletContractId(contractId);
        // 合同废除校验
        ContractCheckFilter.getCommonConfigContractAbolishFilter().filter(wmCustomerContractBo, opUid, opUname);
        return super.invalid(contractId, opUid, opUname);
    }

}

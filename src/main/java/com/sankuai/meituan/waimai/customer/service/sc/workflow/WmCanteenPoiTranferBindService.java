package com.sankuai.meituan.waimai.customer.service.sc.workflow;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.customer.adapter.WmScEmployAdaptor;
import com.sankuai.meituan.waimai.customer.constant.sc.WmCanteenPoiAuditFlowV2Enum;
import com.sankuai.meituan.waimai.customer.constant.sc.WmChangeBindPoiLogTypeEnum;
import com.sankuai.meituan.waimai.customer.constant.sc.WmScTairLockSceneEnum;
import com.sankuai.meituan.waimai.customer.dao.sc.*;
import com.sankuai.meituan.waimai.customer.domain.sc.*;
import com.sankuai.meituan.waimai.customer.service.sc.WmScLogService;
import com.sankuai.meituan.waimai.customer.service.sc.WmScTagV2Service;
import com.sankuai.meituan.waimai.customer.service.sc.WmScTairService;
import com.sankuai.meituan.waimai.customer.service.sc.check.WmCanteenPoiCheckService;
import com.sankuai.meituan.waimai.customer.service.sc.poi.WmScCanteenInfoService;
import com.sankuai.meituan.waimai.customer.service.sc.poi.WmScCanteenPoiAttributeService;
import com.sankuai.meituan.waimai.customer.service.sc.poi.WmScCanteenPoiService;
import com.sankuai.meituan.waimai.customer.service.sc.status.CanteenStatusMachine;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmCanPoiTaskBaseBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmCanPoiTaskPoiInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmCanPoiTaskSumBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmScCanIllegalPoi;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import com.sankuai.meituan.waimai.util.DaxiangUtilV2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.sankuai.meituan.waimai.customer.service.sc.workflow.WmCanteenPoiBindService.CANTEEN_POI_TASK_LOCKKEY_PREFIX;

@Slf4j
@Service
public class WmCanteenPoiTranferBindService implements WmCanteenPoiTaskService {

    @Autowired
    private WmCanteenPoiCheckService wmCanteenPoiCheckService;

    @Autowired
    private CanteenStatusMachine canteenStatusMachine;

    @Autowired
    private WmAuditPersonService wmAuditPersonService;

    @Autowired
    private WmScCanteenPoiTaskMapper wmScCanteenPoiTaskMapper;

    @Autowired
    private WmScCanteenPoiTaskDetailMapper wmScCanteenPoiTaskDetailMapper;

    @Autowired
    private WmCanteenMapper wmCanteenMapper;

    @Autowired
    private WmScCanteenPoiAuditMapper wmScCanteenPoiAuditMapper;

    @Autowired
    private WmScCanteenPoiAuditDetailMapper wmScCanteenPoiAuditDetailMapper;

    @Autowired
    private WmScCanteenPoiService wmScCanteenPoiService;

    @Autowired
    private WmScCanteenPoiHistoryMapper wmScCanteenPoiHistoryMapper;

    @Autowired
    private WmScTagV2Service wmScTagV2Service;

    @Autowired
    private WmScEmployAdaptor wmScEmployAdaptor;

    @Autowired
    protected WmScLogService wmScLogService;

    @Autowired
    private WmScCanteenPoiAttributeService wmScCanteenPoiAttributeService;

    @Autowired
    private WmScCanteenInfoService wmScCanteenInfoService;

    @Autowired
    private WmScCanteenPoiAttributeMapper wmScCanteenPoiAttributeMapper;

    @Autowired
    private WmScCanteenPoiLabelMapper wmScCanteenPoiLabelMapper;

    @Autowired
    private WmCanteenPoiBindService wmCanteenPoiBindService;

    @Autowired
    private WmScTairService wmScTairService;

    @Autowired
    private WmScCanteenPoiTaskAuditMinutiaMapper wmScCanteenPoiTaskAuditMinutiaMapper;

    @Autowired
    public void setUp(List<WorkFlowService> workFlowList) {
        if (CollectionUtils.isNotEmpty(workFlowList)) {
            workFlowServices.addAll(workFlowList);
        } else {
            log.error("工作流初始化失败，请检查后重新启动！！！");
            throw new RuntimeException("工作流初始化失败，请检查后重新启动！！！");
        }
    }

    private TreeSet<WorkFlowService> workFlowServices = new TreeSet<>(new Comparator<WorkFlowService>() {
        @Override
        public int compare(WorkFlowService o1, WorkFlowService o2) {
            return o1.getOrder() - o2.getOrder();
        }
    });


    @Override
    public CanteenPoiTaskTypeEnum getTaskType() {
        return CanteenPoiTaskTypeEnum.TRANSFER_BIND;
    }

    @Override
    public void checkTask(WmCanteenPoiTaskBO wmCanteenPoiTaskBO) throws WmSchCantException {
        WmCanteenDB canteen = wmCanteenPoiTaskBO.getCanteenTo();
        // 1、换绑食堂不能是当前食堂
        if (wmCanteenPoiTaskBO.getCanteenIdFrom().equals(wmCanteenPoiTaskBO.getCanteenIdTo())) {
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "换绑食堂不能和原食堂一致");
        }
        // 2、换绑食堂不能有流程中任务
        if (canteenStatusMachine.isAuditing(canteen) || canteenStatusMachine.isInsertReject(canteen)) {
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "换绑食堂信息未生效或在审批中，不可保存");
        }
        // 3、公共校验
        wmCanteenPoiCheckService.valid(wmCanteenPoiTaskBO);
        // 4、校验门店是否已绑定其他食堂
        wmCanteenPoiCheckService.validTranferBindOther(wmCanteenPoiTaskBO.getCanteenIdFrom(), wmCanteenPoiTaskBO.getWmPoiIdList());
    }

    /**
     * 食堂换绑门店创建食堂门店审核任务
     * @param wmCanteenPoiTaskBO wmCanteenPoiTaskBO
     * @return 任务ID
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    @Override
    public long createTask(WmCanteenPoiTaskBO wmCanteenPoiTaskBO) throws WmSchCantException {
        log.info("[WmCanteenPoiTranferBindService.createTask] wmCanteenPoiTaskBO = {}", JSONObject.toJSONString(wmCanteenPoiTaskBO));
        // 创建任务前的最新一条任务信息
        WmScCanteenPoiTaskDO beforePoiTaskDo = wmScCanteenPoiTaskMapper.selectLatestTaskByCanteenPrimaryKey(wmCanteenPoiTaskBO.getCanteenIdTo());

        String tairLockKey = CANTEEN_POI_TASK_LOCKKEY_PREFIX
                + wmCanteenPoiTaskBO.getCanteenIdFrom().toString() + "_"
                + wmCanteenPoiTaskBO.getCanteenIdTo().toString();
        // [尝试加锁] 若加锁失败代表当前有任务进行中
        wmScTairService.tryLockWithScene(tairLockKey, WmScTairLockSceneEnum.CANTEEN_POI_TASK);

        // 第一步: 在食堂门店任务主表(wm_sc_canteen_poi_task)中新增一条数据, 返回主键ID即任务ID
        long taskId = insertWmCanteenPoiTask(wmCanteenPoiTaskBO);
        wmCanteenPoiTaskBO.setId(taskId);

        // 第二步: 在食堂门店任务子表(wm_sc_canteen_poi_task_detail)中新增N条数据, N = 绑定门店数
        wmCanteenPoiBindService.insertWmCanteenPoiTaskDetail(wmCanteenPoiTaskBO);

        // 第三步: 在食堂门店审核任务节点表(wm_sc_canteen_poi_task_audit_minutia)中新增一条数据
        Integer nextAuditNode = CanteenAuditProgressEnum.getNextNode(wmCanteenPoiTaskBO.getAuditNodeTypeEnum(), null);
        buildNextWorkFlow(wmCanteenPoiTaskBO, nextAuditNode);

        // [尝试解锁] 若解锁失败可三次重试
        wmScTairService.unLockWithScene(tairLockKey, WmScTairLockSceneEnum.CANTEEN_POI_TASK);

        // 提交成功后更新状态
        CanteenAuditNodeEnum auditNodeEnum = CanteenAuditNodeEnum.of(nextAuditNode);
        if (taskId > 0 && auditNodeEnum != null) {
            wmScCanteenPoiTaskMapper.updateAuditStatusByPrimaryKey(taskId, auditNodeEnum.getAuditStatus());
        } else {
            log.error("createTask 出现异常任务 wmCanteenPoiTaskBO={}", JSONObject.toJSONString(wmCanteenPoiTaskBO));
        }
        // 记录任务发起日志
        recordSaveLog(beforePoiTaskDo,auditNodeEnum,taskId,wmCanteenPoiTaskBO);
        // 记录换绑发起日志
        WmScCanteenPoiChangeBindLogBO wmScCanteenPoiChangeBindLogBO = new WmScCanteenPoiChangeBindLogBO.Builder()
                .canteenPoiTaskId(taskId)
                .wmChangeBindPoiLogTypeEnum(WmChangeBindPoiLogTypeEnum.PUBLISH)
                .changePoiAggreList(wmCanteenPoiTaskBO.getWmPoiAggreList())
                .canteenIdTo(wmCanteenPoiTaskBO.getCanteenIdTo())
                .canteenIdFrom(wmCanteenPoiTaskBO.getCanteenIdFrom())
                .opUid(wmCanteenPoiTaskBO.getUserId())
                .opUname(wmCanteenPoiTaskBO.getUserName())
                .build();
        wmScLogService.saveChangeBindPoiLog(wmScCanteenPoiChangeBindLogBO);
        return taskId;
    }

    /**
     * 在食堂门店审核任务主表(wm_sc_canteen_poi_task)中, 新增一条数据
     * @param wmCanteenPoiTaskBO wmCanteenPoiTaskBO
     * @return 主键ID, 即任务ID
     */
    public long insertWmCanteenPoiTask(WmCanteenPoiTaskBO wmCanteenPoiTaskBO) {
        log.info("[WmCanteenPoiTranferBindService.insertWmCanteenPoiTask] wmCanteenPoiTaskBO = {}", JSONObject.toJSONString(wmCanteenPoiTaskBO));
        WmScCanteenPoiTaskDO wmScCanteenPoiTaskDO = new WmScCanteenPoiTaskDO();
        wmScCanteenPoiTaskDO.setCanteenIdFrom(wmCanteenPoiTaskBO.getCanteenIdFrom());
        wmScCanteenPoiTaskDO.setCanteenIdTo(wmCanteenPoiTaskBO.getCanteenIdTo());
        wmScCanteenPoiTaskDO.setAuditStatus(CanteenPoiAuditStatusV2Enum.WAITING_SUBMIT.getCode());
        wmScCanteenPoiTaskDO.setAuditNodeType(wmCanteenPoiTaskBO.getAuditNodeTypeEnum().getCode());
        wmScCanteenPoiTaskDO.setValid(ValidEnum.VALID.getTypeInt());
        wmScCanteenPoiTaskDO.setTaskType(wmCanteenPoiTaskBO.getTaskType());
        wmScCanteenPoiTaskDO.setTaskReasonType(wmCanteenPoiTaskBO.getTaskReasonType());
        wmScCanteenPoiTaskDO.setTaskReason(wmCanteenPoiTaskBO.getTaskReason());
        wmScCanteenPoiTaskDO.setUserId(wmCanteenPoiTaskBO.getUserId());
        wmScCanteenPoiTaskDO.setUserName(wmCanteenPoiTaskBO.getUserName());
        wmScCanteenPoiTaskMapper.insertSelective(wmScCanteenPoiTaskDO);
        return wmScCanteenPoiTaskDO.getId();
    }

    /**
     * 在食堂门店审核任务节点表(wm_sc_canteen_poi_task_audit_minutia)中, 新增1条数据
     * 并同时创建审批任务 / 发起电子签约
     * @param wmCanteenPoiTaskBO wmCanteenPoiTaskBO
     * @param nextAuditNode 下一个任务节点
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void buildNextWorkFlow(WmCanteenPoiTaskBO wmCanteenPoiTaskBO, Integer nextAuditNode) throws WmSchCantException {
        log.info("[WmCanteenPoiTranferBindService.buildNextWorkFlow] input param: wmCanteenPoiTaskBO = {}, nextAuditNode = {}",
                JSONObject.toJSONString(wmCanteenPoiTaskBO), nextAuditNode);
        CanteenAuditNodeEnum auditNodeEnum = CanteenAuditNodeEnum.of(nextAuditNode);
        if (nextAuditNode == null || auditNodeEnum == CanteenAuditNodeEnum.UNKNOW) {
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "换绑食堂信息任务未找到流程节点");
        }

        WmAuditPersonCondition condition = new WmAuditPersonCondition();
        condition.setAuditNodeEnum(auditNodeEnum);
        condition.setUserId(wmCanteenPoiTaskBO.getUserId());
        condition.setWmCanteenDB(wmCanteenPoiTaskBO.getCanteenTo());
        condition.setWmSchoolDB(wmCanteenPoiTaskBO.getSchoolTo());
        WmAuditPersonBo wmAuditPersonBo = wmAuditPersonService.getAuditPerson(condition);
        if (wmAuditPersonBo == null) {
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "审批负责人不存在，不可保存");
        }

        wmCanteenPoiTaskBO.setNextUser(wmAuditPersonBo);
        wmCanteenPoiTaskBO.setNextNode(auditNodeEnum);
        wmCanteenPoiTaskBO.setAuditStatus(auditNodeEnum.getAuditStatus());
        wmCanteenPoiTaskBO.setTaskTypeEnum(CanteenPoiTaskTypeEnum.TRANSFER_BIND);
        if (nextAuditNode.intValue() != CanteenAuditNodeEnum.SAVE_SUCCESS.getCode()) {
            for (WorkFlowService workFlowService : workFlowServices) {
                workFlowService.buildNext(wmCanteenPoiTaskBO);
            }
        }
    }

    /**
     * 记录任务发起日志
     * @param beforePoiTaskDo beforePoiTaskDo
     * @param auditNodeEnum 任务审批类型
     * @param taskId 任务ID
     * @param wmCanteenPoiTaskBO wmCanteenPoiTaskBO
     */
    private void recordSaveLog(WmScCanteenPoiTaskDO beforePoiTaskDo, CanteenAuditNodeEnum auditNodeEnum, long taskId, WmCanteenPoiTaskBO wmCanteenPoiTaskBO) {
        try{
            // 记录日志
            LinkedHashMap<Byte, String> valuesMap = Maps.newLinkedHashMap();
            valuesMap.put(WmCanteenPoiAuditFlowV2Enum.CRATE_TICKET.getCode(), String.valueOf(taskId));
            CanteenPoiAuditStatusV2Enum preStatus = (beforePoiTaskDo == null || beforePoiTaskDo.getAuditStatus() == null) ? CanteenPoiAuditStatusV2Enum.WAITING_SUBMIT : CanteenPoiAuditStatusV2Enum.getByCode(beforePoiTaskDo.getAuditStatus());
            if (wmCanteenPoiTaskBO.getCanteenIdTo() != null) {
                List<WmScCanteenPoiAttributeDO> wmScCanteenPoiAttributeDOList = wmScCanteenPoiAttributeMapper.selectBycanteenPrimaryId(wmCanteenPoiTaskBO.getCanteenIdTo());
                if (CollectionUtils.isEmpty(wmScCanteenPoiAttributeDOList) && preStatus == CanteenPoiAuditStatusV2Enum.EFFECTED) {
                    preStatus = CanteenPoiAuditStatusV2Enum.WAITING_SUBMIT;
                }
            }

            WmScCanteenPoiSaveLogBO wmScCanteenPoiSaveLogBO = new WmScCanteenPoiSaveLogBO.Builder()
                    .canteenPoiTaskId(taskId)
                    .valuesMap(valuesMap)
                    .preAuditStatus(preStatus)
                    .aftAuditStatus(CanteenPoiAuditStatusV2Enum.of(auditNodeEnum.getAuditStatus()))
                    .canteenPoiTaskTypeEnum(CanteenPoiTaskTypeEnum.BIND)
                    .canteenIdTo(wmCanteenPoiTaskBO.getCanteenIdTo())
                    .canteenIdFrom(wmCanteenPoiTaskBO.getCanteenIdFrom())
                    .opUid(wmCanteenPoiTaskBO.getUserId())
                    .opUname(wmCanteenPoiTaskBO.getUserName())
                    .build();
            wmScLogService.saveCanteenPoiLogV2(wmScCanteenPoiSaveLogBO);
        } catch (Exception e){
            log.error("[recordSaveLog] error. auditNodeEnum = {}, taskId = {}, wmCanteenPoiTaskBO = {}", auditNodeEnum, taskId, wmCanteenPoiTaskBO, e);
        }
    }

    @Override
    public CanteenPoiAuditStatusV2Enum commitTask(WmCanteenPoiTaskAuditMinutiaBO wmCanteenPoiTaskAuditMinutiaBO) throws WmSchCantException {
        log.info("[WmCanteenPoiTranferBindService.commitTask] input param: wmCanteenPoiTaskAuditMinutiaBO = {}", JSONObject.toJSONString(wmCanteenPoiTaskAuditMinutiaBO));
        // 更新审核明细
        for (WorkFlowService workFlowService : workFlowServices) {
            workFlowService.updateAuditResult(wmCanteenPoiTaskAuditMinutiaBO);
        }
        // 流转到终点（审核驳回/任务整体审批完成）直接返回结果
        if (wmCanteenPoiTaskAuditMinutiaBO.getAuditStatusV2Enum() == CanteenPoiAuditStatusV2Enum.EFFECTED
                || wmCanteenPoiTaskAuditMinutiaBO.getAuditResult() == CanteeAuditNodeResultEnum.REJECT.getCode()) {
            return wmCanteenPoiTaskAuditMinutiaBO.getAuditStatusV2Enum();
        }

        // 下一审批节点
        WmCanteenPoiTaskBO wmCanteenPoiTaskBO = wmScCanteenInfoService.buildTaskBO(wmCanteenPoiTaskAuditMinutiaBO.getCanteenPoiTaskId());
        CanteenAuditNodeEnum nextAuditNode = wmCanteenPoiTaskAuditMinutiaBO.getNextNodeEnum();
        if (nextAuditNode == null || nextAuditNode == CanteenAuditNodeEnum.UNKNOW) {
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "换绑食堂信息任务未找到流程节点");
        }
        WmAuditPersonCondition condition = new WmAuditPersonCondition();
        condition.setAuditNodeEnum(nextAuditNode);
        condition.setUserId(wmCanteenPoiTaskBO.getUserId());
        condition.setWmCanteenDB(wmCanteenPoiTaskBO.getCanteenTo());
        condition.setWmSchoolDB(wmCanteenPoiTaskBO.getSchoolTo());
        WmAuditPersonBo wmAuditPersonBo = wmAuditPersonService.getAuditPerson(condition);
        if (wmAuditPersonBo == null) {
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "审批负责人不存在，不可保存");
        }
        wmCanteenPoiTaskBO.setNextUser(wmAuditPersonBo);
        wmCanteenPoiTaskBO.setTaskTypeEnum(CanteenPoiTaskTypeEnum.TRANSFER_BIND);
        wmCanteenPoiTaskBO.setNextNode(wmCanteenPoiTaskAuditMinutiaBO.getNextNodeEnum());
        wmCanteenPoiTaskBO.setAuditStatus(wmCanteenPoiTaskAuditMinutiaBO.getNextNodeEnum().getAuditStatus());
        for (WorkFlowService workFlowService : workFlowServices) {
            workFlowService.buildNext(wmCanteenPoiTaskBO);
        }
        return CanteenPoiAuditStatusV2Enum.of(wmCanteenPoiTaskBO.getAuditStatus());
    }

    @Override
    public void cancelTask(long taskId, int opUid, String opUname) throws WmSchCantException {
        log.info("[WmCanteenPoiTranferBindService.cancelTask] taskId={},opUid={},opUname={}", taskId, opUid, opUname);
        for (WorkFlowService workFlowService : workFlowServices) {
            workFlowService.cancelTask(taskId, opUid, opUname);
        }
    }

    @Override
    public void effectTask(WmCanteenPoiTaskBO wmCanteenPoiTaskBO) throws WmSchCantException, TException {
        log.info("[WmCanteenPoiTranferBindService.effectTask] input param: wmCanteenPoiTaskBO = {}", JSONObject.toJSONString(wmCanteenPoiTaskBO));
        WmCanApprovedPoiResultBo wmCanApprovedPoiResultBo = getValidAndInvalidPoiIdList(wmCanteenPoiTaskBO);
        // 兼容旧流程
        effectTaskCompatibleWithOldTable(wmCanteenPoiTaskBO, wmCanApprovedPoiResultBo);
        // 兼容新流程
        effectTaskWithAddTag(wmCanteenPoiTaskBO, wmCanApprovedPoiResultBo);
        // 记录门店换绑生效日志
        WmScCanteenPoiChangeBindLogBO wmScCanteenPoiChangeBindLogBO = new WmScCanteenPoiChangeBindLogBO.Builder()
                .wmChangeBindPoiLogTypeEnum(WmChangeBindPoiLogTypeEnum.SUCCESS)
                .canteenPoiTaskId(wmCanteenPoiTaskBO.getId())
                .canteenIdTo(wmCanteenPoiTaskBO.getCanteenTo().getId())
                .canteenIdFrom(wmCanteenPoiTaskBO.getCanteenFrom().getId())
                .changePoiAggreList(wmCanteenPoiTaskBO.getWmPoiAggreList())
                .opUid(wmCanteenPoiTaskBO.getUserId())
                .opUname(wmCanteenPoiTaskBO.getUserName())
                .build();
        wmScLogService.saveChangeBindPoiLog(wmScCanteenPoiChangeBindLogBO);
    }

    @Override
    public void getAuditInfo(WmScCanteenPoiTaskDO wmScCanteenPoiTaskDO, WmCanPoiTaskSumBo wmCanPoiTaskSumBo) throws WmSchCantException, TException {
        wmCanPoiTaskSumBo.setTaskType(wmScCanteenPoiTaskDO.getTaskType());
        //通过食堂ID 获取换绑前的食堂 学校 蜂窝信息
        int canteenFrom = wmScCanteenPoiTaskDO.getCanteenIdFrom();
        WmCanPoiTaskBaseBo beforeTaskVo = wmScCanteenInfoService.buildPoiTaskBaseBo(canteenFrom, false);
        wmCanPoiTaskSumBo.setWmCanTaskBeforeVo(beforeTaskVo);
        //通过食堂ID 获取换绑后的食堂 学校 蜂窝信息
        int canteenTo = wmScCanteenPoiTaskDO.getCanteenIdTo();
        WmCanPoiTaskBaseBo afterTaskVo = wmScCanteenInfoService.buildPoiTaskBaseBo(canteenTo, true);
        wmCanPoiTaskSumBo.setWmCanTaskAfterVo(afterTaskVo);

        //获取当前绑定门店信息
        List<WmScCanteenPoiAttributeDO> canteenPoiAttributeDOList = wmScCanteenPoiAttributeService.selectEffectByCanteenPrimaryId(canteenTo);
        List<Long> hasWmPoiIdList = canteenPoiAttributeDOList.stream().map((item) -> {
            return item.getWmPoiId();
        }).collect(Collectors.toList());
        List<WmCanPoiTaskPoiInfoBo> hasPoiInfoList = wmScCanteenInfoService.buildPoiInfoBos(hasWmPoiIdList);
        wmCanPoiTaskSumBo.setHasWmPoiVos(hasPoiInfoList);

        //获取将要添加的门店信息
        List<WmScCanteenPoiTaskDetailDO> taskDetailDOList = wmScCanteenPoiTaskDetailMapper.selectByCanteenPoiTaskId(wmScCanteenPoiTaskDO.getId());
        List<Long> addWmPoiIdList = taskDetailDOList.stream().map((item) -> {
            return item.getWmPoiId();
        }).collect(Collectors.toList());
        List<WmCanPoiTaskPoiInfoBo> addPoiInfoList = wmScCanteenInfoService.buildPoiInfoBos(addWmPoiIdList);
        wmCanPoiTaskSumBo.setAddWmPoiVos(addPoiInfoList);
    }


    /**
     * 任务生效流程 ：兼容旧表逻辑部分
     * @param wmCanteenPoiTaskBO       记录任务详情
     * @param wmCanApprovedPoiResultBo 记录审批通过的合法门店和非法门店
     * @throws WmSchCantException WmSchCantException
     */
    public void effectTaskCompatibleWithOldTable(WmCanteenPoiTaskBO wmCanteenPoiTaskBO, WmCanApprovedPoiResultBo wmCanApprovedPoiResultBo)
            throws WmSchCantException {
        log.info("effectTaskCompatibleWithOldTable wmCanteenPoiTaskBO={},wmCanApprovedPoiResultBo={}", JSONObject.toJSONString(wmCanteenPoiTaskBO),
                JSONObject.toJSONString(wmCanApprovedPoiResultBo));
        int canteenIdTo = wmCanteenPoiTaskBO.getCanteenTo().getId();
        int canteenIdFrom = wmCanteenPoiTaskBO.getCanteenFrom().getId();
        int time = (int) (System.currentTimeMillis() / 1000L);
        // 获取合法的门店列表
        List<Long> validPoiIdList = wmCanApprovedPoiResultBo.getValidPoiIdList();

        // 合法门店为空说明不需要改动任何数据
        if (CollectionUtils.isEmpty(validPoiIdList)) {
            return;
        }

        // 查询原食堂生效任务
        WmScCanteenPoiAuditDB auditFrom = wmScCanteenPoiAuditMapper.getLatestCanteenPoiEffect(canteenIdFrom);
        if (auditFrom == null) {
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "该食堂数据异常，换绑失败");
        }
        // 获取原食堂下绑定门店
        WmScCanteenPoiAuditDBCondition condition = new WmScCanteenPoiAuditDBCondition();
        condition.setCanteenPoiAuditId(auditFrom.getId());
        condition.setCanteenId(canteenIdFrom);
        condition.setValid(ValidEnum.VALID.getTypeInt());
        List<WmScCanteenPoiAuditDetailDB> detailFromList = wmScCanteenPoiAuditDetailMapper.selectPoiByCondition(condition);
        if (CollectionUtils.isEmpty(detailFromList)) {
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "该食堂数据异常，换绑失败");
        }

        // 判断该任务下是否还有其他绑定的门店
        List<Long> bindPoiFromList = detailFromList.stream().map(x -> x.getWmPoiId()).collect(Collectors.toList());
        bindPoiFromList.removeAll(validPoiIdList);
        if (CollectionUtils.isEmpty(bindPoiFromList)) {
            // 如果门店只有当前门店，则将原食堂任务置为无效
            wmScCanteenPoiAuditMapper.updateAuditUnValid(auditFrom.getId());
        }
        wmScCanteenPoiAuditDetailMapper.updateBindUnValid(validPoiIdList, auditFrom.getId(), canteenIdFrom);

        // 查询待绑定食堂是否已经有生效任务
        WmScCanteenPoiAuditDB auditTo = wmScCanteenPoiAuditMapper.getLatestCanteenPoiEffect(canteenIdTo);
        if (auditTo == null) {
            // 新增一条已生效的食堂任务数据
            auditTo  = WmScCanteenPoiAuditDB.builder()
                    .canteenId(canteenIdTo)
                    .auditStatus((int) CanteenPoiAuditStatusEnum.EFFECTED.getCode())
                    .valid(ValidEnum.VALID.getTypeInt())
                    .build();

            auditTo.setUserId(wmCanteenPoiTaskBO.getUserId());
            auditTo.setUserName(wmCanteenPoiTaskBO.getUserName());
            auditTo.setCtime(time);
            auditTo.setUtime(time);
            wmScCanteenPoiAuditMapper.insertSelective(auditTo);
        } else {
            auditTo.setUtime(time);
            wmScCanteenPoiAuditMapper.updateByPrimaryKey(auditTo);
        }

        //新增加生效数据
        List<WmPoiAggre> wmPoiAggreList = wmCanteenPoiTaskBO.getWmPoiAggreList();
        WmScCanteenPoiAuditDB finalAuditTo = auditTo;
        List<WmScCanteenPoiAuditDetailDB> poiDetailList = wmPoiAggreList.stream().filter(
                //如果是处于门店下线状态 或者门店坐标处于学校外面 那么需要剔除该学校
                item -> validPoiIdList.contains(item.getWm_poi_id())
        ).map((item) -> {
            return WmScCanteenPoiAuditDetailDB.builder()
                    .canteenPoiAuditId(finalAuditTo.getId())
                    .wmPoiId(item.getWm_poi_id())
                    .canteenId(canteenIdTo)
                    .valid(ValidEnum.VALID.getTypeInt())
                    .ctime(time)
                    .utime(time)
                    .build();
        }).collect(Collectors.toList());
        wmScCanteenPoiAuditDetailMapper.batchInsert(poiDetailList);
    }

    /**
     * 任务生效流程：打标签部分
     * @param wmCanteenPoiTaskBO       记录任务详情
     * @param wmCanApprovedPoiResultBo 记录审批通过的合法门店和非法门店
     * @throws TException         TException
     * @throws WmSchCantException WmSchCantException
     */
    public void effectTaskWithAddTag(WmCanteenPoiTaskBO wmCanteenPoiTaskBO, WmCanApprovedPoiResultBo wmCanApprovedPoiResultBo)
            throws WmSchCantException, TException {
        log.info("[WmCanteenPoiTranferBindService.effectTaskWithAddTag] wmCanteenPoiTaskBO = {}, wmCanApprovedPoiResultBo = {}",
                JSONObject.toJSONString(wmCanteenPoiTaskBO), JSONObject.toJSONString(wmCanApprovedPoiResultBo));
        int time = (int) (System.currentTimeMillis() / 1000L);
        List<Long> validPoiIdList = wmCanApprovedPoiResultBo.getValidPoiIdList();
        // 合法门店为空说明不需要改动下面数据
        if (CollectionUtils.isEmpty(validPoiIdList)) {
            log.info("[WmCanteenPoiTranferBindService.effectTaskWithAddTag] validPoiIdList is empty. wmCanApprovedPoiResultBo = {}",
                    JSONObject.toJSONString(wmCanApprovedPoiResultBo));
            return;
        }
        WmCanteenDB canteenFrom = wmCanteenPoiTaskBO.getCanteenFrom();
        WmCanteenDB canteenTo = wmCanteenPoiTaskBO.getCanteenTo();
        int canteenIdTo = canteenTo.getId();
        int canteenIdFrom = canteenFrom.getId();

        // 修改原食堂和当前食堂下的门店数量
        int canteenFromPoiNum = wmScCanteenInfoService.getCurrentCanteenPoiNum(canteenIdFrom) - validPoiIdList.size();
        wmCanteenMapper.updateStoreNum(canteenIdFrom, canteenFromPoiNum);

        int canteenToPoiNum = wmScCanteenInfoService.getCurrentCanteenPoiNum(canteenIdTo) + validPoiIdList.size();
        wmCanteenMapper.updateStoreNum(canteenIdTo, canteenToPoiNum);

        // 将门店打标表中的食堂信息从原食堂变成当前食堂
        wmScCanteenPoiLabelMapper.batchUpdateLabelsCanteenMsg(validPoiIdList, canteenFrom.getId(), canteenTo.getId());

        // 如果原食堂的供给分级和被换绑到的食堂的供给分级不一样则需要更新门店的B标
        if ((canteenFrom.getGrade() == null && canteenTo.getGrade() != null)
                || (canteenFrom.getGrade() != null && canteenTo.getGrade() != null && !canteenFrom.getGrade().equals(canteenTo.getGrade()))) {
            wmScTagV2Service.batchChangeScCanPoiTag(canteenIdTo, validPoiIdList, canteenTo.getGrade(), wmCanteenPoiTaskBO.getUserId(), wmCanteenPoiTaskBO.getUserName());
        }

        // 向打标历史记录表中记录数据
        List<WmScCanteenPoiHistoryDO> wmScCanteenPoiHistoryDOList = validPoiIdList.stream().map((item) -> {
            return WmScCanteenPoiHistoryDO.builder()
                    .canteenIdTo(canteenIdTo)
                    .canteenIdFrom(canteenIdFrom)
                    .ctime((long) time)
                    .userId(wmCanteenPoiTaskBO.getUserId())
                    .userName(wmCanteenPoiTaskBO.getUserName())
                    .operateType(CanteenPoiOperateTypeEnum.TRANSFER_BIND.getCode())
                    .operateSource(CanteenPoiOperateSourceEnum.CANTEEN_POI_TASK.getCode())
                    .wmPoiId(item).build();
        }).collect(Collectors.toList());
        wmScCanteenPoiHistoryMapper.batchInsert(wmScCanteenPoiHistoryDOList);
        // 换绑成功后 修改食堂门店属性表(增量)
        wmScCanteenPoiAttributeService.modifyCanteenPoiAttribute(canteenIdTo, canteenIdFrom, validPoiIdList);
    }

    /**
     * 获取合法和非法门店列表（下线状态，或者处于校外的门店是非法门店）
     * @param wmCanteenPoiTaskBO 记录任务详情
     * @throws WmSchCantException WmSchCantException
     */
    private WmCanApprovedPoiResultBo getValidAndInvalidPoiIdList(WmCanteenPoiTaskBO wmCanteenPoiTaskBO) throws WmSchCantException {
        log.info("[WmCanteenPoiTranferBindService.getValidAndInvalidPoiIdList] wmCanteenPoiTaskBO = {}", JSONObject.toJSONString(wmCanteenPoiTaskBO));
        List<Long> wmPoiIdList = wmCanteenPoiTaskBO.getWmPoiIdList();
        //获取合法的门店列表
        List<Long> validPoiIdList = Lists.newArrayList(wmPoiIdList);
        WmCanApprovedPoiResultBo wmCanApprovedPoiResultBo = new WmCanApprovedPoiResultBo();
        //获取非法门店ID列表
        List<WmScCanIllegalPoi> wmScCanIllegalPois = wmScCanteenPoiService.getIllegalCanPois(wmPoiIdList, wmCanteenPoiTaskBO.getSchoolTo().getId());
        if (CollectionUtils.isNotEmpty(wmScCanIllegalPois)) {
            List<Long> invalidPoiIdList = wmScCanIllegalPois.stream().map(WmScCanIllegalPoi::getWmPoiId).collect(Collectors.toList());
            log.info("getValidAndInvalidPoiIdList invalidPoiIdList={}", JSONObject.toJSONString(invalidPoiIdList));
            validPoiIdList.removeAll(invalidPoiIdList);
            wmCanApprovedPoiResultBo.setInvalidPoiIdList(invalidPoiIdList);
            sendMsgAndWriteLog(wmCanteenPoiTaskBO.getCanteenTo().getId(), wmCanteenPoiTaskBO.getUserId(), wmScCanIllegalPois);
        }
        wmCanApprovedPoiResultBo.setValidPoiIdList(validPoiIdList);
        wmCanApprovedPoiResultBo.setInvalidPoiInfoList(wmScCanIllegalPois);
        //记录日志 并且发送消息给负责人大象
        return wmCanApprovedPoiResultBo;
    }

    /**
     * 发送审核通过但由于处于校外或者门店下线等原因绑定失败等门店消息给负责人大象
     * 并且记录在日志当中
     */
    private void sendMsgAndWriteLog(int canteenId,
                                    int userId,
                                    List<WmScCanIllegalPoi> wmScCanIllegalPois) throws WmSchCantException {
        if (CollectionUtils.isEmpty(wmScCanIllegalPois)) {
            return;
        }
        try {
            WmEmploy responsiblePerson = wmScEmployAdaptor.getById(userId);
            if (responsiblePerson != null) {
                String misId = responsiblePerson.getMisId();
                String reciver = String.format("%<EMAIL>", misId);
                String msg = composeMsg(wmScCanIllegalPois);
                DaxiangUtilV2.push(msg, reciver);
            }
            wmScLogService.saveIllegalCanteenPoiLog(canteenId, wmScCanIllegalPois);
        } catch (WmSchCantException e) {
            log.warn("sendMsgAndWriteLog 失败 canteenId={},userId={},wmScCanIllegalPois={}", canteenId, userId, wmScCanIllegalPois, e);
        } catch (Exception e) {
            log.error("sendMsgAndWriteLog 失败 canteenId={},userId={},wmScCanIllegalPois={}", canteenId, userId, wmScCanIllegalPois, e);
        }
    }

    private String composeMsg(List<WmScCanIllegalPoi> wmScCanIllegalPois) {
        StringBuilder msgBuffer = new StringBuilder();
        //根据错误类型进行分类
        Map<Integer, List<WmScCanIllegalPoi>> illegalMap = Maps.newHashMap();
        for (WmIllegalCanPoiTypeEnum wmIllegalCanPoiTypeEnum : WmIllegalCanPoiTypeEnum.values()) {
            illegalMap.put(wmIllegalCanPoiTypeEnum.getCode(), Lists.newLinkedList());
        }
        for (WmScCanIllegalPoi wmScCanIllegalPoi : wmScCanIllegalPois) {
            illegalMap.get(wmScCanIllegalPoi.getIllegalPoiType()).add(wmScCanIllegalPoi);
        }
        Iterator<Map.Entry<Integer, List<WmScCanIllegalPoi>>> illegalPoiIterator = illegalMap.entrySet().iterator();

        while (illegalPoiIterator.hasNext()) {
            Map.Entry<Integer, List<WmScCanIllegalPoi>> entry = illegalPoiIterator.next();
            WmIllegalCanPoiTypeEnum wmIllegalCanPoiTypeEnum = WmIllegalCanPoiTypeEnum.getByCode(entry.getKey());
            List<WmScCanIllegalPoi> wmScCanIllegalPoiList = entry.getValue();
            if(wmScCanIllegalPoiList == null || CollectionUtils.isEmpty(wmScCanIllegalPoiList)){
                continue;
            }
            for (WmScCanIllegalPoi wmScCanIllegalPoi : wmScCanIllegalPoiList) {
                msgBuffer.append(wmScCanIllegalPoi.getPoiName()).append(wmScCanIllegalPoi.getWmPoiId()).append(".");
            }
            msgBuffer.append("因为").append(wmIllegalCanPoiTypeEnum.getName()).append("审批流程完成后无法绑定食堂，请知晓\n");
        }

        return msgBuffer.toString();
    }
}

package com.sankuai.meituan.waimai.customer.settle.grey;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Function;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.infra.constants.WmOrgConstant.OrgType;
import com.sankuai.meituan.waimai.mtauth.manager.WmAdminDataAuthManager;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Nullable;
import java.util.List;

/**
 * 千鹭灰度
 */
@Service
@Slf4j
public class WmHeronSettleGrayService {

    @Autowired
    private WmCustomerService      wmCustomerService;
    @Autowired
    private WmAdminDataAuthManager wmAdminDataAuthManager;

    /**
     * 千鹭结算灰度策略-指定客户id/客户负责人所在外卖城市
     */
    public boolean routHeronSettleGray(int wmCustomerId, int opUid) throws WmCustomerException {
        //全量开启开关-true:全量开启
        if (ConfigUtilAdapter.getBoolean("heron_settle_open", false)) {
            log.info("routHeronSettleGray#true");
            return true;
        }

        //灰度开启开关-true:开启灰度
        if (!ConfigUtilAdapter.getBoolean("heron_settle_gray_open", false)) {
            return false;
        }

        if (wmCustomerId == 0) {
            return false;
        }

        //customerId维度灰度
        String greyForcustomerId = ConfigUtilAdapter.getString("heron_settle_gray_customerId");
        if (!StringUtils.isEmpty(greyForcustomerId)) {
            String[] splitGreyForcustomerId = greyForcustomerId.split(",|，");
            List<Integer> orgList = Lists.newArrayList(Lists.transform(Lists.newArrayList(splitGreyForcustomerId), new Function<String, Integer>() {
                @Nullable
                @Override
                public Integer apply(@Nullable String input) {
                    return Integer.valueOf(input);
                }
            }));
            if (orgList.contains(wmCustomerId)) {
                log.info("routHeronSettleGray#true greyForcustomerId");
                return true;
            }
        }

        WmCustomerDB customerDB = wmCustomerService.selectCustomerById(wmCustomerId);
        if (customerDB == null || customerDB.getOwnerUid() == null || customerDB.getOwnerUid() == 0) {
            return false;
        }

        //优先使用入参操作人,无操作人信息时使用客户负责人信息
        Integer uid = null;
        if (opUid != 0) {
            uid = opUid;
        } else {
            uid = customerDB.getOwnerUid();
        }

        boolean isHq = wmAdminDataAuthManager.isHQ(uid);

        //总部权限使用开关
        if (ConfigUtilAdapter.getBoolean("heron_settle_gray_isHq", false) && isHq) {
            log.info("routeDDDGrey#true isHq");
            return true;
        }

        if (isHq) {
            return false;
        }

        //BD权限开关
        String canUseManualPackOrgId = com.sankuai.meituan.util.ConfigUtilAdapter.getString("heron_settle_gray_OrgId");
        if (StringUtils.isEmpty(canUseManualPackOrgId)) {
            return false;
        }

        //总部权限会查出所有的外卖城市
        List<Integer> orgIds = wmAdminDataAuthManager.getOrgIds(uid, OrgType.WM_ORG_CITY);
        if (CollectionUtils.isEmpty(orgIds)) {
            return false;
        }

        String[] orgString = canUseManualPackOrgId.split(",|，");
        List<Integer> orgList = Lists.newArrayList(Lists.transform(Lists.newArrayList(orgString), new Function<String, Integer>() {
            @Nullable
            @Override
            public Integer apply(@Nullable String input) {
                return Integer.valueOf(input);
            }
        }));

        boolean result = CollectionUtils.containsAny(orgList, orgIds);
        if (result) {
            log.info("routHeronSettleGray#true result:[{}]",JSON.toJSONString(result));
        }
        return result;
    }
}

package com.sankuai.meituan.waimai.customer.ddd.contract.domain.atom.service.impl;


import com.alibaba.fastjson.JSON;
import com.google.common.base.MoreObjects;
import com.sankuai.meituan.waimai.customer.contract.annotations.ServiceTag;
import com.sankuai.meituan.waimai.customer.contract.domain.WmContractVersionDB;
import com.sankuai.meituan.waimai.customer.ddd.contract.domain.aggre.WmContractAggre;
import com.sankuai.meituan.waimai.customer.ddd.contract.domain.aggre.WmContractSignAggre;
import com.sankuai.meituan.waimai.customer.ddd.contract.domain.atom.service.impl.check.ContractCheckFilter;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.util.ContractNumberUtil;
import com.sankuai.meituan.waimai.thrift.customer.constant.WmTempletContractTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerContractStatus;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyBizTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractBasicBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractSignBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.CustomerBlackWhiteBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractQuaRealLetterInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskApplyBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskConfigBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.util.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.UUID;

@Service
@ServiceTag(templetTypes = {WmTempletContractTypeEnum.QUA_REAL_LETTER_E})
public class WmQuaRealLetterEContractTempletAtomService extends AbstractWmEContractTempletAtomService {

    private static Logger logger = LoggerFactory.getLogger(WmQuaRealLetterEContractTempletAtomService.class);

    @Override
    EcontractTaskApplyBo buildEcontractTaskApplyBo(WmCustomerContractBo contractBo, WmContractVersionDB versionDB) throws WmCustomerException {

        EcontractTaskApplyBo applyBo = new EcontractTaskApplyBo();
        applyBo.setRecordId(versionDB.getVersion_number());
        applyBo.setBizId(String.valueOf(contractBo.getBasicBo().getParentId()));
        applyBo.setBizTypeEnum(EcontractTaskApplyBizTypeEnum.CUSTOMER_ID);
        applyBo.setApplyTypeEnum(EcontractTaskApplyTypeEnum.QUA_REAL_LETTER);

        EcontractQuaRealLetterInfoBo quaRealLetterInfoBo = new EcontractQuaRealLetterInfoBo();
        quaRealLetterInfoBo.setContractNum(contractBo.getBasicBo().getContractNum());

        WmTempletContractSignBo partyASignerBo = WmContractSignAggre.Factory.makeWithSignBo(contractBo.getSignBoList()).getPartyASignerBo();
        quaRealLetterInfoBo.setPartAName(partyASignerBo.getSignName());
        String signTime = partyASignerBo.getSignTime();
        if (StringUtils.isNotBlank(signTime)) {
            String[] signTimeArr = signTime.split("-");
            if (3 == signTimeArr.length) {
                quaRealLetterInfoBo.setSignTimeYear(signTimeArr[0]);
                quaRealLetterInfoBo.setSignTimeMonth(signTimeArr[1]);
                quaRealLetterInfoBo.setSignTimeDay(signTimeArr[2]);
            } else {
                assembleSignTime(quaRealLetterInfoBo);
            }
        } else {
            assembleSignTime(quaRealLetterInfoBo);
        }

        WmCustomerDB wmCustomerDB = wmCustomerService.selectCustomerById(contractBo.getBasicBo().getParentId());
        quaRealLetterInfoBo.setQuaName(wmCustomerDB.getCustomerName());
        quaRealLetterInfoBo.setQuaNumber(wmCustomerDB.getCustomerNumber());

        applyBo.setConfigBo(new EcontractTaskConfigBo());
        applyBo.setApplyInfoBo(JSON.toJSONString(quaRealLetterInfoBo));
        return applyBo;
    }

    private void assembleSignTime(EcontractQuaRealLetterInfoBo contractInfoBo) {
        String nowTime = DateUtil.secondsToString(DateUtil.unixTime());
        String[] nowTimeArr = nowTime.split("-");
        if (3 == nowTimeArr.length) {
            contractInfoBo.setSignTimeYear(nowTimeArr[0]);
            contractInfoBo.setSignTimeMonth(nowTimeArr[1]);
            contractInfoBo.setSignTimeDay(nowTimeArr[2]);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean effect(long templetContractId, int opUid, String opUname) throws WmCustomerException, TException {
        Boolean effect = super.effect(templetContractId, opUid, opUname);
        insertWhiteList(templetContractId, opUid, opUname);
        return effect;
    }

    private void insertWhiteList(long templetContractId, int opUid, String opUname) throws WmCustomerException, TException {
        Integer customerId = WmContractAggre.Factory.make(templetContractId).getBasicById(true, opUid, opUname).getParentId();
        CustomerBlackWhiteBo toSaveBo = new CustomerBlackWhiteBo();
        toSaveBo.setBizId(customerId.longValue())
                .setBizType(CustomerConstants.WHITE_LIST_BIZTYPE_QUA_REAL_LETTER)
                .setType(CustomerConstants.TYPE_WHITE).setOpUid(opUid);
        WmContractAggre.Factory.make(templetContractId).saveCustomerBlackWhiteList(toSaveBo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer save(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException, TException {
        contractBo.getBasicBo().setContractNum(UUID.randomUUID().toString());
        Integer insertId = super.save(contractBo, opUid, opName);
        String quaRealLetterENum = ContractNumberUtil.genQuaRealLetterENum(insertId);
        contractBo.getBasicBo().setContractNum(quaRealLetterENum);
        logger.info("生成合同编号  contractId:{}  contractNumber:{}", insertId, quaRealLetterENum);
        wmTempletContractDBMapper.updateNumberById(insertId.longValue(), quaRealLetterENum);
        return insertId;
    }

}

package com.sankuai.meituan.waimai.customer.service.sign.manualpack.strategy.precheck;

import com.sankuai.meituan.waimai.customer.contract.config.dto.WmManualSignTaskContext;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmContractService;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignManualTaskDB;
import com.sankuai.meituan.waimai.thrift.customer.constant.config.ContractSourceEnum;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: liuyunjie05
 * @create: 2024/6/21 19:34
 */
@Service
public class CommonConfigContractPreCheck implements PreCheck{

    @Resource
    private WmContractService wmContractService;

    @Override
    public void preCheck(String module, List<WmEcontractSignManualTaskDB> taskInfos, int commitUid) throws WmCustomerException, TException {
        List<Long> bizIdList = taskInfos.stream().map(WmEcontractSignManualTaskDB::getWmPoiId).collect(Collectors.toList());
        for (Long bizId : bizIdList) {
            WmManualSignTaskContext manualSignTaskContext = WmManualSignTaskContext.builder()
                    .customerId(taskInfos.get(0).getCustomerId())
                    .contractId(bizId)
                    .opUid(commitUid)
                    .opUname("")
                    .contractSource(ContractSourceEnum.CONFIG.getCode())
                    .build();
            wmContractService.startSignPreCheck(manualSignTaskContext);
        }
    }
}

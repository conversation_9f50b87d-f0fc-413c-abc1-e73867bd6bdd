package com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractSignVersionBo;
import java.util.List;
import java.util.Map;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.util.DateUtil;
import com.sankuai.meituan.waimai.customer.aspect.DataWrapper;
import com.sankuai.meituan.waimai.customer.service.sign.signTemplet.SignTempletManagerService;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.constants.PdfConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataWrapperEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractSignTempletConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.PdfTemplet;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.thrift.util.MapUtil;

/**
 * 配送信息生成pdf
 */
@Service
@Slf4j
@DataWrapper(wrapperEnum = EcontractDataWrapperEnum.DELIVERY)
public class WmEcontractDeliveryDataWrapperService implements IWmEcontractDataWrapperService {

    public static final String        SUPPORT_MARK                      = "support";

    private static final String       TEMPLET_NAME                      = "delivery_info_v3.ftl";
    private static final String       TEMPLET_BASE_NAME                 = "delivery_single_base_info";
    private static final String       TEMPLET_PREFERENTIAL_APPLY_NAME   = "delivery_preferential_apply_info";
    private static final String       TEMPLET_PERFORMANCE_GUARANCE_NAME = "delivery_performance_guarance_info";
    private static final String       TEMPLET_DELIVERY_SLA_OTHER_NAME   = "delivery_sla_other_info";

    private static final String       DEFAULT_TEMPLET_PREFERENTIAL_APPLY_NAME   = "delivery_preferential_apply_info_v2";
    private static final String       DEFAULT_TEMPLET_PERFORMANCE_GUARANCE_NAME = "delivery_performance_guarance_info_v2";

    @Autowired
    private SignTempletManagerService signTempletManagerService;

    @Override
    public List<PdfContentInfoBo> wrap(EcontractBatchContextBo contextBo) throws IllegalAccessException, WmCustomerException {
        EcontractTaskBo taskBo = WmEcontractContextUtil.selectByApplyType(contextBo, EcontractTaskApplyTypeEnum.POIFEE);

        EcontractDeliveryInfoBo deliveryInfoBo = JSON.parseObject(taskBo.getApplyContext(), EcontractDeliveryInfoBo.class);
        deliveryInfoBo.setDeliveryArea(null);//PDF数据无需配送范围信息
        Map<String, String> map = MapUtil.Object2Map(deliveryInfoBo);

        String signVersion = ConfigUtilAdapter.getString(EcontractSignTempletConstant.CURRENT_DELIVERY_SIGN_VERSION);
        //获取配送模块单店模板配置
        PdfTemplet deliveryPdfTemplet = getDefaultPdfTemplate();
        if(StringUtils.isNotEmpty(signVersion)){
            deliveryPdfTemplet = signTempletManagerService.getPdfTemplet(EcontractSignTempletConstant.BIZ_TYPE_DELIVERY, signVersion + "A");
            if(deliveryPdfTemplet == null){
                log.error("签约模板组装异常,taskBo={}",JSONObject.toJSONString(taskBo));
                deliveryPdfTemplet = getDefaultPdfTemplate();
            }else{
                EcontractSignVersionBo econtractSignVersionBo = contextBo
                        .getEcontractSignVersionBo();
                if(econtractSignVersionBo == null){
                    econtractSignVersionBo = new EcontractSignVersionBo();
                }
                econtractSignVersionBo.setDeliverySignVersion(signVersion + "A");
                contextBo.setEcontractSignVersionBo(econtractSignVersionBo);
            }
        }

        List<PdfContentInfoBo> result = Lists.newArrayList();
        //基本信息页
        PdfContentInfoBo pdfInfoBo = new PdfContentInfoBo();
        pdfInfoBo.setPdfTemplateName(deliveryPdfTemplet.getFeeInfoTemplet());
        pdfInfoBo.setPdfMetaContent(map);
        result.add(pdfInfoBo);

        if (SUPPORT_MARK.equals(deliveryInfoBo.getSupportSLA())) {
            result.add(genDefaultPdfTemplate(deliveryPdfTemplet.getSlaOtherInfoTemplet()));
        }

        if (SUPPORT_MARK.equals(deliveryInfoBo.getSupportExclusive())) {
            //战略合作优惠政策申请书
            result.add(genDefaultPdfTemplate(deliveryPdfTemplet.getPreferentialApplyInfoTemplet(), contextBo));
            //履约保证函
            result.add(genDefaultPdfTemplate(deliveryPdfTemplet.getPerformanceGuaranceInfoTemplet(), contextBo));
        }

        return result;
    }

    private PdfContentInfoBo genDefaultPdfTemplate(String templateName) {
        PdfContentInfoBo pdfInfoBo = new PdfContentInfoBo();
        pdfInfoBo.setPdfTemplateName(templateName);
        pdfInfoBo.setPdfMetaContent(Maps.<String, String> newHashMap());
        return pdfInfoBo;
    }

    /**
     * 默认需要商家签章
     * @param templateName
     * @param contextBo
     * @return
     */
    private PdfContentInfoBo genDefaultPdfTemplate(String templateName, EcontractBatchContextBo contextBo) {
        PdfContentInfoBo pdfInfoBo = new PdfContentInfoBo();
        pdfInfoBo.setPdfTemplateName(templateName);
        pdfInfoBo.setPdfMetaContent(generatePdfObject(contextBo));
        return pdfInfoBo;
    }

    private Map<String, String> generatePdfObject(EcontractBatchContextBo contextBo) {
        Map<String, String> pdfMap = Maps.newHashMap();
        String signTime = DateUtil.secondsToString(DateUtil.unixTime());
        pdfMap.put("partA", StringUtils.defaultIfEmpty(contextBo.getCustomerInfoBo().getCustomerName(), StringUtils.EMPTY));
        pdfMap.put("partAStampName", StringUtils.defaultIfEmpty(WmEcontractContextUtil.getCustomerName(contextBo), StringUtils.EMPTY));
        pdfMap.put("partAEstamp", PdfConstant.POI_SIGNKEY);
        pdfMap.put("partASignTime", StringUtils.defaultIfEmpty(signTime, StringUtils.EMPTY));
        return pdfMap;
    }

    private PdfTemplet getDefaultPdfTemplate() {
        return new PdfTemplet.Builder().feeInfoTemplet(TEMPLET_BASE_NAME).preferentialApplyInfoTemplet(DEFAULT_TEMPLET_PREFERENTIAL_APPLY_NAME)
                .performanceGuaranceInfoTemplet(DEFAULT_TEMPLET_PERFORMANCE_GUARANCE_NAME).slaOtherInfoTemplet(TEMPLET_DELIVERY_SLA_OTHER_NAME).build();
    }

}

package com.sankuai.meituan.waimai.customer.contract.frame;

import com.sankuai.meituan.banma.deliverycontract.admin.common.enums.CustomerTypeEnum;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmSubjectChangeSupplementEContractTempletService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.customer.util.SSOUtil;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerDeviceType;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerBasicBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.apache.thrift.TException;

import javax.annotation.Resource;

/**
 * @description: 合同类型（CooperateMode）多端校验
 * @author: zhang<PERSON>hao02
 * @create: 2024/8/5 19:14
 */
public abstract class CooperateModeAuthority {

     public abstract Boolean isCooperateModeAuthority(int cooperateMode, Long uid, Integer wmCustomerId, Integer businessGroupLine) throws TException, WmCustomerException;

     public static final String COOPERATE_MODE_AUTHORITY_FOR_PC = "cooperateModeAuthorityForPC";

     public static final String COOPERATE_MODE_AUTHORITY_FOR_APP = "cooperateModeAuthorityForAPP";

     @Resource
     private WmCustomerService wmCustomerService;

     @Resource
     private WmSubjectChangeSupplementEContractTempletService wmSubjectChangeSupplementEContractTempletService;

     public Boolean authSubjectChangeSupplement(Integer wmCustomerId) throws TException, WmCustomerException {
         WmCustomerBasicBo wmCustomerBasicBo = wmCustomerService.getCustomerById(wmCustomerId);
         return wmSubjectChangeSupplementEContractTempletService.checkSubjectChangeSupplementPermission(wmCustomerBasicBo);
     }

     public static String getBeanNameByDeviceType(CustomerDeviceType deviceType) {
        if (deviceType.equals(CustomerDeviceType.PC)) {
            return COOPERATE_MODE_AUTHORITY_FOR_PC;
        } else if (deviceType.equals(CustomerDeviceType.APP)) {
            return COOPERATE_MODE_AUTHORITY_FOR_APP;
        }

        return null;
     }
}

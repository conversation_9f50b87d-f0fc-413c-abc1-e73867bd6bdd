package com.sankuai.meituan.waimai.customer.service.kp.sensitive.decrypt;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.constant.KmsKeyNameEnum;
import com.sankuai.meituan.waimai.customer.service.kp.sensitive.kms.EncryptIdentifyIdServiceFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.security.GeneralSecurityException;

/**
 * 解密证件号
 *
 * <AUTHOR>
 * @date 2021/8/5
 */
@Service
@Slf4j
public class IdentifyIdDecryptHandle implements IDecryptHandle {

    @Autowired
    private EncryptIdentifyIdServiceFactory encryptIdentifyIdServiceFactory;

    @Override
    public KmsKeyNameEnum handleType() {
        return KmsKeyNameEnum.IDENTIFY_ID;
    }

    @Override
    public String execute(KeyDecrypt keyDecrypt) {
        log.debug("execute::keyDecrypt = {}", JSON.toJSONString(keyDecrypt));

        if (keyDecrypt == null || StringUtils.isEmpty(keyDecrypt.getValueForDecrypt())) {
            return "";
        }
        try {
            String identifyId = encryptIdentifyIdServiceFactory.getEncryptServer().decryptUTF8String(keyDecrypt.getValueForDecrypt());
            log.debug("证件号号解密execute::keyDecrypt = {},result={}", JSON.toJSONString(keyDecrypt), identifyId);
            return identifyId;
        } catch (GeneralSecurityException e) {
            log.error("execute::证件号解密异常keyDecrypt = {}", JSON.toJSONString(keyDecrypt), e);
            return "";
        }
    }
}

package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext;

import com.alibaba.fastjson.JSONObject;
import java.util.List;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.sankuai.meituan.waimai.customer.bo.sign.EcontractBatchMiddleBo;
import com.sankuai.meituan.waimai.customer.service.sign.applyecontract.WmEcontractDiffInfoWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.EcontractBatchBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageBatchInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataWrapperEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;

/**
 * 组装特殊交互
 */
@Service
@Slf4j
public class WmEcontractDataShowDiffCollector implements IWmEcontractDataCollector {

    @Autowired
    private WmEcontractDiffInfoWrapperService wmEcontractDiffInfoWrapperService;

    @Override
    public void collect(EcontractBatchContextBo originContext, EcontractBatchMiddleBo middleContext, EcontractBatchBo targetContext)
            throws WmCustomerException, IllegalAccessException {
        List<String> flowList = targetContext.getFlowList();
        log.info("#WmEcontractDataShowDiffCollector,flowList={}",JSONObject.toJSONString(flowList));
        List<StageBatchInfoBo> stageInfoBoList = targetContext.getStageInfoBoList();
        //仅结算单独签约时需要展示diff信息
        if (flowList.size() == 1 && flowList.get(0).equals(SignTemplateConstant.TAB_SETTLE)) {
            wmEcontractDiffInfoWrapperService.addContext(stageInfoBoList, originContext, EcontractDataWrapperEnum.SETTLE);
        }
    }
}

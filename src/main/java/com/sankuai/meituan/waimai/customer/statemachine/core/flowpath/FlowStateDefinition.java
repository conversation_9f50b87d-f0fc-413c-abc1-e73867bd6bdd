package com.sankuai.meituan.waimai.customer.statemachine.core.flowpath;

import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by jinh<PERSON> on 16/8/11.
 */
public class FlowStateDefinition {
    private String stateName;
    private String stateLabel;
    private Map<FlowStateDefinition, FlowConnectionDefinition> connections = new HashMap<FlowStateDefinition, FlowConnectionDefinition>();

    public FlowConnectionDefinition getFlowConnectionDefinition(FlowStateDefinition toStateDefinition) {
        return connections.get(toStateDefinition);
    }

    public String getStateName() {
        return stateName;
    }

    public void setStateName(String stateName) {
        this.stateName = stateName;
    }

    public String getStateLabel() {
        return StringUtils.isEmpty(stateLabel)?stateName:stateLabel;
    }

    public void setStateLabel(String stateLabel) {
        this.stateLabel = stateLabel;
    }

    public Map<FlowStateDefinition, FlowConnectionDefinition> getConnections() {
        return connections;
    }

    public void setConnections(Map<FlowStateDefinition, FlowConnectionDefinition> connections) {
        this.connections = connections;
    }
}

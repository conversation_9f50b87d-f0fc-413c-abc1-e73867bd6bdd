package com.sankuai.meituan.waimai.customer.settle.service;

import static com.sankuai.meituan.waimai.thrift.constants.WmPoiFieldQueryConstant.WM_POI_FIELD_IS_DELETE;
import static com.sankuai.meituan.waimai.thrift.constants.WmPoiFieldQueryConstant.WM_POI_FIELD_WM_POI_ID;

import com.sankuai.meituan.waimai.customer.util.ListUtil;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sankuai.meituan.waimai.customer.adapter.WmPoiClient;
import com.sankuai.meituan.waimai.customer.bo.WmPoiDomain;
import com.sankuai.meituan.waimai.customer.service.common.WmCustomerTairService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiService;
import com.sankuai.meituan.waimai.customer.settle.constant.WmSettlePushDaXiangConstant;
import com.sankuai.meituan.waimai.customer.settle.dao.WmPoiSettleAuditedDBMapper;
import com.sankuai.meituan.waimai.customer.settle.dao.WmPoiSettleDBMapper;
import com.sankuai.meituan.waimai.customer.settle.dao.WmSettleAuditedDBMapper;
import com.sankuai.meituan.waimai.customer.settle.dao.WmSettleDBMapper;
import com.sankuai.meituan.waimai.customer.util.ObjectUtil;
import com.sankuai.meituan.waimai.customer.util.SetUtil;
import com.sankuai.meituan.waimai.infra.constants.WmVirtualOrgSourceEnum;
import com.sankuai.meituan.waimai.mtauth.util.WmEmployUtils;
import com.sankuai.meituan.waimai.poimanager.constant.WmPhoneLimitConstant;
import com.sankuai.meituan.waimai.poimanager.service.WmPoiManagerThriftService;
import com.sankuai.meituan.waimai.thrift.constant.WmContractConstant;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.customer.domain.settle.BatchUnbindOfflinePoiErrorBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettle;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.util.BeanDiffUtil;

@Service
public class WmSettleCheckService {
    private static final Logger LOGGER = LoggerFactory.getLogger(WmSettleCheckService.class);

    private final int DEFAULT_BATCHUNBINDOFFLINEPOI_SIZE = 500;

    @Autowired
    private WmSettleDBMapper wmSettleDBMapper;
    @Autowired
    private WmSettleAuditedDBMapper wmSettleAuditedDBMapper;
    @Autowired
    private WmPoiSettleDBMapper wmPoiSettleDBMapper;
    @Autowired
    private WmPoiSettleAuditedDBMapper wmPoiSettleAuditedDBMapper;
    @Autowired
    private WmPoiManagerThriftService.Iface wmPoiManagerThriftService;
    @Autowired
    private WmSettlePushDaXiangService wmSettlePushDaXiangService;
    @Autowired
    private WmCustomerPoiService wmCustomerPoiService;
    @Autowired
    private WmCustomerTairService wmCustomerTairService;
    @Autowired
    private WmPoiClient wmPoiClient;
    @Autowired
    private WmSettleSwitchService wmSettleSwitchService;

    /**
     * 根据结算名称校验
     */
    public boolean checkWmSettleByName(WmSettle wmSettle) {

        if(ConfigUtilAdapter.getBoolean("close_settle_name_check",true)){
            return true;
        }

        if (wmSettle == null || StringUtils.isEmpty(wmSettle.getName())) {
            return true;
        }

        //校验结算线下表是否重名
        if (!checkSettleName(wmSettle)) {
            return false;
        }
        //校验结算线上表是否重名
        return checkSettleAuditedName(wmSettle);
    }

    /**
     * 根据结算名称校验
     */
    public boolean checkWmSettleByName(List<WmSettle> wmSettleList) {
        for (WmSettle wmSettle : wmSettleList) {
            if (!checkWmSettleByName(wmSettle)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 大客户BD才可以选择：1：固定日期结算，2：固定月末结算
     */
    public boolean checkSettleType(List<WmSettle> wmSettleList, int opUid, boolean isBrandBD, boolean isAutoRenew) throws WmCustomerException {
        if (CollectionUtils.isEmpty(wmSettleList)) {
            return true;
        }

        // 判断是否是总部用户
//        boolean isHQ = isAutoRenew || WmEmployUtils.isHQ(opUid);

        // 覆盖输入的isBrandBD
        isBrandBD = isAutoRenew || WmEmployUtils.isUserInOrg(opUid, WmVirtualOrgSourceEnum.DAKEHU.getSource());

        for (WmSettle wmSettle : wmSettleList) {
            if (!isBrandBD && (wmSettle.getSettle_type() == WmContractConstant.SETTLETYPE_DATE || wmSettle.getSettle_type() == WmContractConstant.SETTLETYPE_LASTDAY)) {
                throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "大客户BD才可选择 1.固定日期结算 2.固定月末结算");
            }
            if (wmSettle.getSettle_type() == WmContractConstant.SETTLETYPE_CIRCLE && !checkSettleTypeCircle(wmSettle)) {
                throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "周期结算必须选择结算周期");
            }
            if (wmSettle.getSettle_type() == WmContractConstant.SETTLETYPE_DATE && (wmSettle.getPay_day_of_month() > 28 || wmSettle.getPay_day_of_month() < 1)) {
                throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "固定日期结算只能选择1~28日");
            }
            if (wmSettle.getSettle_type() == WmContractConstant.SETTLETYPE_SELF && wmSettle.getWmPoiIdList().size() > 1) {
                throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "多个门店不允许设置商家自提");
            }
        }
        return true;
    }


    /**
     * 校验手机号不能为美团电话
     */
    public void checkPhoneNum(List<String> phoneNums) throws WmCustomerException {
        LOGGER.info("#checkPhoneNum#phoneNums={}", JSONObject.toJSONString(phoneNums));
        Map<Integer, List<String>> mtPhoneMap = Maps.newHashMap();
        try {
            mtPhoneMap = wmPoiManagerThriftService.hasMtPhone(phoneNums);
        } catch (WmServerException | TException e) {
            LOGGER.error("调用wmPoiManager服务化异常", e);
        }
        LOGGER.info("#checkPhoneNum#mtPhoneMap={}", JSONObject.toJSONString(mtPhoneMap));
        List<String> mtEmployPhones = mtPhoneMap.get(WmPhoneLimitConstant.TYPE_MT_EMPLOY_PHONE); // 1.为美团员工列表且不在白名单里的电话
        List<String> mtServicePhones = mtPhoneMap.get(WmPhoneLimitConstant.TYPE_MT_SERVICE_PHONE); // 2.为美团电话且不在白名单里的电话

        if (CollectionUtils.isNotEmpty(mtEmployPhones)
                && CollectionUtils.isNotEmpty(mtServicePhones)) {
            throw new WmCustomerException(WmContractErrorCodeConstant.INPUT_ERROR, "联系人电话不能为美团员工电话("
                    + Joiner.on(",").join(mtEmployPhones) + ")或美团电话("
                    + Joiner.on(",").join(mtServicePhones) + ")");
        } else if (CollectionUtils.isNotEmpty(mtEmployPhones)) {
            throw new WmCustomerException(WmContractErrorCodeConstant.INPUT_ERROR, "联系人电话不能为美团员工电话("
                    + Joiner.on(",").join(mtEmployPhones) + ")");
        } else if (CollectionUtils.isNotEmpty(mtServicePhones)) {
            throw new WmCustomerException(WmContractErrorCodeConstant.INPUT_ERROR, "联系人电话不能为美团电话("
                    + Joiner.on(",").join(mtServicePhones) + ")");
        }
    }


//    public boolean checkWmPoiIdListInSettleAndPushDX(int wmCustomerId,
//                                                     int wmSettleId,
//                                                     List<Integer> wmPoiIdList,
//                                                     WmSettlePushDaXiangConstant.errorType errorType,
//                                                     WmSettlePushDaXiangConstant.errorModule errorModule,
//                                                     int opUid,
//                                                     boolean isEffectContract) throws WmCustomerException {
//        List<Integer> wmPoiIdForSettleList;
//        if (!isEffectContract) {
//            wmPoiIdForSettleList = wmPoiSettleDBMapper.getWmPoiIdListBySettleId(wmSettleId);
//        } else {
//            wmPoiIdForSettleList = wmPoiSettleAuditedDBMapper.getPoiIdsBySettleId(wmSettleId);
//        }
//        return checkWmPoiIdListInSettleAndPushDX(wmCustomerId, wmPoiIdList, errorType, errorModule, opUid, wmPoiIdForSettleList);
//    }

    public boolean checkWmPoiIdListInSettleAndPushDX(int wmCustomerId,
                                                     List<Integer> wmPoiIdList,
                                                     WmSettlePushDaXiangConstant.errorType errorType,
                                                     WmSettlePushDaXiangConstant.errorModule errorModule,
                                                     int opUid,
                                                     List<Integer> wmPoiCheckList) throws WmCustomerException {

        List<Integer> commonList = BeanDiffUtil.genCommonList(wmPoiIdList, wmPoiCheckList);
        if (CollectionUtils.isEmpty(commonList)) {
            return true;
        }
        //发大象
        wmSettlePushDaXiangService.pushDaXiangForErrorMsg(wmCustomerId,
                errorType,
                errorModule,
                WmSettlePushDaXiangConstant.errorReason.OFFLINE_SETTLE_REL,
                opUid, commonList);
        //抛异常
        throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "存在门店已经关联其他结算,详细信息请看大象通知");
    }

    /**
     * 周期结算校验
     *
     * @param wmSettle 结算
     * @return true:校验通过 false:校验失败
     */
    private boolean checkSettleTypeCircle(WmSettle wmSettle) throws WmCustomerException {
        if (wmSettle == null) {
            return false;
        }

        if ((wmSettle.getPay_period_num() == 3 && wmSettle.getPay_period_unit() == 3)//3天
            || (wmSettle.getPay_period_num() == 1 && wmSettle.getPay_period_unit() == 1)//1周
            || (wmSettle.getPay_period_num() == 2 && wmSettle.getPay_period_unit() == 1)//2周
            || (wmSettle.getPay_period_num() == 1 && wmSettle.getPay_period_unit() == 2)) {//1月
            return true;
        }
        if (ConfigUtilAdapter.getBoolean("settle_check_white_list_open", true)
            && wmSettle.getPay_period_num() == 1 && wmSettle.getPay_period_unit() == 3) {
            wmCustomerTairService.checkSettleWhiteList(wmSettle);
            return true;
        }
        return false;
    }


    /**
     * 校验结算名不与其他线下结算名重复
     */
    private boolean checkSettleName(WmSettle wmSettle) {
        if (wmSettle == null || StringUtils.isEmpty(wmSettle.getName())) {
            return true;
        }

        List<Integer> wmSettleIdList = wmSettleDBMapper.getWmSettleIdListBySettleName(wmSettle.getName());
        if (CollectionUtils.isEmpty(wmSettleIdList)) {
            return true;
        }
        return wmSettleIdList.contains(wmSettle.getId());
    }

    /**
     * 校验结算名不与其他线上结算名重复
     */
    private boolean checkSettleAuditedName(WmSettle wmSettle) {
        if (wmSettle == null || StringUtils.isEmpty(wmSettle.getName())) {
            return true;
        }

        List<Integer> wmSettleIdList =
                wmSettleIdList = wmSettleAuditedDBMapper.getWmSettleIdListBySettleName(wmSettle.getName());
        if (CollectionUtils.isEmpty(wmSettleIdList)) {
            return true;
        }
        return wmSettleIdList.contains(wmSettle.getId());
    }

    public boolean checkInputWmPoiIdSize(List<Integer> wmPoiIdList) throws WmCustomerException {
        if (CollectionUtils.isEmpty(wmPoiIdList)) {
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "商家数量不能为空");
        }
        if (wmPoiIdList.size() > 50) {
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "商家数量达到上限");
        }
        return true;
    }

    public boolean checkWmPoiIdListInWmCustomerPoiAndPushDXWithEffect(int wmCustomerId, List<Integer> wmPoiIdList, int opUid,
                                                                      WmSettlePushDaXiangConstant.errorType opType,
                                                                      WmSettlePushDaXiangConstant.errorModule module, boolean isEffective)
            throws WmCustomerException {
        List<Integer> storeList = Lists.newArrayList();
        storeList = ObjectUtil.longList2IntList(wmCustomerPoiService.selectWmPoiIdsByCustomerId(wmCustomerId));
        List<Long> switchingWmPoiIdList = wmSettleSwitchService.getSwitchingWmPoiIdList(wmCustomerId,true);
        storeList.addAll(ObjectUtil.longList2IntList(switchingWmPoiIdList));

        LOGGER.info("#checkWmPoiIdListInWmCustomerPoiAndPushDXWithEffect,storeList={},switchingWmPoiIdList={}",storeList,switchingWmPoiIdList);

        if (!storeList.containsAll(wmPoiIdList)) {
            //产生不在store范围内的门店
            List<Integer> removeList = BeanDiffUtil.genDeleteList(storeList, wmPoiIdList);
            //发大象
            wmSettlePushDaXiangService.pushDaXiangForErrorMsg(wmCustomerId, opType, module, WmSettlePushDaXiangConstant.errorReason.CUSTOMER, opUid,
                    removeList);
            //抛异常
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "存在非本客户关联商家,详细信息请看大象通知");
        }
        return true;
    }

    /**
     * 查询结算是否是钱包
     * @throws WmServerException
     */
    public boolean checkSettleIsWalletOrNot(List<WmSettle> wmSettleList, Integer wmContractId) {
        if (CollectionUtils.isEmpty(wmSettleList)) {
            return true;
        }

        int walletNum = 0;
        for (WmSettle wmSettle : wmSettleList) {
            if (wmSettle.getCard_type() == WmContractConstant.CardType.WALLET.getIndex()) {
                walletNum++;
            }
        }

        //无钱包
        if (walletNum == 0) {
            return true;
        }

        //不都是钱包
        if (walletNum != wmSettleList.size()) {
            return false;
        }

        return true;
    }

    public Set<Long> checkBatchUnbindOfflinePoi(int wmCustomerId, String wmPoiIds,Set<Long> settleOfflineWmPoiSet) throws WmCustomerException{
        //0.门店id格式正确
        //1.门店不超过500个
        //2.门店id是否有效-正确的门店ID
        //3.门店id属于该客户+允许操作物理删除的
        String[] wmPoiIdArray = wmPoiIds.split(",");
        Set<Long> wmPoiIdSet = Sets.newHashSet();
        Long wmPoiId = null;

        try{
            for(String temp : wmPoiIdArray){
                wmPoiId = Long.parseLong(temp);
                if(wmPoiId<=0){
                    throw new WmCustomerException(WmContractErrorCodeConstant.INPUT_ERROR,"执行失败,请按照正确格式输入");
                }
                wmPoiIdSet.add(wmPoiId);
            }
        }catch(NumberFormatException e){
            LOGGER.warn("数据转换异常",e);
            throw new WmCustomerException(WmContractErrorCodeConstant.INPUT_ERROR,"执行失败,请按照正确格式输入");
        }

        if (wmPoiIdSet.size() > DEFAULT_BATCHUNBINDOFFLINEPOI_SIZE) {
            throw new WmCustomerException(WmContractErrorCodeConstant.INPUT_ERROR,"执行失败,最多可录入500个门店");
        }

        List<WmPoiDomain> wmPoiDomains = wmPoiClient
                .pageGetWmPoiByWmPoiIdList(Lists.newArrayList(wmPoiIdSet), ImmutableSet.of(
                        WM_POI_FIELD_WM_POI_ID,
                        WM_POI_FIELD_IS_DELETE));
        Map<Long,Byte> wmPoiIsDeleteMap = Maps.newHashMap();
        for(WmPoiDomain temp : wmPoiDomains){
            wmPoiIsDeleteMap.put((long)temp.getWmPoiId(),temp.getIsDelete());
        }

        //不存在的门店
        Set<Long> invalidPoiSet = SetUtil.gendeleteSet(wmPoiIsDeleteMap.keySet(), wmPoiIdSet);
        //存在的门店
        Set<Long> validPoiSet = SetUtil.genCommonSet(wmPoiIsDeleteMap.keySet(),wmPoiIdSet);

        //存在的门店是否属于该结算
        Set<Long> notBelongToOfflineSettle = SetUtil.gendeleteSet(settleOfflineWmPoiSet,validPoiSet);

        BatchUnbindOfflinePoiErrorBo errorBo = null;

        if(CollectionUtils.isNotEmpty(invalidPoiSet)){
            if(errorBo == null){
                errorBo = new BatchUnbindOfflinePoiErrorBo();
            }
            errorBo.setInputError(Lists.newArrayList(invalidPoiSet));
        }

        if(CollectionUtils.isNotEmpty(notBelongToOfflineSettle)){
            if(errorBo == null){
                errorBo = new BatchUnbindOfflinePoiErrorBo();
            }
            errorBo.setNoPermission(Lists.newArrayList(notBelongToOfflineSettle));
        }

        if (errorBo != null) {
            throw new WmCustomerException(WmContractErrorCodeConstant.NO_PERMISSION_ERROR,
                    JSONObject.toJSONString(errorBo));
        }

        if(CollectionUtils.isEmpty(wmPoiIdSet)){
            throw new WmCustomerException(WmContractErrorCodeConstant.INPUT_ERROR,"执行失败,请按照正确格式输入");
        }

        List<Long> switchingWmPoiIdListFromOldWmCustomerId = wmSettleSwitchService.getSwitchingWmPoiIdListFromOldWmCustomerId(wmCustomerId);
        List<Long> commonList = ListUtil.genCommonList(switchingWmPoiIdListFromOldWmCustomerId,
                Lists.newArrayList(wmPoiIdSet));
        if (!org.springframework.util.CollectionUtils.isEmpty(commonList)) {
            String errorMsg = "提交失败;门店id" + Joiner.on(",").join(wmPoiIdSet) + "即将切换至其他客户，结算信息无法修改。如需修改，请先取消切换客户任务。";
            throw new WmCustomerException(WmContractErrorCodeConstant.INPUT_ERROR, errorMsg);
        }
        return wmPoiIdSet;
    }
}

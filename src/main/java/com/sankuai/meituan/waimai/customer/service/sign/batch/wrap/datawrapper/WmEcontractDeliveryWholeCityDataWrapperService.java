package com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.aspect.DataWrapper;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataWrapperEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryWholeCityInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.util.MapUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * 配送全城送信息生成pdf
 */
@Service
@Slf4j
@DataWrapper(wrapperEnum = EcontractDataWrapperEnum.DELIVERY_WHOLE_CITY)
public class WmEcontractDeliveryWholeCityDataWrapperService implements IWmEcontractDataWrapperService {

    private String WHOLE_CITE_TEMPLATE_NAME = "delivery_whole_city_info";

    @Override
    public List<PdfContentInfoBo> wrap(EcontractBatchContextBo contextBo) throws IllegalAccessException, WmCustomerException {
        EcontractTaskBo taskBo = WmEcontractContextUtil.selectByApplyType(contextBo, EcontractTaskApplyTypeEnum.POIFEE);
        EcontractDeliveryInfoBo deliveryInfoBo = JSON.parseObject(taskBo.getApplyContext(), EcontractDeliveryInfoBo.class);
        EcontractDeliveryWholeCityInfoBo econtractDeliveryWholeCityInfoBo = deliveryInfoBo
                .getEcontractDeliveryWholeCityInfoBo();
        if(econtractDeliveryWholeCityInfoBo == null){
            return Lists.newArrayList();
        }
        econtractDeliveryWholeCityInfoBo.setDeliveryArea(null);//PDF数据无需配送范围信息
        Map<String, String> map = MapUtil.Object2Map(econtractDeliveryWholeCityInfoBo);
        List<PdfContentInfoBo> result = Lists.newArrayList();
        //基本信息页
        PdfContentInfoBo pdfInfoBo = new PdfContentInfoBo();
        pdfInfoBo.setPdfTemplateName(WHOLE_CITE_TEMPLATE_NAME);
        pdfInfoBo.setPdfMetaContent(map);
        result.add(pdfInfoBo);
        return result;
    }

}

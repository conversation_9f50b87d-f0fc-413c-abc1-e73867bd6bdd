package com.sankuai.meituan.waimai.customer.service.kp.dboperator;

import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKpTemp;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;

import java.util.List;

/**
 * <AUTHOR>
 */
@Deprecated
public interface KpDBOperate {

    /**
     * 对KP信息进行数据持久化操作
     *
     * @param wmCustomer          客户基础信息对象
     * @param oldWmCustomerKpList DB中现有的KP列表
     * @param addKpList           待添加的KP列表
     * @param deleteKpList        待删除的KP列表
     * @param upgradeKpList       待更新的KP列表
     * @return DBOperateResult 数据库操作结果
     */
    DBOperateResult operate(WmCustomerDB wmCustomer, List<WmCustomerKp> oldWmCustomerKpList, List<WmCustomerKp> addKpList, List<WmCustomerKp> deleteKpList, List<WmCustomerKp> upgradeKpList, int uid, String uname) throws WmCustomerException;

    /**
     * 使签约人变更KP数据生效
     * @param kpTempDB
     * @param oldCustomerKp
     */
    void tempKpEffect(WmCustomerKpTemp kpTempDB, WmCustomerKp oldCustomerKp);

}

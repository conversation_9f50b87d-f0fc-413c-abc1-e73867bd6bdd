package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant;

import java.lang.annotation.ElementType;
import java.lang.annotation.Inherited;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataWrapperEnum;

@Inherited
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface SignTemplateWrapper {

    SignTemplateEnum wrapperEnum();

}

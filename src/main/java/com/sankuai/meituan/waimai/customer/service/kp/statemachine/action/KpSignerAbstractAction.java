package com.sankuai.meituan.waimai.customer.service.kp.statemachine.action;

import com.sankuai.meituan.waimai.customer.service.kp.statemachine.context.KpSignerStatusMachineContext;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.constant.event.KpSignerEventEnum;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.machine.KpSignerBaseSM;
import com.sankuai.meituan.waimai.thrift.customer.constant.KpSignerStateMachine;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.squirrelframework.foundation.fsm.AnonymousAction;

@Component
@Slf4j
public abstract class KpSignerAbstractAction extends AnonymousAction<KpSignerBaseSM, KpSignerStateMachine, KpSignerEventEnum, KpSignerStatusMachineContext> {

}

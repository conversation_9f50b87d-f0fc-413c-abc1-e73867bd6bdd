package com.sankuai.meituan.waimai.customer.adapter;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.poisearch.thrift.execption.WmPoiSearchException;
import com.sankuai.meituan.waimai.poisearch.thrift.service.WmPoiScrollQueryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/11/23
 */
@Slf4j
@Service
public class WmPoiSearchAdapter {

    @Resource
    private WmPoiScrollQueryService wmPoiScrollQueryService;


    /**
     * 查询门店信息
     *
     * @param wmPoiIdList  外卖门店ID集合
     * @param onLineStatus 门店上线状态
     * @return
     */
    public List<Long> searchPoi(List<Long> wmPoiIdList, Integer onLineStatus) {
        log.info("searchPoiViaEs::wmPoiIdList = {},onLineStatus={}", JSON.toJSONString(wmPoiIdList), onLineStatus);
        try {
            if (CollectionUtils.isEmpty(wmPoiIdList)) {
                return Lists.newArrayList();
            }
            //调用ES查询门店
            StringBuffer sb = new StringBuffer("select wmPoiId from poi.poi_index_rw where ");
            String valueStr = StringUtils.join(wmPoiIdList, ",");
            sb.append(" wmPoiId in (").append(valueStr).append(")");
            if (onLineStatus != null) {
                sb.append(" and valid =").append(onLineStatus);
            }
           // sb.append(" order by wmPoiId ASC ");
            List<Long> result = wmPoiScrollQueryService.getWmPoiIdsBySql(sb.toString());
            log.info("searchPoiViaEs::result={}", JSON.toJSONString(result));
            return result;
        } catch (WmPoiSearchException e) {
            log.error("搜索商家信息时发生异常，wmPoiIdList = {},onLineStatus={}, e.code = {}, e.msg = {}", JSON.toJSONString(wmPoiIdList), onLineStatus, e.code, e.msg, e);
        } catch (TException e) {
            log.error("搜索商家信息时发生异常，wmPoiIdList = {},onLineStatus={}, e.msg = {}", JSON.toJSONString(wmPoiIdList), onLineStatus, e.getMessage(), e);
        }
        return Lists.newArrayList();
    }

}

package com.sankuai.meituan.waimai.customer.service.sign.biz;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiService;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyBizTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchDeliveryInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Service
public class WmEcontractTaskPoiService {

    @Resource
    private WmCustomerPoiService wmCustomerPoiService;

    private static Map<String, String> applyTypeAndApplyBizTypeMap = Maps.newHashMap();

    static {
        applyTypeAndApplyBizTypeMap.put(EcontractTaskApplyTypeEnum.C1CONTRACT.getName(),
                                        EcontractTaskApplyBizTypeEnum.CUSTOMER_ID.getName());
        applyTypeAndApplyBizTypeMap.put(EcontractTaskApplyTypeEnum.C2CONTRACT.getName(),
                                        EcontractTaskApplyBizTypeEnum.CUSTOMER_ID.getName());
        applyTypeAndApplyBizTypeMap.put(EcontractTaskApplyTypeEnum.SETTLE.getName(),
                                        EcontractTaskApplyBizTypeEnum.CUSTOMER_ID.getName());
        applyTypeAndApplyBizTypeMap.put(EcontractTaskApplyTypeEnum.POIFEE.getName(),
                                        EcontractTaskApplyBizTypeEnum.WM_POI_ID.getName());
    }

    /**
     * 查找任务关联门店
     */
    public List<Long> relateWmPoiIdList(EcontractTaskBo taskBo) {
        //客户ID
        if (EcontractTaskApplyBizTypeEnum.CUSTOMER_ID.getName()
            .equals(toApplyBizType(taskBo.getApplyType()))) {
            return wmCustomerPoiService.selectWmPoiIdsByCustomerIdFromDBAndSwitchCentre(taskBo.getCustomerId());
        } else if (EcontractTaskApplyBizTypeEnum.WM_POI_ID.getName()
            .equals(toApplyBizType(taskBo.getApplyType()))) {
            return Lists.newArrayList(Long.valueOf(taskBo.getBizId()));
        }

        //处理批量配送信息
        if(taskBo.getApplyType().equals(EcontractTaskApplyTypeEnum.BATCHPOIFEE.getName())){
            EcontractBatchDeliveryInfoBo batchDeliveryInfoBo = JSONObject
                .parseObject(taskBo.getApplyContext(), EcontractBatchDeliveryInfoBo.class);
            return Lists.newArrayList(batchDeliveryInfoBo.getWmPoiIdList());
        }
        return Lists.newArrayList();
    }

    private String toApplyBizType(String applyType) {
        return applyTypeAndApplyBizTypeMap.get(applyType);
    }

}

package com.sankuai.meituan.waimai.customer.service.kp.statemachine.action.signer;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerKpAuditMapper;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerKpDBMapper;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerKpTempDBMapper;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerKpAudit;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerSensitiveWordsService;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpAuditService;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpBuryingPointService;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpLogService;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.action.KpSignerAbstractAction;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.constant.event.KpSignerEventEnum;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.context.KpSignerStatusMachineContext;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.machine.KpSignerBaseSM;
import com.sankuai.meituan.waimai.thrift.customer.constant.KpAuditConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.KpSignerStateMachine;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.CertNumberModifyEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKpTemp;
import com.sankuai.meituan.waimai.thrift.exception.StatusMachineException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.sankuai.meituan.waimai.thrift.customer.constant.KpConstants.UN_EFFECTIVE;
import static com.sankuai.meituan.waimai.thrift.customer.constant.KpConstants.UN_VALID;

/**
 * <AUTHOR>
 * @date 20240422
 * @desc 授权失败事件
 */
@Service
@Slf4j
public class AuthFailAction extends KpSignerAbstractAction {

    @Autowired
    private WmCustomerKpDBMapper wmCustomerKpDBMapper;

    @Autowired
    private WmCustomerSensitiveWordsService wmCustomerSensitiveWordsService;

    @Autowired
    private WmCustomerKpLogService wmCustomerKpLogService;

    @Autowired
    private WmCustomerKpAuditService wmCustomerKpAuditService;

    @Autowired
    private WmCustomerKpTempDBMapper wmCustomerKpTempDBMapper;

    @Autowired
    private WmCustomerKpAuditMapper wmCustomerKpAuditMapper;

    @Autowired
    private WmCustomerKpBuryingPointService wmCustomerKpBuryingPointService;

    /**
     * KP签约人提审事件
     *
     * @param from
     * @param to
     * @param eventEnum
     * @param context
     * @param kpSignerBaseSM
     */
    @Override
    public void execute(KpSignerStateMachine from, KpSignerStateMachine to, KpSignerEventEnum eventEnum,
                        KpSignerStatusMachineContext context, KpSignerBaseSM kpSignerBaseSM) {
        log.info("AuthFailAction,授权失败action开始执行,from={},to={},context={}", from, to, JSON.toJSONString(context));

        try {
            WmCustomerKpAudit wmCustomerKpAudit = context.getWmCustomerKpAudit();
            WmCustomerKp customerKp = context.getWmCustomerKp();
            //未生效过
            if (!context.getExistEffectiveFlag()) {
                customerKp.setEffective(UN_EFFECTIVE);
                customerKp.setState(KpSignerStateMachine.CHANGE_ORIGIN_SIGNER_CANCEL.getState());
                customerKp.setFailReason("取消授权");

                //更新审核表
                wmCustomerKpAudit.setResult("取消授权");
                wmCustomerKpAudit.setValid(UN_VALID);
                wmCustomerKpAuditMapper.updateByPrimaryKey(wmCustomerKpAudit);
                //更新签约人KP表
                wmCustomerSensitiveWordsService.writeKpWhenInsertOrUpdate(customerKp);
                wmCustomerKpDBMapper.updateByPrimaryKey(customerKp);
                wmCustomerKpLogService.changeState(customerKp.getCustomerId(), customerKp, "取消授权", 0, "法人短信授权");
                log.info("dealSignerKpLegalAuthResult,新增签约人的短信授权结果处理完成，customerKp={},wmCustomerKpAudit={}", JSON.toJSONString(customerKp), JSON.toJSONString(wmCustomerKpAudit));
                //记录埋点信息
                wmCustomerKpBuryingPointService.afterSingKp(null, wmCustomerKpAudit.getAcctId(),
                        null, customerKp);
            } else {
                WmCustomerKpTemp kpTemp = context.getTempKp();
                WmCustomerKp newCustomerKp = wmCustomerKpAuditService.transformWmCustomerKpTemp(kpTemp);
                newCustomerKp.setEffective(UN_EFFECTIVE);
                //取消授权
                kpTemp.setState(KpSignerStateMachine.CHANGE_ORIGIN_SIGNER_CANCEL.getState());
                kpTemp.setFailReason("原签约人取消授权");
                String stateMsg = "变更授权取消";
                //重置法人授权的状态说明
                if (wmCustomerKpAudit.getType() == KpAuditConstants.TYPE_AUTH_FOR_SIGNER_AGENT_LEGAL) {
                    kpTemp.setFailReason("法人授权取消授权");
                    stateMsg = "法人授权取消";
                }
                wmCustomerKpDBMapper.updateCertNumberModify(CertNumberModifyEnum.YES.getCode(), customerKp.getId());

                //更新审核表
                wmCustomerKpAudit.setResult(stateMsg);
                wmCustomerKpAudit.setValid(UN_VALID);
                wmCustomerKpAuditMapper.updateByPrimaryKey(wmCustomerKpAudit);
                //更新临时签约人数据
                wmCustomerSensitiveWordsService.writeKpSourceWhenUpdate(kpTemp);
                wmCustomerKpTempDBMapper.updateByPrimaryKey(kpTemp);
                //添加操作日志
                wmCustomerKpLogService.changeState(customerKp.getCustomerId(), customerKp, stateMsg, 0, "短信授权");

                wmCustomerKpBuryingPointService.afterSingKp(null, wmCustomerKpAudit.getAcctId(), newCustomerKp, customerKp);
            }
        } catch (Exception e) {
            log.error("AuthFailAction.execute,KP签约人授权失败操作异常,context={}", JSON.toJSONString(context), e);
            throw new StatusMachineException("KP签约人授权失败操作异常");
        }


    }
}

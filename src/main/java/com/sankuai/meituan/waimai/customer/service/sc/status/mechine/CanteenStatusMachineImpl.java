package com.sankuai.meituan.waimai.customer.service.sc.status.mechine;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.customer.domain.sc.WmCanteenDB;
import com.sankuai.meituan.waimai.customer.domain.sc.WmScCanteenAuditDO;
import com.sankuai.meituan.waimai.customer.service.sc.status.CanteenStatusMachine;
import com.sankuai.meituan.waimai.customer.service.sc.status.CanteenStatusMachineFactory;
import com.sankuai.meituan.waimai.customer.service.sc.status.CanteenStatusSubMachine;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.CanteenAuditStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.CanteenDetailStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.ScConstants;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.CanteenAuditDiffBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.DiffField;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmScTicketNoticeBo;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.CanteenDetailStatusEnum.INSERT_STATUS;
import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants.NOT_EDIT_AUDITING_STATUS_ERROR;

/**
 * <AUTHOR>
 * @date 2020/11/24 17:30
 */
@Component
@Slf4j
public class CanteenStatusMachineImpl implements CanteenStatusMachine {

    @Autowired
    private CanteenStatusMachineFactory canteenStatusMachineFactory;

    /**
     * 食堂信息新建流转
     * @param canteenDB 食堂主表对象
     * @param wmScCanteenAuditDO 食堂审核表对象
     * @throws WmSchCantException 无法流转的状态会抛出异常
     */
    @Override
    public void ticket(WmCanteenDB canteenDB, WmScCanteenAuditDO wmScCanteenAuditDO) throws WmSchCantException {
        log.info("[CanteenStatusMachineImpl.ticket] input param: canteenDB = {}, wmScCanteenAuditDO = {}",
                JSONObject.toJSONString(canteenDB), JSONObject.toJSONString(wmScCanteenAuditDO));
        if (canteenDB.getAuditDetailStatus() == null) {
            canteenDB.setAuditDetailStatus((int) INSERT_STATUS.getType());
        }

        if (isAuditing(canteenDB)) {
            throw new WmSchCantException(NOT_EDIT_AUDITING_STATUS_ERROR, "食堂信息未生效或在审批中，不可保存");
        }

        CanteenDetailStatusEnum startStatus = CanteenDetailStatusEnum.getByType(canteenDB.getAuditDetailStatus());
        CanteenStatusSubMachine canteenStatusSubMachine = canteenStatusMachineFactory.getCanteenStatusSubMachine(canteenDB);
        canteenStatusSubMachine.changeStatus(canteenDB, wmScCanteenAuditDO);
        CanteenDetailStatusEnum endStatus = CanteenDetailStatusEnum.getByType(canteenDB.getAuditDetailStatus());
        log.info("[CanteenStatusMachineImpl.ticket] changeStatus. canteenDB = {}, startStatus = {}, endStatus = {}",
                JSONObject.toJSONString(canteenDB), startStatus.getName(), endStatus.getName());
    }

    @Override
    public void auditCallback(WmScTicketNoticeBo wmScTicketNoticeBo, WmCanteenDB wmCanteenDB, WmScCanteenAuditDO wmScCanteenAuditDO) throws TException, WmSchCantException {
        switch (wmScTicketNoticeBo.getTicketStage()) {
            case ScConstants.CRM_TICKET_APPROVE_STAGE :
                // 任务系统审批通过
                approve(wmCanteenDB, wmScCanteenAuditDO);
                break;
            case ScConstants.CRM_TICKET_REJECT_STAGE:
                // 任务系统审批驳回
                reject(wmCanteenDB, wmScCanteenAuditDO);
                break;
            default:
                throw new RuntimeException("无法识别的任务系统回调状态");
        }
    }

    @Override
    public CanteenAuditDiffBo getCanteenAuditDiffBo(WmCanteenDB canteenDB) throws WmSchCantException {
        CanteenStatusSubMachine canteenStatusSubMachine = canteenStatusMachineFactory.getCanteenStatusSubMachine(canteenDB);
        log.info("[CanteenStatusMachineImpl.canteenStatusSubMachine] canteenDB = {}, canteenStatusSubMachine = {}",
                JSONObject.toJSONString(canteenDB), JSONObject.toJSONString(canteenStatusSubMachine));
        return canteenStatusSubMachine.getCanteenAuditDiffBo(canteenDB);
    }

    @Override
    public List<DiffField> getCanteenAuditDiffList(WmCanteenDB canteenDB) {
        CanteenStatusSubMachine canteenStatusSubMachine = canteenStatusMachineFactory.getCanteenStatusSubMachine(canteenDB);
        log.info("[CanteenStatusMachineImpl.getCanteenAuditDiffList] canteenDB = {}, canteenStatusSubMachine = {}",
                JSONObject.toJSONString(canteenDB), JSONObject.toJSONString(canteenStatusSubMachine));
        return canteenStatusSubMachine.getCanteenAuditDiffList(canteenDB);
    }

    /**
     * 判断食堂是否正在审核中
     * @param canteenDB 食堂
     */
    @Override
    public boolean isAuditing(WmCanteenDB canteenDB) {
        CanteenStatusSubMachine canteenStatusSubMachine = canteenStatusMachineFactory.getCanteenStatusSubMachine(canteenDB);
        log.info("[CanteenStatusMachineImpl.isAuditing] canteenDB = {}, canteenStatusSubMachine = {}",
                JSONObject.toJSONString(canteenDB), JSONObject.toJSONString(canteenStatusSubMachine));
        return canteenStatusSubMachine.isAuditing(canteenDB);
    }

    @Override
    public boolean isReject(WmCanteenDB canteenDB) {
        CanteenStatusSubMachine canteenStatusSubMachine = canteenStatusMachineFactory.getCanteenStatusSubMachine(canteenDB);
        return canteenStatusSubMachine.isReject(canteenDB);
    }

    @Override
    public boolean isAudited(WmCanteenDB canteenDB) {
        CanteenStatusSubMachine canteenStatusSubMachine = canteenStatusMachineFactory.getCanteenStatusSubMachine(canteenDB);
        return canteenStatusSubMachine.isReject(canteenDB);
    }

    @Override
    public boolean isInsertAuditing(WmCanteenDB canteenDB) {
        CanteenStatusSubMachine canteenStatusSubMachine = canteenStatusMachineFactory.getCanteenStatusSubMachine(canteenDB);
        return canteenStatusSubMachine.isInsertAuditing(canteenDB);
    }

    @Override
    public boolean isInsertReject(WmCanteenDB canteenDB) {
        CanteenStatusSubMachine canteenStatusSubMachine = canteenStatusMachineFactory.getCanteenStatusSubMachine(canteenDB);
        return canteenStatusSubMachine.isInsertReject(canteenDB);
    }

    /**
     * 任务系统审批通过
     * @param canteenDB canteenDB
     * @param wmScCanteenAuditDO wmScCanteenAuditDO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    private void approve(WmCanteenDB canteenDB, WmScCanteenAuditDO wmScCanteenAuditDO) throws WmSchCantException {
        if (!isAuditing(canteenDB)) {
            log.error("审核通过回调，食堂状态不是审核中");
            return;
        }

        canteenDB.setAuditStatus((int)CanteenAuditStatusEnum.AUDITED.getType());
        CanteenDetailStatusEnum startStatus = CanteenDetailStatusEnum.getByType(canteenDB.getAuditDetailStatus());
        CanteenStatusSubMachine canteenStatusSubMachine = canteenStatusMachineFactory.getCanteenStatusSubMachine(canteenDB);
        canteenStatusSubMachine.changeStatus(canteenDB, wmScCanteenAuditDO);
        CanteenDetailStatusEnum endStatus = CanteenDetailStatusEnum.getByType(canteenDB.getAuditDetailStatus());

        log.info("CanteenStatusMachine::状态流转: " + startStatus.getName() + "->" + endStatus.getName());
    }

    /**
     * 任务系统审批驳回
     * @param canteenDB canteenDB
     * @param wmScCanteenAuditDO wmScCanteenAuditDO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    private void reject(WmCanteenDB canteenDB, WmScCanteenAuditDO wmScCanteenAuditDO) throws WmSchCantException {
        if (!isAuditing(canteenDB)) {
            log.info("审核驳回回调，食堂状态不是审核中");
            return;
        }

        canteenDB.setAuditStatus((int) CanteenAuditStatusEnum.REJECT.getType());
        CanteenDetailStatusEnum startStatus = CanteenDetailStatusEnum.getByType(canteenDB.getAuditDetailStatus());
        CanteenStatusSubMachine canteenStatusSubMachine = canteenStatusMachineFactory.getCanteenStatusSubMachine(canteenDB);
        canteenStatusSubMachine.changeStatus(canteenDB, wmScCanteenAuditDO);
        CanteenDetailStatusEnum endStatus = CanteenDetailStatusEnum.getByType(canteenDB.getAuditDetailStatus());

        log.info("CanteenStatusMachine::状态流转: " + startStatus.getName() + "->" + endStatus.getName());
    }

}





package com.sankuai.meituan.waimai.customer.adapter;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.sankuai.waimai.crm.authenticate.client.constant.ResponseCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.sankuai.waimai.crm.authenticate.client.service.AuthenticateRoleService;
import com.sankuai.waimai.crm.authenticate.client.service.response.AuthenticateUserRoleResponse;

import java.util.List;

@Slf4j
@Service
public class AuthenticateRoleServiceAdaptor {

    // 租户id
    private static final int tenantId = 1;

    @Autowired
    private AuthenticateRoleService authenticateRoleService;


    public List<String> getUserRole(int userId) {
        AuthenticateUserRoleResponse response = authenticateRoleService.userRoleQuery(tenantId, userId);
        log.info("getUserRole userId={},response={}", userId, JSONObject.toJSONString(response));
        if (response.getCode() != null && response.getCode().intValue() == ResponseCode.SUCCESS.getCode()) {
            return response.getRoles();
        } else {
            return Lists.newArrayList();
        }
    }


}

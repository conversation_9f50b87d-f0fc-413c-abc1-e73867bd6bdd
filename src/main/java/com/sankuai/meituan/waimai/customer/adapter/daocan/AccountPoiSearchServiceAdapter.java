package com.sankuai.meituan.waimai.customer.adapter.daocan;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.nibmp.infra.amp.authorize.lib.service.AccountPoiSearchService;
import com.sankuai.nibmp.infra.amp.authorize.lib.vo.GetPoiNumberRequest;
import com.sankuai.nibmp.infra.amp.authorize.lib.vo.GetPoiNumberResponse;
import com.sankuai.nibmp.infra.amp.authorize.lib.vo.GetPoiPageRequest;
import com.sankuai.nibmp.infra.amp.authorize.lib.vo.GetPoiPageResponse;
import com.sankuai.nibmp.infra.amp.common.contant.BizLineEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description:
 * @author: liuyunjie05
 * @create: 2024/8/19 10:24
 */
@Slf4j
@Service
public class AccountPoiSearchServiceAdapter {

    @Resource
    private AccountPoiSearchService accountPoiSearchService;

    // 开店宝，到餐
    private static final Integer PERMISSION_SPACE = 1;

    /**
     * 分页查询门店账号关联门店
     *
     * @param request 入参
     * @return 返回值
     */
    public GetPoiPageResponse getPoiIdByAccountIdAndPage(GetPoiPageRequest request) {
        log.info("AccountPoiSearchServiceAdapter#getPoiIdByAccountIdAndPage, request: {}", JSON.toJSONString(request));
        GetPoiPageResponse response = accountPoiSearchService.getPoiIdByAccountIdAndPage(request);
        log.info("AccountPoiSearchServiceAdapter#getPoiIdByAccountIdAndPage, response: {}", JSON.toJSONString(response));
        return response;
    }

    /**
     * 查询账号关联的门店总数
     *
     * @param request 入参
     * @return 返回值
     */
    public Integer getPoiNumber(GetPoiNumberRequest request) throws WmCustomerException {
        try {
            log.info("AccountPoiSearchServiceAdapter#getPoiNumber, request: {}", JSON.toJSONString(request));
            GetPoiNumberResponse poiNumberResponse = accountPoiSearchService.getPoiNumber(request);
            log.info("AccountPoiSearchServiceAdapter#getPoiNumber, poiNumberResponse: {}", JSON.toJSONString(poiNumberResponse));
            return poiNumberResponse.getTotalCount();
        } catch (Exception e) {
            log.error("AccountPoiSearchServiceAdapter#getPoiNumber, error", e);
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "查询账号关联门店总数失败");
        }
    }

    public List<Long> getPoiIdByAccountId(long accountId) {
        log.info("AccountPoiSearchServiceAdapter#getPoiIdByAccountId, accountId: {}", accountId);
        List<Long> poiIdList = accountPoiSearchService.getPoiIdByAccountId(accountId, BizLineEnum.FOOD.getCode(), PERMISSION_SPACE);
        log.info("AccountPoiSearchServiceAdapter#getPoiIdByAccountId, poiIdList: {}", JSON.toJSONString(poiIdList));
        return poiIdList;
    }

}

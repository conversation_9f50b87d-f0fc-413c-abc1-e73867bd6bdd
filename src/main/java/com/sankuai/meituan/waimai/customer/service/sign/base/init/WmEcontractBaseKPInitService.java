package com.sankuai.meituan.waimai.customer.service.sign.base.init;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.domain.WmEContractSignBaseDB;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractBaseBizService;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBaseApplyBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBaseInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractCustomerKPBo;

import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class WmEcontractBaseKPInitService {

    @Resource
    private WmEcontractBaseBizService wmEcontractBaseBizService;

    public WmEContractSignBaseDB init(EcontractBaseApplyBo applyBo) throws WmCustomerException {
        WmEContractSignBaseDB signBaseDB = wmEcontractBaseBizService.queryByCustomerId(applyBo.getCustomerId());
        EcontractBaseInfoBo baseInfoBo = signBaseDB == null ? new EcontractBaseInfoBo() : JSON.parseObject(signBaseDB.getBaseContext(), EcontractBaseInfoBo.class);
        EcontractCustomerKPBo kpBo = JSON.parseObject(applyBo.getApplyInfoBo(), EcontractCustomerKPBo.class);
        baseInfoBo.setKpBo(kpBo);

        if (signBaseDB == null) {
            signBaseDB = new WmEContractSignBaseDB();
        }
        signBaseDB.setCustomerId(applyBo.getCustomerId());
        signBaseDB.setBaseContext(JSON.toJSONString(baseInfoBo));
        return signBaseDB;
    }
}

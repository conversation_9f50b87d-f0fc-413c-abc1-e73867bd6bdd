package com.sankuai.meituan.waimai.customer.service.sign.mafka.producer;

import com.alibaba.fastjson.JSON;
import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.ConsumerConstants;
import com.meituan.mafka.client.producer.IProducerProcessor;
import com.meituan.mafka.client.producer.ProducerResult;
import com.meituan.mafka.client.producer.ProducerStatus;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignSuccessMsg;
import java.util.Properties;
import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * 电子签约batch生效消息通知
 * Created by lixuepeng on 2022/2/19.
 */
@Component
public class WmEContractBatchSuccessProducer {

    private static final Logger LOG = LoggerFactory.getLogger(WmEContractBatchSuccessProducer.class);

    private static IProducerProcessor producer;

    @PostConstruct
    public void init() {
        Properties properties = new Properties();
        properties.setProperty(ConsumerConstants.MafkaBGNamespace, "waimai");
        properties.setProperty(ConsumerConstants.MafkaClientAppkey, "com.sankuai.waimai.e.customer");
        try {
            // 创建topic对应的producer对象（注意每次build调用会产生一个新的实例）
            producer = MafkaClient.buildProduceFactory(properties, "wm.econtract.sign.batch.success", false);
        } catch (Exception e) {
            LOG.error("WmEContractBatchSuccessProducer.init buildProduceFactory", e);
        }
    }

    @PreDestroy
    public void destroy() {
        try {
            producer.close();
        } catch (Exception e) {
            LOG.error("WmEContractBatchSuccessProducer.destroy", e);
        }
    }

    public boolean sendMsg(WmEcontractSignSuccessMsg msg) {
        try {
            ProducerResult result = producer.sendMessage(JSON.toJSONString(msg));
            if (result.getProducerStatus() == ProducerStatus.SEND_FAILURE) { // 失败时重试一次
                result = producer.sendMessage(JSON.toJSONString(msg));
            }
            if (result != null && result.getProducerStatus() == ProducerStatus.SEND_OK) {
                LOG.info("WmEContractBatchSuccessProducer.sendMsg success msg={}", JSON.toJSONString(msg));
            }
            return true;
        } catch (Exception e) {
            LOG.error("WmEContractBatchSuccessProducer.sendMsg msg:{}", JSON.toJSONString(msg), e);
        }
        return false;
    }
}

package com.sankuai.meituan.waimai.customer.service.sc.thrift;

import com.meituan.waimai.common.utils.JacksonUtils;
import com.sankuai.meituan.waimai.customer.dao.sc.WmScCanteenPoiAttributeMapper;
import com.sankuai.meituan.waimai.customer.domain.sc.WmScCanteenPoiAttributeDO;
import com.sankuai.meituan.waimai.customer.service.sc.auth.WmCanteenStallAuthService;
import com.sankuai.meituan.waimai.customer.service.sc.canteenstall.WmCanteenStallBindService;
import com.sankuai.meituan.waimai.customer.service.sc.canteenstall.WmCanteenStallClueService;
import com.sankuai.meituan.waimai.customer.service.sc.canteenstall.WmCanteenStallManageService;
import com.sankuai.meituan.waimai.customer.service.sc.canteenstall.check.WmCanteenStallCheckService;
import com.sankuai.meituan.waimai.customer.service.sc.canteenstall.status.WmCanteenStallAuditService;
import com.sankuai.meituan.waimai.customer.service.sc.poi.WmScCanteenPoiTaskSimpleService;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmPoiTransferLimitDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.canteenstall.*;
import com.sankuai.meituan.waimai.thrift.customer.service.sc.WmCanteenStallThriftService;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants.SERVER_ERROR;

/**
 * 食堂档口管理实现类
 * <AUTHOR>
 * @date 2024/05/11
 * @email  <EMAIL>
 */
@Slf4j
@Service
public class WmCanteenStallThriftServiceImpl implements WmCanteenStallThriftService {

    @Autowired
    private WmCanteenStallClueService wmCanteenStallClueService;

    @Autowired
    private WmCanteenStallCheckService wmCanteenStallCheckService;

    @Autowired
    private WmCanteenStallAuditService wmCanteenStallAuditService;

    @Autowired
    private WmCanteenStallManageService wmCanteenStallManageService;

    @Autowired
    private WmCanteenStallBindService wmCanteenStallBindService;

    @Autowired
    private WmCanteenStallAuthService wmCanteenStallAuthService;

    @Autowired
    private WmScCanteenPoiTaskSimpleService wmScCanteenPoiTaskSimpleService;

    @Autowired
    private WmScCanteenPoiAttributeMapper wmScCanteenPoiAttributeMapper;

    /**
     * 批量创建线索-校验上传的Excel文件列表内容
     * @param canteenPrimaryId 食堂主键ID
     * @param excelDTOList 上传的Excel文件列表
     * @return List<WmCanteenStallClueErrorDTO>
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    @Override
    public List<WmCanteenStallClueExcelDTO> checkClueExcelDTOList(Integer canteenPrimaryId, List<WmCanteenStallClueExcelDTO> excelDTOList)
            throws WmSchCantException, TException {
        return wmCanteenStallCheckService.checkClueExcelDTOList(canteenPrimaryId, excelDTOList);
    }

    /**
     * 批量创建线索-保存上传的Excel文件列表内容
     * @param canteenPrimaryId 食堂主键ID
     * @param excelDTOList excelDTOList
     * @param userId 用户ID
     * @return 档口管理任务ID
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    @Override
    public Integer saveClueExcelDTOList(Integer canteenPrimaryId, List<WmCanteenStallClueExcelDTO> excelDTOList, Integer userId)
            throws WmSchCantException, TException {
        return wmCanteenStallClueService.saveClueExcelDTOList(canteenPrimaryId, excelDTOList, userId);
    }

    /**
     * 根据食堂档口管理ID查询线索信息列表
     * @param manageId 档口管理任务ID
     * @return WmCanteenStallClueListDTO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    @Override
    public WmCanteenStallClueListDTO getClueListByManageId(Integer manageId) throws WmSchCantException, TException {
        return wmCanteenStallClueService.getClueListByManageId(manageId);
    }

    /**
     * 根据食堂档口线索主键ID删除线索信息
     * @param cluePrimaryId 线索主键ID
     * @param userId 用户ID
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    @Override
    public void deleteClueByCluePrimaryId(Integer cluePrimaryId, Integer userId) throws WmSchCantException, TException {
        wmCanteenStallClueService.deleteClueByCluePrimaryId(cluePrimaryId, userId);
    }


    /**
     * 保存食堂档口线索扎点信息
     * @param clueDetailDTO 线索详情DTO
     * @param uid 用户UID
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    @Override
    public void saveClueCoordinate(WmCanteenStallClueDTO clueDetailDTO, Integer uid) throws WmSchCantException, TException {
        wmCanteenStallClueService.saveClueCoordinate(clueDetailDTO, uid);
    }

    /**
     * 创建档口绑定任务列表（创建线索）
     * @param manageId 食堂档口管理任务ID
     * @param userId 用户ID
     * @param userName 用户名称
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    @Override
    public void createStallBindListByManageId(Integer manageId, Integer userId, String userName) throws WmSchCantException, TException {
        wmCanteenStallBindService.createStallBindListByManageId(manageId, userId, userName);
    }


    /**
     * 查询档口管理任务列表
     * @param queryDTO 查询参数
     * @return WmCanteenStallManageListDTO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    @Override
    public WmCanteenStallManageListDTO getManageList(WmCanteenStallManageQueryDTO queryDTO) throws WmSchCantException, TException {
        return wmCanteenStallManageService.getManageList(queryDTO);
    }

    /**
     * 查询档口绑定任务列表(食堂详情页面)
     * @param queryDTO 查询参数
     * @return WmCanteenStallBindQueryDTO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    @Override
    public WmCanteenStallBindListDTO getBindListWithCanteenPrimaryId(WmCanteenStallBindQueryDTO queryDTO) throws WmSchCantException, TException {
        return wmCanteenStallBindService.getBindListWithCanteenPrimaryId(queryDTO);
    }

    /**
     * 查询档口绑定任务列表(档口绑定页面)
     * @param queryDTO 查询参数
     * @return WmCanteenStallBindQueryDTO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    @Override
    public WmCanteenStallBindListDTO getBindListWithManageId(WmCanteenStallBindQueryDTO queryDTO) throws WmSchCantException, TException {
        return wmCanteenStallBindService.getBindListWithManageId(queryDTO);
    }

    /**
     * 食堂档口绑定线索跟进状态提审
     * @param submitDTO submitDTO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    @Override
    public void submitClueAuditTask(WmCanteenStallAuditSubmitDTO submitDTO) throws WmSchCantException, TException {
        wmCanteenStallAuditService.submitClueAuditTask(submitDTO);
    }

    /**
     * 根据档口绑定任务ID查询审批页面档口绑定列表
     * @param auditSystemId 任务系统任务ID
     * @return WmCanteenStallBindListDTO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    @Override
    public WmCanteenStallBindListDTO getAuditBindListByAuditSystemId(String auditSystemId) throws WmSchCantException, TException {
        return wmCanteenStallAuditService.getAuditBindListByAuditSystemId(auditSystemId);
    }

    /**
     * 根据档口绑定任务ID查询审批流信息
     * @param auditSystemId 任务系统任务ID
     * @return WmCanteenStallBindListDTO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    @Override
    public WmCanteenStallAuditStreamDTO getAuditStreamByAuditSystemId(String auditSystemId) throws WmSchCantException, TException {
        return wmCanteenStallAuditService.getAuditStreamByAuditSystemId(auditSystemId);
    }

    /**
     * 根据档口绑定任务ID查询审批任务信息
     * @param auditSystemId 任务系统任务ID
     * @return WmCanteenStallAuditTaskDTO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    @Override
    public WmCanteenStallAuditTaskDTO getAuditTaskByAuditSystemId(String auditSystemId) throws WmSchCantException, TException {
        return wmCanteenStallAuditService.getAuditTaskByAuditSystemId(auditSystemId);
    }

    /**
     * 根据审批中档口绑定任务ID查询任务系统任务ID
     * @param bindId 档口绑定任务ID
     * @return 任务系统任务ID
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    @Override
    public String getAuditSystemIdByAuditingBindId(Integer bindId) throws WmSchCantException, TException {
        return wmCanteenStallAuditService.getAuditSystemIdByAuditingBindId(bindId);
    }

    /**
     * 公海绑定提交前置校验
     * @param submitDTO submitDTO
     * @return List<WmCanteenStallCheckFailDTO>
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    @Override
    public List<WmCanteenStallCheckFailDTO> checkSubmitByWdcClueBind(WmCanteenStallBindSubmitDTO submitDTO) throws WmSchCantException, TException {
        return wmCanteenStallCheckService.checkSubmitByWdcClueBind(submitDTO);
    }

    /**
     * 公海绑定提交
     * @param submitDTO 提交DTO
     * @return 档口管理ID
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    @Override
    public Integer submitByWdcClueBind(WmCanteenStallBindSubmitDTO submitDTO) throws WmSchCantException, TException {
        return wmCanteenStallManageService.submitByWdcClueBind(submitDTO);
    }

    /**
     * 外卖门店绑定提交前置校验
     * @param submitDTO submitDTO
     * @return List<WmCanteenStallCheckFailDTO>
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    @Override
    public List<WmCanteenStallCheckFailDTO> checkSubmitByWmPoiBind(WmCanteenStallBindSubmitDTO submitDTO) throws WmSchCantException, TException {
        return wmCanteenStallCheckService.checkSubmitByWmPoiBind(submitDTO);
    }

    /**
     * 外卖门店绑定提交
     * @param submitDTO 提交DTO
     * @return 档口管理ID
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    @Override
    public Integer submitByWmPoiBind(WmCanteenStallBindSubmitDTO submitDTO) throws WmSchCantException, TException {
        return wmCanteenStallManageService.submitByWmPoiBind(submitDTO);
    }

    /**
     * 档口绑定任务解绑
     * @param bindId 档口绑定ID
     * @param userId 用户ID
     * @param userName 用户名称
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    @Override
    public void unbindClueAndWmPoiByBindId(Integer bindId, Integer userId, String userName) throws WmSchCantException, TException {
        wmCanteenStallBindService.unbindClueAndWmPoiByBindId(bindId, userId, userName);
    }

    @Override
    public List<WmPoiTransferLimitDTO> getRemainingTransferCount(List<Long> list) throws WmSchCantException, TException {
        return wmScCanteenPoiTaskSimpleService.getRemainingTransferCount(list);
    }

    /**
     * 获取用户对食堂档口管理某一操作的鉴权结果
     * @param uid 用户ID
     * @param operationCode 操作CODE
     * @param manageId 档口管理ID
     * @return true: 有权限 / false: 无权限
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    @Override
    public Boolean getOperationAuthAssertResult(Integer uid, String operationCode, Integer manageId) throws WmSchCantException, TException {
        return wmCanteenStallAuthService.getOperationAuthAssertResult(uid, operationCode, manageId);
    }

    /**
     * 查询用户对单个档口管理多个操作的鉴权结果
     * @param operationList 操作code列表
     * @param manageId 档口管理ID
     * @param uid 用户ID
     * @return Map<String, Boolean>
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    @Override
    public Map<String, Boolean> batchGetOperationAuthAssertResult(Set<String> operationList, Integer manageId, Integer uid)
            throws WmSchCantException, TException {
        return wmCanteenStallAuthService.batchGetOperationAuthAssertResult(operationList, manageId, uid);
    }

    @Override
    public WmCanteenStallBindAttributeResponse getCanteenStallBindAttribute(Long wmPoiId) throws WmSchCantException, TException {
        return ThriftUtils.exec(() -> {
            log.info("[WmCanteenStallThriftService.getCanteenStallBindAttribute]wmPoiId:{}", wmPoiId);
            List<WmScCanteenPoiAttributeDO> wmScCanteenPoiAttributeDOS = wmScCanteenPoiAttributeMapper.selectByWmPoiIdMaster(wmPoiId);
            if (CollectionUtils.isEmpty(wmScCanteenPoiAttributeDOS)) {
                return null;
            }
            if (wmScCanteenPoiAttributeDOS.size() > 1) {
                throw new RuntimeException(String.format("外卖门店id:%s存在多条档口关联记录", wmPoiId));
            }

            WmScCanteenPoiAttributeDO attributeDO = wmScCanteenPoiAttributeDOS.get(0);
            log.info("[WmCanteenStallThriftService.getCanteenStallBindAttribute]attributeDO:{}", JacksonUtils.toJson(attributeDO));
            WmCanteenStallBindAttributeDTO bindAttributeDTO = new WmCanteenStallBindAttributeDTO();
            bindAttributeDTO.setWmPoiId(attributeDO.getWmPoiId());
            bindAttributeDTO.setCanteenId(attributeDO.getCanteenId());
            bindAttributeDTO.setCanteenPrimaryId(attributeDO.getCanteenPrimaryId());
            bindAttributeDTO.setSchoolId(attributeDO.getSchoolId());
            bindAttributeDTO.setSchoolPrimaryId(attributeDO.getSchoolPrimaryId());
            bindAttributeDTO.setCanteenCategory(attributeDO.getCanteenCategory());
            return bindAttributeDTO;

        }, new WmCanteenStallBindAttributeResponse());
    }
}

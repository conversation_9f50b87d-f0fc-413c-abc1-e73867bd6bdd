package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext;

import com.sankuai.meituan.waimai.customer.bo.sign.EcontractBatchMiddleBo;
import com.sankuai.meituan.waimai.customer.constant.sign.WmSignConstant;
import com.sankuai.meituan.waimai.econtrct.client.constants.EcontractRecordSourceEnum;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.EcontractBatchBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

/**
 * 【Author】: 窦旭蒙
 * 【MIS】: douxumeng
 * 【Date】: 2022-09-19 15:45
 * 【Email】: <EMAIL>
 * 【Desc】:
 */
@Service
@Slf4j
public class WmEcontractDataSourceCollector implements IWmEcontractDataCollector {

    @Override
    public void collect(EcontractBatchContextBo originContext, EcontractBatchMiddleBo middleContext, EcontractBatchBo targetContext) throws WmCustomerException, IllegalAccessException, TException {
        log.info("WmEcontractDataSourceCollector#batchId:{}, batchTypeEnum:{}", originContext.getBatchId(), originContext.getBatchTypeEnum());
        String originSource = originContext.getSource();
        if (originSource.equals(WmSignConstant.BATCH_PLATFORM)) {
            targetContext.setEcontractBatchSource(EcontractRecordSourceEnum.BATCH_PLATFORM);
        } else if (originSource.equals(WmSignConstant.OTHER)) {
            targetContext.setEcontractBatchSource(EcontractRecordSourceEnum.NORMAL);
        } else {
            //兜底
            targetContext.setEcontractBatchSource(EcontractRecordSourceEnum.NORMAL);
        }
    }
}

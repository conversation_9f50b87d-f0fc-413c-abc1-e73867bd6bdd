package com.sankuai.meituan.waimai.customer.contract.service.impl.check;

import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.contract.service.IContractValidator;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.thrift.customer.constant.WmTempletContractTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerSignMode;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class CustomerEffectiveValidator implements IContractValidator {

    @Autowired
    private WmCustomerService wmCustomerService;

    @Override
    public boolean valid(WmCustomerContractBo contractBo, int opUid, String opName)
            throws WmCustomerException {
        if (WmTempletContractTypeEnum.isEContract(contractBo.getBasicBo().getType())) {
            return true;
        }
        WmCustomerDB customerDB = wmCustomerService.selectCustomerById(contractBo.getBasicBo().getParentId());
        if (customerDB == null) {
            if(MccConfig.isDisplayMtCustomerId()){
                throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "不存在的客户ID：" + customerDB.getMtCustomerId());
            }else{
                throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "不存在的客户ID：" + contractBo.getBasicBo().getParentId());
            }
        }
        if (customerDB.getSignMode().equals(CustomerSignMode.ELECTTRONIC.getCode())
                && customerDB.isUnEffectived()) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "请在客户信息审核通过后，再创建纸质合同");
        }
        return true;
    }

}

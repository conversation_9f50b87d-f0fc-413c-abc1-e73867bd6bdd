package com.sankuai.meituan.waimai.customer.util.trans;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.Nullable;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Function;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sankuai.meituan.waimai.customer.bo.WmPoiDomain;
import com.sankuai.meituan.waimai.customer.settle.domain.WmPoiSettleAuditedDB;
import com.sankuai.meituan.waimai.customer.settle.domain.WmPoiSettleDB;
import com.sankuai.meituan.waimai.customer.settle.domain.WmSettleAuditedDB;
import com.sankuai.meituan.waimai.customer.settle.domain.WmSettleDB;
import com.sankuai.meituan.waimai.customer.settle.domain.WmSettlePoiWalletDB;
import com.sankuai.meituan.waimai.customer.settle.message.diff.bean.WmSettleDBDiff;
import com.sankuai.meituan.waimai.poibizflow.constant.WmPoiSwitchModuleEnum;
import com.sankuai.meituan.waimai.poibizflow.thrift.switcher.bo.SwitchPoiInfo;
import com.sankuai.meituan.waimai.poibizflow.thrift.switcher.bo.SwitchPoiSettleInfo;
import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmCardValidationInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettle;
import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettleAudited;
import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettlePoiInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettlePoiWalletBo;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiSettle;

/**
 *
 */
public class WmSettleTransUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(WmSettleTransUtil.class);

    public static final Map<String,String> PDF_MAPPING = Maps.newHashMap();

    static{
        PDF_MAPPING.put("acctType1","对公");
        PDF_MAPPING.put("acctType2","对私");
        PDF_MAPPING.put("settleType1","系统自动结算");
        PDF_MAPPING.put("settleType2","商家自提");
        PDF_MAPPING.put("settleType3","系统自动结算");
        PDF_MAPPING.put("settleType4","系统自动结算");
//        PDF_MAPPING.put("payPeriod3","3天");
//        PDF_MAPPING.put("payPeriod7","一周（7天）");
//        PDF_MAPPING.put("payPeriod14","两周（14天）");
//        PDF_MAPPING.put("payPeriod30","一个月（30天）");
        PDF_MAPPING.put("minPayAmountType1","100");
        PDF_MAPPING.put("minPayAmountType2","300");
        PDF_MAPPING.put("minPayAmountType3","500");
        PDF_MAPPING.put("minPayAmountType4","minPayAmount");

        //unit_num
        PDF_MAPPING.put("3_3","3天");
        PDF_MAPPING.put("1_1","7天");
        PDF_MAPPING.put("1_2","14天");
        PDF_MAPPING.put("2_1","30天");
        PDF_MAPPING.put("3_1","1天");

    }

    public static List<WmSettle> WmSettleListDB2Thrift(List<WmSettleDB> aorDBList) {
        List<WmSettle> aorList = new ArrayList<WmSettle>();
        for (WmSettleDB wmAorDB : aorDBList) {
            aorList.add(wmSettleDB2Thrift(wmAorDB));
        }
        return aorList;
    }

    public static WmSettle wmSettleDB2Thrift(WmSettleDB pointDB) {
        if (pointDB == null) {
            return null;
        }
        WmSettle point = new WmSettle();
        TransUtil.transferAll(pointDB, point);
        //float->double 精度问题
        point.setMin_pay_amount(new BigDecimal(pointDB.getMin_pay_amount() + "").doubleValue());
        point.setPay_period_unit(pointDB.getPay_period_unit());
        point.setAcc_cardno(pointDB.getAcc_cardno());
        return point;
    }

    public static Map<Integer, WmSettle> transWmSettleList2Map(List<WmSettle> wmSettleList) {
        if (CollectionUtils.isEmpty(wmSettleList)) {
            return Maps.newHashMap();
        }

        Map<Integer, WmSettle> map = Maps.newHashMap();
        for (WmSettle wmSettle : wmSettleList) {
            if (wmSettle == null) {
                continue;
            }

            map.put(wmSettle.getId(), wmSettle);
        }
        return map;
    }

    public static List<WmSettleAudited> wmSettleAuditedListDB2Thrift(List<WmSettleAuditedDB> wmSettleAuditedDBList) {
        List<WmSettleAudited> wmSettleAuditedList = new ArrayList<WmSettleAudited>();
        if (wmSettleAuditedDBList != null) {
            for (WmSettleAuditedDB wmSettleAuditedDB : wmSettleAuditedDBList) {
                WmSettleAudited wmSettleAudited = wmSettleAuditedDB2Thrift(wmSettleAuditedDB);
                wmSettleAuditedList.add(wmSettleAudited);
            }
        }
        return wmSettleAuditedList;
    }

    public static WmSettleAudited wmSettleAuditedDB2Thrift(WmSettleAuditedDB pointDB) {
        if (pointDB != null) {
            try {
                WmSettleAudited point = new WmSettleAudited();
                BeanUtils.copyProperties(pointDB, point);
                //float->double 精度问题
                point.setMin_pay_amount(new BigDecimal(pointDB.getMin_pay_amount() + "").doubleValue());
                point.setPay_period_unit(pointDB.getPay_period_unit());
                return point;
            } catch (Exception e) {
                LOGGER.error("结算转换错误，settleId:" + pointDB.getId(), e);
            }
        }
        return null;
    }

    public static Map<Integer, WmSettleAudited> transWmSettleAuditedList2Map(List<WmSettleAudited> wmSettleAuditedList) {
        if (CollectionUtils.isEmpty(wmSettleAuditedList)) {
            return Maps.newHashMap();
        }

        Map<Integer, WmSettleAudited> map = Maps.newHashMap();
        for (WmSettleAudited wmSettleAudited : wmSettleAuditedList) {
            if (wmSettleAudited == null) {
                continue;
            }
            map.put(wmSettleAudited.getWm_settle_id(), wmSettleAudited);
        }
        return map;
    }

    public static WmSettleDB wmSettleThrift2DB(WmSettle point) {
        if (point != null) {
            try {
                WmSettleDB pointDB = new WmSettleDB();
                BeanUtils.copyProperties(point, pointDB);
                pointDB.setMin_pay_amount((float) point.getMin_pay_amount());
                pointDB.setPay_period_unit((byte) point.getPay_period_unit());
                pointDB.setName(point.getName());
                return pointDB;
            } catch (Exception e) {
                LOGGER.error("结算转换错误，contract:" + point.getWm_contract_id(), e);
            }
        }
        return null;
    }

    public static List<WmSettleDB> wmSettleThriftList2DBList(List<WmSettle> wmSettleList){
        List<WmSettleDB> result = Lists.newArrayList();
        if(CollectionUtils.isEmpty(wmSettleList)){
            return result;
        }
        for(WmSettle temp : wmSettleList){
            result.add(wmSettleThrift2DB(temp));
        }
        return result;
    }

    public static List<WmSettleAuditedDB> wmSettleAuditedThriftList2DBList(List<WmSettleAudited> wmSettleAuditedList){
        List<WmSettleAuditedDB> result = Lists.newArrayList();
        if(CollectionUtils.isEmpty(wmSettleAuditedList)){
            return result;
        }
        for(WmSettleAudited temp : wmSettleAuditedList){
            result.add(wmSettleAuditedThrift2DB(temp));
        }
        return result;
    }

    public static List<WmPoiSettle> transWmPoiIdList2PoiSettle(List<Integer> wmPoiIdList, Integer wmContractId, Integer wmSettleId) {
        if (CollectionUtils.isEmpty(wmPoiIdList)) {
            return new ArrayList<>();
        }
        List<WmPoiSettle> wmPoiSettleList = new ArrayList<>();
        for (Integer wmPoiId : wmPoiIdList) {
            WmPoiSettle wmPoiSettle = new WmPoiSettle();
            wmPoiSettle.setWmContractId(wmContractId);
            wmPoiSettle.setWmPoiId(wmPoiId);
            wmPoiSettle.setWmSettleId(wmSettleId);
            wmPoiSettleList.add(wmPoiSettle);
        }
        return wmPoiSettleList;
    }

    public static List<WmSettlePoiInfoBo> transWmPoiList2WmSettlePoiInfoList(List<WmPoiDomain> wmPoiDomainList) {
        if (CollectionUtils.isEmpty(wmPoiDomainList)) {
            return new ArrayList<>();
        }

        List<WmSettlePoiInfoBo> wmSettlePoiInfoBoList = new ArrayList<>();
        WmSettlePoiInfoBo wmSettlePoiInfoBo = null;
        for (WmPoiDomain wmPoiDomain : wmPoiDomainList) {
            wmSettlePoiInfoBo = new WmSettlePoiInfoBo();
            wmSettlePoiInfoBo.setWmPoiId((int) wmPoiDomain.getWmPoiId());
            wmSettlePoiInfoBo.setWmPoiName(wmPoiDomain.getName());
            CollectionUtils.addIgnoreNull(wmSettlePoiInfoBoList, wmSettlePoiInfoBo);
        }
        return wmSettlePoiInfoBoList;
    }

    public static List<WmSettle> transWmSettleAuditedList2WmSettleList(List<WmSettleAudited> wmSettleAuditedList) {
        if (CollectionUtils.isEmpty(wmSettleAuditedList)) {
            return new ArrayList<>();
        }

        List<WmSettle> wmSettleList = new ArrayList<>();
        for (WmSettleAudited wmSettleAudited : wmSettleAuditedList) {
            WmSettle wmSettle = transWmSettleAudited2WmSettle(wmSettleAudited);
            wmSettle.setId(wmSettleAudited.getWm_settle_id());
            CollectionUtils.addIgnoreNull(wmSettleList, wmSettle);
        }
        return wmSettleList;
    }

    public static List<WmSettleAudited> transWmSettleList2WmSettleAuditedList(List<WmSettle> wmSettleList) {
        if (CollectionUtils.isEmpty(wmSettleList)) {
            return new ArrayList<>();
        }

        List<WmSettleAudited> wmSettleAuditedList = new ArrayList<>();
        for (WmSettle wmSettle : wmSettleList) {
            WmSettleAudited wmSettleAudited = transWmSettle2WmSettleAudited(wmSettle);
            wmSettleAudited.setId(0);
            CollectionUtils.addIgnoreNull(wmSettleAuditedList, wmSettleAudited);
        }
        return wmSettleAuditedList;
    }

    /**
     * WmSettle 线上表转换为线下表
     *
     * @param wmSettleAudited
     * @return
     */
    public static WmSettle transWmSettleAudited2WmSettle(WmSettleAudited wmSettleAudited) {
        if (wmSettleAudited == null) {
            return null;
        }

        WmSettle wmSettle = new WmSettle();
        wmSettle.setWm_contract_id(wmSettleAudited.getWm_contract_id());
        wmSettle.setWmCustomerId(wmSettleAudited.getWmCustomerId());

        wmSettle.setParty_a_finance_people(wmSettleAudited.getParty_a_finance_people());
        wmSettle.setParty_a_finance_phone(wmSettleAudited.getParty_a_finance_phone());

        wmSettle.setMin_pay_amount(wmSettleAudited.getMin_pay_amount());
        wmSettle.setPay_period_num(wmSettleAudited.getPay_period_num());
        wmSettle.setPay_period_unit(wmSettleAudited.getPay_period_unit());

        wmSettle.setProvince(wmSettleAudited.getProvince());
        wmSettle.setCity(wmSettleAudited.getCity());
        wmSettle.setBankid(wmSettleAudited.getBankid());
        wmSettle.setBranchid(wmSettleAudited.getBranchid());
        wmSettle.setBranchname(wmSettleAudited.getBranchname());

        wmSettle.setAcc_cardno(wmSettleAudited.getAcc_cardno());
        wmSettle.setAcc_name(wmSettleAudited.getAcc_name());
        wmSettle.setAcctype(wmSettleAudited.getAcctype());
        wmSettle.setSettle_type(wmSettleAudited.getSettle_type());
        wmSettle.setPay_day_of_month(wmSettleAudited.getPay_day_of_month());
        wmSettle.setName(wmSettleAudited.getName());

        wmSettle.setWmPoiIdList(wmSettleAudited.getWmPoiIdList());
        wmSettle.setId(wmSettleAudited.getWm_settle_id());

        wmSettle.setCert_type(wmSettleAudited.getCert_type());
        wmSettle.setLegal_cert_num(wmSettleAudited.getLegal_cert_num());
        wmSettle.setLegal_person(wmSettleAudited.getLegal_person());
        wmSettle.setLegal_id_card(wmSettleAudited.getLegal_id_card());
        wmSettle.setReserve_phone(wmSettleAudited.getReserve_phone());
        wmSettle.setCert_num(wmSettleAudited.getCert_num());
        wmSettle.setCard_type(wmSettleAudited.getCard_type());

        wmSettle.setCard_valid(wmSettleAudited.getCard_valid());
        wmSettle.setCard_invalid_reason(wmSettleAudited.getCard_invalid_reason());

        wmSettle.setWm_wallet_id(wmSettleAudited.getWm_wallet_id());

        return wmSettle;
    }

    /**
     * WmSettle 线上表转换为线下表
     */
    public static WmSettleAudited transWmSettle2WmSettleAudited(WmSettle wmSettle) {
        if (wmSettle == null) {
            return null;
        }

        WmSettleAudited wmSettleAudited = new WmSettleAudited();

        wmSettleAudited.setWm_settle_id(wmSettle.getId());
        wmSettleAudited.setWm_contract_id(wmSettle.getWm_contract_id());
        wmSettleAudited.setWmCustomerId(wmSettle.getWmCustomerId());

        wmSettleAudited.setParty_a_finance_people(wmSettle.getParty_a_finance_people());
        wmSettleAudited.setParty_a_finance_phone(wmSettle.getParty_a_finance_phone());

        wmSettleAudited.setMin_pay_amount(wmSettle.getMin_pay_amount());
        wmSettleAudited.setPay_period_num(wmSettle.getPay_period_num());
        wmSettleAudited.setPay_period_unit(wmSettle.getPay_period_unit());

        wmSettleAudited.setProvince(wmSettle.getProvince());
        wmSettleAudited.setCity(wmSettle.getCity());
        wmSettleAudited.setBankid(wmSettle.getBankid());
        wmSettleAudited.setBranchid(wmSettle.getBranchid());
        wmSettleAudited.setBranchname(wmSettle.getBranchname());

        wmSettleAudited.setAcc_cardno(wmSettle.getAcc_cardno());
        wmSettleAudited.setAcc_name(wmSettle.getAcc_name());
        wmSettleAudited.setAcctype(wmSettle.getAcctype());
        wmSettleAudited.setSettle_type(wmSettle.getSettle_type());
        wmSettleAudited.setPay_day_of_month(wmSettle.getPay_day_of_month());
        wmSettleAudited.setName(wmSettle.getName());

        wmSettleAudited.setWmPoiIdList(wmSettle.getWmPoiIdList());
        wmSettleAudited.setId(0);


        wmSettleAudited.setCert_type(wmSettle.getCert_type());
        wmSettleAudited.setLegal_cert_num(wmSettle.getLegal_cert_num());
        wmSettleAudited.setLegal_person(wmSettle.getLegal_person());
        wmSettleAudited.setLegal_id_card(wmSettle.getLegal_id_card());
        wmSettleAudited.setReserve_phone(wmSettle.getReserve_phone());
        wmSettleAudited.setCert_num(wmSettle.getCert_num());
        wmSettleAudited.setCard_type(wmSettle.getCard_type());

        wmSettleAudited.setCard_valid(wmSettle.getCard_valid());
        wmSettleAudited.setCard_invalid_reason(wmSettle.getCard_invalid_reason());

        return wmSettleAudited;
    }

    public static List<WmSettle> encryptWmSettleList(List<WmSettle> wmSettleList) {
        if (CollectionUtils.isEmpty(wmSettleList)) {
            return new ArrayList<>();
        }

        for (WmSettle wmSettle : wmSettleList) {
            String encrypt = encrypt(wmSettle.getAcc_cardno());
            wmSettle.setAcc_cardno(encrypt);
        }
        return wmSettleList;
    }

    /**
     * 银行卡号加密
     *
     * @param str
     * @return
     */
    public static String encrypt(String str) {
        if (StringUtils.isEmpty(str) || str.length() < 10) {
            return "";
        }

        String head = str.substring(0, 4);
        String tail = str.substring(str.length() - 3);

        StringBuilder sb = new StringBuilder();
        sb.append(head);
        sb.append("********");
        sb.append(tail);
        return sb.toString();
    }

    public static List<WmPoiSettleDB> extractWmPoiSettleDB(List<WmSettle> wmSettleList) {
        List<WmPoiSettleDB> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(wmSettleList)) {
            return result;
        }
        int wmCustomerId = 0;
        List<Integer> wmPoiIdList = null;
        for (WmSettle temp : wmSettleList) {
            wmCustomerId = temp.getWmCustomerId();
            wmPoiIdList = temp.getWmPoiIdList();
            if (CollectionUtils.isNotEmpty(wmPoiIdList)) {
                for (Integer wmPoiId : wmPoiIdList) {
                    result.add(WmPoiSettleDB.builder()
                            .wm_settle_id(temp.getId())
                            .wm_poi_id(wmPoiId)
                            .wm_contract_id(wmCustomerId).build());
                }

            }
        }
        return result;
    }

    public static List<WmPoiSettleAuditedDB> extractWmPoiSettleAuditedDB(List<WmSettleAudited> wmSettleAuditedList) {
        List<WmPoiSettleAuditedDB> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(wmSettleAuditedList)) {
            return result;
        }
        int wmCustomerId = 0;
        List<Integer> wmPoiIdList = null;
        for (WmSettleAudited temp : wmSettleAuditedList) {
            wmCustomerId = temp.getWmCustomerId();
            wmPoiIdList = temp.getWmPoiIdList();
            if (CollectionUtils.isNotEmpty(wmPoiIdList)) {
                for (Integer wmPoiId : wmPoiIdList) {
                    result.add(WmPoiSettleAuditedDB.builder()
                            .wm_settle_id(temp.getWm_settle_id())
                            .wm_poi_id(wmPoiId)
                            .wm_contract_id(wmCustomerId).build());
                }
            }
        }
        return result;
    }

    public static WmSettleAuditedDB wmSettleAuditedThrift2DB(WmSettleAudited wmSettleAudited) {
        if (wmSettleAudited == null) {
            return null;
        }

        WmSettleAuditedDB wmSettleAuditedDB = new WmSettleAuditedDB();
        wmSettleAuditedDB.setId(wmSettleAudited.getId());
        wmSettleAuditedDB.setWm_contract_id(wmSettleAudited.getWm_contract_id());
        wmSettleAuditedDB.setWm_settle_id(wmSettleAudited.getWm_settle_id());
        wmSettleAuditedDB.setMin_pay_amount((float) wmSettleAudited.getMin_pay_amount());
        wmSettleAuditedDB.setPay_period_num(wmSettleAudited.getPay_period_num());
        wmSettleAuditedDB.setPay_period_unit((byte) wmSettleAudited.getPay_period_unit());
        wmSettleAuditedDB.setProvince(wmSettleAudited.getProvince());
        wmSettleAuditedDB.setCity(wmSettleAudited.getCity());
        wmSettleAuditedDB.setBankid(wmSettleAudited.getBankid());
        wmSettleAuditedDB.setBranchid(wmSettleAudited.getBranchid());
        wmSettleAuditedDB.setBranchname(wmSettleAudited.getBranchname());
        wmSettleAuditedDB.setAcc_cardno(wmSettleAudited.getAcc_cardno());
        wmSettleAuditedDB.setAcc_name(wmSettleAudited.getAcc_name());
        wmSettleAuditedDB.setAcctype(wmSettleAudited.getAcctype());
        wmSettleAuditedDB.setCuid(wmSettleAudited.getCuid());
        wmSettleAuditedDB.setMuid(wmSettleAudited.getMuid());
        wmSettleAuditedDB.setSettle_type(wmSettleAudited.getSettle_type());
        wmSettleAuditedDB.setPay_day_of_month(wmSettleAudited.getPay_day_of_month());
        wmSettleAuditedDB.setParty_a_finance_people(wmSettleAudited.getParty_a_finance_people());
        wmSettleAuditedDB.setParty_a_finance_phone(wmSettleAudited.getParty_a_finance_phone());
        wmSettleAuditedDB.setName(wmSettleAudited.getName());

        wmSettleAuditedDB.setCard_type(wmSettleAudited.getCard_type());
        wmSettleAuditedDB.setCert_type(wmSettleAudited.getCert_type());
        wmSettleAuditedDB.setLegal_cert_num(wmSettleAudited.getLegal_cert_num());
        wmSettleAuditedDB.setLegal_person(wmSettleAudited.getLegal_person());
        wmSettleAuditedDB.setLegal_id_card(wmSettleAudited.getLegal_id_card());
        wmSettleAuditedDB.setCert_num(wmSettleAudited.getCert_num());
        wmSettleAuditedDB.setReserve_phone(wmSettleAudited.getReserve_phone());
        wmSettleAuditedDB.setWm_wallet_id(wmSettleAudited.getWm_wallet_id());

        wmSettleAuditedDB.setWmCustomerId(wmSettleAudited.getWmCustomerId());

        return wmSettleAuditedDB;
    }

    public static Set<Integer> transWmSettleList2WmSettleIdSet(List<WmSettleDB> wmSettleList) {
        if (CollectionUtils.isEmpty(wmSettleList)) {
            return new HashSet<>();
        }

        Set<Integer> wmSettleIdSet = new HashSet<>();
        for (WmSettleDB wmSettle:wmSettleList) {
            if (wmSettle == null) {
                continue;
            }

            CollectionUtils.addIgnoreNull(wmSettleIdSet, wmSettle.getId());
        }
        return wmSettleIdSet;
    }

    public static Set<Integer> transWmSettleAudited2WmSettleIdSet(List<WmSettleAuditedDB> wmSettleAuditedList) {
        if (CollectionUtils.isEmpty(wmSettleAuditedList)) {
            return new HashSet<>();
        }

        Set<Integer> wmSettleIdSet = new HashSet<>();
        for (WmSettleAuditedDB wmSettleAudited:wmSettleAuditedList) {
            if (wmSettleAudited == null) {
                continue;
            }

            CollectionUtils.addIgnoreNull(wmSettleIdSet, wmSettleAudited.getWm_settle_id());
        }
        return wmSettleIdSet;
    }

    public static Map<Integer, WmSettleDB> transWmSettleDBList2Map(List<WmSettleDB> wmSettleList) {
        if (CollectionUtils.isEmpty(wmSettleList)) {
            return new HashMap<>();
        }

        Map<Integer, WmSettleDB> map = new HashMap<>();
        for (WmSettleDB wmSettle:wmSettleList) {
            if (wmSettle == null) {
                continue;
            }

            map.put(wmSettle.getId(), wmSettle);
        }
        return map;
    }

    public static WmSettleAuditedDB transWmSettleDB(WmSettleDB wmSettleDB) {
        if (wmSettleDB == null) {
            return null;
        }
        WmSettleAuditedDB wmSettleAuditedDB = new WmSettleAuditedDB();
        TransUtil.transferAll(wmSettleDB, wmSettleAuditedDB);
        wmSettleAuditedDB.setWm_settle_id(wmSettleDB.getId());
        return wmSettleAuditedDB;
    }

    public static WmSettleDBDiff transWmSettleDB2DiffBean(WmSettleAuditedDB wmSettleAuditedDB) {
        if (wmSettleAuditedDB == null) {
            return null;
        }
        WmSettleDBDiff wmSettleDBDiff = new WmSettleDBDiff();
        wmSettleDBDiff.setMinPayAmount(wmSettleAuditedDB.getMin_pay_amount());
        wmSettleDBDiff.setPayPeriodNum(wmSettleAuditedDB.getPay_period_num());
        wmSettleDBDiff.setPayPeriodUnit(wmSettleAuditedDB.getPay_period_unit());
        wmSettleDBDiff.setProvince(wmSettleAuditedDB.getProvince());
        wmSettleDBDiff.setCity(wmSettleAuditedDB.getCity());
        wmSettleDBDiff.setBankid(wmSettleAuditedDB.getBankid());
        wmSettleDBDiff.setBranchid(wmSettleAuditedDB.getBranchid());
        wmSettleDBDiff.setBranchname(wmSettleAuditedDB.getBranchname());
        wmSettleDBDiff.setAccCardno(wmSettleAuditedDB.getAcc_cardno());
        wmSettleDBDiff.setAccName(wmSettleAuditedDB.getAcc_name());
        wmSettleDBDiff.setAcctype(wmSettleAuditedDB.getAcctype());
        wmSettleDBDiff.setSettleType(wmSettleAuditedDB.getSettle_type());
        wmSettleDBDiff.setPayDayOfMonth(wmSettleAuditedDB.getPay_day_of_month());
        wmSettleDBDiff.setWmWalletId(wmSettleAuditedDB.getWm_wallet_id());
        wmSettleDBDiff.setWm_contract_id(wmSettleAuditedDB.getWm_contract_id());
        wmSettleDBDiff.setWm_settle_id(wmSettleAuditedDB.getWm_settle_id());
        wmSettleDBDiff.setName(wmSettleAuditedDB.getName());
        wmSettleDBDiff.setParty_a_finance_people(wmSettleAuditedDB.getParty_a_finance_people());
        wmSettleDBDiff.setParty_a_finance_phone(wmSettleAuditedDB.getParty_a_finance_phone());
        return wmSettleDBDiff;
    }

    public static Map<Integer, WmSettleAuditedDB> transWmSettleAuditedDBList2Map(
            List<WmSettleAuditedDB> wmSettleAuditedList) {
        if (CollectionUtils.isEmpty(wmSettleAuditedList)) {
            return new HashMap<>();
        }

        Map<Integer, WmSettleAuditedDB> map = new HashMap<>();
        for (WmSettleAuditedDB wmSettleAudited:wmSettleAuditedList) {
            if (wmSettleAudited == null) {
                continue;
            }

            map.put(wmSettleAudited.getWm_settle_id(), wmSettleAudited);
        }
        return map;
    }

    public static void assembleWmCardValidationInfoBo(WmSettle wmSettle) {
        if (wmSettle != null) {
            WmCardValidationInfoBo validationInfoBo = new WmCardValidationInfoBo();
            int cardValid = wmSettle.getCard_valid();
            if (cardValid == 0) {
                validationInfoBo.setCardStatus("success");
            } else if (cardValid == 1) {
                validationInfoBo.setCardStatus("failed");
                validationInfoBo.setFailedReason(wmSettle.getCard_invalid_reason());
            }
            wmSettle.setCardValidationInfoBo(validationInfoBo);
        }
    }


    public static List<WmSettlePoiWalletDB> wmSettlePoiWalletBo2DBlist(WmSettlePoiWalletBo wmSettlePoiWalletBo) {
        if (wmSettlePoiWalletBo == null) {
            return null;
        }

        List<WmSettlePoiWalletDB> wmSettlePoiWalletDBList = Lists.newArrayList();
        List<Integer> wmPoiIdList = wmSettlePoiWalletBo.getWmPoiIdList();

        for (Integer wmPoiId : wmPoiIdList) {
            WmSettlePoiWalletDB wmSettlePoiWalletDB = new WmSettlePoiWalletDB();
            wmSettlePoiWalletDB.setWm_poi_id(wmPoiId);
            wmSettlePoiWalletDB.setWm_settle_id(wmSettlePoiWalletBo.getWm_settle_id());
            wmSettlePoiWalletDB.setWm_wallet_id(wmSettlePoiWalletBo.getWm_wallet_id());
            wmSettlePoiWalletDB.setCtime(wmSettlePoiWalletBo.getCtime());
            wmSettlePoiWalletDB.setUtime(wmSettlePoiWalletBo.getUtime());
            wmSettlePoiWalletDB.setValid(wmSettlePoiWalletBo.getValid());

            wmSettlePoiWalletDBList.add(wmSettlePoiWalletDB);
        }
        return wmSettlePoiWalletDBList;
    }

    public static Map<Integer, WmSettleAudited> transList2Map(List<WmSettleAudited> wmSettleAuditedList) {
        if (wmSettleAuditedList == null) {
            return Maps.newHashMap();
        }

        return Maps.uniqueIndex(wmSettleAuditedList, new Function<WmSettleAudited, Integer>() {
            @Nullable
            @Override
            public Integer apply(@Nullable WmSettleAudited input) {
                return input.getWm_settle_id();
            }
        });
    }

    /**
     * 融合结算线上表数据和切换中心数据
     * @param source 未过滤门店的结算线上表数据
     * @param switchPoiInfoList 切换中心数据
     * @return
     */
    public static List<WmSettleAudited> aggregateWmSettleOnline(List<WmSettleAudited> source, List<SwitchPoiInfo> switchPoiInfoList) {
        LOGGER.info("#aggregateWmSettleOnline,source={},switchPoiInfoList={}", JSONObject.toJSONString(source),
                JSONObject.toJSONString(switchPoiInfoList));

        if (CollectionUtils.isEmpty(source) || CollectionUtils.isEmpty(switchPoiInfoList)) {
            return source;
        }
        Map<Integer, WmSettleAudited> sourceMap = Maps.newHashMap();
        for (WmSettleAudited temp : source) {
            sourceMap.put(temp.getWm_settle_id(), temp);
        }
        SwitchPoiSettleInfo switchPoiSettleInfo = null;
        Long switchSettleOnlineId = null;
        List<Integer> wmPoiIdList = null;
        Set<Integer> wmPoiIdTempSet = null;
        WmSettleAudited wmSettleAuditedTemp = null;
        for (SwitchPoiInfo temp : switchPoiInfoList) {
            switchPoiSettleInfo = WmPoiSwitchModuleEnum.SETTLE.toBean(temp.getModuleInfo().get(WmPoiSwitchModuleEnum.SETTLE));
            if (switchPoiSettleInfo != null) {
                switchSettleOnlineId = switchPoiSettleInfo.getOnlineWmSettleId();
                //将切换中心的关联信息合并到线上结算中
                if (switchSettleOnlineId != null && switchSettleOnlineId > 0 && sourceMap.containsKey(switchSettleOnlineId.intValue())) {
                    wmSettleAuditedTemp = sourceMap.get(switchSettleOnlineId.intValue());
                    wmPoiIdList = wmSettleAuditedTemp.getWmPoiIdList();
                    if (CollectionUtils.isNotEmpty(wmPoiIdList)) {
                        wmPoiIdTempSet = Sets.newHashSet(wmPoiIdList);
                        wmPoiIdTempSet.add((int) temp.getWmPoiId());
                    } else {
                        wmPoiIdTempSet = Sets.newHashSet((int) temp.getWmPoiId());
                    }
                    wmSettleAuditedTemp.setWmPoiIdList(Lists.newArrayList(wmPoiIdTempSet));
                }
            }
        }
        return source;
    }

    /**
     * 融合结算线下表数据和切换中心数据
     * @param source 未过滤门店的结算线下表数据
     * @param switchPoiInfoList 切换中心数据
     * @return
     */
    public static List<WmSettle> aggregateWmSettleOffline(List<WmSettle> source, List<SwitchPoiInfo> switchPoiInfoList) {
        if (CollectionUtils.isEmpty(source) || CollectionUtils.isEmpty(switchPoiInfoList)) {
            return source;
        }
        Map<Integer, WmSettle> sourceMap = Maps.newHashMap();
        for (WmSettle temp : source) {
            sourceMap.put(temp.getId(), temp);
        }

//        LOGGER.info("aggregateWmSettleOffline source:[{}]",JSON.toJSONString(source));

        SwitchPoiSettleInfo switchPoiSettleInfo = null;
        Long switchSettleOfflineId = null;
        List<Integer> wmPoiIdList = null;
        Set<Integer> wmPoiIdTempSet = Sets.newHashSet();
        WmSettle wmSettleTemp = null;
        for (SwitchPoiInfo temp : switchPoiInfoList) {
            switchPoiSettleInfo = WmPoiSwitchModuleEnum.SETTLE.toBean(temp.getModuleInfo().get(WmPoiSwitchModuleEnum.SETTLE));
            if (switchPoiSettleInfo != null) {
                switchSettleOfflineId = switchPoiSettleInfo.getOfflineWmSettleId();
                //将切换中心的关联信息合并到线下结算中
                if (switchSettleOfflineId != null && switchSettleOfflineId > 0 && sourceMap.containsKey(switchSettleOfflineId.intValue())) {
                    wmSettleTemp = sourceMap.get(switchSettleOfflineId.intValue());
                    wmPoiIdList = wmSettleTemp.getWmPoiIdList();
                    if(CollectionUtils.isNotEmpty(wmPoiIdList)){
                        wmPoiIdTempSet = Sets.newHashSet(wmPoiIdList);
                        wmPoiIdTempSet.add((int)temp.getWmPoiId());
                    }else{
                        wmPoiIdTempSet = Sets.newHashSet((int)temp.getWmPoiId());
                    }
                    wmSettleTemp.setWmPoiIdList(Lists.newArrayList(wmPoiIdTempSet));
                }
            }
        }
        return source;
    }

    public static Pair<List<Integer>, List<Integer>> aggregateWmSettlePoiPair(Pair<List<Integer>, List<Integer>> pair,
                                                                              List<SwitchPoiInfo> switchPoiInfoList, boolean isEffective) {
        if (pair == null || CollectionUtils.isEmpty(switchPoiInfoList)) {
            return pair;
        }
        List<Integer> wmPoiIdList = pair.getRight();
        SwitchPoiSettleInfo switchPoiSettleInfo = null;
        for (SwitchPoiInfo temp : switchPoiInfoList) {
            switchPoiSettleInfo = WmPoiSwitchModuleEnum.SETTLE.toBean(temp.getModuleInfo().get(WmPoiSwitchModuleEnum.SETTLE));
            if (switchPoiSettleInfo != null) {
                if (!isEffective && switchPoiSettleInfo.getOfflineWmSettleId() != null && switchPoiSettleInfo.getOfflineWmSettleId() > 0) {
                    wmPoiIdList.add((int) temp.getWmPoiId());
                } else if (isEffective && switchPoiSettleInfo.getOnlineWmSettleId() != null && switchPoiSettleInfo.getOnlineWmSettleId() > 0) {
                    wmPoiIdList.add((int) temp.getWmPoiId());
                }
            }
        }
        return pair;
    }

    /**
     * 门店集合在切换中心匹配是否有关联的结算ID
     * @param wmPoiIdList
     * @param switchPoiInfoList
     * @param isEffective
     * @return
     */
    public static List<Integer> getMatchedWmPoiSettleFromSwitchCentre(List<Integer> wmPoiIdList, List<SwitchPoiInfo> switchPoiInfoList,
                                                                      boolean isEffective) {
        List<Integer> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(wmPoiIdList) || CollectionUtils.isEmpty(switchPoiInfoList)) {
            return result;
        }
        SwitchPoiSettleInfo switchPoiSettleInfo = null;
        Long offlineWmSettleId = null;
        Long onlineWmSettleId = null;

        for (SwitchPoiInfo temp : switchPoiInfoList) {
            switchPoiSettleInfo = WmPoiSwitchModuleEnum.SETTLE.toBean(temp.getModuleInfo().get(WmPoiSwitchModuleEnum.SETTLE));
            if (switchPoiSettleInfo != null) {
                offlineWmSettleId = switchPoiSettleInfo.getOfflineWmSettleId();
                onlineWmSettleId = switchPoiSettleInfo.getOnlineWmSettleId();
                if (!isEffective && offlineWmSettleId != null && offlineWmSettleId > 0 && wmPoiIdList.contains((int) temp.getWmPoiId())) {
                    result.add(offlineWmSettleId.intValue());
                } else if (isEffective && onlineWmSettleId != null && onlineWmSettleId > 0 && wmPoiIdList.contains((int) temp.getWmPoiId())) {
                    result.add(onlineWmSettleId.intValue());
                }
            }
        }
        return result;
    }
}

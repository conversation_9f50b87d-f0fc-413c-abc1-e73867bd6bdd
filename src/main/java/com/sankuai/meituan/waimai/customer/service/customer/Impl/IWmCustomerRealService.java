package com.sankuai.meituan.waimai.customer.service.customer.Impl;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerNumberRepeatVo;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import com.sankuai.meituan.waimai.thrift.customer.constant.BusinessTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerRelationStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.WmCustomerOplogBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.BooleanResult;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.mq.CustomerMQEventEnum;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiLabelRel;
import com.sankuai.meituan.waimai.thrift.exception.MtCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import com.sankuai.nibcus.inf.customer.client.enums.BusinessLineEnum;
import org.apache.thrift.TException;

import java.util.*;

/**
 *
 */
public interface IWmCustomerRealService {

    /**
     * 保存或修改客户
     *
     * @param wmCustomerBasicBo
     * @param force
     * @param opUid
     * @param opName
     * @throws WmCustomerException
     * @throws TException
     */
    public ValidateResultBo saveOrUpdateCustomer(WmCustomerBasicBo wmCustomerBasicBo, Boolean force, Integer opUid, String opName, int channel) throws WmCustomerException, TException;

    /**
     * 获取客户详细信息，包括审核diff
     *
     * @param customerId
     * @return
     * @throws TException
     * @throws WmCustomerException
     */
    public WmCustomerDetailBo getCustomerWithAuditById(Integer customerId) throws TException, WmCustomerException;

    /**
     * 获取客户标签信息
     *
     * @param customerId
     * @param userId
     * @return
     * @throws TException
     * @throws WmCustomerException
     */
    List<WmCustomerLabelBo> getCustomerLabelInfo(int customerId, int userId) throws TException, WmCustomerException;

    /**
     * 获取客户标签信息，批量
     *
     * @param customerIdList
     * @param userId
     * @return
     * @throws TException
     * @throws WmCustomerException
     */
    Map<Integer, List<WmCustomerLabelBo>> getCustomerLabelInfoBatch(List<Integer> customerIdList, int userId) throws TException, WmCustomerException;

    /**
     * 获取客户列表
     *
     * @param wmCustomerFormBo
     * @return
     */
    public WmCustomerPageDate getCustomerList(WmCustomerFormBo wmCustomerFormBo) throws WmCustomerException, TException;

    /**
     * 获取到餐客户列表
     * @param wmCustomerFormBo
     * @return
     */
    WmCustomerPageDate getDcCustomerList(WmCustomerFormBo wmCustomerFormBo) throws WmCustomerException, TException;

    public boolean checkPoiQuaForCustomerAudit(int customerId, int opUid, String opName) throws WmCustomerException, TException;

    /**
     * 审核回调处理
     *
     * @param wmCustomerAuditBo
     * @throws WmCustomerException
     */
    public void auditCustomerCallBack(WmCustomerAuditBo wmCustomerAuditBo) throws WmCustomerException;

    /**
     * 检查客户有效性
     *
     * @param id
     * @return
     * @throws WmCustomerException
     */
    public Boolean checkCustomerEffect(Integer id) throws WmCustomerException;

    /**
     * 插入客户信息
     *
     * @param wmCustomerBasicBo
     * @return
     */
    public Integer insertCustomer(WmCustomerBasicBo wmCustomerBasicBo, int channel,Integer opUid) throws WmCustomerException;


    /**
     * 删除客户信息
     *
     * @param customerId
     */
    public void deleteCustomer(Integer customerId, Integer opUid, String opName) throws WmCustomerException, TException;

    /**
     * 解绑该客户的上级客户信息
     *
     * @param customerId
     * @return
     * @throws TException
     * @throws WmCustomerException
     */
    public boolean unBindSuperCustomer(Integer customerId, Integer opUid, String opName) throws TException, WmCustomerException;

    /**
     * 根据客户ID查询客户信息
     *
     * @param id
     * @return
     */
     WmCustomerDB selectCustomerById(Integer id) throws WmCustomerException;

    /**
     * 根据客户ID查询客户信息走从库
     *
     * @param id
     * @return
     */
     WmCustomerDB selectPlatformCustomerByIdFromSlave(Integer id) throws WmCustomerException;

    /**
     * 专门为灰度策略提供顾客信息查询的服务
     *
     * @param id
     * @return
     */
    Integer selectCustomerById4Grey(Integer id);

    public WmCustomerAddressBo getCustomerAddressForAutoBringIn(int customerId, int wmPoiId) throws TException, WmCustomerException;


    /**
     * 根据id批量查询客户信息
     *
     * @param customerIdSet
     * @return
     */
    public List<WmCustomerDB> selectCustomerByIds(Set<Integer> customerIdSet) throws WmCustomerException;

    /**
     * 查找该上级客户Id下的所有ID。
     * 列表数量不会太大
     *
     * @param superCustomerId
     * @return
     */
    public List<WmCustomerDB> selectCustomerBySuperCustomerId(Integer superCustomerId) throws WmCustomerException;

    /**
     * 根据客户ID查询客户信息(查主库)
     *
     * @param id
     * @return
     */
    public WmCustomerDB selectCustomerByIdRT(Integer id) throws WmCustomerException;


    /**
     * 根据客户ID查询生效客户数据
     *
     * @param id
     * @return
     */
    public WmCustomerDB selectEffectCustomerById(Integer id) throws WmCustomerException;

    /**
     * 根据门店ID查询客户信息
     *
     * @param wmPoiId
     * @return
     */
    public WmCustomerDB selectCustomerByWmPoiId(Long wmPoiId) throws WmCustomerException;


    /**
     * 插入客户审核信息
     *
     * @param wmCustomerBasicBo
     * @param effective
     * @param ownerUid
     * @return
     */
    public Integer insertCustomerAudit(WmCustomerBasicBo wmCustomerBasicBo, Integer effective, Integer ownerUid, Integer batchSubmit, Integer opUid, String opName) throws WmCustomerException;

    /**
     * 插入客户审核信息
     *
     * @param wmCustomerDB
     * @return
     */
    public Integer insertCustomerAudit(WmCustomerDB wmCustomerDB, Integer batchSubmit, Integer opUid, String opName) throws WmCustomerException;

    /**
     * 更新提审信息为已打包提审
     *
     * @param customerId
     * @throws TException
     * @throws WmCustomerException
     */
    public void packageCommitAudited(int customerId) throws WmCustomerException;

    public WmCustomerBatchAuditBo commitAudit(Integer customerId, Integer opUid, String opName, Boolean force) throws WmCustomerException, TException;

    public Integer commitAuditHeron(Integer customerId, Integer opUid, String opName, Boolean force) throws WmCustomerException, TException;

    /**
     * 提交审核
     *
     * @param wmCustomerDB
     * @param bizId
     * @param opUid
     * @param force
     * @return
     * @throws WmCustomerException
     */
    public Integer commitAudit(WmCustomerDB wmCustomerDB, Integer bizId, Integer opUid, String opName, Boolean force) throws WmCustomerException;

    /**
     * 插入客户操作日志
     *
     * @param customerId
     * @param userId
     * @param userName
     * @param wmCustomerBasicBo
     * @param sourceCustomer
     * @param opType
     * @throws WmCustomerException
     * @throws TException
     */
    public void insertCustomerOpLog(Integer customerId, Integer userId, String userName, WmCustomerBasicBo wmCustomerBasicBo, WmCustomerDB sourceCustomer, WmCustomerOplogBo.OpType opType) throws WmCustomerException, TException;

    /**
     * 插入客户操作日志
     *
     * @param customerId
     * @param userId
     * @param userName
     * @param opType
     * @param log
     * @throws WmCustomerException
     * @throws TException
     */
    public void insertCustomerOpLog(Integer customerId, Integer userId, String userName, WmCustomerOplogBo.OpType opType, String log) throws WmCustomerException;

    /**
     * 插入客户操作日志
     *
     * @param customerId
     * @param userId
     * @param userName
     * @param opType
     * @param log
     * @param remark
     * @throws WmCustomerException
     * @throws TException
     */
    public void insertCustomerOpLog(Integer customerId, Integer userId, String userName, WmCustomerOplogBo.OpType opType, String log, String remark) throws WmCustomerException;


    /**
     * 分配责任人
     *
     * @param customerIdList
     * @param userId
     */
    public void distributeCustomer(List<Integer> customerIdList, int userId, Integer opUid, String opName) throws WmCustomerException;

    /**
     * 客户责任人变更发送大象消息, 异步的方式 。
     *
     * @param customerList
     * @param userId       新责任人
     * @param opUid        操作人id
     * @return
     */
    public void sendOwnerChangeMsgAsyn(final List<WmCustomerDB> customerList, final int userId, final Integer opUid);

    /**
     * @param wmCustomerDB
     * @param newOwner
     * @param operator     操作人
     * @return
     */
    public boolean sendOwnerChangeMsg(WmCustomerDB wmCustomerDB, WmEmploy newOwner, WmEmploy operator) throws TException, WmServerException;

    /**
     * 变更客户责任人(员工离职)
     *
     * @param oldOwnerUid
     * @param newOwnerUid
     */
    public void changeCustomerOwner(Integer oldOwnerUid, Integer newOwnerUid) throws WmCustomerException;


    public void changeCustomerOwnerUidByWmPoi(Long wmPoiId, int ownerUid, int opUid, String opUname) throws WmCustomerException;

    /**
     * 通过关键字查询客户列表(用于下拉提示)
     *
     * @param keyword
     * @param searchType
     */
    public List<WmCustomerDB> selectCustomerListByKeyword(String keyword, Integer searchType, int isLeaf) throws WmCustomerException;

    /**
     * 通过客户Ids查询客户责任人
     *
     * @param customerIdList
     * @return
     */
    public List<WmCustomerDB> selectCustomerOwnUidList(List<Integer> customerIdList) throws WmCustomerException;

    /**
     * 保存客户共用资质证明
     *
     * @param customerId
     * @param urlSet
     * @param otherUrlSet
     * @param opUid
     * @param opName
     * @throws WmCustomerException
     */
    public void saveOrUpdateCustomerCommonQua(int customerId, Set<String> urlSet, Set<String> otherUrlSet, int opUid, String opName) throws WmCustomerException;

    /**
     * 增加客户共用资质证明
     *
     * @param customerId
     * @param commonQuaUrlSet
     * @param otherUrlSet
     * @param opUid
     * @param opName
     * @throws WmCustomerException
     */
    public void addCustomerCommonQuaAndOther(int customerId, Set<String> commonQuaUrlSet, Set<String> otherUrlSet, int opUid, String opName) throws WmCustomerException;

    public void saveOrUpdateCustomerCommonQuaToDB(int customerId, Set<String> urlSet, Set<String> otherUrlSet) throws WmCustomerException;

    public void saveOrUpdateCustomerCommonQuaOtherToDB(int customerId, Set<String> otherUrlSet) throws WmCustomerException;


    /**
     * 获取客户共用资质证明列表
     *
     * @param customerId
     * @return
     */
    public Set<String> getCustomerQuaList(Integer customerId);

    /**
     * 获取客户其他附件列表
     *
     * @param customerId
     * @return
     */
    public List<String> getCustomerQuaOtherList(Integer customerId);

    /**
     * 获取客户其他附件列表
     *
     * @param customerId
     * @return
     */
    public Set<String> getCustomerQuaOtherListMaster(Integer customerId);

    /**
     * 发送客户状态MQ
     *
     * @param customerId
     * @param customerMQEventEnum
     * @param wmPoiIdSet
     * @param opUid
     * @param opName
     */
    public void sendCustomerStatusNoticeMQ(Integer customerId, CustomerMQEventEnum customerMQEventEnum, Set<Long> wmPoiIdSet, Integer opUid, String opName);

    public void sendCustomerStatusNoticeMQWithExtension(Integer customerId, CustomerMQEventEnum customerMQEventEnum, Set<Long> wmPoiIdSet, Integer opUid, String opName, Map<String, Object> extension);

    /**
     * 发送客户状态MQ
     *
     * @param customerId
     * @param customerMQEventEnum
     * @param extraData
     */
    public void sendCustomerStatusNoticeMQ(Integer customerId, CustomerMQEventEnum customerMQEventEnum, JSONObject extraData);


    public boolean saveOrUpdateCustomerOtherScan(Integer customerId, List<String> urlList, int opUid, String opName) throws WmCustomerException;

    /**
     * 查询客户类型
     *
     * @param userId
     * @return
     * @throws TException
     * @throws WmCustomerException
     * @throws WmServerException
     */
    public WmCustomerRealTypeAggreDTO getCustomerRealType(Integer userId, int authType) throws TException, WmCustomerException, WmServerException;


    /**
     * 通过编号和客户类型获取id大于1一千万的客户
     *
     * @param customerNum
     * @param customerType
     * @return
     * @throws TException
     * @throws WmCustomerException
     */
    public WmCustomerBasicBo getNewCustomerByNumberAndType(String customerNum, int customerType) throws TException, WmCustomerException;

    /**
     * 根据客户ID、客户类型以及业务线查询客户信息
     *
     * @param customerNum
     * @param customerType
     * @param bizCode
     * @return
     * @throws TException
     * @throws WmCustomerException
     */
    WmCustomerBasicBo getCustomerByNumberAndTypeAndBizCode(String customerNum, int customerType, int bizCode) throws TException, WmCustomerException;

    public WmCustomerBasicBo getCustomerByIdOrMtCustomerId(long customerId) throws TException, WmCustomerException;

    public List<WmCustomerBasicBo> getCustomerListByIdOrMtCustomerId(Set<Long> customerIdSet) throws WmCustomerException;

    /**
     * 更新客户信息到美团客户平台
     *
     * @param customerId 客户ID
     * @return
     * @throws TException
     */
    public boolean updateCustomerToMtCustomer(long customerId) throws TException, WmCustomerException;

    /**
     * 通过门店ID批量查客户信息
     *
     * @param wmPoiIds
     * @return
     */
    Map<Long, WmCustomerBasicBo> getCustomerByWmPoiIds(List<Long> wmPoiIds) throws TException, WmCustomerException;

    /**
     * 更新客户信息到美团客户平台
     */
    public long updateMtCustomer(WmCustomerDB wmCustomerDB, boolean isUploadImg) throws MtCustomerException;


    /**
     * 校验审核是否可修改客户资质
     */
    ValidateResultBo validCustomerUpdate(String data, int customerId, int opUid, String opName) throws TException, WmCustomerException;

    /**
     * 审核回调&&修改客户
     */
    void updateAndCallBack(WmCustomerAuditBo wmCustomerAuditBo, int opUid, String opName) throws TException, WmCustomerException;


    void washCustmerRealType(List<Integer> customerIdList, int washNum, String misId) throws WmCustomerException,
            TException;

    /**
     * 根据门店ID查询最新客户数据，审核中、生效
     *
     * @param wmPoiId
     * @return
     * @throws TException
     * @throws WmCustomerException
     */
    WmCustomerBasicBo getLatestWmCustomerBasicBo(Long wmPoiId) throws TException, WmCustomerException;

    /**
     * 根据wmPoiId，获取门店为上单中、审核通过待上线、上线的门店数量。并且对门店进行了去重
     *
     * @param wmPoiIdList
     * @return
     */
    List<Long> getActivePoiList(List<Long> wmPoiIdList);

    /**
     * 通过美团客户id查询外卖客户id
     *
     * @param mtCustomerId
     * @return
     */
    long getWmCustomerIdByMtCustomerId(long mtCustomerId);

    /**
     * 通过外卖客户id查询美团客户id
     */
    long getMtCustomerIdByWmCustomerId(long wmCustomerId);

    /**
     * 客户打标，调用打标平台
     */
    void batchAddCustomerLabel(Long wmCustomerId, Long labelId, Integer opUid, String opName) throws WmCustomerException, TException;

    /**
     * 查询客户的标签信息
     */
    WmPoiLabelRel queryCustomerLabelInfo(Long wmCustomerId, Long labelId) throws WmCustomerException, TException;

    /**
     * 判断某个客户是否包含某个标签
     */
    boolean customerLabelTypeJudge(Long wmCustomerId, Long labelId) throws WmCustomerException, TException;

    /**
     * 获取客户标签列表
     */
    List<WmCustomerLabelTypeBo> getCustomerLabelTypeList(Integer opUid) throws TException, WmCustomerException;

    /**
     * 获取客户标签列表，指定客户id
     */
    List<WmCustomerLabelBo> getCustomerLabelList(Long wmCustomerId, Integer opUid) throws TException, WmCustomerException;

    /**
     * 客户标签变更更新es
     */
    void customerLabelSync(Long mtCustomerId, Integer opUid, String opUname, Long wmCustomerLabelId) throws TException, WmCustomerException;

    /**
     * 根据客户id获取客户标签名称
     */
    List<WmCustomerLabelBo> queryCustomerLabelNamesByCustomerId(Integer wmCustomerId, Integer opUid) throws TException, WmCustomerException;

    /**
     * 通过外卖客户ID查询配送公司客户ID
     */
    Long queryBmCompanyCustomerIdByWmCustomerId(long wmCustomerId) throws TException, WmCustomerException;

    /**
     * 通过外卖门店ID查询配送门店客户ID
     */
    Long queryBmPoiCustomerIdByWmPoiId(long wmPoiId) throws TException, WmCustomerException;

    /**
     * 通过外卖门店id查询外卖客户id
     */
    Integer selectWmCustomerIdByWmPoiId(Long wmPoiId);

    /**
     * 同步指定客户的资质到客户平台
     *
     * @param mtCustomerId
     * @return
     */
    long syncMtCustomerQua(long mtCustomerId);

    void sendBindOrUnBindMessageToOwnersAndInsertOpLog(WmEmploy operator, Integer customerId, Integer newSuperWmCustomerId, Integer oldSuperCustomerId) throws WmCustomerException;

    /**
     * 获取客户列表
     *
     * @param queryDTO
     * @return
     */
    List<WmCustomerBasicBo> getCustomerListByDB(WmCustomerQueryDTO queryDTO) throws WmCustomerException, TException;

    /**
     * 客户过期处理，如果过期则标记为过期
     *
     * @param wmCustomerId
     * @param opUid
     * @param opName
     * @return
     * @throws TException
     * @throws WmCustomerException
     */
    BooleanResult overdueCustomer(long wmCustomerId, WmCustomerBasicBo wmCustomerBasicBo, Integer opUid, String opName) throws TException, WmCustomerException;

    /**
     * 客户后置状态处理
     *
     * @param wmCustomerBasicBo
     * @param oldWmCustomerDB
     * @param customerId
     * @param opUid
     * @param opName
     */
    void doPostByCustomerStatus(WmCustomerBasicBo wmCustomerBasicBo, WmCustomerDB oldWmCustomerDB, Integer customerId, Integer opUid, String opName);


    /**
     * 校验一级类型下客户编号唯一
     *
     * @param wmCustomerNumberRepeatVo
     * @return
     */
    WmCustomerDB validateCustomerNumber(WmCustomerNumberRepeatVo wmCustomerNumberRepeatVo) throws WmCustomerException;

    /**
     * 校验一级类型下客户编号唯一
     *
     * @param wmCustomerNumberRepeatVo
     * @return
     */
    List<WmCustomerDB> validateCustomerNumberNew(WmCustomerNumberRepeatVo wmCustomerNumberRepeatVo) throws WmCustomerException;

    /**
     * 验证realTypeSpInfo
     * @param wmCustomerBasicBo
     * @param opUid
     * @return
     * @throws TException
     * @throws WmCustomerException
     * @throws WmServerException
     */
    ValidateResultBo validateCustomerRealTypeSpInfo(WmCustomerBasicBo wmCustomerBasicBo, Integer opUid) throws TException, WmCustomerException, WmServerException;

    CustomerRelationStatusEnum getWmPoiRelationStatus(long wmPoiId);

    WmCustomerPoiBo getWmCustomerIdAndWmPoiRelationStatus(long wmPoiId);

    BooleanResult canReleaseFromPreBindStatus(long wmPoiId);

    BooleanResult hasBindCustomerSuccess(long wmPoiId);

    WmCustomerBasicBo getPreBindCustomerByWmPoiId(long wmPoiId) throws TException, WmCustomerException;

    /**
     * 客户管理批量分配责任人-筛选出的全部数据
     *
     * @param wmCustomerFormBo
     * @return
     */
    List<Long> getMtCustomerIds(WmCustomerFormBo wmCustomerFormBo) throws WmCustomerException, TException;

    /**
     * 查询客户的经营形式
     * @return
     */
    Set<Long> getCustomerBusinessLine(WmCustomerDB wmCustomerDB);

    /**
     * 根据关键词查询客户信息
     * @param searchCustomerConditionBO
     * @return
     */
    List<WmCustomerDB> searchCustomerListByCondition(SearchCustomerConditionBO searchCustomerConditionBO) throws WmCustomerException;



}

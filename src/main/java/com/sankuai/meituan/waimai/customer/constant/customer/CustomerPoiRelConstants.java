package com.sankuai.meituan.waimai.customer.constant.customer;

import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerRealTypeEnum;

import java.util.List;

/**
 * 客户门店关系流程处理使用的常量定义
 */
public class CustomerPoiRelConstants {

    /**
     * 单店客户类型组合：外卖单店、医药单店、闪购单店
     */
    public static final List<Integer> SingleCustomerTypes = Lists.newArrayList(CustomerRealTypeEnum.DANDIAN.getValue(),
            CustomerRealTypeEnum.DANDIAN_YAOPIN.getValue(), CustomerRealTypeEnum.DANDIAN_SG.getValue());

    /**
     * 非单店、美食城、食堂、聚合配送商、食堂承包商以外的其他客户类型集合
     */
    public static final List<Integer> otherCustomerRealTypes = Lists.newArrayList(CustomerRealTypeEnum.DALIANSUO.getValue(),
            CustomerRealTypeEnum.XIAOLIANSUO.getValue(), CustomerRealTypeEnum.LIULIANGLIANSUO.getValue(),
            CustomerRealTypeEnum.ZONGBU_SHANGCHAO_LIANSUO.getValue(), CustomerRealTypeEnum.ZONGBU_SHENGXIAN_LIANSUO.getValue(),
            CustomerRealTypeEnum.ZONGBU_YAOPIN_LIANSUO.getValue(), CustomerRealTypeEnum.ZONGBU_XIANHUA_LIANSUO.getValue(),
            CustomerRealTypeEnum.ZONGBU_MUYING_LIANSUO.getValue(), CustomerRealTypeEnum.ZONGBU_MEIZHUANG_LIANSUO.getValue(),
            CustomerRealTypeEnum.ZONGBU_FUSHIXIEMAO_LIANSUO.getValue(), CustomerRealTypeEnum.ZONGBU_RIYONGPIN_LIANSUO.getValue(),
            CustomerRealTypeEnum.B2C_DRUG.getValue(), CustomerRealTypeEnum.SG_CKA.getValue(),CustomerRealTypeEnum.DAOCAN.getValue(),CustomerRealTypeEnum.DAIGOU.getValue());

    /**
     * 绑定规则策略事件
     */
    public static final String BIND_RULE_STRATEGY_EVENT = "bindRuleStrategyEvent";

    /**
     * 绑定规则未匹配到策略
     */
    public static final String BIND_RULE_NO_MATCH_STRATEGY = "bindRuleNoMatchStrategy";


    /**
     * 绑定签约回调规则未匹配到策略
     */
    public static final String BIND_SIGN_CALL_BACK_NO_MATCH_STRATEGY = "bindSignCallBackNoMatchStrategy";

    /**
     * 解绑规则策略事件
     */
    public static final String UNBIND_RULE_STRATEGY_EVENT = "unBindRuleStrategyEvent";

    /**
     * 解绑规则未匹配到策略
     */
    public static final String UNBIND_RULE_NO_MATCH_STRATEGY = "unBindRuleNoMatchStrategy";


    /**
     * 解绑签约回调规则未匹配到策略
     */
    public static final String UNBIND_SIGN_CALL_BACK_NO_MATCH_STRATEGY = "unBindSignCallBackNoMatchStrategy";


}

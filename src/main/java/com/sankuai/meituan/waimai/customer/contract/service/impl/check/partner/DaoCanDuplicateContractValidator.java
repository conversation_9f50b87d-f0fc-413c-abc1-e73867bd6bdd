package com.sankuai.meituan.waimai.customer.contract.service.impl.check.partner;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.contract.dao.WmTempletContractDBMapper;
import com.sankuai.meituan.waimai.customer.contract.domain.WmTempletContractDB;
import com.sankuai.meituan.waimai.customer.contract.service.IContractValidator;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmContractSignService;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.SignType;
import com.sankuai.meituan.waimai.thrift.customer.constant.WmTempletContractTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractBasicBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractSignBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractTypeBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.partner.DaoCanContractInfo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description: 到餐合同重复校验
 * @author: liuyunjie05
 * @create: 2024/8/19 11:00
 */
@Slf4j
@Service
public class DaoCanDuplicateContractValidator implements IContractValidator {

    @Resource
    private WmTempletContractDBMapper wmTempletContractDBMapper;

    @Resource
    private WmContractSignService wmContractSignService;

    private static final List<Integer> DAO_CAN_CONTRACT_TYPE_CODE_LIST = Lists.newArrayList(
            WmTempletContractTypeEnum.DAOCAN_SERVICE_C1_CONTRACT.getCode(),
            WmTempletContractTypeEnum.DAOCAN_SERVICE_C2_CONTRACT.getCode()
    );

    @Override
    public boolean valid(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException {
        if (!DAO_CAN_CONTRACT_TYPE_CODE_LIST.contains(contractBo.getBasicBo().getType())) {
            return true;
        }
        WmTempletContractBasicBo basicBo = contractBo.getBasicBo();
        List<WmTempletContractDB> wmTempletContractDBList
                = wmTempletContractDBMapper.selectValidByParentIdAndType((long) basicBo.getParentId(), basicBo.getType());
        if (CollectionUtils.isEmpty(wmTempletContractDBList)) {
            return true;
        }
        validateContracts(contractBo, wmTempletContractDBList);
        return true;
    }

    private void validateContracts(WmCustomerContractBo contractBo, List<WmTempletContractDB> wmTempletContractDBList) throws WmCustomerException {
        WmTempletContractTypeBo typeBo = new WmTempletContractTypeBo(contractBo.getBasicBo().getType());
        SignType signType = SignType.getByCode(typeBo.getSignType());
        // 到餐C1不进行任何校验
        if (typeBo.getCooperateMode() == WmTempletContractTypeBo.COOPERATEMODE_DAOCAN_SERVICE_C1_CONTRACT) {
            return;
        }
        if (typeBo.getCooperateMode() == WmTempletContractTypeBo.COOPERATEMODE_DAOCAN_SERVICE_C2_CONTRACT) {
            validateDcC2Contract(contractBo, wmTempletContractDBList, signType);
        }
    }

    private void validateDcC1Contract(WmCustomerContractBo contractBo, List<WmTempletContractDB> wmTempletContractDBList) throws WmCustomerException {
        DaoCanContractInfo dcContractInfo = contractBo.getBasicBo().getDaoCanContractInfo();
        // 换签场景不需要判断
        if (!dcContractInfo.isNewSignContract()) {
            return;
        }
        // 新签不能重复发起
        for (WmTempletContractDB templetContractDB : wmTempletContractDBList) {
            DaoCanContractInfo dcContractInfoInDB = JSONObject.parseObject(templetContractDB.getBizData(), DaoCanContractInfo.class);
            if (dcContractInfoInDB.isNewSignContract()) {
                String msg = String.format("提交失败，该客户已有与美团的%s，请勿重复添加", WmTempletContractTypeEnum.DAOCAN_SERVICE_C1_CONTRACT.getMsg());
                throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, msg);
            }
        }
    }

    private void validateDcC2Contract(WmCustomerContractBo contractBo, List<WmTempletContractDB> wmTempletContractDBList, SignType signType) throws WmCustomerException {
        WmTempletContractSignBo modifyPartyBSigner = wmContractSignService.getPartyBSigner(contractBo.getSignBoList());

        for (WmTempletContractDB wmTempletContractDB : wmTempletContractDBList) {
            // 校验线上表
            WmTempletContractSignBo auditedPartyBSignerInDb = wmContractSignService.getAuditedPartyBSignerWithOutSignPhone(wmTempletContractDB.getId());
            if (auditedPartyBSignerInDb != null && auditedPartyBSignerInDb.getSignId() == modifyPartyBSigner.getSignId()) {
                String msg = String.format("提交失败，该客户与合作商[%s]已存在%s合同，请勿重复添加", getAgentName(wmTempletContractDB.getId()), signType.getDesc());
                throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, msg);
            }

            // 校验线下表
            WmTempletContractSignBo offlinePartyBSignerInDb = wmContractSignService.getPartyBSignerWithOutSignPhone(wmTempletContractDB.getId());
            if (offlinePartyBSignerInDb != null && offlinePartyBSignerInDb.getSignId() == modifyPartyBSigner.getSignId()) {
                String msg = String.format("提交失败，该客户与合作商[%s]已存在%s合同，请勿重复添加", getAgentName(wmTempletContractDB.getId()), signType.getDesc());
                throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, msg);
            }
        }
    }

    private String getAgentName(long templetId) {
        WmTempletContractSignBo partyBSigner = wmContractSignService.getPartyBSignerWithOutSignPhone(templetId);
        return partyBSigner == null ? "" : partyBSigner.getSignName();
    }

}

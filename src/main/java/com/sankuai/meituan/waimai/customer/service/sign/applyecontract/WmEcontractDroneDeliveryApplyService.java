package com.sankuai.meituan.waimai.customer.service.sign.applyecontract;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper.WmEcontractAreaDataWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignFlowConstant;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateEnum;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.constants.EcontractApplySubDataEnum;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.EcontractApplySubDataBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchDeliveryInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import org.apache.commons.collections.MapUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.ca.WmEcontractCAHNLXWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.ca.WmEcontractCAPoiWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper.WmEcontractDateWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.sms.WmEcontractSmsWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.stamp.WmEcontractStampHNLXWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.stamp.WmEcontractStampMTWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.stamp.WmEcontractStampPoiWrapperService;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.EcontractBatchBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageBatchInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataWrapperEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;

import static com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateConstant.TAB_DRONE_DELIVERY;
import static com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateConstant.TAB_DRONE_DELIVERY_PERFORMANCE_SERVICE;

/**
 * 无人机配送信息组装
 *
 * @Author: wangyongfang
 * @Date: 2024-05-09
 */
@Service
public class WmEcontractDroneDeliveryApplyService extends AbstractWmEcontractApplyAdapterService  {
    private static final String DRONE_DELIVERY = EcontractTaskApplyTypeEnum.DRONE_DELIVERY.getName();
    private static List<String> flowList = Lists.newArrayList();
    private static List<String> poiStampList = Lists.newArrayList();
    private static List<String> mtStampList = Lists.newArrayList();
    private static Map<String, EcontractDataWrapperEnum> dataWrapperMap = Maps.newHashMap();
    static {
        flowList.add(TAB_DRONE_DELIVERY);
        flowList.add(TAB_DRONE_DELIVERY_PERFORMANCE_SERVICE);
        poiStampList.add(TAB_DRONE_DELIVERY);
        poiStampList.add(TAB_DRONE_DELIVERY_PERFORMANCE_SERVICE);
        dataWrapperMap.put(TAB_DRONE_DELIVERY, EcontractDataWrapperEnum.DRONE_DELIVERY);
        dataWrapperMap.put(TAB_DRONE_DELIVERY_PERFORMANCE_SERVICE, EcontractDataWrapperEnum.DRONE_PERFORMANCE_SERVICE_CONTRACT);
    }
    @Resource
    private WmEcontractDateWrapperService wmEcontractDateWrapperService;
    @Resource
    private WmEcontractCAPoiWrapperService wmEcontractCAPoiWrapperService;
    @Resource
    private WmEcontractSmsWrapperService wmEcontractSmsWrapperService;
    @Resource
    private WmEcontractStampPoiWrapperService wmEcontractStampPoiWrapperService;

    @Autowired
    private WmEcontractAreaDataWrapperService wmEcontractAreaDataWrapperService;
    @Override
    public EcontractBatchBo wrapEcontractBo(EcontractBatchContextBo batchContextBo)
            throws TException, IllegalAccessException, WmCustomerException {
        List<StageBatchInfoBo> batchInfoBoList = Lists.newArrayList();
        batchInfoBoList.add(wmEcontractDateWrapperService.wrap(batchContextBo, dataWrapperMap));
        batchInfoBoList.add(wmEcontractCAPoiWrapperService.wrap(batchContextBo));
        batchInfoBoList.add(wmEcontractStampPoiWrapperService.wrap(batchContextBo, poiStampList));
        batchInfoBoList.add(wmEcontractSmsWrapperService.wrap(batchContextBo));
        // 增加区域
        wmEcontractAreaDataWrapperService.addContext(batchInfoBoList, batchContextBo, EcontractDataWrapperEnum.DRONE_DELIVERY);

        EcontractBatchBo econtractBatchBo =  new EcontractBatchBo.Builder()
                .token(getToken())
                .econtractBizId(getBizId(batchContextBo))
                .econtractType(SignFlowConstant.POI_STAMP_FOR_DELIVERY)
                .stageInfoBoList(batchInfoBoList)
                .flowList(flowList)
                .econtractBatchSource(getSource(batchContextBo))
                .build();
        // 组装子数据
        econtractBatchBo.setApplySubDataBoList(buildApplySubDataBoList(batchContextBo));
        return econtractBatchBo;
    }

    private List<EcontractApplySubDataBo> buildApplySubDataBoList(EcontractBatchContextBo batchContextBo) throws WmCustomerException {
        List<EcontractApplySubDataBo> applySubDataBoList = Lists.newArrayList();
        EcontractTaskBo taskBo = WmEcontractContextUtil.selectByApplyType(batchContextBo, EcontractTaskApplyTypeEnum.DRONE_DELIVERY);
        EcontractBatchDeliveryInfoBo batchDeliveryInfoBo = JSON.parseObject(taskBo.getApplyContext(), EcontractBatchDeliveryInfoBo.class);
        EcontractApplySubDataBo multiPerSubDataBo = new EcontractApplySubDataBo();
        multiPerSubDataBo.setApplySubDataEnum(EcontractApplySubDataEnum.DRONE_MULTI_PERFORMANCE_PDF);
        multiPerSubDataBo.setWmPoiAndBizMap(new HashMap<>());
        EcontractApplySubDataBo mulAreaSubDataBo = new EcontractApplySubDataBo();
        mulAreaSubDataBo.setApplySubDataEnum(EcontractApplySubDataEnum.DRONE_MULTI_AREA);
        mulAreaSubDataBo.setWmPoiAndBizMap(new HashMap<>());
        for (EcontractDeliveryInfoBo deliveryInfoBo : batchDeliveryInfoBo.getEcontractDeliveryInfoBoList()) {
            if ("true".equals(deliveryInfoBo.getHasExternalPerInfo())) {
                multiPerSubDataBo.getWmPoiAndBizMap().put(Long.valueOf(deliveryInfoBo.getWmPoiId()), Long.valueOf(deliveryInfoBo.getExternalPerInfoBizId()));
            }
            if ("true".equals(deliveryInfoBo.getHasExternalAreaInfo())) {
                mulAreaSubDataBo.getWmPoiAndBizMap().put(Long.valueOf(deliveryInfoBo.getWmPoiId()), Long.valueOf(deliveryInfoBo.getExternalAreaInfoBizId()));
            }
        }
        if (MapUtils.isNotEmpty(multiPerSubDataBo.getWmPoiAndBizMap())) {
            applySubDataBoList.add(multiPerSubDataBo);
        }
        if (MapUtils.isNotEmpty(mulAreaSubDataBo.getWmPoiAndBizMap())) {
            applySubDataBoList.add(mulAreaSubDataBo);
        }
        return applySubDataBoList;
    }
}

package com.sankuai.meituan.waimai.customer.service.customer.check;

import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPlatformDataParseService;
import com.sankuai.meituan.waimai.customer.util.Preconditions;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.springframework.beans.factory.annotation.Autowired;

public class BaseWmCustomerValidator {

    @Autowired
    private WmCustomerPlatformDataParseService wmCustomerPlatformDataParseService;

    protected WmCustomerDB getExistWmCustomer(Integer customerId) throws WmCustomerException {
        WmCustomerDB wmCustomerDB = wmCustomerPlatformDataParseService.selectCustomerFromPlatformById(customerId);
        Preconditions.checkArgument(wmCustomerDB != null, CustomerConstants.RESULT_CODE_ERROR, "客户ID无法查到有效数据");
        return wmCustomerDB;
    }
}

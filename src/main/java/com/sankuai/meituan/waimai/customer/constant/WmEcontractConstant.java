package com.sankuai.meituan.waimai.customer.constant;

import com.google.common.collect.Sets;
import com.sankuai.meituan.waimai.customer.config.MccSignConfig;
import com.sankuai.meituan.waimai.econtrct.client.constants.TaskConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter;

import java.util.Set;

public class WmEcontractConstant {

    /*申请电子合同平台*/
    public static final String APPLY_ECONTRACT = "apply_econtract";

    /*创建pdf*/
    public static final String CREATE_PDF = TaskConstant.CREATE_PDF;

    /*CA认证*/
    public static final String CA_POI = TaskConstant.CA_CERTIFY_PART_D;
    public static final String CA_AGENT = TaskConstant.CA_CERTIFY_PART_C;
    public static final String CA_MT = TaskConstant.CA_CERTIFY_PART_B;
    public static final String CA_MTSH = TaskConstant.CA_CERTIFY_PART_E;
    public static final String CA_PERFORMANCE_SERVICE_FEE = TaskConstant.CA_CERTIFY_PART_F;
    public static final String CA_QDB = TaskConstant.CA_CERTIFY_PART_A;
    public static final String CA_B2C_MED = TaskConstant.CA_CERTIFY_PART_G;
    public static final String CA_BJSKWL = TaskConstant.CA_CERTIFY_PART_I;
    public static final String CA_HNLX = TaskConstant.CA_CERTIFY_PART_J;
    public static final String CA_CDZHY = TaskConstant.CA_CERTIFY_PART_K;

    /*发短信*/
    public static final String SMS_POI = TaskConstant.SMS_SIGNER_D;

    /*实名认证*/
    public static final String REALNAME_AUTH_AGENT = TaskConstant.REAL_NAME_AUTH_C;
    public static final String REALNAME_AUTH_POI = TaskConstant.REAL_NAME_AUTH_D;

    /*签章*/
    public static final String STAMP_POI = TaskConstant.ECONTRACT_STAMP_D;
    public static final String STAMP_AGENT = TaskConstant.ECONTRACT_STAMP_C;
    public static final String STAMP_MT = TaskConstant.ECONTRACT_STAMP_B;
    public static final String STAMP_MTSH = TaskConstant.ECONTRACT_STAMP_E;
    public static final String STAMP_PERFORMANCE_SERVICE_FEE = TaskConstant.ECONTRACT_STAMP_F;
    public static final String STAMP_QDB = TaskConstant.ECONTRACT_STAMP_A;
    public static final String STAMP_B2C = TaskConstant.ECONTRACT_STAMP_H;
    public static final String STAMP_BJSKWL = TaskConstant.ECONTRACT_STAMP_I;
    public static final String STAMP_HNLX = TaskConstant.ECONTRACT_STAMP_J;
    public static final String STAMP_CDZH = TaskConstant.ECONTRACT_STAMP_K;
    public static final String STAMP_CDSK = TaskConstant.ECONTRACT_STAMP_L;

    /*合同生效*/
    public static final String EFFECT = TaskConstant.ECONTRACT_FINISH;

    /*签约失败*/
    public static final String NOT_SIGN = TaskConstant.ECONTRACT_FINISH;

    public static final String CERTIFY_CUSTOMERID_MTSH = ConfigUtilAdapter.getString("certify_customerid_mtsh", "8B3C2196CA58228E");


    //深圳百寿健康信息技术有限公司
    public static final String CERTIFY_CUSTOMER_SHENZHENBAISHOU = ConfigUtilAdapter.getString
            ("certify_customer_shenzhenbaishou","21207A103F45886E");


    //KANGAROO DELIVERY LIMITED（B2C跨境）
    public static final String KANGAROO_DELIVERY_LIMITED = ConfigUtilAdapter.getString
            ("certify_customer_b2c_med","92D93B4F785EA58CC7F794562DDB040A");

    //北京三快网络科技有限公司
    public static final String CERTIFY_CUSTOMER_BJSKWL = MccSignConfig.getCertifyBJSKWL();

    //海南两心有限公司
    public static final String CERTIFY_CUSTOMER_HNLX = MccSignConfig.getCertifyHNLX();

    // 成都众亨源商贸有限公司
    public static final String CERTIFY_CUSTOMER_CDZH = MccSignConfig.getCertifyChengDuZhongHeng();

    // 成都三快在线科技有限公司
    public static final String CERTIFY_CUSTOMER_CDSK = MccSignConfig.getCertifyChengDuSankuai();


    /**
     * 复用需要商家以及美团签章的流程
     */
    public static final String FLOW_WITH_POI_MT_STAMP = "type_framecontract_c1";

    /**
     * 复用需要商家+美团+钱袋宝签章的流程
     */
    public static final String FLOW_WITH_POI_MT_QDB_STAMP = "type_framecontract_poifee_qdb";



    //SLA坐标数据
    public static final String VIEW_CONTENT = "econtract_view_content";

    public static final String JUMP_CONTENT = "econtract_external_jump_content";

    public static final String ECONTRACT_UP_STREAM_CONTENT = "econtract_up_stream_content";

    public static final String NOT_PACK = "not_pack";
    public static final String TO_NOTIFY = "to_notify";
    public static final String TO_COMMIT = "to_commit";
    public static final String TO_PACK = "to_pack";
    public static final String FINISH_PACK = "finish_pack";
    public static final String CANCEL_PACK = "cancel_pack";



    public static final Set<String> MANUALBATCH_REACHED_STATUS = Sets.newHashSet(NOT_PACK,TO_PACK);

    public static final Set<String> AGENT_SIGNER_SUPPORT_TYPE = Sets.newHashSet();
    static{
        AGENT_SIGNER_SUPPORT_TYPE.add(EcontractTaskApplyTypeEnum.POIFEE.getName());
        AGENT_SIGNER_SUPPORT_TYPE.add(EcontractTaskApplyTypeEnum.BATCHPOIFEE.getName());
        AGENT_SIGNER_SUPPORT_TYPE.add(EcontractTaskApplyTypeEnum.CUSTOMER.getName());
    }

    public static final String DEFAULT_DELIVERY_CANCEL_DENY_TIPS = "您所选择的配送服务产品将于{date}到期，请您及时签署新配送服务协议。\n自到期日起，如您未及时签署新配送服务协议但仍继续使用美团配送服务配送餐品，则视为您同意接受新协议，美团外卖将按照新配送服务协议的约定进行履约，具体内容请查看新协议。\n如您有疑问或者不准备签署，则美团外卖无法继续向您提供配送服务，为保证您的餐品能够正常配送，请您尽快与您的业务经理联系更换配送服务。";

    public static final String DEFAULT_DELIVERY_SLA_WHOLE_CITY_DOCUMENT = "特别说明：\n"
            + "1、本示意图仅做参考示意使用，而非实际配送范围的承诺。具体配送范围将结合您在商家中心所选择的配送产品、您门店所处地理位置、订单用户与您门店的具体距离、实时运力状态等实际情况综合展示，详见商家中心，您对此同意并认可。\n"
            + "2、因恶劣天气、交通管制等场景，美团外卖为保证骑手生命安全以及您的商业声誉有权单方临时关闭或调整“全城送”相关服务的权利。\n"
            + "3、美团外卖因业务策略调整等原因，有权在您“全城送”服务包到期后不再续约或不继续提供相应服务。\n"
            + "4、对于商家中心展示的配送范围，在包括但不限于下列场景中，美团外卖保留单方调整配送范围并即时生效的权利，您对此知悉并予以认可：\n"
            + "（1）因客观原因、恶劣天气、第三方原因及不可抗力因素导致配送范围需要临时/紧急调整，调整后的配送范围将以商家中心展示为准，具体包括但不限于如下场景：\n"
            + "*天气原因\n"
            + "包括但不限于：因雨雪、低温天气导致路面积水、结冰；大风、台风、洪水、泥石流等恶劣天气、自然灾害影响配送人员的人身安全或骑行安全等情况，具体天气以相关第三方数据为准。\n"
            + "*客观原因\n"
            + "包括但不限于：因道路、桥梁损坏，树木、建筑物倾倒、拆迁等突发状况导致无法取送订单；特殊时令、重大节庆（包括但不限于春节、国庆等）造成的订单、配送人员（骑手）数量发生较大变化或外卖市场供需发生较大变化等；\n"
            + "配送范围覆盖立交桥、河流、山区、景区等客观无法配送的区域或存在无法正常通行的道路（如跨江跨山、封闭性道路等天然屏障）。\n"
            + "*第三方原因\n"
            + "包括但不限于：相关部门有组织有管理的特殊事件（如马拉松、游行、临时性限行/封路、改道、交通管制等）或其他非天气原因引发的突发情况导致车辆或道路通行困难、道路无法通行；\n"
            + "配合/协同相关执法、监管部门的临时性/短期管控措施或根据相应法律法规需调整运营策略等。\n"
            + "（2）因您的相应违约（包括但不限于您所签署的美团外卖协议/合同，您所使用美团外卖及其关联方提供服务的相关规则/规范等）行为，美团外卖将按照相关约定调整配送范围。\n"
            + "（3）为保障您的商业声誉和订单履约质量，在如下场景中美团外卖将结合您过往订单履约能力酌情调整配送范围避免您承担过多损失。\n"
            + "包括但不限于：外卖订单量迅速激增，出餐时间延长/超出承诺出餐时长；平台系统显示或配送人员投诉或用户投诉延时出餐或因延时出餐导致订单无法准时送达。\n"
            + "（4）其他特殊原因需要调整配送范围的情况。\n"
            + "包括但不限于：实际配送范围/历史配送范围与您现行选择配送产品的配送范围标准不符；\n"
            + "当您所属区域的订单取送件范围发生变化后，为保障您订单履约质量及用户体验，您的配送范围将酌情进行调整。\n"
            + "美团外卖重大业务调整；或美团外卖配送合作方重大业务调整或取消/终止与美团外卖合作等。\n"
            + "（5）针对您所参加的临时活动或美团外卖给予您相应优惠奖励等情况，美团外卖将酌情调整您的配送范围。\n"
            + "5、美团外卖因业务调整或美团外卖配送合作方重大业务调整导致您所在区域无法继续提供“全城送”服务的，美团外卖有权终止/解除相应服务，但美团外卖应提前7天书面通知，且应对停止服务前已接订单完成相应配送服务。";


    public static final String DEFAULT_DELIVERY_COMPANY_CUSTOMER_LONG_DISTANCE_DOCUMENT = "特别说明：\n" +
            "1、本示意图仅做参考示意使用，而非实际配送范围的承诺。具体配送范围将结合您在商家中心所选择的配送产品、您门店所处地理位置、订单用户与您门店的具体距离、实时运力状态等实际情况综合展示，详见商家中心，您对此同意并认可。\n" +
            "\n" +
            "2、因恶劣天气、交通管制等场景，美团外卖为保证骑手生命安全以及您的商业声誉有权单方临时关闭或调整“企客远距离”相关服务的权利。\n" +
            "\n" +
            "3、美团外卖因业务策略调整等原因，有权在您“企客远距离”服务包到期后不再续约或不继续提供相应服务。\n" +
            "\n" +
            "4、对于商家中心展示的配送范围，在包括但不限于下列场景中，美团外卖保留单方调整配送范围并即时生效的权利，您对此知悉并予以认可：\n" +
            "\n" +
            "（1）因客观原因、恶劣天气、第三方原因及不可抗力因素导致配送范围需要临时/紧急调整，调整后的配送范围将以商家中心展示为准，具体包括但不限于如下场景：\n" +
            "\n" +
            "*天气原因\n" +
            "\n" +
            "包括但不限于：因雨雪、低温天气导致路面积水、结冰；大风、台风、洪水、泥石流等恶劣天气、自然灾害影响配送人员的人身安全或骑行安全等情况，具体天气以相关第三方数据为准。\n" +
            "\n" +
            "*客观原因\n" +
            "\n" +
            "包括但不限于：因道路、桥梁损坏，树木、建筑物倾倒、拆迁等突发状况导致无法取送订单；特殊时令、重大节庆（包括但不限于春节、国庆等）造成的订单、配送人员（骑手）数量发生较大变化或外卖市场供需发生较大变化等；\n" +
            "\n" +
            "配送范围覆盖立交桥、河流、山区、景区等客观无法配送的区域或存在无法正常通行的道路（如跨江跨山、封闭性道路等天然屏障）。\n" +
            "\n" +
            "*第三方原因\n" +
            "\n" +
            "包括但不限于：相关部门有组织有管理的特殊事件（如马拉松、游行、临时性限行/封路、改道、交通管制等）或其他非天气原因引发的突发情况导致车辆或道路通行困难、道路无法通行；\n" +
            "\n" +
            "配合/协同相关执法、监管部门的临时性/短期管控措施或根据相应法律法规需调整运营策略等。\n" +
            "\n" +
            "（2）因您的相应违约（包括但不限于您所签署的美团外卖协议/合同，您所使用美团外卖及其关联方提供服务的相关规则/规范等）行为，美团外卖将按照相关约定调整配送范围。\n" +
            "\n" +
            "（3）为保障您的商业声誉和订单履约质量，在如下场景中美团外卖将结合您过往订单履约能力酌情调整配送范围避免您承担过多损失。\n" +
            "\n" +
            "包括但不限于：外卖订单量迅速激增，出餐时间延长/超出承诺出餐时长；平台系统显示或配送人员投诉或用户投诉延时出餐或因延时出餐导致订单无法准时送达。\n" +
            "\n" +
            "（4）其他特殊原因需要调整配送范围的情况。\n" +
            "\n" +
            "包括但不限于：实际配送范围/历史配送范围与您现行选择配送产品的配送范围标准不符；\n" +
            "\n" +
            "当您所属区域的订单取送件范围发生变化后，为保障您订单履约质量及用户体验，您的配送范围将酌情进行调整。\n" +
            "\n" +
            "美团外卖重大业务调整；或美团外卖配送合作方重大业务调整或取消/终止与美团外卖合作等。\n" +
            "\n" +
            "（5）针对您所参加的临时活动或美团外卖给予您相应优惠奖励等情况，美团外卖将酌情调整您的配送范围。\n" +
            "\n" +
            "5、美团外卖因业务调整或美团外卖配送合作方重大业务调整导致您所在区域无法继续提供“企客远距离”服务的，美团外卖有权终止/解除相应服务，但美团外卖应提前7天书面通知，且应对停止服务前已接订单完成相应配送服务。";
}

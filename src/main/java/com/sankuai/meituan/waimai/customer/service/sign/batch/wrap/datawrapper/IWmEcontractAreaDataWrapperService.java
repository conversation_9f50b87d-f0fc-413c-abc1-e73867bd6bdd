package com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper;

import com.sankuai.meituan.waimai.econtrct.client.domain.biz.EcontractContentBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.util.List;

public interface IWmEcontractAreaDataWrapperService {

    List<EcontractContentBo> wrap(EcontractBatchContextBo contextBo) throws IllegalAccessException, WmCustomerException;

}

package com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.constants.PdfConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractPoiInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractSettleInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 多店结算登月版商家信息结算单
 */
@Service
public class WmEcontractSettleMoonAdviceWrapperService {

    private static final String TEMPLET_NAME = "settle_moon_advice_info_v3.ftl";

    public List<PdfContentInfoBo> wrap(EcontractBatchContextBo contextBo, EcontractTaskBo taskBo)
        throws IllegalAccessException {
        Map<String, String> pdfMap = generatePdfObject(contextBo, taskBo);
        List<Map<String, String>> pdfList = generateAdvicePdfList(contextBo, taskBo);

        PdfContentInfoBo pdfInfoBo = new PdfContentInfoBo();
        pdfInfoBo.setPdfMetaContent(pdfMap);
        pdfInfoBo.setPdfBizContent(pdfList);
        pdfInfoBo.setPdfTemplateName(ConfigUtilAdapter.getString("PDF_SETTLE_MOON_ADVICE_INFO_V3", TEMPLET_NAME));
        return Lists.newArrayList(pdfInfoBo);
    }

    /**
     * 生成商家信息结算单-多店列表
     */
    private Map<String, String> generatePdfObject(EcontractBatchContextBo contextBo, EcontractTaskBo taskBo) {
        List<EcontractSettleInfoBo> settleInfoBoList = JSON.parseArray(taskBo.getApplyContext(), EcontractSettleInfoBo.class);

        //抽取一个开钱包的对象-业务规则上允许部分开钱包
        EcontractSettleInfoBo infoBo = null;
        for(EcontractSettleInfoBo temp : settleInfoBoList){
            if(temp.isSupportWallet()){
                infoBo = temp;
                break;
            }
        }

        Map<String, String> pdfMap = Maps.newHashMap();
        pdfMap.put("qdbNumber", StringUtils.defaultIfEmpty(infoBo.getQdbNumber(), StringUtils.EMPTY));
        pdfMap.put("partA", StringUtils.defaultIfEmpty(contextBo.getCustomerInfoBo().getCustomerName(), StringUtils.EMPTY));
        pdfMap.put("partAStampName", StringUtils.defaultIfEmpty(WmEcontractContextUtil.getCustomerName(contextBo), StringUtils.EMPTY));
        pdfMap.put("partAEstamp", PdfConstant.POI_SIGNKEY);
        return pdfMap;
    }

    /**
     * 生成商家信息结算单-多店列表
     */
    private List<Map<String, String>> generateAdvicePdfList(EcontractBatchContextBo contextBo, EcontractTaskBo taskBo) {
        List<EcontractSettleInfoBo> settleInfoBoList = JSON.parseArray(taskBo.getApplyContext(), EcontractSettleInfoBo.class);
        List<Map<String, String>> pdfList = Lists.newArrayList();

        List<EcontractSettleInfoBo> moonSettleInfoBoList = Lists.newArrayList();
        for(EcontractSettleInfoBo temp : settleInfoBoList){
            if(temp.isSupportWallet()){
                moonSettleInfoBoList.add(temp);
            }
        }

        for (EcontractSettleInfoBo settleInfoBo : moonSettleInfoBoList) {
            for (EcontractPoiInfoBo poiInfoBo : settleInfoBo.getPoiInfoBoList()) {
                CollectionUtils.addIgnoreNull(pdfList, parse(settleInfoBo, poiInfoBo));
            }
        }
        return pdfList;
    }

    private Map<String, String> parse(EcontractSettleInfoBo settleInfoBo, EcontractPoiInfoBo poiInfoBo) {
        Map<String, String> map = Maps.newHashMap();
        map.put("poiName", StringUtils.defaultIfEmpty(poiInfoBo.getName(), StringUtils.EMPTY));
        map.put("poiAddress", StringUtils.defaultIfEmpty(poiInfoBo.getAddress(), StringUtils.EMPTY));
        map.put("accountName", StringUtils.defaultIfEmpty(settleInfoBo.getAccountName(), StringUtils.EMPTY));
        map.put("accountCardNum", StringUtils.defaultIfEmpty(settleInfoBo.getAccountCardNum(), StringUtils.EMPTY));
        map.put("province", StringUtils.defaultIfEmpty(settleInfoBo.getProvince(), StringUtils.EMPTY));
        map.put("city", StringUtils.defaultIfEmpty(settleInfoBo.getCity(), StringUtils.EMPTY));
        map.put("bank", StringUtils.defaultIfEmpty(settleInfoBo.getBank(), StringUtils.EMPTY));
        map.put("branch", StringUtils.defaultIfEmpty(settleInfoBo.getBranch(), StringUtils.EMPTY));
        map.put("financialContact", StringUtils.defaultIfEmpty(settleInfoBo.getFinancialContact(), StringUtils.EMPTY));
        map.put("financialContactPhone", StringUtils.defaultIfEmpty(settleInfoBo.getFinancialContactPhone(), StringUtils.EMPTY));
        return map;
    }
}



package com.sankuai.meituan.waimai.customer.service.sc.area;

import com.alibaba.fastjson.JSONObject;
import com.carrotsearch.sizeof.RamUsageEstimator;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.infomatiq.jsi.rtree.RTree;
import com.sankuai.meituan.waimai.customer.domain.sc.area.WmScArea;
import com.sankuai.meituan.waimai.customer.domain.sc.area.WmScPoint;
import com.sankuai.meituan.waimai.customer.domain.sc.WmScSchoolAreaDO;
import com.sankuai.meituan.waimai.customer.domain.sc.WmScSchoolAreaSearchCondition;
import com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolAreaService;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScAreaTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;

/**
 * 学校范围
 */
@Slf4j
@Service
public class SchoolAreaServiceImpl implements InitializingBean {
    /**
     * 分页查询分页大小
     */
    private static final int PAGE_SIZE = 50;
    /**
     * 学校范围主键ID与区域映射
     */
    private Map<Integer, WmScArea> schoolAreaMapV2 = Maps.newHashMap();
    /**
     * 学校范围RTreeV2
     */
    private RTree schoolAreaV2 = null;

    @Autowired
    private WmScSchoolAreaService wmScSchoolAreaService;

    public void initV2() {
        log.info("SchoolAreaV2 init begin");
        List<WmScSchoolAreaDO> list = getWmScSchoolAreaDOList();
        // 使用学校范围主键ID作为业务主键
        buildSchoolAreaV2(list);
        log.info("SchoolAreaV2 init end");
    }

    public void addV2(int id, String area, WmScAreaTypeEnum areaTypeEnum) {
        if (areaTypeEnum != WmScAreaTypeEnum.SCHOOL_AREA && areaTypeEnum != WmScAreaTypeEnum.ALL) {
            return;
        }
        if (StringUtils.isBlank(area)) {
            return;
        }
        if (id <= 0) {
            return;
        }
        log.info("[SchoolAreaServiceImpl.addV2][start] id={}, area={}, areaTypeEnum={} ", id, area, areaTypeEnum);
        if (schoolAreaMapV2.get(id) == null) {
            List<WmScPoint> wmScPoints = JSONObject.parseArray(area, WmScPoint.class);
            if (CollectionUtils.isEmpty(wmScPoints)) {
                return;
            }
            WmScArea wmScArea = WmRtreeUtil.getArea(id, wmScPoints);
            if (wmScArea == null) {
                return;
            }
            schoolAreaMapV2.put(id, wmScArea);
            WmRtreeUtil.addAreas(schoolAreaV2, Lists.newArrayList(wmScArea));
        } else {
            updateV2(id, area, areaTypeEnum);
        }
        log.info("[SchoolAreaServiceImpl.addV2][end] id={}, area={}, areaTypeEnum={}", id, area, areaTypeEnum);
    }

    public void deleteV2(int id, WmScAreaTypeEnum areaTypeEnum) {
        if (areaTypeEnum != WmScAreaTypeEnum.SCHOOL_AREA && areaTypeEnum != WmScAreaTypeEnum.ALL) {
            return;
        }
        if (id <= 0) {
            return;
        }
        log.info("[SchoolAreaServiceImpl.deleteV2] id={},areaTypeEnum={} start", id, areaTypeEnum);
        if (schoolAreaMapV2.get(id) == null) {
            return;
        }
        WmScArea wmScArea = schoolAreaMapV2.get(id);
        if (wmScArea == null) {
            return;
        }
        WmRtreeUtil.deleteAreas(schoolAreaV2, Lists.newArrayList(wmScArea));
        schoolAreaMapV2.put(id, null);
        log.info("[SchoolAreaServiceImpl.deleteV2] id={},areaTypeEnum={} end", id, areaTypeEnum);
    }

    public void updateV2(int id, String area, WmScAreaTypeEnum areaTypeEnum) {
        if (areaTypeEnum != WmScAreaTypeEnum.SCHOOL_AREA && areaTypeEnum != WmScAreaTypeEnum.ALL) {
            return;
        }
        if (id <= 0) {
            return;
        }
        log.info("[SchoolAreaServiceImpl.updateV2] id={}, area={}, areaTypeEnum={} start", id, area, areaTypeEnum);
        deleteV2(id, areaTypeEnum);
        addV2(id, area, areaTypeEnum);
        log.info("[SchoolAreaServiceImpl.updateV2] id={}, area={}, areaTypeEnum={} end", id, area, areaTypeEnum);
    }

    public List<Integer> selectV2(int x, int y, WmScAreaTypeEnum areaTypeEnum) {
        log.info("SchoolAreaServiceImpl.selectV2 x = {}, y = {}, areaTypeEnum = {}", x, y, areaTypeEnum);
        if (areaTypeEnum != WmScAreaTypeEnum.SCHOOL_AREA && areaTypeEnum != WmScAreaTypeEnum.ALL) {
            return Lists.newArrayList();
        }
        if (x <= 0 || y <= 0) {
            return Lists.newArrayList();
        }
        WmScPoint wmScPoint = new WmScPoint(x, y);
        return WmRtreeUtil.search(schoolAreaV2, wmScPoint, schoolAreaMapV2);
    }

    /**
     * 获取学校范围列表(全量)
     * @return 学校范围列表
     */
    private List<WmScSchoolAreaDO> getWmScSchoolAreaDOList() {
        List<WmScSchoolAreaDO> list = Lists.newArrayList();
        WmScSchoolAreaSearchCondition condition = new WmScSchoolAreaSearchCondition();
        condition.setPageSize(PAGE_SIZE);
        int pageNo = 1;
        while (true) {
            condition.setPageFrom((pageNo - 1) * PAGE_SIZE);
            List<WmScSchoolAreaDO> areaDOList = wmScSchoolAreaService.selectByCondition(condition);
            if (CollectionUtils.isEmpty(areaDOList)) {
                break;
            }
            list.addAll(areaDOList);
            pageNo++;
        }
        return list;
    }

    /**
     * 构建学校范围Rtree
     * @param list 学校范围列表
     */
    public void buildSchoolAreaV2(List<WmScSchoolAreaDO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Map<Integer, WmScArea> areaMap = Maps.newHashMap();
        List<WmScArea> wmScAreas = Lists.newArrayList();
        for (WmScSchoolAreaDO scSchoolAreaDO : list) {
            // 使用学校范围主键ID作为业务主键
            int id = scSchoolAreaDO.getId().intValue();
            if (id <= 0) {
                continue;
            }
            String area = scSchoolAreaDO.getArea();
            if (StringUtils.isBlank(area)) {
                continue;
            }
            List<WmScPoint> wmScPoints = JSONObject.parseArray(area, WmScPoint.class);
            if (CollectionUtils.isEmpty(wmScPoints)) {
                continue;
            }
            WmScArea wmScArea = WmRtreeUtil.getArea(id, wmScPoints);
            if (wmScArea == null) {
                continue;
            }
            areaMap.put(id, wmScArea);
            wmScAreas.add(wmScArea);
        }
        schoolAreaMapV2 = areaMap;
        schoolAreaV2 = WmRtreeUtil.buildRtreeByAreas(wmScAreas);
        log.info("[SchoolAreaServiceImpl.buildSchoolAreaV2] schoolAreaMapV2 size = {}, schoolAreaV2 size = {}",
                RamUsageEstimator.humanSizeOf(schoolAreaMapV2), RamUsageEstimator.humanSizeOf(schoolAreaV2));
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        initV2();
    }
}

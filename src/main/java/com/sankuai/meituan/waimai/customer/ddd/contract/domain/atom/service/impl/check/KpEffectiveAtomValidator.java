package com.sankuai.meituan.waimai.customer.ddd.contract.domain.atom.service.impl.check;

import com.sankuai.meituan.waimai.customer.ddd.contract.domain.atom.service.IContractValidator;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpService;
import com.sankuai.meituan.waimai.thrift.customer.constant.WmTempletContractTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class KpEffectiveAtomValidator implements IContractValidator {

    @Autowired
    private WmCustomerKpService wmCustomerKpService;

    @Override
    public boolean valid(WmCustomerContractBo contractBo, int opUid, String opName)
            throws WmCustomerException {
        if (WmTempletContractTypeEnum.isPaperContract(contractBo.getBasicBo().getType())) {
            return true;
        }
        if (wmCustomerKpService.getCustomerKpOfEffectiveSigner(contractBo.getBasicBo().getParentId()) != null) {
            return true;
        }
        throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "请等待签约人生效后再提交合同");
    }

}

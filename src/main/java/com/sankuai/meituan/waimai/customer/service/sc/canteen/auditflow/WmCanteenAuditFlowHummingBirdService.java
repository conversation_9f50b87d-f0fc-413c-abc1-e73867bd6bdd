package com.sankuai.meituan.waimai.customer.service.sc.canteen.auditflow;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.customer.adapter.WmAuditApiAdaptor;
import com.sankuai.meituan.waimai.customer.domain.sc.WmCanteenInfoAuditorBO;
import com.sankuai.meituan.waimai.customer.domain.sc.WmScCanteenAuditDO;
import com.sankuai.meituan.waimai.customer.domain.sc.WmScCanteenAuditNodeDO;
import com.sankuai.meituan.waimai.thrift.constatnt.audit.AuditTaskOpTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.canteeninfo.CanteenInfoAuditTaskTypeEnum;
import com.sankuai.meituan.waimai.thrift.domain.WmAuditCommitObj;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class WmCanteenAuditFlowHummingBirdService {

    @Autowired
    private WmAuditApiAdaptor auditApiAdaptor;

    /**
     * 食堂信息新建
     */
    public final static int SAVE_CANTEEN_HUMMING_BIRD_AUDIT_CODE = 73;
    /**
     * 食堂信息修改
     */
    public final static int EDIT_CANTEEN_HUMMING_BIRD_AUDIT_CODE = 74;

    /**
     * 创建蜂鸟审批任务
     * @return 任务ID
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    public String createAuditTask(WmScCanteenAuditDO auditDO, WmScCanteenAuditNodeDO auditNodeDO, WmCanteenInfoAuditorBO auditorBO)
            throws WmSchCantException, TException {
        log.info("[WmCanteenAuditFlowHummingBirdService.createAuditTask] input param: auditDO = {}, auditNodeDO = {}, auditorBO = {}",
                JSONObject.toJSONString(auditDO), JSONObject.toJSONString(auditNodeDO), JSONObject.toJSONString(auditorBO));

        WmAuditCommitObj commitObj = new WmAuditCommitObj();
        commitObj.setBiz_id(auditNodeDO.getId().intValue());
        commitObj.setBiz_type(getAuditBizType(auditDO));
        commitObj.setSubmit_uid(auditDO.getUserId());
        commitObj.setData(getAuditData(auditDO).toJSONString());

        Integer auditSystemId = auditApiAdaptor.commitAuditWithCodeReturn(commitObj);

        log.info("[WmCanteenAuditFlowHummingBirdService.createAuditTask] auditSystemId = {}", auditSystemId);
        return String.valueOf(auditSystemId);
    }

    private int getAuditBizType(WmScCanteenAuditDO auditDO) {
        CanteenInfoAuditTaskTypeEnum taskTypeEnum = CanteenInfoAuditTaskTypeEnum.getByType(auditDO.getAuditTaskType());
        switch (taskTypeEnum) {
            case SAVE_CANTEEN_AUDIT:
                return SAVE_CANTEEN_HUMMING_BIRD_AUDIT_CODE;
            case EDIT_CANTEEN_AUDIT:
                return EDIT_CANTEEN_HUMMING_BIRD_AUDIT_CODE;
            default:
                return -1;
        }
    }

    private JSONObject getAuditData(WmScCanteenAuditDO auditDO) {
        JSONObject data = new JSONObject();
        data.put("otherSystemAssign", false);
        CanteenInfoAuditTaskTypeEnum taskTypeEnum = CanteenInfoAuditTaskTypeEnum.getByType(auditDO.getAuditTaskType());
        switch (taskTypeEnum) {
            case SAVE_CANTEEN_AUDIT:
                data.put("opType", AuditTaskOpTypeEnum.OP_CREATE.getCode());
                break;
            case EDIT_CANTEEN_AUDIT:
                data.put("opType", AuditTaskOpTypeEnum.OP_MODIFY.getCode());
                break;
            default:
                break;
        }
        return data;
    }


}

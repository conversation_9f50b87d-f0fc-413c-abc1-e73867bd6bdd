package com.sankuai.meituan.waimai.customer.service.kp.statemachine.machine;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.constant.event.KpSignerEventEnum;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.context.KpSignerStatusMachineContext;
import com.sankuai.meituan.waimai.thrift.customer.constant.KpSignerStateMachine;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 20240416
 * @desc 已生效签约人KP非身份证-变更状态机
 */
@Service
@Slf4j
public class KpSignerNotIdCardEffectSM extends KpSignerBaseSM {

    /**
     * 每次流转完成时
     *
     * @param fromState
     * @param toState
     * @param event
     * @param context
     */
    @Override
    protected void afterTransitionCompleted(KpSignerStateMachine fromState, KpSignerStateMachine toState, KpSignerEventEnum event, KpSignerStatusMachineContext context) {
        log.info("KpSignerNotIdCardEffectSM.afterTransitionCompleted,fromState={},toState={},event={},context={}", JSON.toJSONString(fromState), JSON.toJSONString(toState), JSON.toJSONString(event), JSON.toJSONString(context));
        super.afterTransitionCompleted(fromState, toState, event, context);
    }


}

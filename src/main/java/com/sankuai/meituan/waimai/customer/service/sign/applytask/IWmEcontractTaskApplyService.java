package com.sankuai.meituan.waimai.customer.service.sign.applytask;

import com.sankuai.meituan.waimai.thrift.customer.domain.base.BooleanResult;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.LongResult;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskApplyBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.RetrySmsResponse;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;

import org.apache.thrift.TException;

/**
 * 任务处理接口
 */
public interface IWmEcontractTaskApplyService {

    public LongResult applyTask(EcontractTaskApplyBo applyBo)
        throws WmCustomerException, TException, WmServerException, IllegalAccessException;

    LongResult easyApplyTask(EcontractTaskApplyBo applyBo)
            throws WmCustomerException, TException, WmServerException, IllegalAccessException;

    public LongResult triggerApplyTask(EcontractTaskApplyBo applyBo)
            throws WmCustomerException, TException, WmServerException, IllegalAccessException;

    public RetrySmsResponse resendMsg(Long taskId) throws WmCustomerException, TException, WmServerException;

    public RetrySmsResponse easyResendMsg(Long taskId) throws WmCustomerException, TException, WmServerException;


    public BooleanResult cancelSign(Long taskId,String actionSource) throws WmCustomerException, TException;

    RetrySmsResponse resendMsgPack(Long signBatchId) throws WmCustomerException, TException;

}

package com.sankuai.meituan.waimai.customer.contract.partner.ability.query;

import com.sankuai.meituan.waimai.thrift.customer.domain.contract.partnercontract.*;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;

import java.util.List;

/**
 * @description:
 * @author: liuyunjie05
 * @create: 2024/8/9 18:50
 */
public interface WmPartnerCustomerContractQueryAbilityService {

    List<CustomerContractQueryResponseDTO> queryCustomerContract(CustomerContractQueryRequestDTO requestDTO) throws WmCustomerException;

    List<C2ContractCheckResponseDTO> hasC2Contract(C2ContractCheckRequestDTO requestDTO) throws WmCustomerException;

    ContractSignTypeResponseDTO queryContractSignType(ContractSignTypeRequestDTO requestDTO) throws WmCustomerException;
}

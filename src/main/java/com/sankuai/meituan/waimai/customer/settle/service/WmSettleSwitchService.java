package com.sankuai.meituan.waimai.customer.settle.service;

import com.sankuai.meituan.waimai.customer.settle.dao.WmPoiSettleAuditedDBMapper;
import com.sankuai.meituan.waimai.customer.settle.domain.WmPoiSettleAuditedDB;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Nullable;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Function;
import com.google.common.base.MoreObjects;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sankuai.meituan.waimai.customer.util.ObjectUtil;
import com.sankuai.meituan.waimai.poibizflow.constant.WmPoiSwitchModuleEnum;
import com.sankuai.meituan.waimai.poibizflow.constant.WmPoiSwitchTaskTypeEnum;
import com.sankuai.meituan.waimai.poibizflow.thrift.exception.WmPoiBizException;
import com.sankuai.meituan.waimai.poibizflow.thrift.switcher.WmPoiSwitchThriftService;
import com.sankuai.meituan.waimai.poibizflow.thrift.switcher.bo.SwitchPoiInfo;
import com.sankuai.meituan.waimai.poibizflow.thrift.switcher.bo.SwitchPoiSettleInfo;
import com.sankuai.meituan.waimai.poibizflow.thrift.switcher.bo.SwitchTaskBo;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettle;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.util.BeanDiffUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class WmSettleSwitchService {
    @Autowired
    private WmPoiSwitchThriftService wmPoiSwitchThriftService;
    @Autowired
    private WmPoiSettleAuditedDBMapper wmPoiSettleAuditedDBMapper;

    /**
     * 获取该客户下所有切换中的门店
     * @param wmCustomerId
     * @param isFilterByOldKpConfirmed 是否过滤得到原kp确认解绑后的信息
     * @return
     */
    public List<Long> getSwitchingWmPoiIdList(int wmCustomerId,boolean isFilterByOldKpConfirmed) {
        log.info("#getSwitchingWmPoiIdList,wmCustomerId={},isFilterByOldKpConfirmed={}",wmCustomerId,isFilterByOldKpConfirmed);
        List<SwitchPoiInfo> switchPoiInfoList = Lists.newArrayList();
        try {
            if(isFilterByOldKpConfirmed){
                switchPoiInfoList = wmPoiSwitchThriftService.getToBizWmPoiListInConfirmingProc(wmCustomerId + "", WmPoiSwitchTaskTypeEnum.CUS_SWITCH, 0, "系统");
            }else{
                switchPoiInfoList = wmPoiSwitchThriftService.getToBizWmPoiList(wmCustomerId + "", WmPoiSwitchTaskTypeEnum.CUS_SWITCH, 0, "系统");
            }
        } catch (WmPoiBizException | TException e) {
            log.error("getToBizWmPoiList异常,wmCustomerId={}", wmCustomerId, e);
        }
        List<Long> transform = Lists.transform(switchPoiInfoList, new Function<SwitchPoiInfo, Long>() {
            @Nullable
            @Override
            public Long apply(@Nullable SwitchPoiInfo input) {
                return input.getWmPoiId();
            }
        });

        return Lists.newArrayList(transform);
    }

    public List<Long> getSwitchingWmPoiIdListFromOldWmCustomerId(int wmCustomerId) {
        List<SwitchPoiInfo> switchPoiInfoList = Lists.newArrayList();
        try {
            switchPoiInfoList = wmPoiSwitchThriftService.getFromBizWmPoiList(wmCustomerId + "", WmPoiSwitchTaskTypeEnum.CUS_SWITCH, 0, "系统");
        } catch (WmPoiBizException | TException e) {
            log.error("getFromBizWmPoiList,wmCustomerId={}", wmCustomerId, e);
        }

        List<Long> transform = Lists.transform(switchPoiInfoList, new Function<SwitchPoiInfo, Long>() {
            @Nullable
            @Override
            public Long apply(@Nullable SwitchPoiInfo input) {
                return input.getWmPoiId();
            }
        });
        return Lists.newArrayList(transform);
    }


    /**
     * 获取该客户下-结算模块-切换中的上下文数据
     * @param wmCustomerId
     * @return
     * @throws WmCustomerException
     */
    public List<SwitchPoiInfo> getSettleSwitchPoiInfoList(int wmCustomerId) throws WmCustomerException {
        log.info("#getSettleSwitchPoiInfoList,wmCustomerId={}",wmCustomerId);
        //获取当前客户正在切换的信息
        List<SwitchPoiInfo> switchPoiInfoList = Lists.newArrayList();
        List<SwitchPoiInfo> collect = Lists.newArrayList();
        try {
            switchPoiInfoList = wmPoiSwitchThriftService.getToBizWmPoiListWithModuleInfo(wmCustomerId + "", WmPoiSwitchTaskTypeEnum.CUS_SWITCH,
                    WmPoiSwitchModuleEnum.SETTLE, 0, "系统");

            log.debug("#getSettleSwitchPoiInfoList,switchPoiInfoList={}",JSONObject.toJSONString(switchPoiInfoList));

            collect = switchPoiInfoList.stream()
                    .filter(temp -> (temp.getModuleInfo() != null && temp.getModuleInfo().containsKey(WmPoiSwitchModuleEnum.SETTLE)))
                    .collect(Collectors.toList());

        } catch (WmPoiBizException | TException e) {
            log.error("getToBizWmPoiListWithModuleInfo异常", e);
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "数据查询异常");
        }
        return collect;
    }

    public List<SwitchPoiInfo> getSettleSwitchPoiInfoListByWmPoiList(int wmCustomerId,List<Long> wmPoiIdList) throws WmCustomerException{
        log.info("#getSettleSwitchPoiInfoListByWmPoiList,wmCustomerId={},wmPoiIdList={}",wmCustomerId,wmPoiIdList);
        //获取当前客户正在切换的信息
        List<SwitchPoiInfo> switchPoiInfoList = Lists.newArrayList();
        List<SwitchPoiInfo> collect = Lists.newArrayList();
        try {
            switchPoiInfoList = wmPoiSwitchThriftService.getReadyBindWithModuleInfo(wmCustomerId + "", WmPoiSwitchTaskTypeEnum.CUS_SWITCH,
                    WmPoiSwitchModuleEnum.SETTLE, wmPoiIdList);

            log.debug("#getSettleSwitchPoiInfoList,switchPoiInfoList={}",JSONObject.toJSONString(switchPoiInfoList));

            collect = switchPoiInfoList.stream()
                    .filter(temp -> (temp.getModuleInfo() != null && temp.getModuleInfo().containsKey(WmPoiSwitchModuleEnum.SETTLE)))
                    .collect(Collectors.toList());

        } catch (WmPoiBizException | TException e) {
            log.error("getToBizWmPoiListWithModuleInfo异常", e);
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "数据查询异常");
        }
        return collect;
    }

    /**
     * 门店维度-删除切换中的线下结算门店关联关系
     * @param targetCustomerId
     * @param wmPoiIdList
     * @param opUid
     * @param opUname
     * @throws WmCustomerException
     */
    public void deleteOfflineSettlePoiInSwitchCentre(int targetCustomerId, List<Long> wmPoiIdList, int opUid, String opUname)
            throws WmCustomerException {
        List<SwitchPoiInfo> settleSwitchPoiInfoList = getSettleSwitchPoiInfoList(targetCustomerId);
        List<SwitchPoiInfo> actualSaveDate = getDeleteSwitchPoiInfo(wmPoiIdList, settleSwitchPoiInfoList);
        if (CollectionUtils.isNotEmpty(actualSaveDate)) {
            saveToBizWmPoiListWithModuleInfo(targetCustomerId, actualSaveDate, opUid, opUname);
        }
    }

    /**
     * 结算id维度-删除切换中心线下结算
     * @param targetCustomerId
     * @param wmSettleIdList
     * @param opUid
     * @param opUname
     * @throws WmCustomerException
     */
    public void deleteOfflineSettleInSwitchCentre(int targetCustomerId, List<Integer> wmSettleIdList, int opUid, String opUname)
            throws WmCustomerException {
        List<SwitchPoiInfo> settleSwitchPoiInfoList = getSettleSwitchPoiInfoList(targetCustomerId);
        List<SwitchPoiInfo> actualSaveDate = getDeleteSwitchPoiInfoByWmSettleId(wmSettleIdList, settleSwitchPoiInfoList);
        if (CollectionUtils.isNotEmpty(actualSaveDate)) {
            saveToBizWmPoiListWithModuleInfo(targetCustomerId, actualSaveDate, opUid, opUname);
            noticePoiSwitchForModuleConfirmByToBiz(targetCustomerId,actualSaveDate.stream().map(x->(int)x.getWmPoiId()).collect(Collectors.toList()));
        }
    }

//    /**
//     * 保存-切换中门店-线上结算门店关联关系到切换中心
//     * @param targetCustomerId
//     * @param opUid
//     * @param opUname
//     */
//    public void saveOnlineSettlePoiToSwitchCentre(int targetCustomerId, int opUid, String opUname) {
//        //切换中心数据生效
//
//    }

    /**
     * 保存-切换中门店-线下结算门店关联关系到切换中心
     * @param targetCustomerId
     * @param toSave
     * @param opUid
     * @param opUname
     * @throws WmCustomerException
     */
    @Deprecated
    public void saveOfflineSettlePoiToSwitchCentre(int targetCustomerId, List<WmSettle> toSave, List<SwitchPoiInfo> settleSwitchPoiInfoList,
                                                   int opUid, String opUname)
            throws WmCustomerException {
        //wmPoiId->wmSettleId
        Map<Long, Integer> wmSettlePoiMap = transToWmSettlePoiMap(toSave);
        saveOfflineSettlePoiToSwitchCentre(targetCustomerId, wmSettlePoiMap, settleSwitchPoiInfoList, opUid, opUname);
    }

    /**
     * 保存-切换中门店-线下结算门店关联关系到切换中心
     * @param targetCustomerId
     * @param wmSettlePoiMap wmPoiId->wmSettleId
     * @param opUid
     * @param opUname
     * @throws WmCustomerException
     */
    @Deprecated
    public void saveOfflineSettlePoiToSwitchCentre(int targetCustomerId, Map<Long, Integer> wmSettlePoiMap, List<SwitchPoiInfo> settleSwitchPoiInfoList, int opUid, String opUname)
            throws WmCustomerException {
        if (MapUtils.isEmpty(wmSettlePoiMap)) {
            return;
        }

        //        settleSwitchPoiInfoList = getSettleSwitchPoiInfoList(targetCustomerId);

        log.info("#saveOfflineSettlePoiToSwitchCentre,targetCustomerId={},wmSettlePoiMap={},settleSwitchPoiInfoList={}",
                JSONObject.toJSONString(targetCustomerId), JSONObject.toJSONString(wmSettlePoiMap), JSONObject.toJSONString(settleSwitchPoiInfoList));

        Set<Long> oldWmPoiIdSet = getAllWmPoiIdFromSwitchCentre(settleSwitchPoiInfoList);
        List<Long> oldWmPoiIdList = Lists.newArrayList(oldWmPoiIdSet);

        Set<Long> toSaveWmPoiIdSet = wmSettlePoiMap.keySet();
        List<Long> toSaveWmPoiIdList = Lists.newArrayList(toSaveWmPoiIdSet);

        List<SwitchPoiInfo> actualSaveDate = Lists.newArrayList();

        //新增门店
        List<Long> addWmPoiIdList = BeanDiffUtil.genAddGenericList(toSaveWmPoiIdList, oldWmPoiIdList);
        actualSaveDate.addAll(getAddSwitchPoiInfo(addWmPoiIdList, wmSettlePoiMap, settleSwitchPoiInfoList));
        //更新门店
        List<Long> commonWmPoiIdList = BeanDiffUtil.genCommonGenericList(toSaveWmPoiIdList, oldWmPoiIdList);
        actualSaveDate.addAll(getUpdateSwitchPoiInfo(commonWmPoiIdList, wmSettlePoiMap, settleSwitchPoiInfoList));
        //删除门店
        List<Long> deleteWmPoiIdList = BeanDiffUtil.genDeleteGenericList(toSaveWmPoiIdList, oldWmPoiIdList);
        actualSaveDate.addAll(getDeleteSwitchPoiInfo(deleteWmPoiIdList, settleSwitchPoiInfoList));
        if (CollectionUtils.isNotEmpty(actualSaveDate)) {
            saveToBizWmPoiListWithModuleInfo(targetCustomerId, actualSaveDate, opUid, opUname);
        }
    }



    /**
     * 封装切换中心-门店上下文数据保存接口
     * @param targetCustomerId
     * @param switchPoiInfoList
     * @param opUid
     * @param opUname
     * @throws WmCustomerException
     */
    public void saveToBizWmPoiListWithModuleInfo(int targetCustomerId, List<SwitchPoiInfo> switchPoiInfoList, int opUid, String opUname)
            throws WmCustomerException {
        log.info("#saveToBizWmPoiListWithModuleInfo,targetCustomerId={},switchPoiInfoList={}", JSONObject.toJSONString(targetCustomerId),
                JSONObject.toJSONString(switchPoiInfoList));
        try {
            wmPoiSwitchThriftService.saveToBizWmPoiListWithModuleInfo(String.valueOf(targetCustomerId), WmPoiSwitchTaskTypeEnum.CUS_SWITCH,
                    switchPoiInfoList, opUid, opUname);
        } catch (WmPoiBizException | TException e) {
            log.error("saveToBizWmPoiListWithModuleInfo异常", e);
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "切换中心数据保存异常");
        }
    }


    public void saveToBizWmPoiListWithModuleInfo(List<Long> wmPoiIdList, final Integer offlineWmSettleId, Integer
            targetCustomerId,  Map<Long,Map<WmPoiSwitchModuleEnum, String>> switchPoiInfoMap,int opUid, String opUname) {

        List<SwitchPoiInfo> switchPoiInfoList = Lists.transform(wmPoiIdList, new Function<Long, SwitchPoiInfo>() {

            @Nullable
            @Override
            public SwitchPoiInfo apply(@Nullable Long wmPoiId) {

                SwitchPoiInfo switchPoiInfo = new SwitchPoiInfo();
                switchPoiInfo.setWmPoiId(wmPoiId);

                Map<WmPoiSwitchModuleEnum, String> moduleInfo = Maps.newHashMap();

                SwitchPoiSettleInfo switchPoiSettleInfo = new SwitchPoiSettleInfo();
                switchPoiSettleInfo.setOfflineWmSettleId(Long.valueOf(offlineWmSettleId));

                //保留线上结算标识
                Map<WmPoiSwitchModuleEnum, String> wmPoiSwitchModuleEnumStringMap=switchPoiInfoMap.get(wmPoiId);
                if(wmPoiSwitchModuleEnumStringMap!=null&&!wmPoiSwitchModuleEnumStringMap.isEmpty()){
                    SwitchPoiSettleInfo switchPoiSettle=WmPoiSwitchModuleEnum.SETTLE.toBean(wmPoiSwitchModuleEnumStringMap.get(WmPoiSwitchModuleEnum.SETTLE));
                    if(switchPoiSettle!=null){
                        boolean onlineWmSettleIdBoolean= switchPoiSettle.getOnlineWmSettleId()!=null&&switchPoiSettle
                                .getOnlineWmSettleId().longValue()!=0L;
                        if(onlineWmSettleIdBoolean){
                            switchPoiSettleInfo.setOnlineWmSettleId(switchPoiSettle.getOnlineWmSettleId());
                        }
                    }
                }else{
                    switchPoiSettleInfo.setOnlineWmSettleId(0L);
                }

                moduleInfo.put(WmPoiSwitchModuleEnum.SETTLE, JSON.toJSONString(switchPoiSettleInfo));

                switchPoiInfo.setModuleInfo(moduleInfo);
                return switchPoiInfo;
            }
        });

        try {
            saveToBizWmPoiListWithModuleInfo(targetCustomerId, switchPoiInfoList, opUid, opUname);
        } catch (WmCustomerException e) {
            log.error("saveToBizWmPoiListWithModuleInfo异常", e);
        }
    }

    /**
     * 获取该客户下所有切换任务
     * @param targetCustomerId
     * @param opUid
     * @param opUname
     * @return
     */
    public List<SwitchTaskBo> getSwitchTaskBo(int targetCustomerId, int opUid, String opUname) {
        List<SwitchTaskBo> switchTaskBoList = null;
        try {
            switchTaskBoList=wmPoiSwitchThriftService.getSwitchTaskListByToBizId(String.valueOf
                            (targetCustomerId), WmPoiSwitchTaskTypeEnum.CUS_SWITCH,
                    opUid, opUname);

        } catch (WmPoiBizException e) {
            log.error("getSwitchTaskBo 异常", e);
        } catch (Exception e) {
            log.error("getSwitchTaskBo 系统异常", e);
        }

        return switchTaskBoList;

    }

    /**
     * 根据结算id，获取切换中心该结算下关联的线下门店
     * @param wmCustomerId
     * @param wmSettleId
     * @return
     * @throws WmCustomerException
     */
    public List<Long> getOffineWmPoiIdByWmSettleId(int wmCustomerId,int wmSettleId) throws WmCustomerException{
        List<SwitchPoiInfo> settleSwitchPoiInfoList = getSettleSwitchPoiInfoList(wmCustomerId);
        Set<Long> wmPoiIdList = getOfflineWmPoiIdFromSwitchCentreFilterWithWmSettleId(
                wmSettleId, settleSwitchPoiInfoList);
        return Lists.newArrayList(wmPoiIdList);
    }

    /**
     * 根据新客户id、门店id获取结算id
     * @param wmCustomerId
     * @param wmPoiId
     * @return
     * @throws WmCustomerException
     */
    public Integer getWmSettleIdFromSwitchCentreByWmCustomerIdAndWmPoiId(int wmCustomerId, long wmPoiId, boolean isEffective)
            throws WmCustomerException {
        List<SwitchPoiInfo> settleSwitchPoiInfoList = getSettleSwitchPoiInfoList(wmCustomerId);
        if (CollectionUtils.isEmpty(settleSwitchPoiInfoList)) {
            return null;
        }
        SwitchPoiSettleInfo switchPoiSettleInfo = null;
        Integer wmSettleId = null;
        for (SwitchPoiInfo temp : settleSwitchPoiInfoList) {
            switchPoiSettleInfo = WmPoiSwitchModuleEnum.SETTLE.toBean(temp.getModuleInfo().get(WmPoiSwitchModuleEnum.SETTLE));
            if (switchPoiSettleInfo == null || temp.getWmPoiId() != wmPoiId) {
                continue;
            }
            if (!isEffective && MoreObjects.firstNonNull(switchPoiSettleInfo.getOfflineWmSettleId(), 0L) > 0) {
                wmSettleId = switchPoiSettleInfo.getOfflineWmSettleId().intValue();
            } else if (isEffective && MoreObjects.firstNonNull(switchPoiSettleInfo.getOnlineWmSettleId(), 0L) > 0) {
                wmSettleId = switchPoiSettleInfo.getOnlineWmSettleId().intValue();
            }
        }
        return wmSettleId;
    }

    /**
     * 切换中数据-签约成功/审核通过后,设置切换中心数据生效
     * @param targetCustomerId
     * @param settleSwitchPoiInfoList
     * @param opUid
     * @param opUname
     * @throws WmCustomerException
     */
    public void effectSwitchingSettle(int targetCustomerId, List<SwitchPoiInfo> settleSwitchPoiInfoList, int opUid, String opUname) throws WmCustomerException{
        if(CollectionUtils.isEmpty(settleSwitchPoiInfoList)){
            return;
        }
        SwitchPoiSettleInfo switchPoiSettleInfo = null;
        SwitchPoiSettleInfo switchPoiSettleInfoTemp = null;
        List<SwitchPoiInfo> toSaveInfoList = Lists.newArrayList();
        Map<WmPoiSwitchModuleEnum, String> moduleInfo = null;
        for(SwitchPoiInfo temp : settleSwitchPoiInfoList){
            switchPoiSettleInfo = WmPoiSwitchModuleEnum.SETTLE.toBean(temp.getModuleInfo().get(WmPoiSwitchModuleEnum.SETTLE));
            if(switchPoiSettleInfo == null){
                continue;
            }
            if(!switchPoiSettleInfo.getOfflineWmSettleId().equals(switchPoiSettleInfo.getOnlineWmSettleId())){
                switchPoiSettleInfoTemp = new SwitchPoiSettleInfo();
                switchPoiSettleInfoTemp.setOfflineWmSettleId(switchPoiSettleInfo.getOfflineWmSettleId());
                switchPoiSettleInfoTemp.setOnlineWmSettleId(switchPoiSettleInfo.getOfflineWmSettleId());
                moduleInfo = Maps.newHashMap();
                moduleInfo.put(WmPoiSwitchModuleEnum.SETTLE, JSONObject.toJSONString(switchPoiSettleInfoTemp));
                toSaveInfoList.add(SwitchPoiInfo.builder().wmPoiId(temp.getWmPoiId()).moduleInfo(moduleInfo).build());
            }
        }
        if(CollectionUtils.isNotEmpty(toSaveInfoList)){
            saveToBizWmPoiListWithModuleInfo(targetCustomerId,toSaveInfoList,opUid,opUname);
        }
    }

    /**
     * 生效后通知切换中心
     * @param wmCustomerId
     * @param poiIds
     */
    public void noticePoiSwitchForModuleConfirmByToBiz(int wmCustomerId, List<Integer> poiIds) {
        try {
            wmPoiSwitchThriftService.noticePoiSwitchForModuleConfirmByToBiz(wmCustomerId + "", WmPoiSwitchModuleEnum.SETTLE,
                    ObjectUtil.intList2LongList(poiIds), 0, "客户切换");
        } catch (WmPoiBizException | TException e) {
            log.error("noticePoiSwitchForModuleConfirmByToBiz异常", e);
        }
    }

    /*private method*/


    private Map<Long,Integer> transToWmSettlePoiMap(List<WmSettle> toSave){
        Map<Long,Integer> result = Maps.newHashMap();
        for(WmSettle temp : toSave){
            for(Integer wmPoiId : temp.getWmPoiIdList()){
                result.put(wmPoiId.longValue(),temp.getId());
            }
        }
        return result;
    }

    /**
     * 通过结算id-获取需要删除的线下切换中心数据
     * @param wmSettleIdList
     * @param settleSwitchPoiInfoList
     * @return
     */
    private List<SwitchPoiInfo> getDeleteSwitchPoiInfoByWmSettleId(List<Integer> wmSettleIdList, List<SwitchPoiInfo> settleSwitchPoiInfoList) {
        Set<Long> deleteWmSettleIdSet = Sets.newHashSet(ObjectUtil.intList2LongList(wmSettleIdList));
        SwitchPoiSettleInfo switchPoiSettleInfo = null;
        List<SwitchPoiInfo> toSaveSettleSwitchPoiInfoList = Lists.newArrayList();
        for(SwitchPoiInfo temp : settleSwitchPoiInfoList){
            switchPoiSettleInfo = WmPoiSwitchModuleEnum.SETTLE
                    .toBean(temp.getModuleInfo().get(WmPoiSwitchModuleEnum.SETTLE));
            if(switchPoiSettleInfo!=null && deleteWmSettleIdSet.contains(switchPoiSettleInfo.getOfflineWmSettleId())){
                switchPoiSettleInfo.setOfflineWmSettleId(0L);
                temp.getModuleInfo().put(WmPoiSwitchModuleEnum.SETTLE,JSONObject.toJSONString(switchPoiSettleInfo));
                toSaveSettleSwitchPoiInfoList.add(temp);
            }
        }
        return toSaveSettleSwitchPoiInfoList;
    }

    /**
     * 新增的关联关系
     * @param addWmPoiIdList
     * @param wmSettlePoiMap
     * @return
     */
    public List<SwitchPoiInfo> getAddSwitchPoiInfo(List<Long> addWmPoiIdList, Map<Long, Integer> wmSettlePoiMap,
                                                   List<SwitchPoiInfo> settleSwitchPoiInfoList) {
        SwitchPoiSettleInfo switchPoiSettleInfo = null;
        Map<WmPoiSwitchModuleEnum, String> moduleInfo = null;

        Map<Long, SwitchPoiInfo> collect = settleSwitchPoiInfoList.stream().collect(Collectors.toMap(x -> x.getWmPoiId(), x -> x));

        SwitchPoiInfo switchPoiInfoTemp = null;
        List<SwitchPoiInfo> toSaveSettleSwitchPoiInfoList = Lists.newArrayList();
        for (Long temp : addWmPoiIdList) {
            moduleInfo = Maps.newHashMap();
            switchPoiInfoTemp = collect.get(temp);
            //切换中心未维护的门店结算上下文-新增
            if(switchPoiInfoTemp == null){
                switchPoiSettleInfo = SwitchPoiSettleInfo.builder().offlineWmSettleId(wmSettlePoiMap.get(temp).longValue()).build();
            }
            //切换中心已维护该门店结算上下文信息
            else{
                switchPoiSettleInfo = WmPoiSwitchModuleEnum.SETTLE.toBean(switchPoiInfoTemp.getModuleInfo().get(WmPoiSwitchModuleEnum.SETTLE));
                switchPoiSettleInfo.setOfflineWmSettleId(wmSettlePoiMap.get(temp).longValue());
            }
            moduleInfo.put(WmPoiSwitchModuleEnum.SETTLE, JSONObject.toJSONString(switchPoiSettleInfo));
            toSaveSettleSwitchPoiInfoList.add(SwitchPoiInfo.builder().wmPoiId(temp).moduleInfo(moduleInfo).build());
        }
        return toSaveSettleSwitchPoiInfoList;
    }

    /**
     * 删除的关联关系
     * @param deleteWmPoiIdList
     * @param settleSwitchPoiInfoList
     * @return
     * @throws WmCustomerException
     */
    public List<SwitchPoiInfo> getDeleteSwitchPoiInfo(List<Long> deleteWmPoiIdList, List<SwitchPoiInfo> settleSwitchPoiInfoList) throws WmCustomerException{
        Set<Long> deleteWmPoiIdSet = Sets.newHashSet(deleteWmPoiIdList);
        SwitchPoiSettleInfo switchPoiSettleInfo = null;
        List<SwitchPoiInfo> toSaveSettleSwitchPoiInfoList = Lists.newArrayList();
        for(SwitchPoiInfo temp : settleSwitchPoiInfoList){
            if(!deleteWmPoiIdSet.contains(temp.getWmPoiId())){
                continue;
            }
            switchPoiSettleInfo = WmPoiSwitchModuleEnum.SETTLE
                    .toBean(temp.getModuleInfo().get(WmPoiSwitchModuleEnum.SETTLE));
            if(!switchPoiSettleInfo.getOfflineWmSettleId().equals(0L)){
                switchPoiSettleInfo.setOfflineWmSettleId(0L);
                temp.getModuleInfo().put(WmPoiSwitchModuleEnum.SETTLE,JSONObject.toJSONString(switchPoiSettleInfo));
                toSaveSettleSwitchPoiInfoList.add(temp);
            }
        }
        return toSaveSettleSwitchPoiInfoList;
    }

    /**
     * 更新的关联关系
     * @param commonWmPoiIdList
     * @param wmSettlePoiMap
     * @param settleSwitchPoiInfoList
     * @return
     * @throws WmCustomerException
     */
    public List<SwitchPoiInfo> getUpdateSwitchPoiInfo(List<Long> commonWmPoiIdList, Map<Long, Integer> wmSettlePoiMap,
            List<SwitchPoiInfo> settleSwitchPoiInfoList)
            throws WmCustomerException {
        Set<Long> commonWmPoiIdSet = Sets.newHashSet(commonWmPoiIdList);
        List<SwitchPoiInfo> toSaveSettleSwitchPoiInfoList = Lists.newArrayList();
        SwitchPoiSettleInfo switchPoiSettleInfo = null;
        for (SwitchPoiInfo temp : settleSwitchPoiInfoList) {
            if (!commonWmPoiIdSet.contains(temp.getWmPoiId())) {
                continue;
            }
            switchPoiSettleInfo = WmPoiSwitchModuleEnum.SETTLE.toBean(temp.getModuleInfo().get(WmPoiSwitchModuleEnum.SETTLE));
            if (!switchPoiSettleInfo.getOfflineWmSettleId().equals((long) wmSettlePoiMap.get(temp.getWmPoiId()))) {
                switchPoiSettleInfo.setOfflineWmSettleId((long) wmSettlePoiMap.get(temp.getWmPoiId()));
                temp.getModuleInfo().put(WmPoiSwitchModuleEnum.SETTLE, JSONObject.toJSONString(switchPoiSettleInfo));
                toSaveSettleSwitchPoiInfoList.add(temp);
            }
        }
        return toSaveSettleSwitchPoiInfoList;
    }

    /**
     * 获取切换中心-所有-结算线下门店集合
     * @param settleSwitchPoiInfoList
     * @return
     */
    private Set<Long> getAllWmPoiIdFromSwitchCentre(List<SwitchPoiInfo> settleSwitchPoiInfoList) {
        Set<Long> result = Sets.newHashSet();
        for (SwitchPoiInfo temp : settleSwitchPoiInfoList) {
            result.add(temp.getWmPoiId());
        }
        return result;
    }

    /**
     * 获取切换中心-关联-有效结算线下id的门店集合
     * @param wmSettleId
     * @param settleSwitchPoiInfoList
     * @return
     */
    private Set<Long> getOfflineWmPoiIdFromSwitchCentreFilterWithWmSettleId(int wmSettleId,List<SwitchPoiInfo> settleSwitchPoiInfoList) {
        Set<Long> result = Sets.newHashSet();
        SwitchPoiSettleInfo switchPoiSettleInfo = null;
        for (SwitchPoiInfo temp : settleSwitchPoiInfoList) {
            switchPoiSettleInfo = WmPoiSwitchModuleEnum.SETTLE.toBean(temp.getModuleInfo().get(WmPoiSwitchModuleEnum.SETTLE));
            if (switchPoiSettleInfo != null && switchPoiSettleInfo.getOfflineWmSettleId().equals((long)wmSettleId)) {
                result.add(temp.getWmPoiId());
            }
        }
        return result;
    }

    public List<Integer> getWmSettleIdListByCustomerIdAndWmPoiIdList(int wmCustomerId,
            List<Long> wmPoiIdList) {
        Set<Integer> result = Sets.newHashSet();
        if(wmCustomerId <= 0 || CollectionUtils.isEmpty(wmPoiIdList)){
            return Lists.newArrayList(result);
        }
        List<WmPoiSettleAuditedDB> wmPoiSettleAuditedList = wmPoiSettleAuditedDBMapper.getByContractIdAndWmPoiIdListMaster(
                wmCustomerId, wmPoiIdList);
        for(WmPoiSettleAuditedDB temp : wmPoiSettleAuditedList){
            if(temp.getWm_settle_id() <= 0){
                continue;
            }
            result.add(temp.getWm_settle_id());
        }
        return Lists.newArrayList(result);
    }
}

package com.sankuai.meituan.waimai.customer.adapter;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import com.sankuai.waimai.crm.cd.saas.lead.campus.sdk.dto.contact.ContactDto;

import com.sankuai.waimai.crm.cd.saas.lead.campus.sdk.dto.contract.ContractApplyInfoDto;
import com.sankuai.waimai.crm.cd.saas.lead.campus.sdk.dto.partner.PartnerInfoDto;
import com.sankuai.waimai.crm.cd.saas.lead.campus.sdk.resp.contact.ContactGetResponse;
import com.sankuai.waimai.crm.cd.saas.lead.campus.sdk.resp.contract.ContractQueryResp;
import com.sankuai.waimai.crm.cd.saas.lead.campus.sdk.resp.partner.PartnerSearchResponse;
import com.sankuai.waimai.crm.cd.saas.lead.campus.sdk.thrift.contact.CampusContactThriftService;
import com.sankuai.waimai.crm.cd.saas.lead.campus.sdk.thrift.contract.CampusContractThriftService;
import com.sankuai.waimai.crm.cd.saas.lead.campus.sdk.thrift.partner.CampusPartnerThriftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * CRM侧学校用户信息适配器
 * <AUTHOR>
 * @date 2024/02/26
 * @email <EMAIL>
 */
@Slf4j
@Service
public class WmCampusContactServiceAdapter {

    @Autowired
    private CampusContactThriftService campusContactThriftService;

    @Autowired
    private CampusPartnerThriftService campusPartnerThriftService;

    @Autowired
    private CampusContractThriftService campusContractThriftService;

    /**
     * 根据学校联系人ID查询联系人信息(单个查询)
     * @param userId 学校联系人ID
     * @return ContactDto
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public ContactDto getSchoolContactByUserId(String userId) throws WmSchCantException {
        List<ContactDto> contactDtoList = new ArrayList<>();
        try {
            log.info("[WmCrmCampusServiceAdapter.getSchoolContactByUserId] userId = {}", userId);
            List<String> idList = new ArrayList<>();
            idList.add(userId);
            ContactGetResponse contactGetResponse = campusContactThriftService.getContactByIdList(idList);
            contactDtoList = contactGetResponse.getData();
            log.info("[WmCrmCampusServiceAdapter.getSchoolContactByUserId] contactGetResponse = {}", JSONObject.toJSONString(contactGetResponse));
        } catch (Exception e) {
            log.error("[WmCrmCampusServiceAdapter.getSchoolContactByUserId] Exception. userId = {}", userId, e);
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "查询学校联系人信息异常");
        }

        if (CollectionUtils.isEmpty(contactDtoList)) {
            log.error("[WmCrmCampusServiceAdapter.getSchoolContactByUserId] contactDtoList is empty. userId = {}", userId);
            return null;
        }

        if (contactDtoList.size() > 1) {
            log.error("[WmCrmCampusServiceAdapter.getSchoolContactByUserId] contactDtoList error. userId = {}, contactDtoList = {}",
                    userId, JSONObject.toJSONString(contactDtoList));
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "查询学校联系人信息异常");
        }

        return contactDtoList.get(0);
    }

    /**
     * 根据学校合伙人ID查询合伙人信息
     * @param userId 学校合伙人ID
     * @return PartnerInfoDto
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public PartnerInfoDto getSchoolPartnerByUserId(String userId) throws WmSchCantException {
        try {
            log.info("[WmCampusContactServiceAdapter.getSchoolPartnerByUserId] userId = {}", userId);
            PartnerSearchResponse response = campusPartnerThriftService.getPartnerById(userId);
            log.info("[WmCampusContactServiceAdapter.getSchoolPartnerByUserId] response = {}", JSONObject.toJSONString(response));
            return response.getData();
        } catch (Exception e) {
            log.error("[WmCrmCampusServiceAdapter.getSchoolPartnerByUserId] Exception. userId = {}", userId);
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "查询学校合伙人信息异常");
        }
    }

    /**
     * 根据学校联系人ID查询联系人信息(批量查询)
     * @param userIds 学校联系人ID列表
     * @return ContactDto
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public List<ContactDto> getSchoolContactsByUserIds(List<String> userIds) throws WmSchCantException {
        try {
            log.info("[WmCampusContactServiceAdapter.getSchoolContactsByUserIds] userIds = {}", JSONObject.toJSONString(userIds));
            ContactGetResponse contactGetResponse = campusContactThriftService.getContactByIdList(userIds);
            log.info("[WmCampusContactServiceAdapter.getSchoolContactsByUserIds] contactGetResponse = {}", JSONObject.toJSONString(contactGetResponse));
            return contactGetResponse.getData();
        } catch (Exception e) {
            log.error("[WmCrmCampusServiceAdapter.getSchoolContactByUserId] Exception. userIds = {}",
                    JSONObject.toJSONString(userIds), e);
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "查询学校联系人信息异常");
        }
    }

    /**
     * 根据商机ID查询学校合同信息
     * @param opportunityId
     * @return
     * @throws WmSchCantException
     */
    public List<ContractApplyInfoDto> getSchoolContractAppliesByOpportunity(Long opportunityId) throws WmSchCantException {
        try {
            log.info("[WmCampusContactServiceAdapter.getSchoolContractApplysByOpportunity] opportunity = {}", opportunityId);
            ContractQueryResp resp = campusContractThriftService.getByOpportunityId(opportunityId);
            log.info("[WmCampusContactServiceAdapter.getSchoolContractApplysByOpportunity] resp = {}", JSONObject.toJSONString(resp));

            if (resp.getCode() != 0) {
                log.error("[WmCrmCampusServiceAdapter.getSchoolContractApplysByOpportunity] Exception. code:{}, msg:{}", resp.getCode(), resp.getMsg());
                throw new RuntimeException(resp.getMsg());
            }
            if (CollectionUtils.isEmpty(resp.getData())) {
                return Collections.emptyList();
            }
            resp.getData().sort((c1, c2) -> {
                if (Objects.isNull(c1.getApprovalTime())) {
                    return 1;
                }
                if (Objects.isNull(c2.getApprovalTime())) {
                    return -1;
                }
                return c2.getApprovalTime().compareTo(c1.getApprovalTime());
            });
            return resp.getData();
        } catch (Exception e) {
            log.error("[WmCrmCampusServiceAdapter.getSchoolContractApplysByOpportunity] Exception. opportunity = {}",
                    opportunityId, e);
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "查询合同申请失败");
        }
    }

    public ContractApplyInfoDto getSchoolContractById(Long contractId) throws WmSchCantException {
        try {
            log.info("[WmCampusContactServiceAdapter.getSchoolContractById] contractId = {}", contractId);
            ContractQueryResp resp = campusContractThriftService.getByIdList(Collections.singletonList(contractId));
            log.info("[WmCampusContactServiceAdapter.getSchoolContractById] resp = {}", JSONObject.toJSONString(resp));

            if (resp.getCode() != 0) {
                log.error("[WmCrmCampusServiceAdapter.getSchoolContractById] Exception. code:{}, msg:{}", resp.getCode(), resp.getMsg());
                throw new RuntimeException(resp.getMsg());
            }
            if (CollectionUtils.isEmpty(resp.getData())) {
                return null;
            }
            return resp.getData().get(0);
        } catch (Exception e) {
            log.error("[WmCrmCampusServiceAdapter.getSchoolContractById] Exception. contractId = {}",
                    contractId, e);
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "根据id查询合同申请失败");
        }
    }
}

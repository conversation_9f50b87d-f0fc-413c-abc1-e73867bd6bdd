package com.sankuai.meituan.waimai.customer.dao.sc;

import com.sankuai.meituan.waimai.customer.domain.sc.WmScCanteenPoiTaskDO;
import com.sankuai.meituan.waimai.customer.domain.sc.WmScCanteenPoiTaskSearchCondition;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface WmScCanteenPoiTaskMapper {

    int insertSelective(WmScCanteenPoiTaskDO record);

    WmScCanteenPoiTaskDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(WmScCanteenPoiTaskDO record);

    int updateAuditStatusByPrimaryKey(@Param("Id") Long id,@Param("auditStatus") Integer auditStatus);

    WmScCanteenPoiTaskDO selectLatestTaskByCanteenPrimaryKey(int canteenPrimaryKey);

    /**
     * 通过食堂主键ID获取最新的任务(不判断valid)
     * @param canteenPrimaryKey 食堂主键ID
     */
    WmScCanteenPoiTaskDO selectUncertainValidLatestTaskByCanteenPrimaryKey(int canteenPrimaryKey);

    List<WmScCanteenPoiTaskDO> selectByCondition(WmScCanteenPoiTaskSearchCondition condition);

    List<WmScCanteenPoiTaskDO> selectTaskByWmPoiId(@Param("wmPoiId") Long wmPoiId,@Param("canteenIdFrom") Long canteenIdFrom);

}
package com.sankuai.meituan.waimai.customer.dao;

import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiLogisticsSubjectDB;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public interface WmCustomerPoiLogisticsSubjectDBMapper {

    /**
     * 批量插入门店主体快照记录
     * @param list
     */
    int batchInsertCustomerPoiSubject(@Param("list") List<WmCustomerPoiLogisticsSubjectDB> list);

    /**
     * 插入门店主体快照记录
     * @param record
     */
    int insertCustomerPoiSubject(WmCustomerPoiLogisticsSubjectDB record);

    /**
     * 修改门店主体快照记录
     * @param record
     */
    int updateCustomerPoiSubject(WmCustomerPoiLogisticsSubjectDB record);

    /**
     * 获取最新一条主体记录
     * @return
     */
    WmCustomerPoiLogisticsSubjectDB getLatestPoiSubject(@Param("customerId") Integer customerId,@Param("wmPoiId") Long wmPoiId);
}
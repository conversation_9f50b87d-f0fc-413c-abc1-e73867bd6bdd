package com.sankuai.meituan.waimai.customer.adapter.config;

import com.alibaba.fastjson.JSON;
import com.meituan.mtrace.Tracer;
import com.sankuai.inf.octo.mns.util.ProcessInfoUtil;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.contract.config.dto.*;
import com.sankuai.meituan.waimai.econtrct.client.domain.config.framecontract.FrameContractCompleteConfigResponseDTO;
import com.sankuai.meituan.waimai.econtrct.client.service.FrameContractConfigThriftService;
import com.sankuai.meituan.waimai.thrift.customer.domain.config.*;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.util.DaxiangUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * @description:
 * @author: liuyunjie05
 * @create: 2024/5/20 14:02
 */
@Slf4j
@Service
public class FrameContractConfigThriftServiceAdapter {

    @Resource
    private FrameContractConfigThriftService frameContractConfigThriftService;

    public List<FrameContractCompleteConfigResponseDTO> getAllContractConfigInfo() {
        try {
            return frameContractConfigThriftService.getAllContractConfigInfo();
        } catch (Exception e) {
            log.error("FrameContractConfigThriftServiceAdapter#getAllContractConfigInfo, error", e);
            return Collections.emptyList();
        }
    }

    public FrameContractCompleteConfigResponseDTO getContractConfigInfo(Integer contractId) throws WmCustomerException {
        int retryTimes = MccConfig.getRetryTimesToQueryConfigContract();
        for (int i = 0; i < retryTimes; i++) {
            try {
                return frameContractConfigThriftService.getContractConfigInfo(contractId);
            } catch (Exception e) {
                log.warn("FrameContractConfigThriftServiceAdapter#getContractConfigInfo, 查询配置化信息失败, error", e);
            }
        }
        log.error("FrameContractConfigThriftServiceAdapter#getContractConfigInfo, 重试后仍然失败");
        DaxiangUtil.push("<EMAIL>", ProcessInfoUtil.getHostEnv() + "环境, 查询配置化信息失败, trace: " + Tracer.id(),
                MccConfig.getDaXiangAlarmMisList());
        return null;
    }

    public FrameContractCompleteConfigResponseDTO getConfigInfoByContractCode(String contractCode) {
        int retryTimes = MccConfig.getRetryTimesToQueryConfigContract();
        for (int i = 0; i < retryTimes; i++) {
            try {
                return frameContractConfigThriftService.getConfigInfoByContractCode(contractCode);
            } catch (Exception e) {
                log.warn("FrameContractConfigThriftServiceAdapter#getConfigInfoByContractCode, 查询配置化信息失败, error", e);
            }
        }
        log.error("FrameContractConfigThriftServiceAdapter#getConfigInfoByContractCode, 重试后仍然失败");
        DaxiangUtil.push("<EMAIL>", ProcessInfoUtil.getHostEnv() + "环境, 查询配置化信息失败, trace: " + Tracer.id(),
                MccConfig.getDaXiangAlarmMisList());
        return null;
    }

    public static ContractConfigAllInfoDTO convertConfigToDTO(ContractConfigInfo configInfo) {
        if (configInfo == null) {
            return null;
        }
        try {
            ContractConfigAllInfoDTO allInfoDTO = new ContractConfigAllInfoDTO();
            allInfoDTO.setContractId(configInfo.getContractId());
            allInfoDTO.setContractName(configInfo.getContractName());
            allInfoDTO.setContractCode(configInfo.getContractCode());
            allInfoDTO.setConfigPropertyDTO(convertContractPropertyToDTO(configInfo.getContractProperty()));
            allInfoDTO.setStatus(configInfo.getStatus());
            allInfoDTO.setOperationDTO(convertToOperationDTO(configInfo.getSupportOpInfo()));
            allInfoDTO.setSourceAuthInfoDTO(convertToSourceAuthInfoDTO(configInfo.getSourceAuthInfo()));
            return allInfoDTO;
        } catch (Exception e) {
            log.warn("FrameContractConfigThriftServiceAdapter#convertConfigToDTO, configInfo: {}, error", JSON.toJSONString(configInfo), e);
            return null;
        }
    }

    private static SourceAuthInfoDTO convertToSourceAuthInfoDTO(SourceAuthInfo sourceAuthInfo) {
        SourceAuthInfoDTO targetDTO = new SourceAuthInfoDTO();
        targetDTO.setCanSaveInBee(sourceAuthInfo.getCanSaveInBee());
        targetDTO.setCanDisplayInBee(sourceAuthInfo.getCanDisplayInBee());
        targetDTO.setCanDisplayInSingleView(sourceAuthInfo.getCanDisplayInSingleView());
        targetDTO.setCanDisplayInMultiView(sourceAuthInfo.getCanDisplayInMultiView());

        return targetDTO;
    }

    private static ConfigContractOperationDTO convertToOperationDTO(OperationSupportInfo supportOpInfo) {
        ConfigContractOperationDTO operationDTO = new ConfigContractOperationDTO();
        operationDTO.setCanInvalidContract(supportOpInfo.getCanAbolishContract());
        return operationDTO;
    }

    private static ContractConfigPropertyDTO convertContractPropertyToDTO(ContractProperty contractProperty) {
        ContractConfigPropertyDTO configPropertyDTO = new ContractConfigPropertyDTO();
        configPropertyDTO.setPartPropertyDTOList(convertContractPartPropertyToDTO(contractProperty.getPartPropertyList()));
        configPropertyDTO.setValidityDTO(convertContractValidityPropertyToDTO(contractProperty.getContractValidityProperty()));
        configPropertyDTO.setEffectiveTimeDTO(convertContractEffectiveTimePropertyToDTO(contractProperty.getContractEffectiveTimeProperty()));
        return configPropertyDTO;
    }

    private static ContractEffectiveTimePropertyDTO convertContractEffectiveTimePropertyToDTO(ContractEffectiveTimeInfo effectiveTimeInfo) {
        ContractEffectiveTimePropertyDTO effectiveTimePropertyDTO = new ContractEffectiveTimePropertyDTO();
        BeanUtils.copyProperties(effectiveTimeInfo, effectiveTimePropertyDTO);

        effectiveTimePropertyDTO.setLeftInterval(convertToTimePropertyDTO(effectiveTimeInfo.getLeftInterval()));
        effectiveTimePropertyDTO.setRightInterval(convertToTimePropertyDTO((effectiveTimeInfo.getRightInterval())));
        return effectiveTimePropertyDTO;
    }

    private static TimePropertyDTO convertToTimePropertyDTO(ContractTimeInfo sourceTimeInfo) {
        if (sourceTimeInfo == null) {
            return null;
        }
        TimePropertyDTO targetDTO = new TimePropertyDTO();
        BeanUtils.copyProperties(sourceTimeInfo, targetDTO);
        return targetDTO;
    }

    private static ContractValidityPropertyDTO convertContractValidityPropertyToDTO(ContractValidityInfo validityInfo) {
        ContractValidityPropertyDTO targetValidityPropertyDTO = new ContractValidityPropertyDTO();
        BeanUtils.copyProperties(validityInfo, targetValidityPropertyDTO);

        targetValidityPropertyDTO.setLeftInterval(convertToTimePropertyDTO(validityInfo.getLeftInterval()));
        targetValidityPropertyDTO.setRightInterval(convertToTimePropertyDTO(validityInfo.getRightInterval()));

        return targetValidityPropertyDTO;

    }

    private static List<ContractConfigPartPropertyDTO> convertContractPartPropertyToDTO(List<ContractPartProperty> partPropertyList) {
        List<ContractConfigPartPropertyDTO> dtoList = new ArrayList<>();
        for (ContractPartProperty sourcePartProperty : partPropertyList) {
            ContractConfigPartPropertyDTO targetPartProperty = new ContractConfigPartPropertyDTO();
            BeanUtils.copyProperties(sourcePartProperty, targetPartProperty);
            dtoList.add(targetPartProperty);
        }
        return dtoList;
    }
}

package com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.atom;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.google.common.util.concurrent.Uninterruptibles;
import com.meituan.mtrace.thread.TraceRunnable;
import com.meituan.mtrace.thread.pool.TraceExecutors;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerPoiDBMapper;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiDB;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiRelService;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerRelationStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.task.CustomerTaskStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.task.CustomerTaskTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.*;

/**
 * 门店解绑客户原子能力
 */
@Service
@Slf4j
public class PoiUnBindCustomerAtomService {

    @Autowired
    private WmCustomerPoiRelService wmCustomerPoiRelService;

    @Autowired
    private WmCustomerPoiDBMapper wmCustomerPoiDBMapper;

    private static final ThreadFactory THREAD_FACTORY = new ThreadFactoryBuilder().setNameFormat("CUSTOMER_POI_REL_POOL_%d").build();


    private static ExecutorService executorService = TraceExecutors.getTraceExecutorService(
            new ThreadPoolExecutor(10, 30, 30L, TimeUnit.MINUTES, new ArrayBlockingQueue<Runnable>(200), THREAD_FACTORY,
                    new RejectedExecutionHandler() {
                        @Override
                        public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
                            if (!executorService.isShutdown()) {
                                try {
                                    executor.getQueue().put(r);
                                } catch (InterruptedException e) {
                                    throw new RejectedExecutionException("Reject from " + executor.toString());
                                }
                            } else {
                                throw new RejectedExecutionException("Executor " + executor.toString() + " have shutdown.");
                            }
                        }
                    }));

    /**
     * 客户与门店直接解绑原子能力
     *
     * @param customerId
     * @param wmPoiIdSet
     * @param poiUnBindMaps
     */
    public void directUnbindCustomer(Integer customerId, Set<Long> wmPoiIdSet, Map<Long, Integer> poiUnBindMaps) {
        Integer taskId = null;
        for (Long wmPoiId : wmPoiIdSet) {
            if (MapUtils.isNotEmpty(poiUnBindMaps) && poiUnBindMaps.get(wmPoiId) != null) {
                taskId = poiUnBindMaps.get(wmPoiId);
            }
            // 批量删除门店和客户的关系
            wmCustomerPoiRelService.unBindCustomerPoi(wmPoiId, customerId, taskId);
        }
    }

    /**
     * 客户与门店关系更新解绑确认中
     *
     * @param customerId
     * @param wmPoiIdSet
     * @param poiUnBindMaps
     */
    public void poiPreUnBindCustomer(Integer customerId, Set<Long> wmPoiIdSet, Map<Long, Integer> poiUnBindMaps) {
        if (CollectionUtils.isEmpty(wmPoiIdSet)) {
            return;
        }
        executorService.execute(new TraceRunnable(new Runnable() {
            @Override
            public void run() {
                try {
                    int batchUpdateMax = MccCustomerConfig.getBatchDealCustomerPoiCount();
                    List<List<WmCustomerPoiDB>> lists = Lists.newArrayList();
                    //按照每100个分组，组装更新客户门店关系list对象
                    List<List<Long>> partWmPoiIdList = Lists.partition(Lists.newArrayList(wmPoiIdSet), batchUpdateMax);
                    for (List<Long> wmPoiIdList : partWmPoiIdList) {
                        List<WmCustomerPoiDB> poiDBList = listUpdateWmPoiDB(wmPoiIdList, customerId, poiUnBindMaps);
                        lists.add(poiDBList);
                    }

                    //门店任务关联MAP为空则走原更新方法
                    if (CollectionUtils.isEmpty(poiUnBindMaps)) {
                        for (List<Long> list : partWmPoiIdList) {
                            wmCustomerPoiDBMapper.batchUpdateCustomerPoiRelationByParams(list, CustomerConstants.IS_UNBINDING_YES, CustomerRelationStatusEnum.CONFIRM_UNBINDING.getCode(), null, customerId);
                            Uninterruptibles.sleepUninterruptibly(500, TimeUnit.MILLISECONDS);
                        }
                    } else {
                        for (List<WmCustomerPoiDB> poiDBList : lists) {
                            wmCustomerPoiDBMapper.batchUpdateCustomerPoiRelationByList(poiDBList);
                            Uninterruptibles.sleepUninterruptibly(500, TimeUnit.MILLISECONDS);
                        }
                    }
                } catch (Exception e) {
                    log.error("PoiUnBindCustomerAtomService.poiPreUnBindCustomer失败 wmPoiIdSet={},customerId={}", JSONObject.toJSONString(wmPoiIdSet), customerId, e);
                }
            }
        }));
    }


    /**
     * 取消解绑
     *
     * @param customerId
     * @param wmPoiIdSet
     */
    public void cancelUnBind(Integer customerId, Set<Long> wmPoiIdSet) {
        if (CollectionUtils.isEmpty(wmPoiIdSet)) {
            return;
        }
        executorService.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    List<List<Long>> partWmPoiIdList = Lists.partition(Lists.newArrayList(wmPoiIdSet), 100);
                    for (List<Long> wmPoiIdList : partWmPoiIdList) {
                        int isUnbindingStatus = CustomerConstants.IS_UNBINDING_NO;
                        wmCustomerPoiDBMapper.batchCancelUnBind(wmPoiIdList, isUnbindingStatus, CustomerRelationStatusEnum.BIND.getCode(), customerId);
                        Uninterruptibles.sleepUninterruptibly(500, TimeUnit.MILLISECONDS);
                    }
                } catch (Exception e) {
                    log.error("rejectUnBind失败 wmPoiIdSet={},customerId={},switchTaskId={}", JSONObject.toJSONString(wmPoiIdSet), customerId, null, e);
                }
            }
        });
    }

    /**
     * 获取本批次的更新对象列表
     *
     * @param wmPoiIdList
     * @param customerId
     * @param poiUnBindMaps
     * @return
     */
    private List<WmCustomerPoiDB> listUpdateWmPoiDB(List<Long> wmPoiIdList, Integer customerId, Map<Long, Integer> poiUnBindMaps) {
        List<WmCustomerPoiDB> batchList = Lists.newArrayList();
        for (Long wmPoiId : wmPoiIdList) {
            Integer bizTaskId = (CollectionUtils.isEmpty(poiUnBindMaps) || poiUnBindMaps.get(wmPoiId) == null) ? 0 : poiUnBindMaps.get(wmPoiId);
            WmCustomerPoiDB wmCustomerPoiDB = getConfirmUnBindingDB(customerId, wmPoiId, bizTaskId);
            batchList.add(wmCustomerPoiDB);
        }
        return batchList;
    }

    /**
     * 获取确认解绑中对象
     *
     * @param customerId
     * @param wmPoiId
     * @param bizTaskId
     * @return
     */
    private WmCustomerPoiDB getConfirmUnBindingDB(Integer customerId, Long wmPoiId, Integer bizTaskId) {
        WmCustomerPoiDB wmCustomerPoiDB = new WmCustomerPoiDB();
        wmCustomerPoiDB.setWmPoiId(wmPoiId);
        wmCustomerPoiDB.setIsUnbinding(CustomerConstants.IS_UNBINDING_YES);
        wmCustomerPoiDB.setRelationStatus(CustomerRelationStatusEnum.CONFIRM_UNBINDING.getCode());
        wmCustomerPoiDB.setSwitchTaskId(null);
        wmCustomerPoiDB.setBizTaskId(bizTaskId);
        wmCustomerPoiDB.setCustomerId(customerId);
        return wmCustomerPoiDB;
    }
}

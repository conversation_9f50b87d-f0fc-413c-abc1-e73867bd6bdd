package com.sankuai.meituan.waimai.customer.service.kp;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerKpAuditMapper;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerKpDBMapper;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerKpAudit;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerSensitiveWordsService;
import com.sankuai.meituan.waimai.customer.service.kp.dboperator.KpDBOperateImpl;
import com.sankuai.meituan.waimai.thrift.customer.constant.*;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.KpVersionEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.PreAuthResultTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.PreAuthResultBO;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.KpLegalAuthMonitorReq;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * KP法人授权流程添加监控处理
 *
 * <AUTHOR>
 * @date 20221222
 */
@Service
@Slf4j
public class KpLegalAuthMonitorService {

    @Autowired
    private WmCustomerKpDBMapper wmCustomerKpDBMapper;

    @Autowired
    private WmCustomerSensitiveWordsService wmCustomerSensitiveWordsService;

    @Autowired
    public KpDBOperateImpl kpDBOperate;

    @Autowired
    private WmCustomerKpService wmCustomerKpService;

    @Autowired
    private WmCustomerKpAuditMapper wmCustomerKpAuditMapper;

    /**
     * 校验变更后的数据是否符合规则
     *
     * @param kpLegalAuthMonitorReq
     * @return
     */
    public String checkChangeKpInfo(KpLegalAuthMonitorReq kpLegalAuthMonitorReq) {
        try {
            log.info("checkChangeKpInfo,KP信息发生变更开始校验数据,kpLegalAuthMonitorReq={}", JSON.toJSONString(kpLegalAuthMonitorReq));
            //关键参数确实则不校验
            if (kpLegalAuthMonitorReq == null || kpLegalAuthMonitorReq.getKpType() == null
                    || kpLegalAuthMonitorReq.getId() == null) {
                return null;
            }
            String opType = kpLegalAuthMonitorReq.getOperateType();
            Integer kpType = kpLegalAuthMonitorReq.getKpType();
            //KP类型只关注签约人和法人
            if (kpType != KpTypeEnum.LEGAL.getType() && kpType != KpTypeEnum.SIGNER.getType()) {
                return null;
            }
            switch (opType) {
                case "INSERT":
                    checkInsertData(kpLegalAuthMonitorReq);
                    break;
                case "UPDATE":
                    checkUpdateData(kpLegalAuthMonitorReq);
                    break;
                default:
                    return null;
            }
            return null;
        } catch (Exception e) {
            log.error("checkChangeKpInfo,KP表信息发生变更校验数据合法性发生异常,kpLegalAuthMonitorReq={}", JSON.toJSONString(kpLegalAuthMonitorReq), e);
        }
        return null;
    }

    /**
     * 校验新增的数据是否符合规则
     *
     * @param kpLegalAuthMonitorReq
     * @return
     */
    private String checkInsertData(KpLegalAuthMonitorReq kpLegalAuthMonitorReq) {
        Integer kpType = kpLegalAuthMonitorReq.getKpType();
        if (kpType == KpTypeEnum.SIGNER.getType()) {
            return checkKpSignerInsertData(kpLegalAuthMonitorReq);
        } else if (kpType == KpTypeEnum.LEGAL.getType()) {
            return checkKpLegalInsertData(kpLegalAuthMonitorReq);
        }
        return null;
    }

    /**
     * 更新操作的BCP校验
     *
     * @param kpLegalAuthMonitorReq
     * @return
     */
    private String checkUpdateData(KpLegalAuthMonitorReq kpLegalAuthMonitorReq) {
        Integer kpType = kpLegalAuthMonitorReq.getKpType();
        if (kpType == KpTypeEnum.SIGNER.getType()) {
            return checkKpSignerUpdateData(kpLegalAuthMonitorReq);
        } else if (kpType == KpTypeEnum.LEGAL.getType()) {
            return checkKpLegalUpdateData(kpLegalAuthMonitorReq);
        }
        return null;
    }


    /**
     * 签约人KP新增规则校验
     *
     * @param kpLegalAuthMonitorReq
     * @return
     */
    private String checkKpSignerInsertData(KpLegalAuthMonitorReq kpLegalAuthMonitorReq) {
        Integer signType = kpLegalAuthMonitorReq.getSignerType();
        if (signType == KpSignerTypeEnum.AGENT.getType()
                && (kpLegalAuthMonitorReq.getLegalAuthType() == null ||
                kpLegalAuthMonitorReq.getLegalAuthType() == LegalAuthTypeEnum.UN_SETTING.getCode())) {
            log.info("新增的签约代理人授权方式非法,kpLegalAuthMonitorReq={}", JSON.toJSONString(kpLegalAuthMonitorReq));
            return String.format("新增的签约代理人授权方式非法,id=%s,customerId=%s,legalAuthType=%s",
                    kpLegalAuthMonitorReq.getId(), kpLegalAuthMonitorReq.getCustomerId(), kpLegalAuthMonitorReq.getLegalAuthType());
        }
        return null;
    }

    /**
     * 校验KP法人数据是否合法
     *
     * @param kpLegalAuthMonitorReq
     * @return
     */
    private String checkKpLegalInsertData(KpLegalAuthMonitorReq kpLegalAuthMonitorReq) {
        //全量开关打开后必须都是V3版本号
        if (kpLegalAuthMonitorReq.getVersion() == null
                || kpLegalAuthMonitorReq.getVersion() != KpVersionEnum.V3.getCode()) {
            log.info("新增KP法人数据版本号非空或者不是V3,kpLegalAuthMonitorReq={}", JSON.toJSONString(kpLegalAuthMonitorReq));
            return String.format("新增KP法人数据版本号非空或者不是V3,id=%s,customerId=%s", kpLegalAuthMonitorReq.getId(), kpLegalAuthMonitorReq.getCustomerId());
        }
        //KP版本为V3校验状态
        if (kpLegalAuthMonitorReq.getVersion() != null
                && kpLegalAuthMonitorReq.getVersion() == KpVersionEnum.V3.getCode()) {
            Integer state = kpLegalAuthMonitorReq.getState();
            if (KpLegalStateMachine.getByState(state.byteValue()) == null) {
                log.info("KP法人版本号为V3状态非有效状态,kpLegalAuthMonitorReq={}", JSON.toJSONString(kpLegalAuthMonitorReq));
                return String.format("KP法人版本号为V3状态非有效状态,id=%s,customerId=%s,state=%s", kpLegalAuthMonitorReq.getId(), kpLegalAuthMonitorReq.getCustomerId(), state);
            }
        }
        return null;
    }

    /**
     * 签约人KP更新数据合法性校验
     *
     * @param kpLegalAuthMonitorReq
     * @return
     */
    private String checkKpSignerUpdateData(KpLegalAuthMonitorReq kpLegalAuthMonitorReq) {
        String tableName = kpLegalAuthMonitorReq.getTableName();
        //temp表的kpId不能为空
        if (tableName.equals("wm_customer_kp_temp") && kpLegalAuthMonitorReq.getKpId() == null) {
            return null;
        }
        //KP主表的ID
        Integer kpId = kpLegalAuthMonitorReq.getId();
        if (tableName.equals("wm_customer_kp_temp")) {
            kpId = kpLegalAuthMonitorReq.getKpId();
        }
        Integer signType = kpLegalAuthMonitorReq.getSignerType();
        Integer state = kpLegalAuthMonitorReq.getState();
        Integer customerId = kpLegalAuthMonitorReq.getCustomerId();
        //签约方式不是代理人但是状态更新为 短信授权中
        if (signType != null
                && signType != KpSignerTypeEnum.AGENT.getType()
                && kpLegalAuthMonitorReq.getState() == KpSignerStateMachine.LEGAL_AUTH_AUTHORIZE_ING.getState()) {
            log.info("签约人签约方式非代理人，状态为短信授权中,kpLegalAuthMonitorReq={}", JSON.toJSONString(kpLegalAuthMonitorReq));
            return String.format("签约人签约方式非代理人，状态为短信授权中,id=%s,kpId=%s,state=%s,tableName=%s",
                    kpLegalAuthMonitorReq.getId(), kpId,
                    kpLegalAuthMonitorReq.getState(), kpLegalAuthMonitorReq.getTableName());
        }
        //签约人+代理人+短信授权
        if (signType != null && signType == KpSignerTypeEnum.AGENT.getType()
                && kpLegalAuthMonitorReq.getLegalAuthType() != null
                && kpLegalAuthMonitorReq.getLegalAuthType() == LegalAuthTypeEnum.MESSAGE_AUTH.getCode()) {
            //变更为生效
            if (state == KpSignerStateMachine.EFFECT.getState()) {
                //确认KP法人数据是否为生效
                WmCustomerKp wmCustomerKp = wmCustomerKpService.getEffectiveLegalKp(customerId);
                if (wmCustomerKp == null) {
                    log.info("签约代理人授权方式为短信签约，状态更新为生效时KP法人无生效数据,kpLegalAuthMonitorReq={}", JSON.toJSONString(kpLegalAuthMonitorReq));
                    return String.format("签约代理人授权方式为短信签约，状态更新为生效时KP法人无生效数据,id=%s,kpId={},tableName=%s",
                            kpLegalAuthMonitorReq.getId(), kpId, kpLegalAuthMonitorReq.getTableName());
                }
            } else if (state == KpSignerStateMachine.LEGAL_AUTH_AUTHORIZE_ING.getState()) {
                //变更为短信授权中，确认是否存在短信授权签约的任务
                WmCustomerKpAudit wmCustomerKpAudit = wmCustomerKpAuditMapper.selectByKpIdAndType(kpId, Integer.valueOf(KpAuditConstants.TYPE_AUTH_FOR_SIGNER_AGENT_LEGAL));
                if (wmCustomerKpAudit == null) {
                    log.info("签约代理人授权方式为短信签约，状态更新授权中未查询到有效的短信授权审核任务,kpLegalAuthMonitorReq={}", JSON.toJSONString(kpLegalAuthMonitorReq));
                    return String.format("签约代理人授权方式为短信签约，状态更新授权中未查询到有效的短信授权审核任务,id=%s,kpId={},tableName=%s",
                            kpLegalAuthMonitorReq.getId(), kpId, kpLegalAuthMonitorReq.getTableName());
                }
            }
        }
        return null;
    }

    /**
     * KP法人数据更新合法性校验
     *
     * @return
     */
    private String checkKpLegalUpdateData(KpLegalAuthMonitorReq kpLegalAuthMonitorReq) {
        String tableName = kpLegalAuthMonitorReq.getTableName();
        //temp表的kpId不能为空
        if (tableName.equals("wm_customer_kp_temp") && kpLegalAuthMonitorReq.getKpId() == null) {
            return null;
        }
        //KP主表的ID
        Integer kpId = kpLegalAuthMonitorReq.getId();
        if (tableName.equals("wm_customer_kp_temp")) {
            kpId = kpLegalAuthMonitorReq.getKpId();
        }

        //KP版本为V3校验状态
        if (kpLegalAuthMonitorReq.getVersion() != null
                && kpLegalAuthMonitorReq.getVersion() == KpVersionEnum.V3.getCode()) {
            Integer state = kpLegalAuthMonitorReq.getState();
            if (KpLegalStateMachine.getByState(state.byteValue()) == null) {
                log.info("KP法人版本号为V3状态非有效状态,kpLegalAuthMonitorReq={}", JSON.toJSONString(kpLegalAuthMonitorReq));
                return String.format("KP法人版本号为V3状态非有效状态,id=%s,kpId=%s,state=%s",
                        kpLegalAuthMonitorReq.getId(),
                        kpId, state);
            }
            // Kp法人新版本数据只能是身份证
            if (kpLegalAuthMonitorReq.getCertType() != null
                    && kpLegalAuthMonitorReq.getCertType() != CertTypeEnum.ID_CARD.getType()) {
                log.info("KP法人版本号为V3但是证件类型非身份证,kpLegalAuthMonitorReq={}", JSON.toJSONString(kpLegalAuthMonitorReq));
                return String.format("KP法人版本号为V3但是证件类型非身份证,id=%s,kpId=%s,state=%s",
                        kpLegalAuthMonitorReq.getId(),
                        kpId, state);
            }
        }

        Integer state = kpLegalAuthMonitorReq.getState();
        if (state != null && state == KpLegalStateMachine.EFFECT.getState() && MccCustomerConfig.getCheckKpLegalCertNumPreSwitch()) {
            WmCustomerKp wmCustomerKp = wmCustomerKpDBMapper.selectByPrimaryKey(kpLegalAuthMonitorReq.getId());
            if (wmCustomerKp == null) {
                log.info("checkKpLegalUpdateData,未查询到有效的KP信息,kpId={}", kpId);
                return null;
            }
            wmCustomerSensitiveWordsService.readKpWhenSelect(wmCustomerKp);
            //实名认证流程
            PreAuthResultBO result = kpDBOperate.preAuthAndErrorMsgNew(wmCustomerKp, 0, "系统");
            String preAuthErrorMsg = (result == null ? null : (result.getResult() == PreAuthResultTypeEnum.FAIL.getType() ? result.getMsg() : null));
            //实名失败则需要报警
            if (StringUtils.isNotBlank(preAuthErrorMsg)) {
                log.info("KP法人状态为生效但是证件号实名未通过,kpLegalAuthMonitorReq={}", JSON.toJSONString(kpLegalAuthMonitorReq));
                return String.format("KP法人状态为生效但是证件号实名未通过,id=%s,kpId=%s",
                        kpLegalAuthMonitorReq.getId(),
                        kpId);
            }
        }
        return null;
    }
}

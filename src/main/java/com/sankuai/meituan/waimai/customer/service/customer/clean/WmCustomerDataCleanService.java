package com.sankuai.meituan.waimai.customer.service.customer.clean;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import com.meituan.mtrace.util.RateLimiter;
import com.meituan.mtrace.util.StringUtils;
import com.sankuai.meituan.waimai.customer.adapter.WmPoiFlowlineLabelThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmCustomerOplogService;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerDBMapper;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerPoiDBMapper;
import com.sankuai.meituan.waimai.customer.ddd.adapter.MtCustomerThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.ddd.adapter.WmPoiQueryAdapter;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerCleanQuery;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiDB;
import com.sankuai.meituan.waimai.customer.domain.job.DateLimitDTO;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerESService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.customer.util.ExecutorUtil;
import com.sankuai.meituan.waimai.label.constants.LabelSubjectTypeEnum;
import com.sankuai.meituan.waimai.poi.constants.PoiOrgEnum;
import com.sankuai.meituan.waimai.thrift.constants.WmPoiFieldQueryConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerRealTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.CustomerBizOrgEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.WmCustomerOplogBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.job.TempCustomerDataUpdateJobParam;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiLabelRel;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 20230118
 * @desc 客户基础字段清洗使用服务
 */
@Service
@Slf4j
public class WmCustomerDataCleanService {

    @Autowired
    private WmCustomerService wmCustomerService;

    @Autowired
    private WmCustomerDBMapper wmCustomerDBMapper;

    @Autowired
    private WmCustomerPoiDBMapper wmCustomerPoiDBMapper;

    @Autowired
    private WmPoiQueryAdapter wmPoiQueryAdapter;

    @Autowired
    private WmCustomerESService wmCustomerESService;

    @Autowired
    private MtCustomerThriftServiceAdapter mtCustomerThriftServiceAdapter;

    @Autowired
    private WmCustomerOplogService wmCustomerOplogService;

    @Autowired
    private WmPoiFlowlineLabelThriftServiceAdapter wmPoiFlowlineLabelThriftServiceAdapter;

    /**
     * 闪购业务 的客户类型
     */
    private static List<Integer> sgMedCustomerRealTypes = Arrays.asList(7, 8, 10, 11, 12, 13, 14, 19, 20);

    /**
     * 医药业务 的客户类型
     */
    private static List<Integer> medCustomerRealTypes = Arrays.asList(9, 16, 18);

    /**
     * 美食城、食堂、大连锁、小连锁、流量连锁
     */
    private static List<Integer> wmCustomerRealTypes = Arrays.asList(1, 2, 3, 4, 5, 6, 15, 17);


    /**
     * 根据任务请求参数清洗客户的客户类型字段
     *
     * @param jobParam
     */
    public void dealCustomerRealTypeByParams(TempCustomerDataUpdateJobParam jobParam) throws WmCustomerException {
        log.info("dealCustomerRealTypeByParams,根据参数开始处理客户数据,jobParam={}", JSON.toJSONString(jobParam));
        int mode = jobParam.getMode();
        if (mode == 0 || (mode != 1 && mode != 2)) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "mode参数不合法");
        }
        if (mode == 1 && CollectionUtils.isEmpty(jobParam.getCustomerIdList())) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "mode为1参数customerIdList不能为空");
        }
        try {
            //小于0则默认为500
            if (jobParam.getPageNum() <= 0) {
                jobParam.setPageNum(500);
            }
            //限流小于0默认设置为30
            int rateLimit = jobParam.getRateLimit();
            if (rateLimit < 1) {
                rateLimit = 30;
            }

            //获取多资金账户客户ID
            List<Integer> multiZjCustomerIdList = MccCustomerConfig.getMultiZjAccountCustomerListConfig();

            //按照客户ID处理
            if (mode == 1) {
                WmCustomerCleanQuery queryVo = new WmCustomerCleanQuery();
                queryVo.setIds(jobParam.getCustomerIdList());
                queryVo.setPageSize(jobParam.getPageNum());
                List<WmCustomerDB> wmCustomerDBList = wmCustomerService.listWmCustomerDBByCleanQuery(queryVo);
                if (CollectionUtils.isEmpty(wmCustomerDBList)) {
                    log.info("dealCustomerRealTypeByParams,根据客户ID未查询到客户信息，不再处理");
                    return;
                }
                //获取本次处理的限流配置
                RateLimiter rateLimiter = RateLimiter.create(getLimitRate(rateLimit));
                log.info("getLimitRate,根据配置计算当前限流，rateLimit={}", rateLimit);
                for (WmCustomerDB wmCustomerDB : wmCustomerDBList) {
                    updateCustomerRealType(wmCustomerDB, rateLimiter);
                }
                log.info("dealCustomerRealTypeByParams,处理完成");
            } else if (mode == 2) {
                //按照参数批量处理
                int minId = jobParam.getMinId();
                int maxId = jobParam.getMaxId();

                while (true) {
                    //批量处理数据临时关闭开关
                    if (MccCustomerConfig.getCleanCustomerRealTypeAndBizCodeStopSwitch()) {
                        log.info("dealCustomerRealTypeByParams|批量洗数据开关打开，任务结束不再执行.");
                        break;
                    }

                    WmCustomerCleanQuery queryVo = new WmCustomerCleanQuery();
                    queryVo.setPageSize(jobParam.getPageNum());
                    queryVo.setMinId(minId);
                    queryVo.setMaxId(maxId);
                    List<WmCustomerDB> wmCustomerDBList = wmCustomerService.listWmCustomerDBByCleanQuery(queryVo);
                    if (CollectionUtils.isEmpty(wmCustomerDBList)) {
                        log.info("dealCustomerRealTypeByParams,根据客户ID未查询到客户信息，不再处理");
                        break;
                    }

                    //根据时间规则重新计算限流配置
                    RateLimiter rateLimiter = RateLimiter.create(getLimitRate(rateLimit));
                    log.info("getLimitRate,根据配置计算当前限流，rateLimit={}", rateLimit);
                    for (WmCustomerDB wmCustomerDB : wmCustomerDBList) {
                        //多资金账户的客户ID不能再批量任务进行处理，只能灰度名单中处理
                        if (CollectionUtils.isNotEmpty(multiZjCustomerIdList)
                                && multiZjCustomerIdList.contains(wmCustomerDB.getId())) {
                            log.info("dealCustomerRealTypeByParams,在多资金账户客户ID名单中，批量处理模式不进行处理,customerId={}", wmCustomerDB.getId());
                            continue;
                        }
                        updateCustomerRealType(wmCustomerDB, rateLimiter);
                    }
                    minId = wmCustomerDBList.get(wmCustomerDBList.size() - 1).getId();
                    log.info("dealCustomerRealTypeByParams,本批次处理完成,minId={},count={}", minId, wmCustomerDBList.size());
                }
            }
        } catch (Exception e) {
            log.error("dealCustomerRealTypeByParams发生异常,jobParam={}", JSON.toJSONString(jobParam), e);
        }

    }

    /**
     * 批量刷新单店美食城客户类型
     *
     * @param customerIdList
     */
    public List<Integer> batchRefreshSinglePoiFoodCityCustomer(List<String> customerIdList) {
        log.info("batchRefreshSinglePoiFoodCityCustomer,开始处理单店美食城客户类型清洗任务 customerIdList:{}", customerIdList);
        List<Integer> notUpdatedCustomerIdList = new ArrayList<>();
        if (CollectionUtils.isEmpty(customerIdList)) {
            return notUpdatedCustomerIdList;
        }

        int rateLimit = 30;
        Set<Long> mtCustomerIdSet = new HashSet<>();
        for (String customerId : customerIdList) {
            mtCustomerIdSet.add(Long.parseLong(customerId));
        }
        List<WmCustomerDB> wmCustomerDBS = wmCustomerDBMapper.selectCustomerByMtCustomerIdList(mtCustomerIdSet);
        if (CollectionUtils.isEmpty(wmCustomerDBS)) {
            log.info("batchRefreshSinglePoiFoodCityCustomer,根据客户ID未查询到客户信息，不再处理");
            return notUpdatedCustomerIdList;
        }
        RateLimiter rateLimiter = RateLimiter.create(getLimitRate(rateLimit));
        for (WmCustomerDB wmCustomerDB : wmCustomerDBS) {
            boolean isUpdated = refreshSinglePoiFoodCityCustomer(wmCustomerDB, rateLimiter);
            if (!isUpdated) {
                notUpdatedCustomerIdList.add(wmCustomerDB.getId());
            }
        }
        return notUpdatedCustomerIdList;
    }


    /**
     * 根据客户信息处理客户类型字段
     *
     * @param wmCustomerDB
     */
    private void updateCustomerRealType(WmCustomerDB wmCustomerDB, RateLimiter rateLimiter) {
        Integer oldCustomerRealType = wmCustomerDB.getCustomerRealType();
        //开启限流设置
        rateLimiter.acquire();

        //处理客户类型字段开关-默认开启
        if (MccCustomerConfig.getCleanCustomerRealTypeDealSwitch()) {
            Integer customerRealTypeNew = calculateCustomerRealType(wmCustomerDB);
            //新设置的客户类型大于0且与当前不一致则需要更新
            if (customerRealTypeNew != null && customerRealTypeNew > 0 && customerRealTypeNew != oldCustomerRealType) {
                wmCustomerDB.setCustomerRealType(customerRealTypeNew);
                updateCustomerRealType(wmCustomerDB.getId(), customerRealTypeNew);
                //修改客户类型记录操作记录
                insertCustomerRealTypeChangeOpLog(wmCustomerDB.getId(), oldCustomerRealType, customerRealTypeNew);
                log.info("WmCustomerDataCleanService#updateCustomerRealType,更新客户类型完成，customerId={},customerRealTypeNew={},oldCustomerRealType={}",
                        wmCustomerDB.getId(), CustomerRealTypeEnum.getNameByValue(customerRealTypeNew),
                        (oldCustomerRealType == null || oldCustomerRealType == 0) ? "无" : CustomerRealTypeEnum.getNameByValue(oldCustomerRealType));
            }
        }

        //查询最新的客户类型，并更新业务线
        updateBizOrgCodeByCustomerRealType(wmCustomerDB);
    }

    /**
     * 刷新单店美食城客户
     *
     * @param wmCustomerDB
     */
    private boolean refreshSinglePoiFoodCityCustomer(WmCustomerDB wmCustomerDB, RateLimiter rateLimiter) {
        Integer oldCustomerRealType = wmCustomerDB.getCustomerRealType();
        if (oldCustomerRealType == null || CustomerRealTypeEnum.MEISHICHENG.getValue() != oldCustomerRealType) {
            log.info("refreshSinglePoiFoodCityCustomer, 当前客户类型不是单店美食城，不进行处理,customerId={}", wmCustomerDB.getId());
            return false;
        }
        //开启限流设置
        rateLimiter.acquire();
        //查询客户下门店

        List<WmCustomerPoiDB> customerPoiList = listByCustomerId(wmCustomerDB.getId());
        if (CollectionUtils.isEmpty(customerPoiList)) {
            log.info("refreshSinglePoiFoodCityCustomer,当前客户下无门店信息,customerId={}", wmCustomerDB.getId());
            return false;
        }
        Integer poiCount = customerPoiList.size();
        if (poiCount > 1) {
            log.info("refreshSinglePoiFoodCityCustomer,当前客户下门店数量大于1,customerId={},poiCount={}", wmCustomerDB.getId(), poiCount);
            return false;
        }
        // 单店美食城需要变更客户类型
        Integer customerRealTypeNew = CustomerRealTypeEnum.DANDIAN.getValue();
        wmCustomerDB.setCustomerRealType(customerRealTypeNew);
        wmCustomerDB.setCustomerRealTypeSpInfo("");
        wmCustomerDBMapper.updateCustomer(wmCustomerDB);

        //去除客户及门店标签
        WmPoiLabelRel customerLabel = wmPoiFlowlineLabelThriftServiceAdapter.selectLabelRelCache(wmCustomerDB.getMtCustomerId(), MccCustomerConfig.getAuditedMSCCustomerLabel(), LabelSubjectTypeEnum.CUSTOMER.getCode());
        if (customerLabel != null && customerLabel.getId() > 0L) {
            //客户去标
            wmPoiFlowlineLabelThriftServiceAdapter.delCustomerLabelRel(MccCustomerConfig.getAuditedMSCCustomerLabel(),
                    wmCustomerDB.getMtCustomerId(), 0, "客户系统");
            List<WmCustomerPoiDB> wmCustomerPoiDBList = wmCustomerPoiDBMapper.selectWmCustomerPoiByCustomerId(wmCustomerDB.getId());
            List<Long> wmPoiIdList = wmCustomerPoiDBList.stream().map(x -> x.getWmPoiId()).collect(Collectors.toList());
            //门店去标
            wmPoiFlowlineLabelThriftServiceAdapter.batchDeleteLabelToPois(MccCustomerConfig.getMSCWmPoiLabel(), wmPoiIdList, 0, "客户系统");
        }

        //修改客户类型记录操作记录
        insertCustomerRealTypeChangeOpLog(wmCustomerDB.getId(), oldCustomerRealType, customerRealTypeNew);
        log.info("WmCustomerDataCleanService#updateCustomerRealType,更新客户类型完成，customerId={},customerRealTypeNew={},oldCustomerRealType={}",
                wmCustomerDB.getId(), CustomerRealTypeEnum.getNameByValue(customerRealTypeNew),
                (oldCustomerRealType == null || oldCustomerRealType == 0) ? "无" : CustomerRealTypeEnum.getNameByValue(oldCustomerRealType));
        return true;
    }

    /**
     * 重新计算客户类型是否变更
     *
     * @param wmCustomerDB
     * @return
     */
    private Integer calculateCustomerRealType(WmCustomerDB wmCustomerDB) {
        Integer customerRealType = wmCustomerDB.getCustomerRealType();
        Integer customerRealTypeNew = null;
        switch (customerRealType) {
            case 0:
                customerRealTypeNew = dealNoCustomerRealTypeData(wmCustomerDB);
                break;
            case 1:
                customerRealTypeNew = dealWmSingleTypeData(wmCustomerDB);
                break;
            case 2:
            case 3:
            case 4:
            case 5:
            case 6:
                customerRealTypeNew = dealPartMultiWmTypeData(wmCustomerDB);
                break;
            case 7:
            case 8:
            case 10:
            case 11:
            case 12:
            case 13:
            case 14:
            case 19:
                customerRealTypeNew = dealSgCustomerRealTypeData(wmCustomerDB);
                break;
            case 20:
                customerRealTypeNew = dealSgDanDian(wmCustomerDB);
                break;
            default:
                break;
        }
        return customerRealTypeNew;
    }


    /**
     * 处理无客户类型的数据-即客户类型为0
     *
     * @param wmCustomerDB
     */
    private Integer dealNoCustomerRealTypeData(WmCustomerDB wmCustomerDB) {
        log.info("WmCustomerDataCleanService#dealNoCustomerRealTypeData,开始处理客户类型为空的数据,wmCustomerDb={}", JSON.toJSONString(wmCustomerDB));
        List<WmCustomerPoiDB> customerPoiList = listByCustomerId(wmCustomerDB.getId());
        Integer oldCustomerRealType = wmCustomerDB.getCustomerRealType();
        Integer customerRealTypeNew = oldCustomerRealType;
        //未绑定门店更新为外卖单店
        if (CollectionUtils.isEmpty(customerPoiList)) {
            customerRealTypeNew = CustomerRealTypeEnum.DANDIAN.getValue();
        } else {
            Integer poiCount = customerPoiList.size();
            //只有一个门店
            if (poiCount == 1) {
                Long wmPoiId = customerPoiList.get(0).getWmPoiId();
                WmPoiAggre wmPoiAggre = getWmPoiInfoById(customerPoiList.get(0).getWmPoiId());
                if (wmPoiAggre == null) {
                    log.error("dealNoCustomerRealTypeData,根据门店ID未查询到门店信息，请关注此门店,wmPoiId={}", wmPoiId);
                    return null;
                }
                Integer bizOrgCode = wmPoiAggre.getBiz_org_code();
                if (bizOrgCode == PoiOrgEnum.WAI_MAI.getCode()) {
                    customerRealTypeNew = CustomerRealTypeEnum.DANDIAN.getValue();
                } else if (bizOrgCode == PoiOrgEnum.SHAN_GOU.getCode()) {
                    customerRealTypeNew = CustomerRealTypeEnum.DANDIAN_SG.getValue();
                } else if (bizOrgCode == PoiOrgEnum.MEDICINE.getCode()) {
                    customerRealTypeNew = CustomerRealTypeEnum.DANDIAN_YAOPIN.getValue();
                } else {
                    log.info("dealNoCustomerRealTypeData,客户只绑定一个门店但门店bizOrgCode非外卖、非医药、非闪购,customerId={},wmPoiId={},bizOrgCode={}",
                            wmCustomerDB.getId(), wmPoiAggre.getWm_poi_id(), wmPoiAggre.getBiz_org_code());
                }
            } else {
                Boolean sgSmallChain = true;
                for (WmCustomerPoiDB wmCustomerPoiDB : customerPoiList) {
                    WmPoiAggre wmPoiAggre = getWmPoiInfoById(wmCustomerPoiDB.getWmPoiId());
                    if (wmPoiAggre.getBiz_org_code() != PoiOrgEnum.SHAN_GOU.getCode()) {
                        sgSmallChain = false;
                        break;
                    }
                }
                //门店都是闪购业务则为闪购小连锁
                if (sgSmallChain) {
                    customerRealTypeNew = CustomerRealTypeEnum.SG_CKA.getValue();
                } else {
                    customerRealTypeNew = CustomerRealTypeEnum.XIAOLIANSUO.getValue();
                }
            }
        }

        return customerRealTypeNew;
    }

    /**
     * 处理外卖单店的数据-客户类型为1
     *
     * @param wmCustomerDB
     */
    private Integer dealWmSingleTypeData(WmCustomerDB wmCustomerDB) {
        log.info("WmCustomerDataCleanService#dealWmSingleTypeData,开始处理客户类型为单店的数据,wmCustomerDb={}", JSON.toJSONString(wmCustomerDB));
        List<WmCustomerPoiDB> customerPoiList = listByCustomerId(wmCustomerDB.getId());
        Integer oldCustomerRealType = wmCustomerDB.getCustomerRealType();
        if (CollectionUtils.isEmpty(customerPoiList)) {
            return oldCustomerRealType;
        }

        Integer customerRealTypeNew = oldCustomerRealType;
        if (customerPoiList.size() > 1) {
            log.info("WmCustomerDataCleanService#dealWmSingleTypeData,客户类型单店绑定门店数超过1个,customerId={},count={}", wmCustomerDB.getId(), customerPoiList.size());
            Boolean sgSmallChain = true;
            for (WmCustomerPoiDB wmCustomerPoiDB : customerPoiList) {
                WmPoiAggre wmPoiAggre = getWmPoiInfoById(wmCustomerPoiDB.getWmPoiId());
                if (wmPoiAggre.getBiz_org_code() == PoiOrgEnum.WAI_MAI.getCode()) {
                    sgSmallChain = false;
                    break;
                }
            }
            //是否为闪购小连锁
            if (sgSmallChain) {
                return CustomerRealTypeEnum.SG_CKA.getValue();
            } else {
                return CustomerRealTypeEnum.XIAOLIANSUO.getValue();
            }
        } else {
            WmPoiAggre wmPoiAggre = getWmPoiInfoById(customerPoiList.get(0).getWmPoiId());
            if (wmPoiAggre == null) {
                log.error("WmCustomerDataCleanService#dealWmSingleTypeData,根据门店ID未查询门店信息,customerId={}", wmCustomerDB.getId());
                return oldCustomerRealType;
            }
            //业务线为闪购则设置为闪购单店
            if (wmPoiAggre.getBiz_org_code() == PoiOrgEnum.SHAN_GOU.getCode()) {
                customerRealTypeNew = CustomerRealTypeEnum.DANDIAN_SG.getValue();
            } else if (wmPoiAggre.getBiz_org_code() == PoiOrgEnum.MEDICINE.getCode()) {
                //业务线为医药则设置为医药单店
                customerRealTypeNew = CustomerRealTypeEnum.DANDIAN_YAOPIN.getValue();
            }

            return customerRealTypeNew;
        }
    }


    /**
     * 处理美食城、食堂、大连锁、小连锁、流量连锁数据
     *
     * @param wmCustomerDB
     */
    private Integer dealPartMultiWmTypeData(WmCustomerDB wmCustomerDB) {
        log.info("WmCustomerDataCleanService#dealPartMultiWmTypeData,开始处理客户类型,wmCustomerDB={}", JSON.toJSONString(wmCustomerDB));
        Integer oldCustomerRealType = wmCustomerDB.getCustomerRealType();

        List<WmCustomerPoiDB> customerPoiList = listByCustomerId(wmCustomerDB.getId());
        if (CollectionUtils.isEmpty(customerPoiList)) {
            return oldCustomerRealType;
        }
        Integer customerRealTypeNew = oldCustomerRealType;
        Integer poiCount = customerPoiList.size();
        if (poiCount == 1) {
            Long wmPoiId = customerPoiList.get(0).getWmPoiId();
            WmPoiAggre wmPoiAggre = getWmPoiInfoById(wmPoiId);
            if (wmPoiAggre == null) {
                log.error("WmCustomerDataCleanService#dealPartMultiWmTypeData,根据门店ID未查询到门店信息，请关注此门店,wmPoiId={}", wmPoiId);
                return oldCustomerRealType;
            }
            Integer bizOrgCode = wmPoiAggre.getBiz_org_code();
            if (bizOrgCode != PoiOrgEnum.WAI_MAI.getCode() && bizOrgCode != PoiOrgEnum.SHAN_GOU.getCode()
                    && bizOrgCode != PoiOrgEnum.MEDICINE.getCode()) {
                log.error("WmCustomerDataCleanService#dealPartMultiWmTypeData,门店所属业务线非外卖、闪购和医药，请关注此门店,wmPoiId={},bizOrgCode={}", wmPoiId, bizOrgCode);
                return oldCustomerRealType;
            }

            if (bizOrgCode == PoiOrgEnum.SHAN_GOU.getCode()) {
                customerRealTypeNew = CustomerRealTypeEnum.DANDIAN_SG.getValue();
            } else if (bizOrgCode == PoiOrgEnum.MEDICINE.getCode()) {
                customerRealTypeNew = CustomerRealTypeEnum.DANDIAN_YAOPIN.getValue();
            }
        } else {
            Boolean sgSmallChain = true;
            for (WmCustomerPoiDB wmCustomerPoiDB : customerPoiList) {
                WmPoiAggre wmPoiAggre = getWmPoiInfoById(wmCustomerPoiDB.getWmPoiId());
                if (wmPoiAggre.getBiz_org_code() == PoiOrgEnum.WAI_MAI.getCode()) {
                    sgSmallChain = false;
                    break;
                }
            }
            if (sgSmallChain) {
                customerRealTypeNew = CustomerRealTypeEnum.SG_CKA.getValue();
            }
        }

        return customerRealTypeNew;

    }

    /**
     * 处理闪购和医药业务的客户类型数据
     *
     * @param wmCustomerDB
     */
    private Integer dealSgCustomerRealTypeData(WmCustomerDB wmCustomerDB) {
        List<WmCustomerPoiDB> customerPoiList = listByCustomerId(wmCustomerDB.getId());
        Integer oldCustomerRealType = wmCustomerDB.getCustomerRealType();
        if (CollectionUtils.isEmpty(customerPoiList)) {
            return oldCustomerRealType;
        }
        Integer poiCount = customerPoiList.size();
        Integer customerRealTypeNew = oldCustomerRealType;
        if (poiCount == 1) {
            Long wmPoiId = customerPoiList.get(0).getWmPoiId();
            WmPoiAggre wmPoiAggre = getWmPoiInfoById(wmPoiId);
            if (wmPoiAggre == null) {
                log.error("WmCustomerDataCleanService#dealSgCustomerRealTypeData,根据门店ID未查询到门店信息，请关注此门店,wmPoiId={}", wmPoiId);
                return oldCustomerRealType;
            }
            Integer bizOrgCode = wmPoiAggre.getBiz_org_code();
            if (bizOrgCode != PoiOrgEnum.WAI_MAI.getCode() && bizOrgCode != PoiOrgEnum.SHAN_GOU.getCode()
                    && bizOrgCode != PoiOrgEnum.MEDICINE.getCode()) {
                log.error("WmCustomerDataCleanService#dealSgCustomerRealTypeData,门店所属业务线非外卖、闪购和医药，请关注此门店,wmPoiId={},bizOrgCode={}", wmPoiId, bizOrgCode);
                return oldCustomerRealType;
            }
            //门店业务线是外卖则设置为外卖单店
            if (bizOrgCode == PoiOrgEnum.WAI_MAI.getCode()) {
                customerRealTypeNew = CustomerRealTypeEnum.DANDIAN.getValue();
            }
            //门店业务线是医药则设置为医药单店
            if (bizOrgCode == PoiOrgEnum.MEDICINE.getCode()) {
                customerRealTypeNew = CustomerRealTypeEnum.DANDIAN_YAOPIN.getValue();
            }
        } else {
            Boolean haveSgPoiFlag = false;
            for (WmCustomerPoiDB wmCustomerPoiDB : customerPoiList) {
                WmPoiAggre wmPoiAggre = getWmPoiInfoById(wmCustomerPoiDB.getWmPoiId());
                //有一个绑定门店业务线为闪购则处理为闪购业务
                if (wmPoiAggre != null && wmPoiAggre.getBiz_org_code() == PoiOrgEnum.SHAN_GOU.getCode()) {
                    haveSgPoiFlag = true;
                    break;
                }
            }
            //关联所有门店无闪购业务线
            if (!haveSgPoiFlag) {
                customerRealTypeNew = CustomerRealTypeEnum.XIAOLIANSUO.getValue();
                log.info("WmCustomerDataCleanService#dealSgCustomerRealTypeData,当前客户关联多个门店，且业务线无一个为闪购，客户类型设置为闪购小连锁,customerId={},customerRealTypeNew={}", wmCustomerDB.getId(), customerRealTypeNew);
            }
        }

        return customerRealTypeNew;

    }


    /**
     * 处理客户类型是闪购单店的数据
     *
     * @param wmCustomerDB
     * @return
     */
    private Integer dealSgDanDian(WmCustomerDB wmCustomerDB) {
        log.info("WmCustomerDataCleanService#dealSgDanDian,开始处理客户类型为闪购单店的数据,wmCustomerDb={}", JSON.toJSONString(wmCustomerDB));
        List<WmCustomerPoiDB> customerPoiList = listByCustomerId(wmCustomerDB.getId());
        Integer oldCustomerRealType = wmCustomerDB.getCustomerRealType();
        if (CollectionUtils.isEmpty(customerPoiList)) {
            return oldCustomerRealType;
        }
        if (customerPoiList.size() > 1) {
            log.error("WmCustomerDataCleanService#dealSgDanDian,客户类型闪购单店绑定门店数超过1个，不处理,customerId={}", wmCustomerDB.getId());
            return oldCustomerRealType;
        }

        Integer customerRealTypeNew = oldCustomerRealType;

        WmPoiAggre wmPoiAggre = getWmPoiInfoById(customerPoiList.get(0).getWmPoiId());
        if (wmPoiAggre == null) {
            log.error("WmCustomerDataCleanService#dealSgDanDian,根据门店ID未查询门店信息,customerId={}", wmCustomerDB.getId());
            return oldCustomerRealType;
        }
        //业务线为外卖则设置为外卖单店
        if (wmPoiAggre.getBiz_org_code() == PoiOrgEnum.WAI_MAI.getCode()) {
            customerRealTypeNew = CustomerRealTypeEnum.DANDIAN.getValue();
        } else if (wmPoiAggre.getBiz_org_code() == PoiOrgEnum.MEDICINE.getCode()) {
            //业务线为医药则设置为医药单店
            customerRealTypeNew = CustomerRealTypeEnum.DANDIAN_YAOPIN.getValue();
        }

        return customerRealTypeNew;

    }


    /**
     * 根据客户类型更新客户业务线字段
     *
     * @param wmCustomerDB -最新的客户信息，客户类型需要是更新后的
     */
    private void updateBizOrgCodeByCustomerRealType(WmCustomerDB wmCustomerDB) {
        if (wmCustomerDB == null || wmCustomerDB.getCustomerRealType() == null) {
            log.info("WmCustomerDataCleanService#updateBizOrgCodeByCustomerRealType,客户对象为空或者客户类型为空，不处理");
            return;
        }
        Integer customerRealType = wmCustomerDB.getCustomerRealType();

        //医药
        if (medCustomerRealTypes.contains(customerRealType)) {
            wmCustomerDB.setBizOrgCode(CustomerBizOrgEnum.MEDICINE.getCode());
            updateCustomerBizOrgCode(wmCustomerDB);
        } else if (sgMedCustomerRealTypes.contains(customerRealType)) {
            //闪购
            wmCustomerDB.setBizOrgCode(CustomerBizOrgEnum.SHAN_GOU.getCode());
            updateCustomerBizOrgCode(wmCustomerDB);
        } else if (wmCustomerRealTypes.contains(customerRealType)) {
            //外卖
            wmCustomerDB.setBizOrgCode(CustomerBizOrgEnum.WAI_MAI.getCode());
            updateCustomerBizOrgCode(wmCustomerDB);
        } else {
            log.info("WmCustomerDataCleanService#updateBizOrgCodeByCustomerRealType，客户类型不在处理范围内,customerRealType={},wmCustomer={}", customerRealType, JSON.toJSONString(wmCustomerDB));
        }
    }

    /**
     * 更新客户的业务线字段
     *
     * @param wmCustomerDB
     */
    private void updateCustomerBizOrgCode(WmCustomerDB wmCustomerDB) {
        try {
            if (wmCustomerDB.getId() == null || wmCustomerDB.getBizOrgCode() == null
                    || wmCustomerDB.getId() <= 0 || wmCustomerDB.getBizOrgCode() <= 0) {
                log.error("updateCustomerBizOrgCode,参数不合法，不能更新客户的业务线字段");
                return;
            }
            Integer bizOrgCode = wmCustomerDB.getBizOrgCode();
            log.info("WmCustomerDataCleanService#updateCustomerBizOrgCode,开始更新客户的业务线字段,customerId={},bizOrgCode={}", wmCustomerDB.getId(), bizOrgCode);
            //更新业务线字段到客户平台
            Boolean updateResult = mtCustomerThriftServiceAdapter.saveMtCustomerExtendAttribute(wmCustomerDB.getMtCustomerId(), bizOrgCode);
            if (updateResult == null || updateResult == false) {
                log.error("WmCustomerDataCleanService#updateCustomerBizOrgCode,更新客户业务线失败,customerId={},bizOrgCode={},baseResponse={}",
                        wmCustomerDB.getId(), bizOrgCode, JSON.toJSONString(updateResult));
                return;
            }

            //执行更新ES业务线字段
            syncUpdate2Es(wmCustomerDB);

            //异步请求查询平台客户信息
            refreshMtCustomerExtendInfo(wmCustomerDB.getMtCustomerId());
        } catch (Exception e) {
            log.error("WmCustomerDataCleanService#updateCustomerBizOrgCode,根据客户ID个更新业务线字段发生异常,customerId={},bizOrgCode={}", wmCustomerDB.getId(), wmCustomerDB.getBizOrgCode(), e);
        }
    }

    /**
     * 根据客户ID查询关联的门店记录
     *
     * @param customerId
     * @return
     */
    private List<WmCustomerPoiDB> listByCustomerId(Integer customerId) {
        List<WmCustomerPoiDB> list = new ArrayList<>();
        try {
            list = wmCustomerPoiDBMapper.selectWmCustomerPoiByCustomerId(customerId);
        } catch (Exception e) {
            log.error("WmCustomerDataCleanService#listByCustomerId,根据客户ID查询关联的客户门店记录列表发生异常，customerId={}", customerId, e);
        }
        return list;
    }

    /**
     * 更新客户类型
     *
     * @param customerId
     * @param customerRealType
     */
    private void updateCustomerRealType(Integer customerId, Integer customerRealType) {
        if (customerId == null || customerRealType == null
                || customerRealType <= 0 || customerId <= 0) {
            return;
        }
        wmCustomerDBMapper.updateCustomerRealType(customerId, customerRealType);
    }

    /**
     * 根据门店ID查询业务线
     *
     * @param wmPoiId
     * @return
     */
    private WmPoiAggre getWmPoiInfoById(Long wmPoiId) {
        return wmPoiQueryAdapter.getWmPoiAggre(wmPoiId,
                Sets.newHashSet(WmPoiFieldQueryConstant.WM_POI_FIELD_WM_POI_ID, WmPoiFieldQueryConstant.WM_POI_FIELD_BIZ_ORG_CODE));
    }

    /**
     * 客户类型变更添加操作记录
     *
     * @param customerId
     * @param oldCustomerRealType
     * @param newCustomerRealType
     */
    public void insertCustomerRealTypeChangeOpLog(Integer customerId, Integer oldCustomerRealType,
                                                  Integer newCustomerRealType) {
        try {
            if (customerId == null || customerId < 1
                    || newCustomerRealType == null || newCustomerRealType <= 0) {
                return;
            }

            WmCustomerOplogBo wmCustomerOplogBo = new WmCustomerOplogBo(customerId, WmCustomerOplogBo.OpModuleType.CUSTOMER, customerId);
            wmCustomerOplogBo.setOpType(WmCustomerOplogBo.OpType.UPDATE.type);
            wmCustomerOplogBo.setLog(String.format("客户类型变更: %s->%s",
                    (oldCustomerRealType == null || oldCustomerRealType == 0) ? "无" : CustomerRealTypeEnum.getNameByValue(oldCustomerRealType),
                    CustomerRealTypeEnum.getNameByValue(newCustomerRealType)));
            wmCustomerOplogBo.setOpUid(-1);
            wmCustomerOplogBo.setOpUname("系统");
            wmCustomerOplogBo.setRemark(null);
            wmCustomerOplogService.insert(wmCustomerOplogBo);
        } catch (Exception e) {
            log.error("WmCustomerDataCleanService#insertCustomerRealTypeChangeOpLog,修改客户类型记录操作日志发生异常,customerId={},oldCustomerRealType={},newCustomerRealType={}",
                    customerId, oldCustomerRealType, newCustomerRealType, e);
        }

    }


    /**
     * 延时查询平台客户信息-客户平台要求更新业务线后延时查询
     *
     * @param mtCustomerId
     */
    private void refreshMtCustomerExtendInfo(Long mtCustomerId) {
        //添加开关控制是否延迟查询
        if (!MccCustomerConfig.getCleanJobQueryMtCustomerSwitch()) {
            return;
        }
        ExecutorUtil.execAsync(new ExecutorUtil.Executor() {
            @Override
            public Object exec() {
                try {
                    mtCustomerThriftServiceAdapter.refreshCustomerExtendInfoByMtCustomerId(mtCustomerId);
                } catch (Exception e) {
                    log.error("WmCustomerDataCleanService#queryMtCustomerInfo,根据平台客户ID查询客户信息发生异常,mtCustomerId={}",
                            mtCustomerId, e);
                }
                return null;
            }

            @Override
            public boolean shouldRetry(Object o) {
                return false;
            }
        }, MccCustomerConfig.getUpdateBizOrdCode2MtCustomerDelayTime());

    }

    /**
     * 异步更新ES
     *
     * @param wmCustomerDB
     */
    private void syncUpdate2Es(WmCustomerDB wmCustomerDB) {
        ExecutorUtil.execAsync(new ExecutorUtil.Executor() {
            @Override
            public Object exec() {
                try {
                    wmCustomerESService.refreshToUpsertEs(wmCustomerDB);
                } catch (Exception e) {
                    log.error("WmCustomerDataCleanService#syncUpdate2Es,更新客户的业务线到ES发生异常,wmCustomerDB={}", JSON.toJSONString(wmCustomerDB), e);
                }
                return null;
            }

            @Override
            public boolean shouldRetry(Object o) {
                return false;
            }
        }, 0);

    }

    /**
     * 获取限流配置
     *
     * @return
     */
    private Integer getLimitRate(int rateLimit) {
        String rateStrConfig = MccCustomerConfig.getCustomerRealTypeLimitConfig();
        if (StringUtils.isBlank(rateStrConfig)) {
            return rateLimit;
        }

        List<DateLimitDTO> dateLimitDTOS = JSON.parseArray(rateStrConfig, DateLimitDTO.class);
        if (CollectionUtils.isEmpty(dateLimitDTOS)) {
            return rateLimit;
        }
        Calendar calendar = Calendar.getInstance();
        int hour = calendar.get(Calendar.HOUR_OF_DAY);
        //左闭右开
        for (DateLimitDTO dateLimitDTO : dateLimitDTOS) {
            if (hour >= dateLimitDTO.getStartTime()
                    && hour < dateLimitDTO.getEndTime()) {
                rateLimit = dateLimitDTO.getLimitRate();
                break;
            }
        }
        return rateLimit;
    }

}

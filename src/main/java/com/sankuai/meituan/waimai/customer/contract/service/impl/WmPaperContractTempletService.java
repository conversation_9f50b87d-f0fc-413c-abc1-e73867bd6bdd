package com.sankuai.meituan.waimai.customer.contract.service.impl;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.util.MetricHelper;
import com.google.common.base.MoreObjects;
import com.google.common.collect.Lists;
import com.sankuai.meituan.util.DateUtil;
import com.sankuai.meituan.waimai.customer.constant.customer.MetricConstant;
import com.sankuai.meituan.waimai.customer.contract.service.impl.check.ContractCheckFilter;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.util.RejectReasonUtil;
import com.sankuai.meituan.waimai.thrift.customer.constant.WmTempletContractTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractTypeBo;
import com.sankuai.meituan.waimai.thrift.constatnt.WmAuditTaskBizTypeConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.LongResult;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskApplyBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskConfigBo;
import com.sankuai.meituan.waimai.thrift.domain.WmAuditCommitObj;
import com.sankuai.meituan.waimai.thrift.domain.WmAuditMsg;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import com.sankuai.meituan.waimai.thrift.vo.WmFrameContractCommitData;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;

@Service
public class WmPaperContractTempletService extends AbstractWmContractTempletService {

    private static Logger logger = LoggerFactory.getLogger(WmPaperContractTempletService.class);

    @Override
    public Integer startSign(WmCustomerContractBo contractBo, int opUid, String opName) throws
            WmCustomerException, TException {
        throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "纸质合同不需要签约");
    }

    @Override
    public Integer startSignForContractHeron(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException, TException {
        throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "纸质合同不需要签约");
    }

    @Override
    public Boolean signFail(long templetContractId, String failReason, int opUid, String opUname) throws WmCustomerException, TException {
        throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "纸质合同不需要签约");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer update(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException, TException {
        Integer update = super.update(contractBo, opUid, opName);
        wmTempletContractDBMapper.updateNumberById(contractBo.getBasicBo().getTempletContractId(), contractBo.getBasicBo().getContractNum());
        return update;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer commitAudit(WmCustomerContractBo contractBo, int opUid, String opName)
            throws WmCustomerException, TException {
        logger.info("WmPaperContractTempletService#合同提审开始 contractBo：{}  opUid:{}  opUname:{}", JSON.toJSON(contractBo), opUid, opName);
        ContractCheckFilter.contractCommitAuditValidFilter().filter(contractBo, opUid, opName);
        WmCustomerContractBo oldBo = wmContractService
                .getWmCustomerContractBoById(contractBo.getBasicBo().getTempletContractId(), false, opUid, opName);
        Integer contractId = insertOrUpdate(contractBo, opUid, opName);

        //新建并发起签约时，同步暂存状态
        contractLogService.logStatusChange(MoreObjects.firstNonNull(oldBo.getBasicBo(), new WmTempletContractBasicBo()).getStatus(),
                contractBo.getBasicBo(), opUid, opName);

        contractBo.getBasicBo().setTempletContractId(contractId);
        contractLogService.logUpdate(oldBo, contractBo, opUid, opName);

        WmAuditCommitObj wmAuditCommitObj = buildWmAuditCommitObj(contractBo, opUid);
        try {
            WmAuditMsg wmAuditMsg = wmAuditApiService.commitAudit(wmAuditCommitObj);
            logger.info("WmPaperContractTempletService#commitAudit, wmAuditMsg: {}", JSON.toJSONString(wmAuditMsg));
        } catch (WmServerException e) {
            logger.info("提审失败，原因：" + e.getMsg(), e);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, e.getMsg());
        }
        toNextContractStatus(contractId, CustomerContractStatus.AUDITING.getCode(), opName);
        contractBo.getBasicBo().setStatus(CustomerContractStatus.AUDITING.getCode());
        contractLogService.logStatusChange(MoreObjects.firstNonNull(oldBo.getBasicBo(), new WmTempletContractBasicBo()).getStatus(),
                contractBo.getBasicBo(), opUid, opName);
        logger.info("合同提审成功 templetId：{}  opUid:{}  opUname:{}", contractBo.getBasicBo().getTempletContractId(), opUid, opName);
        mtriceService.metricContractCommitaudit(WmTempletContractTypeEnum.getByCode(contractBo.getBasicBo().getType()).getMsg());
        return contractId;
    }

    @Override
    public Integer commitAuditForContractHeron(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException, TException {
        logger.info("千鹭合同提审开始 contractBo：{}  opUid:{}  opUname:{}", JSON.toJSON(contractBo), opUid, opName);
        if (contractBo == null || contractBo.getBasicBo() == null || contractBo.getBasicBo().getTempletContractId() <= 0){
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "合同不存在，不能提审");
        }
        WmAuditCommitObj wmAuditCommitObj = buildWmAuditCommitObj(contractBo, opUid);
        try {
            wmAuditApiService.commitAudit(wmAuditCommitObj);
        } catch (WmServerException e) {
            logger.info("提审失败，原因：" + e.getMsg(), e);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, e.getMsg());
        }
        mtriceService.metricContractCommitaudit("配送纸质合同");
        return (int)contractBo.getBasicBo().getTempletContractId();
    }

    private WmAuditCommitObj buildWmAuditCommitObj(WmCustomerContractBo contractBo, int opUid)
            throws WmCustomerException {
        WmAuditCommitObj wmAuditCommitObj = new WmAuditCommitObj();

        WmFrameContractCommitData data = buildWmFrameContractData(contractBo);

        wmAuditCommitObj.setData(JSON.toJSONString(data));
        wmAuditCommitObj.setBiz_id((int) contractBo.getBasicBo().getTempletContractId());
        if (new WmTempletContractTypeBo(contractBo.getBasicBo().getType()).getCooperateMode() == WmTempletContractTypeBo.TYPE_C1) {
            wmAuditCommitObj.setBiz_type(WmAuditTaskBizTypeConstant.FRAME_CONTRACT_C1);
        } else {
            wmAuditCommitObj.setBiz_type(WmAuditTaskBizTypeConstant.FRAME_CONTRACT_C2);
        }
        wmAuditCommitObj.setSubmit_uid(opUid);
        wmAuditCommitObj.setCustomer_id(contractBo.getBasicBo().getParentId());
        return wmAuditCommitObj;
    }

    private WmFrameContractCommitData buildWmFrameContractData(WmCustomerContractBo contractBo)
            throws WmCustomerException {

        WmTempletContractSignBo partyASigner = wmContractSignService.getPartyASigner(contractBo.getSignBoList());
        WmTempletContractSignBo partyBSigner = wmContractSignService.getPartyBSigner(contractBo.getSignBoList());

        CustomerPaperContractRemarkBo remarkBo = JSON.parseObject(contractBo.getBasicBo().getExtStr(), CustomerPaperContractRemarkBo.class);

        WmFrameContractCommitData data = new WmFrameContractCommitData();
        data.setContractId((int) contractBo.getBasicBo().getTempletContractId());
        data.setContractNo(contractBo.getBasicBo().getContractNum());
        data.setContractExpireDate(contractBo.getBasicBo().getDueDate());
        data.setContractUrl(remarkBo.getContractScan().getList().get(0).getUrl());
        // 设置履约服务主体
        data.setDistributionServiceSubject(contractBo.getBasicBo().getLogisticsSubject());

        if (remarkBo.getOtherContractScan() != null && remarkBo.getOtherContractScan().getList() != null) {
            List<String> otherContractScan = Lists.newArrayList();
            for (MultiFileJsonBo.CustomerFile s : remarkBo.getOtherContractScan().getList()) {
                otherContractScan.add(s.getUrl());
            }
            data.setOtherContractScans(otherContractScan);
        }
        WmCustomerDB customerDB = wmCustomerService.selectCustomerById(contractBo.getBasicBo().getParentId());
        data.setFirstPartyAddress(customerDB.getAddress());
        data.setOwnerName(customerDB.getLegalPerson());
        if (customerDB.getCustomerType().equals(CustomerType.CUSTOMER_TYPE_IDCARD.getCode())) {
            data.setOwnerName(customerDB.getCustomerName());
        }
        fillPartyASigner(partyASigner, data);

        fillPartyBSigner(partyBSigner, data);

        return data;
    }

    private void fillPartyBSigner(WmTempletContractSignBo partyBSigner, WmFrameContractCommitData data) {
        data.setSecondParty(partyBSigner.getSignName());
        data.setSecondPartyPeople(partyBSigner.getSignPeople());
        data.setSecondPartyPhone(partyBSigner.getSignPhone());
        data.setSecondPartySignDate(DateUtil.date2Unixtime(DateUtil.string2DateDay(partyBSigner.getSignTime())));
    }

    private void fillPartyASigner(WmTempletContractSignBo partyASigner, WmFrameContractCommitData data) {
        data.setFirstParty(partyASigner.getSignName());
        data.setFirstPartyPeople(partyASigner.getSignPeople());
        data.setFirstPartyPhone(partyASigner.getSignPhone());
        data.setFirstPartySignDate(DateUtil.date2Unixtime(DateUtil.string2DateDay(partyASigner.getSignTime())));
    }

    @Override
    public void toNextStatus(int contractId, int toStatus, int opUid, String opUname) throws WmCustomerException {
        logger.info("##toNextStatus# contractId:{} toStatus:{} opUid:{} opUname:{}",
                contractId, toStatus, opUid, opUname);
        toNextContractStatus(contractId, toStatus, opUname);
    }

    @Override
    public Boolean reject(long templetContractId, String rejectReason, int opUid, String opUname)
            throws WmCustomerException, TException {
        logger.info("合同审核被驳回  contractId:{}, rejectReason:{} , opUid:{} ,  opUname:{}",
                templetContractId, rejectReason, opUid, opUname);
        WmTempletContractBasicBo basic = wmContractService.getBasicById(templetContractId, false, opUid, opUname);
        if (basic == null || CustomerContractStatus.isInvalid(basic.getStatus())) {
            logger.info("审核驳回合同, 该合同已废除! templetContractId = {}, opUid = {}, opUname = {}", templetContractId, opUid, opUname);
            return true;
        }
        int oldContractStatus = basic.getStatus();
        toNextStatus((int) templetContractId, CustomerContractStatus.REJECT.getCode(), opUid, opUname);
        basic.setStatus(CustomerContractStatus.REJECT.getCode());
        rejectReason = RejectReasonUtil.getTempletC1C2RejectReason(rejectReason);
        contractLogService.logStatusChangeForReject(oldContractStatus, basic, rejectReason, opUid, opUname);
        mtriceService.metricContractReject(WmTempletContractTypeEnum.getByCode(basic.getType()).getMsg());
        return true;
    }

}

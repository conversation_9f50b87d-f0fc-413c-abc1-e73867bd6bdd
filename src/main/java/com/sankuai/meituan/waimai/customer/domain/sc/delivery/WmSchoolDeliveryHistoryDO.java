package com.sankuai.meituan.waimai.customer.domain.sc.delivery;

import com.sankuai.meituan.waimai.thrift.customer.constant.sc.SchoolAgreementTypeEnum;

import com.sankuai.meituan.waimai.thrift.customer.constant.sc.SchoolLevelEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.SchoolTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 学校交付历史信息表DO
 * <AUTHOR>
 * @date 2024/02/08
 * @email <EMAIL>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WmSchoolDeliveryHistoryDO {
    /**
     * 主键ID
     */
    private Integer id;
    /**
     * 交付编号
     */
    private Integer deliveryId;
    /**
     * 学校主键ID
     */
    private Integer schoolPrimaryId;
    /**
     * 学校名称
     */
    private String schoolName;
    /**
     * 学校属性(学校类型)
     * {@link SchoolTypeEnum}
     */
    private Integer schoolType;
    /**
     * 学校人数(在校师生人数)
     */
    private Integer teaStuNum;
    /**
     * 学校级别(学校分级)
     * {@link SchoolLevelEnum}
     */
    private Integer schoolLevel;
    /**
     * 学校蜂窝ID
     */
    private Integer aorId;
    /**
     * 合作模式
     * {@link SchoolAgreementTypeEnum}
     */
    private Integer agreementType;
    /**
     * 合同/授权编号开始时间
     */
    private Integer agreementTimeStart;
    /**
     * 合同/授权编号结束时间
     */
    private Integer agreementTimeEnd;
    /**
     * 交付终结时间
     */
    private Integer deliveryEndTime;
    /**
     * 学校责任人UID
     */
    private Integer schoolOwnerUid;
    /**
     * 客户成功经理UID
     */
    private Integer csmUid;
    /**
     * 学校对应蜂窝负责人UID
     */
    private Integer aormUid;
    /**
     * 聚合渠道经理UID
     */
    private Integer acmUid;
}

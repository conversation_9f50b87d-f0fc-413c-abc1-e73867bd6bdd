package com.sankuai.meituan.waimai.customer.service.sc.poi;

import com.alibaba.fastjson.JSONObject;
import com.dianping.lion.client.util.CollectionUtils;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.dao.sc.WmScCanteenPoiTaskDetailMapper;
import com.sankuai.meituan.waimai.customer.domain.sc.*;
import com.sankuai.meituan.waimai.customer.service.sc.WmScCanteenPoiAuditService;
import com.sankuai.meituan.waimai.customer.service.sc.WmScLogService;
import com.sankuai.meituan.waimai.customer.service.sc.WmScTagService;
import com.sankuai.meituan.waimai.customer.service.sc.dao.WmScCanteenDao;
import com.sankuai.meituan.waimai.customer.service.sc.dao.WmScCanteenPoiAuditDao;
import com.sankuai.meituan.waimai.customer.service.sc.dao.WmScCanteenPoiAuditDetailDao;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.CanteenBaseBo;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 食堂门店强制解绑服务
 */
@Slf4j
@Service
public class WmScCanteenUnbindService {

    @Autowired
    private WmScCanteenPoiTaskDetailMapper wmScCanteenPoiTaskDetailMapper;

    @Autowired
    private WmScCanteenPoiAuditDao wmScCanteenPoiAuditDao;

    @Autowired
    private WmScTagService wmScTagService;

    @Autowired
    private WmScCanteenPoiAuditDetailDao wmScCanteenPoiAuditDetailDao;

    @Autowired
    private WmScCanteenPoiAuditService wmScCanteenPoiAuditService;

    @Autowired
    private WmScCanteenPoiAttributeService wmScCanteenPoiAttributeService;

    @Autowired
    private WmScLogService wmScLogService;

    @Autowired
    private WmScCanteenInfoService wmScCanteenInfoService;

    @Autowired
    private WmScCanteenDao wmScCanteenDao;

    @Autowired
    private WmScCanteenPoiService wmScCanteenPoiService;

    /**
     * 门店与食堂强制解绑
     * @param baseBo CanteenBaseBo
     * @throws TException org.apache.thrift.TException
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void forceUnbind(CanteenBaseBo baseBo) throws TException, WmSchCantException {
        log.info("[WmScCanteenUnbindService.forceUnbind] input param: baseBo = {}", JSONObject.toJSONString(baseBo));
        WmScCanteenPoiAuditDB poiAuditDB = wmScCanteenPoiAuditDao.getLatestValidPoiAuditByCateenId(baseBo.getId());
        if (poiAuditDB == null) {
            return;
        }
        int canteenPoiCount = wmScCanteenPoiAuditDetailDao.getValidBindPoiCount(poiAuditDB.getId());
        // 如果食堂门店数量为0 也返回
        if (canteenPoiCount == 0) {
            return;
        }
        // 生效的数据才能强制解绑
        if (CanteenPoiAuditStatusEnum.EFFECTED.getCode() != poiAuditDB.getAuditStatus()) {
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "强制解绑失败：此状态不可解绑");
        }

        WmScCanteenPoiAuditDetailDB wmScCanteenPoiAuditDetailDB = wmScCanteenPoiAuditDetailDao.getValidBindPoi(poiAuditDB.getId(), baseBo.getWmPoiId());
        if (wmScCanteenPoiAuditDetailDB == null) {
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "强制解绑失败：无可解绑的门店");
        }

        // 获取换绑中的任务
        List<WmScCanteenPoiTaskDO> wmScCanteenPoiTaskDOList = wmScCanteenPoiService.getCanteenTransferBindTask(baseBo.getId());
        if (CollectionUtils.isNotEmpty(wmScCanteenPoiTaskDOList) && baseBo.getWmPoiId() != null) {
            // 已生效才能解绑
            List<Long> auditTaskIdList = wmScCanteenPoiTaskDOList.stream()
                    .map(WmScCanteenPoiTaskDO::getId)
                    .collect(Collectors.toList());
            WmScCanteenPoiTaskDetailSearchCondition taskDetailSearchCondition = new WmScCanteenPoiTaskDetailSearchCondition();
            taskDetailSearchCondition.setCanteenPoiTaskIdList(auditTaskIdList);
            taskDetailSearchCondition.setWmPoiIdList(Lists.newArrayList(baseBo.getWmPoiId()));
            List<WmScCanteenPoiTaskDetailDO> wmScCanteenPoiTaskDetailDOList = wmScCanteenPoiTaskDetailMapper.selectByCondition(taskDetailSearchCondition);
            if (CollectionUtils.isNotEmpty(wmScCanteenPoiTaskDetailDOList)) {
                throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "强制解绑失败：该门店状态不可解绑");
            }
        }

        // 去标
        wmScTagService.delCanteenPoiTag(baseBo.getId(), baseBo.getWmPoiId(), baseBo.getUserId(), baseBo.getUserName());
        // 改状态
        wmScCanteenPoiAuditDetailDao.forceUnbind(poiAuditDB.getCanteenId(), poiAuditDB.getId(), baseBo.getWmPoiId());

        WmScCanteenPoiAuditDB wmScCanteenPoiAuditDB = wmScCanteenPoiAuditDao.getLatestInValidCanteenPoiAuditLog(poiAuditDB.getCanteenId());
        if (wmScCanteenPoiAuditDB != null && canteenPoiCount == 1) {
            // 强制解绑最后一个有效的门店
            wmScCanteenPoiAuditService.setPoiAuditUnValidAndUnSubmit(wmScCanteenPoiAuditDB);
        }
        // 删除食堂门店属性表记录（生效表）
        wmScCanteenPoiAttributeService.deleteCanteenPoiAttribute(baseBo.getId(), baseBo.getWmPoiId());
        // 更新食堂合作档口数量
        WmCanteenDB wmCanteenDb = new WmCanteenDB();
        wmCanteenDb.setStoreNum(wmScCanteenInfoService.getCurrentCanteenPoiNum(baseBo.getId()));
        wmScCanteenDao.updateStoreNum(wmCanteenDb);
        // 保存日志
        wmScLogService.saveForceUnbindPoiV2Log(baseBo.getWmPoiId(), baseBo.getId(), baseBo.getUserId(), baseBo.getUserName());
        log.info("[WmScCanteenUnbindService.forceUnbind] success. baseBo = {}", JSONObject.toJSONString(baseBo));
    }

}

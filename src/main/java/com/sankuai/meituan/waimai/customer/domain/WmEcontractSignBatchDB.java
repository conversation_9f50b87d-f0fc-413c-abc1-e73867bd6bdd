package com.sankuai.meituan.waimai.customer.domain;


import com.sankuai.meituan.waimai.customer.annotation.Encryption;
import com.sankuai.meituan.waimai.customer.constant.EncryptionTypeConstant;
import lombok.Data;

/**
 * 签约打包后信息
 */
@Data
@Encryption(fields = {"batchContext"}, recordType = EncryptionTypeConstant.ECONTRACT_SIGN_BATCH_RECORD, isJSON = true)
public class WmEcontractSignBatchDB {

    private Long id;

    private String batchState;

    /*JSON格式参考EcontractBatchContextBo*/
    private String batchContext;

    private String recordKey;

    private Byte valid;

    private Long ctime;

    private Long utime;

    private Integer version;

    private Integer customerId;

    private Integer commitUid;

    private String objectName;

    private Long packId = 0L;

    private String notifyInfo;
}

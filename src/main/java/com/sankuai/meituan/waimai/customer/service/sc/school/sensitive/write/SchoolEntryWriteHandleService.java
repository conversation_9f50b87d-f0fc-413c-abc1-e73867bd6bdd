package com.sankuai.meituan.waimai.customer.service.sc.school.sensitive.write;

import com.sankuai.meituan.waimai.customer.constant.KmsKeyNameEnum;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/8/5
 */
@Service
public class SchoolEntryWriteHandleService {

    @Autowired
    private List<ISchoolWriteHandle> iSchoolWriteHandleList;

    private Map<KmsKeyNameEnum, ISchoolWriteHandle> iWriteHandleMap = new HashMap<>();

    @PostConstruct
    public void init() {
        if (CollectionUtils.isEmpty(iSchoolWriteHandleList)) {
            return;
        }
        for (ISchoolWriteHandle handel : iSchoolWriteHandleList) {
            iWriteHandleMap.put(handel.handleType(), handel);
        }
    }


    public void doWriteWhenInsertOrUpdate(SchoolEntryWrite schoolEntryWrite) throws WmSchCantException {
        if (schoolEntryWrite == null) {
            return;
        }
        iWriteHandleMap.get(schoolEntryWrite.getKeyName()).doWriteWhenInsertOrUpdate(schoolEntryWrite);
    }


    public void writeSourceWhenUpdate(SchoolEntryWrite schoolEntryWrite) {
        if (schoolEntryWrite == null) {
            return;
        }
        iWriteHandleMap.get(schoolEntryWrite.getKeyName()).writeSourceWhenUpdate(schoolEntryWrite);
    }


}

package com.sankuai.meituan.waimai.customer.mq.consumer;

import com.alibaba.fastjson.JSONObject;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.consumer.IMessageListener;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.sankuai.meituan.waimai.customer.mq.domain.WmCustomerQikePoiBo;
import com.sankuai.meituan.waimai.customer.service.companycustomer.CompanyCustomerPoiSyncService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class WmCustomerQiKePoiListener implements IMessageListener {

    @Autowired
    private CompanyCustomerPoiSyncService companyCustomerPoiSyncService;

    @Override
    public ConsumeStatus recvMessage(MafkaMessage message, MessagetContext context) {
        try {
            String msg = message.getBody().toString();
            log.info("WmCustomerQiKePoiListener recvMessage = {}", msg);
            WmCustomerQikePoiBo wmCustomerQikePoiBo = JSONObject.parseObject(msg, WmCustomerQikePoiBo.class);
            if (wmCustomerQikePoiBo == null) {
                log.error("WmCustomerQiKePoiListener wmCustomerQikePoiBo is null wmCustomerQikePoiBo={}", JSONObject.toJSONString(wmCustomerQikePoiBo));
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            companyCustomerPoiSyncService.customerPoiCreate(wmCustomerQikePoiBo);
            return ConsumeStatus.CONSUME_SUCCESS;
        } catch (Exception e) {
            log.error("WmCustomerQiKeDelayListener 处理失败 msg={}", message.getBody().toString());
            return ConsumeStatus.RECONSUME_LATER;
        }
    }
}

package com.sankuai.meituan.waimai.customer.contract.service.impl.wrapper;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.util.DateUtil;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.AdOrderBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.joda.time.DateTime;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;

@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class AdOrderWrapper extends AbstractContractWrapper {

    private AdOrderBo adOrderBo;

    public AdOrderWrapper(Long customerId, Integer contractType, Long contractId, WmCustomerContractBo contractBo) {
        super(customerId, contractType, contractId, contractBo);
        adOrderBo = JSON.parseObject(contractBo.getExtraData(), AdOrderBo.class);
    }

    @Override
    protected void wrapContractBasicBo() {
        wrapContractBasicBoBasic();
        int expectStartTime = DateUtil.date2Unixtime(DateTime.now().withMillisOfDay(0).plusDays(1).toDate());
        int startTime = adOrderBo.getStartTime().intValue();
        contractBasicBo.setExpectEffectiveDate(startTime < expectStartTime ? 0 : startTime);
        contractBasicBo.setDueDate(adOrderBo.getEndTime());
    }

    @Override
    protected void wrapContractSignBoList() throws TException, WmCustomerException {
        partyA.setTempletContractId(contractId != null && contractId > 0L ? contractId.intValue() : 0);
        partyA.setSignId(existCustomer.getId());
        partyA.setSignName(adOrderBo.getPartyAName());
        partyA.setSignPeople(adOrderBo.getPartyAContact());
        partyA.setSignPhone(adOrderBo.getPartyAContactPhone());
        partyA.setSignTime(adOrderBo.getSignTime());

        partyB.setTempletContractId(contractId != null && contractId > 0L ? contractId.intValue() : 0);
        partyB.setSignId(0);
        partyB.setSignName(adOrderBo.getPartyBName());
        partyB.setSignPeople(adOrderBo.getPartyBContact());
        partyB.setSignPhone(adOrderBo.getPartyBContactPhone());
        partyB.setSignTime(adOrderBo.getSignTime());
    }
}

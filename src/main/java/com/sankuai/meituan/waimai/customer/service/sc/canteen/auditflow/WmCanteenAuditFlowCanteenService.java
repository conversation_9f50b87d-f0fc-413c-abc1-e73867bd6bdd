package com.sankuai.meituan.waimai.customer.service.sc.canteen.auditflow;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.customer.config.MccScConfig;
import com.sankuai.meituan.waimai.customer.dao.sc.WmCanteenMapper;
import com.sankuai.meituan.waimai.customer.dao.sc.WmScCanteenAuditMapper;
import com.sankuai.meituan.waimai.customer.dao.sc.WmScCanteenAuditNodeMapper;
import com.sankuai.meituan.waimai.customer.domain.sc.*;
import com.sankuai.meituan.waimai.customer.service.sc.WmScLogService;
import com.sankuai.meituan.waimai.customer.service.sc.canteen.sensitive.WmScCanteenSensitiveWordsService;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.CanteenAuditStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.CanteenDetailStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.EffectiveStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.OptTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.canteeninfo.CanteenInfoAuditNodeTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.canteeninfo.CanteenInfoAuditProgressEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.canteeninfo.CanteenInfoAuditResultEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.canteeninfo.CanteenInfoAuditTaskTypeEnum;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.ScConstants.SC_CANTEEN_LOG;
import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants.SERVER_ERROR;

@Slf4j
@Service
public class WmCanteenAuditFlowCanteenService {

    @Autowired
    private WmScCanteenAuditMapper wmScCanteenAuditMapper;

    @Autowired
    private WmScCanteenAuditNodeMapper auditNodeMapper;

    @Autowired
    private WmScCanteenSensitiveWordsService wmScCanteenSensitiveWordsService;

    @Autowired
    private WmCanteenAuditFlowGravityService gravityService;

    @Autowired
    private WmCanteenMapper canteenMapper;

    @Autowired
    private WmScLogService logService;

    /**
     * 审批任务主表更新审批节点
     * @param auditBO auditBO
     * @param nodeTypeEnum 当前审批节点
     */
    public void updateAuditByAuditNode(WmCanteenInfoAuditBO auditBO, CanteenInfoAuditNodeTypeEnum nodeTypeEnum) {
        WmScCanteenAuditDO auditDO = auditBO.getAuditDO();
        auditDO.setAuditNode(nodeTypeEnum.getType());
        wmScCanteenAuditMapper.updateByPrimaryKeySelective(auditDO);
        log.info("[WmCanteenAuditFlowCanteenService.updateAuditByAuditNode] auditDO = {}", JSONObject.toJSONString(auditDO));
    }

    /**
     * 审批任务子表更新任务状态为已通过
     * @param auditBO auditBO
     */
    public void updateAuditNodeByAuditPass(WmCanteenInfoAuditBO auditBO) {
        log.info("[WmCanteenAuditFlowCanteenService.updateAuditNodeByAuditPass] input param: auditBO = {}", JSONObject.toJSONString(auditBO));
        WmScCanteenAuditNodeDO auditNodeDO = auditBO.getAuditNodeDO();
        auditNodeDO.setAuditResult((int) CanteenInfoAuditResultEnum.AUDIT_PASS.getType());
        auditNodeDO.setAuditRemark(auditBO.getAuditRemark());
        auditNodeDO.setAuditTime(auditBO.getAuditTime());
        auditNodeDO.setMuid((long) auditBO.getOpUserUid());
        auditNodeMapper.updateByPrimaryKeySelective(auditNodeDO);
        log.info("[WmCanteenAuditFlowCanteenService.updateAuditNodeByAuditPass] input param: auditNodeDO = {}", JSONObject.toJSONString(auditNodeDO));
    }

    /**
     * 审批任务子表更新任务状态为已驳回
     * @param auditBO auditBO
     */
    public void updateAuditNodeByAuditReject(WmCanteenInfoAuditBO auditBO) {
        log.info("[WmCanteenAuditFlowCanteenService.updateAuditNodeByAuditReject] input param: auditBO = {}", JSONObject.toJSONString(auditBO));
        WmScCanteenAuditNodeDO auditNodeDO = auditBO.getAuditNodeDO();
        auditNodeDO.setAuditResult((int) CanteenInfoAuditResultEnum.AUDIT_REJECT.getType());
        auditNodeDO.setAuditRemark(auditBO.getAuditRemark());
        auditNodeDO.setAuditTime(auditBO.getAuditTime());
        auditNodeDO.setMuid((long) auditBO.getOpUserUid());
        auditNodeMapper.updateByPrimaryKeySelective(auditNodeDO);
        log.info("[WmCanteenAuditFlowCanteenService.updateAuditNodeByAuditReject] input param: auditNodeDO = {}", JSONObject.toJSONString(auditNodeDO));
    }

    /**
     * 审批任务子表更新任务状态为已终止
     * @param auditBO auditBO
     */
    public void updateAuditNodeByAuditStop(WmCanteenInfoAuditBO auditBO) {
        log.info("[WmCanteenAuditFlowCanteenService.updateAuditNodeByAuditStop] input param: auditBO = {}", JSONObject.toJSONString(auditBO));
        WmScCanteenAuditNodeDO auditNodeDO = auditBO.getAuditNodeDO();
        auditNodeDO.setAuditResult((int) CanteenInfoAuditResultEnum.AUDIT_END.getType());
        auditNodeDO.setAuditRemark(auditBO.getAuditRemark());
        auditNodeDO.setAuditTime(auditBO.getAuditTime());
        auditNodeDO.setMuid((long) auditBO.getOpUserUid());
        auditNodeMapper.updateByPrimaryKeySelective(auditNodeDO);
        log.info("[WmCanteenAuditFlowCanteenService.updateAuditNodeByAuditStop] input param: auditNodeDO = {}", JSONObject.toJSONString(auditNodeDO));
    }

    /**
     * 审批任务主表更新任务状态为已驳回
     * @param auditBO auditBO
     */
    public void updateAuditStatusByAuditReject(WmCanteenInfoAuditBO auditBO) {
        log.info("[WmCanteenAuditFlowCanteenService.updateAuditStatusByAuditReject] input param: auditBO = {}", JSONObject.toJSONString(auditBO));
        WmScCanteenAuditDO auditDO = auditBO.getAuditDO();
        auditDO.setAuditStatus((int) CanteenAuditStatusEnum.REJECT.getType());
        wmScCanteenAuditMapper.updateByPrimaryKeySelective(auditDO);
        log.info("[WmCanteenAuditFlowCanteenService.updateAuditStatusByAuditReject] auditDO = {}", JSONObject.toJSONString(auditDO));
    }

    /**
     * 审批任务主表更新任务状态为审批通过
     * @param auditBO auditBO
     */
    public void updateAuditStatusByAuditEffect(WmCanteenInfoAuditBO auditBO) {
        log.info("[WmCanteenAuditFlowCanteenService.updateAuditStatusByAuditEffect] input param: auditBO = {}", JSONObject.toJSONString(auditBO));
        WmScCanteenAuditDO auditDO = auditBO.getAuditDO();
        auditDO.setAuditStatus((int) CanteenAuditStatusEnum.AUDITED.getType());
        wmScCanteenAuditMapper.updateByPrimaryKeySelective(auditDO);
        log.info("[WmCanteenAuditFlowCanteenService.updateAuditStatusByAuditEffect] auditDO = {}", JSONObject.toJSONString(auditDO));
    }

    /**
     * 食堂信息表更新审批状态
     * @param auditBO auditBO
     */
    public void updateCanteenAuditStatusByAuditEffect(WmCanteenInfoAuditBO auditBO) {
        log.info("[WmCanteenAuditFlowCanteenService.updateCanteenAuditStatusByAuditEffect] input param: auditBO = {}", JSONObject.toJSONString(auditBO));
        WmScCanteenAuditDO auditDO = auditBO.getAuditDO();

        WmCanteenDB canteenDB = new WmCanteenDB();
        canteenDB.setId(auditDO.getCanteenId());
        canteenDB.setAuditStatus((int) CanteenAuditStatusEnum.AUDITING.getType());
        canteenDB.setEffective((int) EffectiveStatusEnum.EFFECTIVE.getType());
        canteenDB.setAuditDetailStatus((int) CanteenDetailStatusEnum.AUDITED_STATUS.getType());
        canteenMapper.updateCanteen(canteenDB);
        log.info("[WmCanteenAuditFlowCanteenService.updateCanteenAuditStatusByAuditEffect] canteenDB = {}", JSONObject.toJSONString(canteenDB));
    }

    /**
     * 食堂信息表更新审批状态
     * @param auditBO auditBO
     */
    public void updateCanteenAuditStatusByAuditReject(WmCanteenInfoAuditBO auditBO) {
        log.info("[WmCanteenAuditFlowCanteenService.updateCanteenAuditStatusByAuditReject] input param: auditBO = {}", JSONObject.toJSONString(auditBO));
        WmScCanteenAuditDO auditDO = auditBO.getAuditDO();

        WmCanteenDB canteenDB = new WmCanteenDB();
        canteenDB.setId(auditDO.getCanteenId());
        canteenDB.setAuditStatus((int) CanteenAuditStatusEnum.REJECT.getType());
        int auditDetailStatus = auditDO.getAuditTaskType().equals((int)CanteenInfoAuditTaskTypeEnum.SAVE_CANTEEN_AUDIT.getType())
                ? CanteenDetailStatusEnum.INSERT_AUDIT_REJECT_STATUS.getType()
                : CanteenDetailStatusEnum.UPDATE_AUDIT_REJECT_STATUS.getType();
        canteenDB.setAuditDetailStatus(auditDetailStatus);
        canteenMapper.updateCanteen(canteenDB);
        log.info("[WmCanteenAuditFlowCanteenService.updateCanteenAuditStatusByAuditReject] canteenDB = {}", JSONObject.toJSONString(canteenDB));
    }


    /**
     * 审批任务主表 - 创建任务
     * @param wmScCanteenAuditDO wmScCanteenAuditDO
     * @return 审批任务ID
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public Integer createAuditTaskBySubmit(WmScCanteenAuditDO wmScCanteenAuditDO, WmCanteenDB wmCanteenDB) throws WmSchCantException {
        log.info("[WmCanteenAuditFlowCanteenService.createAuditTaskBySubmit] input param: wmScCanteenAuditDO = {}, wmCanteenDB = {}",
                JSONObject.toJSONString(wmScCanteenAuditDO), JSONObject.toJSONString(wmCanteenDB));
        wmScCanteenSensitiveWordsService.writeWhenInsertOrUpdate(wmScCanteenAuditDO);
        // 1-审批任务类型
        Integer auditTaskType = getCanteenInfoAuditTaskType(wmCanteenDB);
        wmScCanteenAuditDO.setAuditTaskType(auditTaskType);

        // 2-审批流类型
        Integer auditProgressType = getCanteenInfoAuditProgressType(wmCanteenDB, wmScCanteenAuditDO);
        wmScCanteenAuditDO.setAuditProgressType(auditProgressType);

        wmScCanteenAuditMapper.insertSelective(wmScCanteenAuditDO);
        log.info("[WmCanteenAuditFlowCanteenService.createAuditTaskBySubmit] auditTaskId = {}", wmScCanteenAuditDO.getId());
        return wmScCanteenAuditDO.getId();
    }

    private Integer getCanteenInfoAuditTaskType(WmCanteenDB wmCanteenDB) {
        // 1-食堂状态=生效 - 修改审批
        if (wmCanteenDB.getEffective().equals((int) EffectiveStatusEnum.EFFECTIVE.getType())) {
            return (int) CanteenInfoAuditTaskTypeEnum.EDIT_CANTEEN_AUDIT.getType();
        } else {
            // 2-食堂状态=已生效 - 新建审批
            return (int) CanteenInfoAuditTaskTypeEnum.SAVE_CANTEEN_AUDIT.getType();
        }
    }


    private Integer getCanteenInfoAuditProgressType(WmCanteenDB wmCanteenDB, WmScCanteenAuditDO wmScCanteenAuditDO) {
        boolean updateStallNum = getIsChangeStallNum(wmCanteenDB, wmScCanteenAuditDO);
        boolean stallNumDownBeyondThreshold = getStallNumDownBeyondThreshold(wmCanteenDB, wmScCanteenAuditDO);

        return CanteenInfoAuditProgressEnum.getByParam(wmCanteenDB.getEffective(),
                        wmScCanteenAuditDO.getCanteenAttribute(),
                        updateStallNum, stallNumDownBeyondThreshold).getCode();
    }

    public boolean getIsChangeStallNum(WmCanteenDB wmCanteenDB, WmScCanteenAuditDO wmScCanteenAuditDO) {
        return !wmCanteenDB.getStallNum().equals(wmScCanteenAuditDO.getStallNum())
                || !wmCanteenDB.getOfflineBizStallNum().equals(wmScCanteenAuditDO.getOfflineBizStallNum())
                || !wmCanteenDB.getCanteenVideo().equals(wmScCanteenAuditDO.getCanteenVideo());
    }

    public boolean getStallNumDownBeyondThreshold(WmCanteenDB wmCanteenDB, WmScCanteenAuditDO wmScCanteenAuditDO) {
        boolean stallNumDownBeyondThreshold = ((wmScCanteenAuditDO.getStallNum() - wmCanteenDB.getStallNum())*1.0/(wmCanteenDB.getStallNum()*1.0))
                < MccScConfig.getCanteenStallNumDownRatioThreshold();
        boolean offlineBizStallNumDownBeyondThreshold = ((wmScCanteenAuditDO.getOfflineBizStallNum() - wmCanteenDB.getOfflineBizStallNum())*1.0/(wmCanteenDB.getOfflineBizStallNum()*1.0))
                < MccScConfig.getCanteenStallNumDownRatioThreshold();
        log.info("[getStallNumDownBeyondThreshold] stallNumDownBeyondThreshold = {}, offlineBizStallNumDownBeyondThreshold = {}", stallNumDownBeyondThreshold, offlineBizStallNumDownBeyondThreshold);
        return stallNumDownBeyondThreshold || offlineBizStallNumDownBeyondThreshold;
    }



    /**
     * 更新审批任务主表中GravityId和审批节点
     * @param gravityId gravityId
     * @param wmScCanteenAuditDO wmScCanteenAuditDO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void updateAuditTaskGravityIdAndAuditNode(String gravityId, WmScCanteenAuditDO wmScCanteenAuditDO) throws WmSchCantException {
        log.info("[WmCanteenAuditFlowCanteenService.updateAuditTaskGravityIdAndAuditNode] input param: gravityId = {}, wmScCanteenAuditDO = {}",
                gravityId, JSONObject.toJSONString(wmScCanteenAuditDO));
        // 1-根据GravityId查询流程实例获取当前审批节点
        CanteenInfoAuditNodeTypeEnum nodeTypeEnum = gravityService.getGravityAuditNodeByGravityId(gravityId);

        // 2-更新审批任务主表
        wmScCanteenAuditDO.setAuditNode(nodeTypeEnum.getType());
        wmScCanteenAuditDO.setGravityId(gravityId);
        wmScCanteenAuditMapper.updateByPrimaryKeySelective(wmScCanteenAuditDO);
        log.info("[WmCanteenAuditFlowCanteenService.updateAuditTaskGravityIdAndAuditNode] wmScCanteenAuditDO = {}", JSONObject.toJSONString(wmScCanteenAuditDO));
    }

    /**
     * 审批任务子表创建子任务
     * @param wmScCanteenAuditDO wmScCanteenAuditDO
     * @param auditorBO auditorBO
     * @return WmScCanteenAuditNodeDO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public WmScCanteenAuditNodeDO createAuditNodeBySubmit(Integer auditTaskId, WmScCanteenAuditDO wmScCanteenAuditDO, WmCanteenInfoAuditorBO auditorBO)
            throws WmSchCantException {
        log.info("[WmCanteenAuditFlowCanteenService.createAuditNodeBySubmit] input param: wmScCanteenAuditDO = {}, auditorBO = {}",
                JSONObject.toJSONString(wmScCanteenAuditDO), JSONObject.toJSONString(auditorBO));
        if (auditorBO == null) {
            return null;
        }

        WmScCanteenAuditNodeDO auditNodeDO = new WmScCanteenAuditNodeDO();
        auditNodeDO.setAuditTaskId(auditTaskId);
        auditNodeDO.setAuditNode(wmScCanteenAuditDO.getAuditNode());
        auditNodeDO.setAuditSystemType((int) CanteenInfoAuditNodeTypeEnum.getByType(wmScCanteenAuditDO.getAuditNode()).getAuditSystemType());
        auditNodeDO.setAuditorUid(auditorBO.getAuditorUid());
        auditNodeDO.setAuditorMis(auditorBO.getAuditorMis());
        auditNodeDO.setAuditorName(auditorBO.getAuditorName());
        auditNodeDO.setCuid((long) wmScCanteenAuditDO.getUserId());

        int result = auditNodeMapper.insertSelective(auditNodeDO);
        if (result == 0 || auditNodeDO.getId() == null) {
            log.error("[WmCanteenAuditFlowCanteenService.createAuditTaskNodeBySubmit] insertSelective error. auditNodeDO = {}", JSONObject.toJSONString(auditNodeDO));
            throw new WmSchCantException(SERVER_ERROR, "新增审批子任务失败");
        }
        log.info("[WmCanteenAuditFlowCanteenService.createAuditTaskNodeBySubmit] auditNodeDO = {}", JSONObject.toJSONString(auditNodeDO));
        return auditNodeDO;
    }


    public void updateAuditNodeAuditSystemId(WmScCanteenAuditNodeDO auditNodeDO, String auditSystemId) {
        log.info("[WmCanteenAuditFlowCanteenService.updateAuditNodeAuditSystemId] auditNodeDO = {}, auditSystemId = {}", JSONObject.toJSONString(auditNodeDO), auditSystemId);
        if (auditNodeDO == null) {
            return;
        }

        auditNodeDO.setAuditSystemId(auditSystemId);
        auditNodeMapper.updateByPrimaryKeySelective(auditNodeDO);
        log.info("[WmCanteenAuditFlowCanteenService.updateAuditNodeAuditSystemId] success. auditNodeDO = {}", JSONObject.toJSONString(auditNodeDO));
    }


    public void recordSubmitAuditTaskLog(WmScCanteenAuditNodeDO auditNodeDO, WmScCanteenAuditDO auditDO) {
        if (StringUtils.isBlank(auditNodeDO.getAuditorMis())) {
            return;
        }

        CanteenInfoAuditTaskTypeEnum auditTaskTypeEnum = CanteenInfoAuditTaskTypeEnum.getByType(auditDO.getAuditTaskType());
        CanteenInfoAuditNodeTypeEnum auditNodeTypeEnum = CanteenInfoAuditNodeTypeEnum.getByType(auditNodeDO.getAuditNode());

        String logInfo = "操作：提交审批\\n" +
                "审批类型：" + (auditTaskTypeEnum == null ? "-" : auditTaskTypeEnum.getName()) + "\\n" +
                "审批节点：" + (auditNodeTypeEnum == null ? "-" : auditNodeTypeEnum.getName()) + "\\n" +
                "审批人：" + auditNodeDO.getAuditorName() + "(" + auditNodeDO.getAuditorMis() + ")" + "\\n" +
                "审批系统ID：" + auditNodeDO.getAuditSystemId() + "\\n";

        logService.insertScOptLog(OptTypeEnum.INSERT.getType(), SC_CANTEEN_LOG, auditDO.getCanteenId(),
                auditDO.getUserId(), auditDO.getUserName(), logInfo, "");
    }


    public void recordBuildNextNodeLog(WmCanteenInfoAuditBO auditBO) {
        WmScCanteenAuditDO auditDO = auditBO.getAuditDO();
        WmScCanteenAuditNodeDO auditNodeDO = auditBO.getAuditNodeDO();

        CanteenInfoAuditNodeTypeEnum auditNodeTypeEnum = CanteenInfoAuditNodeTypeEnum.getByType(auditNodeDO.getAuditNode());
        String logInfo = "操作：更新审批节点\\n" +
                "审批节点：" + (auditNodeTypeEnum == null ? "-" : auditNodeTypeEnum.getName()) + "\\n" +
                "审批人：" + auditNodeDO.getAuditorName() + "(" + auditNodeDO.getAuditorMis() + ")" + "\\n" +
                "审批系统ID：" + auditNodeDO.getAuditSystemId() + "\\n";

        logService.insertScOptLog(OptTypeEnum.INSERT.getType(), SC_CANTEEN_LOG, auditDO.getCanteenId(),
                auditDO.getUserId(), auditDO.getUserName(), logInfo, "");
    }


    public void recordRejectAuditTaskLog(WmCanteenInfoAuditBO auditBO) {
        WmScCanteenAuditDO auditDO = auditBO.getAuditDO();
        WmScCanteenAuditNodeDO auditNodeDO = auditBO.getAuditNodeDO();

        CanteenInfoAuditNodeTypeEnum auditNodeTypeEnum = CanteenInfoAuditNodeTypeEnum.getByType(auditNodeDO.getAuditNode());
        String logInfo = "操作：审批驳回\\n" +
                "审批节点：" + (auditNodeTypeEnum == null ? "-" : auditNodeTypeEnum.getName()) + "\\n" +
                "审批人：" + auditNodeDO.getAuditorName() + "(" + auditNodeDO.getAuditorMis() + ")" + "\\n" +
                "审批系统ID：" + auditNodeDO.getAuditSystemId() + "\\n";

        logService.insertScOptLog(OptTypeEnum.INSERT.getType(), SC_CANTEEN_LOG, auditDO.getCanteenId(),
                auditNodeDO.getAuditorUid(), auditNodeDO.getAuditorName() + "(" + auditNodeDO.getAuditorMis() + ")", logInfo, "");
    }


    public void recordEffectAuditTaskLog(WmCanteenInfoAuditBO auditBO) {
        WmScCanteenAuditDO auditDO = auditBO.getAuditDO();
        WmScCanteenAuditNodeDO auditNodeDO = auditBO.getAuditNodeDO();
        String logInfo = "操作：审批通过\\n" +
                "审批任务ID：" + auditDO.getId() + "\\n";

        logService.insertScOptLog(OptTypeEnum.INSERT.getType(), SC_CANTEEN_LOG, auditDO.getCanteenId(),
                auditNodeDO.getAuditorUid(), auditNodeDO.getAuditorName() + "(" + auditNodeDO.getAuditorMis() + ")", logInfo, "");
    }


}

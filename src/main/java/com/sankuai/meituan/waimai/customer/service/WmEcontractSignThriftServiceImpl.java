package com.sankuai.meituan.waimai.customer.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Preconditions;
import com.google.common.collect.Sets;
import com.sankuai.meituan.mtcoop.thrift.dto.CoopSignBasicReqInfo;
import com.sankuai.meituan.mtcoop.thrift.dto.TSubmitCoopWithResignRequest;
import com.sankuai.meituan.mtcoop.thrift.enumtype.TCoopSource;
import com.sankuai.meituan.waimai.customer.adapter.daocan.CommonCoopServiceAdapter;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.constant.sign.WmSignConstant;
import com.sankuai.meituan.waimai.customer.dao.WmEcontractTaskDBMapper;
import com.sankuai.meituan.waimai.customer.domain.WmEContractSignBaseDB;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiService;
import com.sankuai.meituan.waimai.customer.service.gray.WmCustomerGrayService;
import com.sankuai.meituan.waimai.customer.service.sign.WmEcontractSignBatchOpService;
import com.sankuai.meituan.waimai.customer.service.sign.WmEcontractSignBzService;
import com.sankuai.meituan.waimai.customer.service.sign.WmEcontractToSignService;
import com.sankuai.meituan.waimai.customer.service.sign.applyecontract.WmEcontractApplyService;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractBaseBizService;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractBatchBizService;
import com.sankuai.meituan.waimai.customer.service.sign.compare.phf.WmEcontractPhfCompareService;
import com.sankuai.meituan.waimai.customer.service.sign.signTemplet.SignTempletManagerService;
import com.sankuai.meituan.waimai.customer.service.sign.signpack.WmEcontractSignPackService;
import com.sankuai.meituan.waimai.customer.util.base.AssertUtil;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskUpstreamStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.WmEcontractBatchConstant;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.BooleanResult;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.IntResult;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.LongResult;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.econtractsign.dto.TaskCustomerPageDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.econtractsign.request.QuerySignTaskReq;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.econtractsign.request.SignTaskCustomerReq;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.econtractsign.request.ToSignCustomerTaskReq;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.econtractsign.response.AccountSignTaskResp;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.econtractsign.response.ApplyBaseConfirmResp;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.econtractsign.response.ApplyBaseConfirmStatusResp;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.econtractsign.response.ToSignTaskQueryResp;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.framecontract.QueryDcMtCustomerIdParam;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.contracttask.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.request.LvyueXzsCustomerInfoRequest;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.response.LvyueXzsCustomerInfoResponse;
import com.sankuai.meituan.waimai.thrift.customer.service.WmEcontractSignThriftService;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Slf4j
@Service
public class WmEcontractSignThriftServiceImpl implements WmEcontractSignThriftService {

    private static final Logger LOG = LoggerFactory.getLogger(WmEcontractSignThriftServiceImpl.class);

    @Resource
    private WmEcontractSignBzService wmEcontractSignBzService;
    @Autowired
    private SignTempletManagerService signTempletManagerService;
    @Autowired
    private WmEcontractBatchBizService wmEcontractBatchBizService;
    @Autowired
    private WmEcontractApplyService wmEcontractApplyService;
    @Autowired
    private WmEcontractSignBatchOpService wmEcontractSignBatchOpService;
    @Autowired
    private WmEcontractToSignService wmEcontractToSignService;
    @Autowired
    private WmEcontractSignPackService wmEcontractSignPackService;

    @Autowired
    private WmCustomerPoiService wmCustomerPoiService;

    @Autowired
    private WmEcontractBaseBizService wmEcontractBaseBizService;

    @Resource
    private WmCustomerGrayService wmCustomerGrayService;

    @Resource
    private WmEcontractTaskDBMapper wmEcontractTaskDBMapper;

    @Resource
    private CommonCoopServiceAdapter commonCoopServiceAdapter;

    @Resource
    private WmEcontractPhfCompareService wmEcontractPhfCompareService;

    @Override
    public LongResult applyTask(EcontractTaskApplyBo applyBo) throws TException, WmCustomerException {
        try {
            return wmEcontractSignBzService.applyTask(applyBo);
        } catch (IllegalAccessException e) {
            LOG.error("任务处理异常, task", e);
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "处理异常");
        }
    }

    @Override
    public LongResult easyApplyTask(EcontractTaskApplyBo applyBo) throws TException, WmCustomerException {
        try {
            return wmEcontractSignBzService.easyApplyTask(applyBo);
        } catch (IllegalAccessException e) {
            LOG.error("任务处理异常, task:{}", applyBo, e);
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "处理异常");
        }
    }

    @Override
    public LongResult triggerApplyTask(EcontractTaskApplyBo applyBo) throws TException, WmCustomerException {
        try {
            return wmEcontractSignBzService.triggerApplyTask(applyBo);
        } catch (IllegalAccessException e) {
            LOG.error("任务处理异常, task:{}", applyBo, e);
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "处理异常");
        }
    }

    @Override
    public LongResult applyBase(EcontractBaseApplyBo applyBo) throws TException, WmCustomerException {
        try {
            return wmEcontractSignBzService.applyBase(applyBo);
        } catch (IllegalAccessException e) {
            LOG.error("基本信息处理异常, task", e);
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "处理异常");
        }
    }

    @Override
    public BooleanResult syncUpstreamStatus(Long taskId, EcontractTaskUpstreamStatusEnum statusEnum) throws TException, WmCustomerException {
        return wmEcontractSignBzService.syncUpstreamStatus(taskId, statusEnum.getType());
    }

    @Override
    public Integer queryAllTaskUpstreamStatus(String recordKey) throws TException, WmCustomerException {
        return wmEcontractSignBzService.queryAllTaskUpstreamStatus(recordKey);
    }

    @Override
    public BooleanResult resendMsg(Long taskId) throws TException, WmCustomerException {
        RetrySmsResponse smsResponse = wmEcontractSignBzService.resendMsg(taskId);
        return new BooleanResult(smsResponse.isOk());
    }

    @Override
    public RetrySmsResponse resendMsgByBatchIdWithResponse(long batchId) throws TException, WmCustomerException {
        return wmEcontractSignBzService.resendMsgByBatchId(batchId);
    }

    @Override
    public RetrySmsResponse resendMsgByPackIdWithResponse(long packId) throws TException, WmCustomerException {
        return wmEcontractSignBzService.resendMsgByPackId(packId);
    }

    @Override
    public BooleanResult cancelSign(Long taskId) throws TException, WmCustomerException {
        LOG.info("WmEcontractSignThriftServiceImpl#cancelSign, taskId: {}", taskId);
        return wmEcontractSignBzService.cancelSign(taskId, Boolean.TRUE);
    }

    @Override
    public BooleanResult cancelSignWithCallBack(Long taskId, Boolean callBack) throws TException, WmCustomerException {
        LOG.info("WmEcontractSignThriftServiceImpl#cancelSignWithCallBack, taskId: {}, callBack: {}", taskId, callBack);
        return wmEcontractSignBzService.cancelSign(taskId, callBack);
    }

    @Override
    public BooleanResult forceCancelSignByTaskId(EcontractForceCancelSignByTaskIdBo forceCancelSignByTaskIdBo) throws TException, WmCustomerException {
        return wmEcontractSignBzService.forceCancelSignByTaskId(forceCancelSignByTaskIdBo);
    }

    @Override
    public BooleanResult cancelSignWithActionSource(Long taskId, String actionSource) throws TException, WmCustomerException {
        return wmEcontractSignBzService.cancelSign(taskId, actionSource);
    }

    @Override
    public BooleanResult cancelSignByBatchId(long batchId) throws TException, WmCustomerException {
        return wmEcontractSignBzService.cancelSignByBatchIdWithSource(batchId, WmEcontractBatchConstant.TASK_MANAGE_BATCH_LIST);
    }

    @Override
    public BooleanResult cancelSignByBatchIdWithReason(long batchId, String reason) throws TException, WmCustomerException {
        BooleanResult result = wmEcontractSignBzService.cancelSignByBatchIdWithSource(batchId, WmEcontractBatchConstant.TASK_MANAGE_BATCH_LIST);
        if (result.isRes()) {
            wmEcontractBatchBizService.updateBatchFailMsgByBatchId(batchId, reason, WmEcontractBatchConstant.TASK_MANAGE_BATCH_LIST);
        }
        return result;
    }

    @Override
    public BooleanResult cancelSignByPackId(long packId) throws TException, WmCustomerException {
        return wmEcontractSignBzService.cancelSignByPackId(packId);
    }

    @Override
    public BooleanResult cancelSignByPackIdWithReason(long packId, String reason) throws TException, WmCustomerException {
        return wmEcontractSignBzService.cancelSignByPackIdWithReason(packId, reason);
    }

    @Override
    public BooleanResult cancelBase(Integer customerId) throws TException, WmCustomerException {
        return wmEcontractSignBzService.cancelBase(customerId);
    }

    @Override
    public BooleanResult unbindPoi(Integer customerId, Long wmPoiId) throws TException, WmCustomerException {
        return wmEcontractSignBzService.unbindPoi(customerId, wmPoiId);
    }

    @Override
    public BooleanResult bindPoi(Integer customerId, List<Long> wmPoiIdList) throws TException, WmCustomerException {
        try {
            return wmEcontractSignBzService.bindPoi(customerId, wmPoiIdList);
        } catch (IllegalAccessException e) {
            LOG.error("处理异常, task", e);
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "处理异常");
        }
    }

    @Override
    public BooleanResult checkCustomerBatchMatch(int customerId, long batchId) throws TException, WmCustomerException {
        AssertUtil.assertIntegerMoreThan0(customerId);
        AssertUtil.assertLongMoreThan0(batchId, "任务ID");
        return wmEcontractSignBzService.checkCustomerBatchMatch(customerId, batchId);
    }

    @Override
    public BooleanResult checkQuaRealLetterBatchMatch(long batchId) throws TException, WmCustomerException {
        AssertUtil.assertLongMoreThan0(batchId, "任务ID");
        return wmEcontractSignBzService.checkQuaRealLetterBatchMatch(batchId);
    }

    @Override
    public BooleanResult checkCustomerTaskMatch(int customerId, long taskId) throws TException, WmCustomerException {
        AssertUtil.assertIntegerMoreThan0(customerId);
        AssertUtil.assertLongMoreThan0(taskId, "任务ID");
        return wmEcontractSignBzService.checkCustomerTaskMatch(customerId, taskId);
    }

    @Override
    public BooleanResult checkQuaRealLetterTaskMatch(long taskId) throws TException, WmCustomerException {
        AssertUtil.assertLongMoreThan0(taskId, "任务ID");
        return wmEcontractSignBzService.checkQuaRealLetterTaskMatch(taskId);
    }

    @Override
    public void forceUnbindByBatchId(int wmCustomerId, long batchId, int userId, String userName)
            throws TException, WmCustomerException {
        List<EcontractTaskBo> taskInfos = wmEcontractSignBzService.getTaskInfoByBatchId(batchId);
        if (taskInfos.size() == 1 && taskInfos.get(0).getApplyType().equals(EcontractTaskApplyTypeEnum.CUSTOMER.getName())) {
            wmCustomerPoiService.forceUnbind(wmCustomerId, taskInfos.get(0).getId(), userId, userName);
        } else {
            throw new WmCustomerException(WmContractErrorCodeConstant.AUTHORIZING, "没有对应任务信息");
        }
    }

    @Override
    @Deprecated
    public LongResult getUnBindPoiTaskId(long batchId) throws TException, WmCustomerException {
        return new LongResult();
    }

    @Override
    public Map<Long,String> getSingleTypeAndTaskId(long batchId) throws TException, WmCustomerException {
        AssertUtil.assertLongMoreThan0(batchId, "批次ID");
        return wmEcontractSignBzService.getSingleTypeAndTaskId(batchId);
    }

    @Override
    public RetrySmsResponse resendMsgWithResponse(Long taskId) throws TException, WmCustomerException {
        return wmEcontractSignBzService.resendMsg(taskId);
    }

    @Override
    public BooleanResult canApplyManualTask(ManualTaskApplyBo manualTaskApplyBo) throws TException, WmCustomerException {
        return wmEcontractSignBzService.canApplyManualTask(manualTaskApplyBo);
    }

    @Override
    public LongResult applyManualTask(ManualTaskApplyBo manualTaskApplyBo) throws TException, WmCustomerException {
        return wmEcontractSignBzService.applyManualTask(manualTaskApplyBo);
    }

    @Override
    public LongResult applyTaskForAutoRenewal(ManualTaskApplyBo manualTaskApplyBo) throws TException, WmCustomerException {
        return wmEcontractSignBzService.applyTaskForAutoRenewal(manualTaskApplyBo);
    }

    @Override
    public BooleanResult cancelManualTask(long manualTaskId, int opUid, String source) throws TException, WmCustomerException {
        AssertUtil.assertLongMoreThan0(manualTaskId, "手动打包任务ID");
        return wmEcontractSignBzService.cancelManualTask(manualTaskId, opUid, source);
    }

    @Override
    public List<LongResult> getManualTaskIdByCustomerId(int wmCustomerId)
            throws TException, WmCustomerException {
        AssertUtil.assertIntegerMoreThan0(wmCustomerId, "客户ID");
        return wmEcontractSignBzService.getManualTaskIdByCustomerId(wmCustomerId);
    }

    @Override
    public List<Long> getManualTaskIdByParam(ManualTaskIdQueryParam manualTaskIdQueryParam) throws TException, WmCustomerException{
        log.info("getManualTaskIdByCustomerId manualTaskIdQueryParam:{}", JSON.toJSON(manualTaskIdQueryParam));
        Integer wmCustomerId = manualTaskIdQueryParam.getCustomerId();
        AssertUtil.assertIntegerMoreThan0(wmCustomerId, "客户ID");
        return wmEcontractSignBzService.getManualTaskIdByCustomerId(manualTaskIdQueryParam);
    }

    @Override
    public List<WmEcontractSignManualTaskBo> getManualTaskInfoByCustomerId(int wmCustomerId) throws TException, WmCustomerException {
        log.info("WmEcontractSignThriftService#getManualTaskInfoByCustomerId,customerId:{}", wmCustomerId);
        AssertUtil.assertIntegerMoreThan0(wmCustomerId, "客户ID");
        return wmEcontractSignBzService.getManualTaskInfoByCustomerId(wmCustomerId);
    }

    @Override
    public LongResult applyManualPack(List<Long> manualTaskIds, int commitUid) throws TException, WmCustomerException {
        AssertUtil.assertCollectionNotEmpty(manualTaskIds, "手动打包任务ID");
        return wmEcontractSignBzService.applyManualPack(manualTaskIds, commitUid, WmSignConstant.OTHER);
    }

    @Override
    public BooleanResult cancelWmCustomerSwitch(Integer targetCustomerId, List<Long> wmPoiIdList, int opUid, String opUname)
            throws TException, WmCustomerException {
        AssertUtil.assertIntegerMoreThan0(targetCustomerId);
        AssertUtil.assertCollectionNotEmpty(wmPoiIdList, "切换门店ID");
        return wmEcontractSignBzService.cancelWmCustomerSwitch(targetCustomerId, wmPoiIdList, opUid, opUname);
    }

    @Override
    public BooleanResult cancelWmCustomerSwitchForWmPoi(CancelSignForSwitchParam param) throws TException, WmCustomerException {
        AssertUtil.assertObjectNotNull(param);
        AssertUtil.assertIntegerMoreThan0(param.getTargetCustomerId());
        AssertUtil.assertCollectionNotEmpty(param.getWmPoiIdList(), "切换门店ID");
        return wmEcontractSignBzService.cancelWmCustomerSwitchForWmPoi(param);
    }

    @Override
    public String getDocument(String bizType, String versionNum) throws TException, WmCustomerException {
        AssertUtil.assertStringNotEmpty(bizType, "业务类型");
        AssertUtil.assertStringNotEmpty(versionNum, "版本号");
        return signTempletManagerService.getDocument(bizType, versionNum);
    }

    @Override
    public SignVersionBo getSignVersionInfo(long taskId) throws TException, WmCustomerException {
        AssertUtil.assertLongMoreThan0(taskId, "任务ID");
        return wmEcontractSignBzService.getSignVersionInfo(taskId);
    }

    @Override
    public List<SignVersionDetailBo> getSignVersionTemplet(SignVersionQueryThriftParam param) throws TException, WmCustomerException {
        AssertUtil.assertStringNotEmpty(param.getBizType(), "业务类型");
        return signTempletManagerService.getSignVersionTemplet(param);
    }

    @Override
    public BooleanResult setSignVersionTempletStatus(SignVersionQueryThriftParam param) throws TException, WmCustomerException {
        AssertUtil.assertStringNotEmpty(param.getBizType(), "业务类型");
        AssertUtil.assertStringNotEmpty(param.getVersionNum(), "版本号");
        AssertUtil.assertStringNotEmpty(param.getStatus(), "版本状态");
        return signTempletManagerService.setSignVersionTempletStatus(param);
    }

    @Override
    public BooleanResult saveSignVersionTemplet(SignVersionDetailBo detail) throws TException, WmCustomerException {
        AssertUtil.assertStringNotEmpty(detail.getBizType(), "业务类型");
        AssertUtil.assertStringNotEmpty(detail.getVersionNum(), "版本号");
        return signTempletManagerService.saveSignVersionTemplet(detail);
    }

    @Override
    public BooleanResult haveExistingTemplet(SignVersionDetailBo detail) throws TException, WmCustomerException {
        AssertUtil.assertStringNotEmpty(detail.getBizType(), "业务类型");
        AssertUtil.assertStringNotEmpty(detail.getVersionNum(), "版本号");
        return new BooleanResult(signTempletManagerService.haveExistingTemplet(detail.getBizType(), detail.getVersionNum()));
    }

    @Override
    public IntResult transModuleSignVersionToSettleMark(String bizType, String moduleSignVersion) throws TException, WmCustomerException {
        AssertUtil.assertStringNotEmpty(bizType, "业务类型");
        return signTempletManagerService.transModuleSignVersionToSettleMark(bizType, moduleSignVersion);
    }

    @Override
    public String getVoiceNoticeMsg(long taskId) throws TException, WmCustomerException {
        AssertUtil.assertLongMoreThan0(taskId, "签约taskId");
        return wmEcontractSignBzService.getVoiceNoticeMsg(taskId);
    }

    /**
     * 查询打包签约详情
     *
     * @param signTaskSearchInputDTOList
     * @return
     * @throws TException
     * @throws WmCustomerException
     */
    @Override
    public List<SignTaskSearchDetailOutPutDTO> queryBatchSignDetailList(List<SignTaskSearchInputDTO> signTaskSearchInputDTOList) throws TException, WmCustomerException {
        AssertUtil.assertCollectionNotEmpty(signTaskSearchInputDTOList, "入参");
        return wmEcontractBatchBizService.queryBatchSignDetailList(signTaskSearchInputDTOList);
    }

    @Override
    public void cancelManualTaskByCustomerIdAndModule(int wmCustomerId, EcontractTaskApplyTypeEnum
            econtractTaskApplyTypeEnum) throws TException, WmCustomerException {

        wmEcontractSignBzService.cancelManualTaskByCustomerIdAndModule(wmCustomerId,
                econtractTaskApplyTypeEnum.getName());
    }

    @Override
    public String getSignShortLink(long taskId) throws TException, WmCustomerException {
        return wmEcontractApplyService.getSignShortLink(taskId);
    }

    @Override
    public void confirmSignPermissionCheck(List<ConfirmSignParam> confirmSignParams) throws TException, WmCustomerException {
        wmEcontractSignBzService.confirmSignPermissionCheck(confirmSignParams);
    }

    @Override
    public void confirmSign(Set<Long> batchSet) throws TException, WmCustomerException {
        wmEcontractSignBzService.confirmSign(batchSet);
    }

    @Override
    public String queryRecordIdByBatchId(long batchId) throws TException, WmCustomerException {
        return wmEcontractSignBzService.queryRecordIdByBatchId(batchId);
    }

    @Override
    public EcontractBatchOpResponse batchOpEcontract(EcontractBatchOpRequest opRequest) throws TException, WmCustomerException, InterruptedException {
        LOG.info("batchOpEcontract batchOpRequest:{}", JSON.toJSONString(opRequest));
        return wmEcontractSignBatchOpService.batchOpEcontract(opRequest);
    }

    @Override
    public EcontractRollPackResponse rollPackEcontract(EcontractRollPackRequest rollPackRequest) throws TException, WmCustomerException, InterruptedException {
        LOG.info("rollPackEcontract request:{}", JSON.toJSONString(rollPackRequest));
        return wmEcontractSignBatchOpService.rollPackEcontract(rollPackRequest);
    }

    @Override
    public void unlockRollPackByCustomerId(Long customerId) throws TException, WmCustomerException {
        LOG.info("unlockRollPackByCustomerId customerId:{}", customerId);
        wmEcontractSignBatchOpService.unlockRollPackByCustomerId(customerId);
    }

    @Override
    public EcontractBatchContextDTO queryBatchTypeByRecordKey(String recordKey) throws TException, WmCustomerException {
        LOG.info("queryBatchTypeByRecordKey recordKey:{}", recordKey);
        return wmEcontractBatchBizService.queryBatchTypeByRecordKey(recordKey);
    }

    @Override
    public BooleanResult queryErrorBatchInfo(Long batchId) throws TException, WmCustomerException {
        LOG.info("queryErrorBatchInfo batchId:{}", batchId);
        return wmEcontractBatchBizService.queryErrorBatchInfo(batchId);
    }

    @Override
    public void deleteErrorBatch(Long batchId) throws TException, WmCustomerException {
        LOG.info("deleteErrorBatch batchId:{}", batchId);
        wmEcontractBatchBizService.deleteErrorBatch(batchId);
    }

    @Override
    public BooleanResult isToSignGrayWmPoiId(Long wmPoiId) throws TException, WmCustomerException {
        LOG.info("isGrayWmPoiId wmPoiId:{}", wmPoiId);
        return new BooleanResult(wmPoiId % 100 < MccConfig.toSignGrayWmPoiIdPercent());
    }

    @Override
    public ToSignCertifyInfoBo queryToSignCertifyInfoBoByWmPoiId(Long wmPoiId) throws TException, WmCustomerException {
        LOG.info("queryToSignCertifyInfoBoByWmPoiId wmPoiId:{}", wmPoiId);
        return wmEcontractToSignService.queryToSignCertifyInfoBoByWmPoiId(wmPoiId);
    }

    @Override
    public List<String> queryToSignTaskRecordKeysByWmPoiIdAndType(Long wmPoiId, String kpPhoneNum, Integer type) throws TException, WmCustomerException {
        LOG.info("queryToSignTaskListByWmPoiIdAndType wmPoiId:{}, kpPhoneNum:{}, type:{}", wmPoiId, kpPhoneNum, type);
        return wmEcontractToSignService.queryToSignTaskRecordKeysByWmPoiIdAndType(wmPoiId, kpPhoneNum, type);
    }

    @Override
    public String queryToSignTipsByWmPoiId(Long wmPoiId) throws TException, WmCustomerException, InterruptedException {
        LOG.info("queryToSignTipsByWmPoiId wmPoiId:{}", wmPoiId);
        return wmEcontractToSignService.queryToSignTipsByWmPoiId(wmPoiId);
    }
    @Override
    public BooleanResult checkPhoneAuthWithRecordKeys(List<String> recordKeys, String kpPhoneNum) throws TException, WmCustomerException {
        LOG.info("checkPhoneAuthWithRecordKeys recordKeys:{}, kpPhoneNum:{}", recordKeys, kpPhoneNum);
        return new BooleanResult(wmEcontractToSignService.checkPhoneAuthWithRecordKeys(recordKeys, kpPhoneNum));
    }

    @Override
    public List<String> queryRecordKeyBySignPackId(Long signPackId) throws TException, WmCustomerException{
        LOG.info("queryRecordKeyBySignPackId signPackId:{}", signPackId);
        return wmEcontractSignPackService.queryRecordKeyBySignPackId(signPackId);
    }

    //无流量
    @Override
    public String querySmsParamInfoByRecordKey(String recordKey) throws TException, WmCustomerException {
        LOG.info("querySmsParamInfoByRecordKey recordKey:{}", recordKey);
        return wmEcontractSignPackService.querySmsParamInfoByRecordKey(recordKey);
    }

    @Override
    public Map<Long, Boolean> hasInProcessingCustomerUnBindWmPoiTask(CustomerUnBindWmPoiTaskSearchParam param)
        throws TException, WmCustomerException {
        return wmEcontractSignBzService.hasInProcessingCustomerUnBindWmPoiTask(param);
    }



    @Override
    public AccountSignTaskResp queryAccountSignTaskInfo(QuerySignTaskReq querySignTaskReq)
        throws TException, WmCustomerException {
        log.info("queryAccountSignTaskInfo入参:querySignTaskReq={}", JSON.toJSONString(querySignTaskReq));
        AccountSignTaskResp resp = wmEcontractSignBzService.queryAccountSignTaskInfo(querySignTaskReq);
        log.info("queryAccountSignTaskInfo出参:resp={}", JSON.toJSONString(resp));
        return resp;
    }

    /**
     * 待签约，客户列表
     *
     * @param signTaskCustomerReq 签约
     * @return 返回客户信息
     * @throws TException          TException异常
     * @throws WmCustomerException WmCustomerException异常
     */
    @Override
    public TaskCustomerPageDTO taskCustomerList(SignTaskCustomerReq signTaskCustomerReq)
        throws TException, WmCustomerException {
        log.info("taskCustomerList入参:querySignTaskReq={}", JSON.toJSONString(signTaskCustomerReq));
        TaskCustomerPageDTO resp = wmEcontractSignBzService.taskCustomerList(signTaskCustomerReq);
        log.info("taskCustomerList出参:resp={}", JSON.toJSONString(resp));
        return resp;
    }

    @Override
    public ToSignTaskQueryResp queryToSignTaskRecordKeysViaCustomer(ToSignCustomerTaskReq toSignCustomerTaskReq)
        throws TException, WmCustomerException {
        log.info("queryToSignTaskRecordKeysViaCustomer入参:toSignCustomerTaskReq={}",
            JSON.toJSONString(toSignCustomerTaskReq));
        ToSignTaskQueryResp resp = wmEcontractToSignService.queryToSignTaskRecordKeysViaCustomer(toSignCustomerTaskReq);
        log.info("queryToSignTaskRecordKeysViaCustomer出参:resp={}", JSON.toJSONString(resp));
        return resp;
    }

    @Override
    public ToSignCertifyInfoBo queryToSignCertifyInfoViaCustomer(int customerId)
        throws TException, WmCustomerException {
        log.info("queryToSignCertifyInfoViaCustomer入参: customerCId={}", customerId);
        ToSignCertifyInfoBo resp = wmEcontractToSignService.queryToSignCertifyInfoViaCustomer(customerId);
        log.info("queryToSignCertifyInfoViaCustomer出参:resp={}", JSON.toJSONString(resp));
        return resp;
    }

    @Override
    public ApplyBaseConfirmResp applyManualSubmitViaCustomer(int customerId, int opUid)
        throws TException, WmCustomerException {
        log.info("ApplyBaseConfirmResp入参: customerCId={}", customerId);
        ApplyBaseConfirmResp resp = wmEcontractToSignService.applyManualSubmitViaCustomer(customerId, opUid);
        log.info("ApplyBaseConfirmResp出参:resp={}", JSON.toJSONString(resp));
        return resp;
    }

    @Override
    public ApplyBaseConfirmStatusResp applyManualSubmitConfirmStatus(String confirmUniqueKey)
        throws TException, WmCustomerException {
        log.info("applyBaseConfirmStatus入参: confirmUniqueKey={}", confirmUniqueKey);
        ApplyBaseConfirmStatusResp resp = wmEcontractToSignService.applyManualSubmitConfirmStatus(confirmUniqueKey);
        log.info("applyBaseConfirmStatus出参:resp={}", JSON.toJSONString(resp));
        return resp;
    }

    @Override
    public LvyueXzsCustomerInfoResponse getLvyueXzsCustomerInfo(LvyueXzsCustomerInfoRequest request) throws WmCustomerException {
        log.warn("getLvyueXzsCustomerInfo request:{}", JSONObject.toJSONString(request));
        Long wmPoiId = request.getWmPoiId();
        if(wmPoiId == null || wmPoiId == 0){
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "参数不能为空");
        }

        //门店查客户
        Set<Integer> wmCustomerIdSet = wmCustomerPoiService.selectCustomerIdByPoiId(Sets.newHashSet(wmPoiId));
        Integer customerId = wmCustomerIdSet.stream().findFirst().orElseGet(()->{
            log.warn("getLvyueXzsCustomerInfo poiId:{}查无关联客户，customerId", request.getWmPoiId());
            return null;
        });
        if(customerId == 0 || customerId == null){
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "门店未关联客户");
        }
        log.info("getLvyueXzsCustomerInfo customerId:{}", customerId);

        //客户kp数据组装
        WmEContractSignBaseDB baseDB = wmEcontractBaseBizService.queryByCustomerId(customerId);
        if (baseDB == null) {
            return null;
        }
        EcontractBaseInfoBo baseInfoBo = JSON.parseObject(baseDB.getBaseContext(), EcontractBaseInfoBo.class);
        EcontractCustomerInfoBo customerInfoBo = baseInfoBo.getCustomerInfoBo();
        EcontractCustomerKPBo kpBo = baseInfoBo.getKpBo();
        LvyueXzsCustomerInfoResponse response = LvyueXzsCustomerInfoResponse.
                builder().
                customerId(customerId).
                quaTypeEnum(customerInfoBo.getQuaTypeEnum()).
                customerName(customerInfoBo.getCustomerName()).
                customerSecondType(customerInfoBo.getCustomerSecondType()).
                quaNum(customerInfoBo.getQuaNum()).
                signerPhoneNum(kpBo.getSignerPhoneNum()).
                signerEmail(kpBo.getSignerEmail()).
                signerName(kpBo.getSignerName()).
                signerIDCardNum(kpBo.getSignerIDCardNum()).
                signerBankName(kpBo.getSignerBankName()).
                signerBankCardNum(kpBo.getSignerBankCardNum()).
                build();
        log.info("getLvyueXzsCustomerInfo response:{}", JSONObject.toJSONString(response));
        return response;
    }

    @Override
    public AccountSignTaskResp queryAccountSignTask(QuerySignTaskReq querySignTaskReq) throws TException, WmCustomerException {
        TSubmitCoopWithResignRequest request = new TSubmitCoopWithResignRequest();
        List<CoopSignBasicReqInfo> coopSignBasicReqInfoList = new ArrayList<>();
        CoopSignBasicReqInfo coopSignBasicReqInfo = new CoopSignBasicReqInfo();
        coopSignBasicReqInfo.setCoopId("fd8ef143-8d25-48f4-ae9c-0d8bcf9f32be");
        coopSignBasicReqInfo.setIsNewSign(true);
        coopSignBasicReqInfo.setCoopType(0);
        coopSignBasicReqInfoList.add(coopSignBasicReqInfo);
        request.setCoopSignBasicReqInfoList(coopSignBasicReqInfoList);
        request.setTCoopSource(TCoopSource.WAIMAI_XIANFU);
        commonCoopServiceAdapter.submitCoopWithResign(request,5893496);
        return null;
    }

    @Override
    public List<Long> queryWmPoiIdListByTaskId(Long taskId) throws TException, WmCustomerException {
        Preconditions.checkArgument(taskId != null && taskId > 0, "任务Id不合法");
        return wmEcontractSignBzService.queryWmPoiIdListByTaskId(taskId);
    }

    @Override
    public String getSmsShortLink(SmsShortLinkQueryParam smsShortLinkQueryParam) throws TException, WmCustomerException {
        Preconditions.checkArgument(smsShortLinkQueryParam != null, "参数异常");
        Preconditions.checkArgument(smsShortLinkQueryParam.getId() != null && smsShortLinkQueryParam.getId() > 0, "任务ID不合法");
        return wmEcontractSignBzService.getSmsShortLink(smsShortLinkQueryParam);
    }

    @Override
    public BooleanResult checkCanCancelSign(CancelSignParam cancelSignParam) {
        Preconditions.checkArgument(cancelSignParam != null, "参数异常");
        Preconditions.checkArgument(cancelSignParam.getId() != null && cancelSignParam.getId() > 0, "任务ID不合法");
        return wmEcontractSignBzService.checkCanCancelSign(cancelSignParam);
    }

    /**
     * 通过param判断是否是到餐的签约任务
     * @param param
     * @return
     */
    @Override
    public Boolean isDcSignTask(IsDcSignTaskQueryParam param) {
        log.info("isDcSignTask param:{}", JSON.toJSON(param));
        if (param == null || param.getId() == null || StringUtils.isEmpty(param.getIdType())) {
            return false;
        }

        return wmEcontractSignBzService.isDcSignTask(param);
    }

    @Override
    public Long queryDcMtCustomerId(QueryDcMtCustomerIdParam param) throws WmCustomerException {
        log.info("queryMtCustomerId param:{}",JSON.toJSON(param));
        if (param == null || param.getId() == null || StringUtils.isEmpty(param.getIdType())) {
            return null;
        }

        return wmEcontractSignBzService.queryDcMtCustomerId(param);
    }

    @Override
    public void savePdfParam(PdfParamSaveRequestDTO requestDTO) throws TException, WmCustomerException {
        Preconditions.checkArgument(requestDTO != null && requestDTO.getType() != 0 , "参数异常");
        log.info("WmEcontractSignThriftServiceImpl#savePdfParam requestDTO={}", JSON.toJSONString(requestDTO));
        wmEcontractPhfCompareService.savePdfParam(requestDTO);
    }

    @Override
    public BatchPdfUrlContentBo queryPdfUrlInfo(PdfUrlRequestDTO pdfUrlRequestDTO) throws TException, WmCustomerException {
        log.info("WmEcontractSignThriftServiceImpl#queryPdfUrlInfo pdfUrlRequestDTO:{}", JSONObject.toJSONString(pdfUrlRequestDTO));
        // 根据confirmID查询sign_batch记录
        BatchPdfUrlContentBo batchPdfUrlContentBo = wmEcontractSignBzService.queryPdfUrlInfo(pdfUrlRequestDTO);
        log.info("WmEcontractSignThriftServiceImpl#queryPdfUrlInfo batchPdfUrlContentBo:{}", JSONObject.toJSONString(batchPdfUrlContentBo));
        return batchPdfUrlContentBo;
    }

    @Override
    public List<String> queryRecordKeyBySignPackIdOrderByPriority(Long signPackId) throws TException, WmCustomerException {
        log.info("queryRecordKeyBySignPackId signPackId:{}", signPackId);
        return wmEcontractSignPackService.queryRecordKeyBySignPackIdOrderByPriority(signPackId);
    }
    
    @Override
    public C1ExpireAfterRenewalResultBo applyTaskForRenewalC1ExpireAfter(ManualTaskApplyBo manualTaskApplyBo) throws TException, WmCustomerException {
        log.info("WmEcontractSignThriftServiceImpl#applyTaskForRenewalC1ExpireAfter, manualTaskApplyBo: {}", JSON.toJSON(manualTaskApplyBo));
        C1ExpireAfterRenewalResultBo result = wmEcontractSignBzService.applyTaskForRenewalC1ExpireAfter(manualTaskApplyBo);
        log.info("WmEcontractSignThriftServiceImpl#applyTaskForRenewalC1ExpireAfter, result: {}", JSON.toJSON(result));
        return result;
    }
}


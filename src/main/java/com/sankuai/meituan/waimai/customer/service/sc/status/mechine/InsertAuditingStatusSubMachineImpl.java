package com.sankuai.meituan.waimai.customer.service.sc.status.mechine;

import com.sankuai.meituan.waimai.customer.domain.sc.WmCanteenDB;
import com.sankuai.meituan.waimai.customer.domain.sc.WmScCanteenAuditDO;
import com.sankuai.meituan.waimai.customer.service.sc.status.CanteenStatusSubMachine;
import com.sankuai.meituan.waimai.thrift.customer.constant.ValidateStatus;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.CanteenAuditStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.CanteenDetailStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.EffectiveStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.ValidEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.CanteenAuditDiffBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.DiffField;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 新增审核中状态流转
 * <AUTHOR>
 * @date 2020/11/24 16:37
 */
@Component("insertAuditingStatusSubMachine")
public class InsertAuditingStatusSubMachineImpl extends AbstractAuditStatusSubMachine implements CanteenStatusSubMachine {


    @Override
    public void changeStatus(WmCanteenDB canteenDB, WmScCanteenAuditDO wmScCanteenAuditDO) throws WmSchCantException {

        if(isToAudited(canteenDB)){
            toAudited(canteenDB, wmScCanteenAuditDO);
        }else {
            toReject(canteenDB, wmScCanteenAuditDO);
        }
    }

    private boolean isToAudited(WmCanteenDB canteenDB){
        return ((int)CanteenAuditStatusEnum.AUDITED.getType()) == canteenDB.getAuditStatus();
    }


    private void toAudited(WmCanteenDB canteenDB, WmScCanteenAuditDO wmScCanteenAuditDO){
        canteenDB.setAuditDetailStatus((int)CanteenDetailStatusEnum.AUDITED_STATUS.getType());
        canteenDB.setAuditStatus((int)CanteenAuditStatusEnum.AUDITED.getType());
        canteenDB.setEffective((int)EffectiveStatusEnum.EFFECTIVE.getType());

        wmScCanteenAuditDO.setAuditStatus((int)CanteenAuditStatusEnum.AUDITED.getType());
        wmScCanteenAuditDO.setValid((int)ValidEnum.INVALID.getType());
    }



    private void toReject(WmCanteenDB canteenDB, WmScCanteenAuditDO wmScCanteenAuditDO){

        canteenDB.setAuditDetailStatus((int)CanteenDetailStatusEnum.INSERT_AUDIT_REJECT_STATUS.getType());
        canteenDB.setAuditStatus((int)CanteenAuditStatusEnum.REJECT.getType());

        wmScCanteenAuditDO.setAuditStatus((int)CanteenAuditStatusEnum.REJECT.getType());
    }


    @Override
    public boolean isAuditing(WmCanteenDB canteenDB){
        return true;
    }

    @Override
    public boolean isInsertAuditing(WmCanteenDB canteenDB){
        return true;
    }

    @Override
    public List<DiffField> getCanteenAuditDiffList(WmCanteenDB canteenDB) {
        return new ArrayList<>();
    }
}

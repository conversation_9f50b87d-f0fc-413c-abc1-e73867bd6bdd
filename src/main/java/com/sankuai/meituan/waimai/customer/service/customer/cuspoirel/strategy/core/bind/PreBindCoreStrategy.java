package com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.strategy.core.bind;

import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.domain.cuspoirel.bind.CustomerPoiBindFlowContext;
import com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.atom.PoiBindCustomerAtomService;
import com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.strategy.core.IBindCoreStrategy;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Set;

/**
 * <AUTHOR>
 * @desc 直接绑定核心操作策略
 * @date 20240115
 */
@Slf4j
@Service
public class PreBindCoreStrategy implements IBindCoreStrategy {

    @Autowired
    private PoiBindCustomerAtomService poiBindCustomerAtomService;


    /**
     * 发起预绑定成功-状态更新为预绑定
     *
     * @param context
     * @throws WmCustomerException
     */
    @Override
    public void execute(CustomerPoiBindFlowContext context) throws WmCustomerException {

        WmCustomerDB wmCustomerDB = context.getWmCustomerDB();
        Set<Long> wmPoiIdSet = context.getWmPoiIdSet();
        poiBindCustomerAtomService.poiPreBindConfirmDoing(wmCustomerDB.getId(), wmPoiIdSet, context.getPoiAndTaskMaps());
    }

}

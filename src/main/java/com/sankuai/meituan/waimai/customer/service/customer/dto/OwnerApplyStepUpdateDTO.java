package com.sankuai.meituan.waimai.customer.service.customer.dto;

import com.sankuai.meituan.waimai.thrift.customer.constant.customer.CustomerOwnerApplyStepStatusEnum;
import lombok.Builder;
import lombok.Data;

/**
 * 客户责任人申请流程节点更新信息
 */
@Data
@Builder
public class OwnerApplyStepUpdateDTO {
   /**
    * 步骤时间
    */
   private Integer stepTime;

   /**
    * 步骤状态
    */
   private CustomerOwnerApplyStepStatusEnum status;
   /**
    * 审核提示信息
    */
   private String auditTips;
   /**
    * 是否当前步骤
    */
   private boolean onStep;
   /**
    * 审核备注
    */
   private String auditRemark;

   /**
    * 步骤名称
    */
   private String stepName;
}

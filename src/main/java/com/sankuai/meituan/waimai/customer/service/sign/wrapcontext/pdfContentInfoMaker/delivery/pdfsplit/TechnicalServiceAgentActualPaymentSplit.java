package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.delivery.pdfsplit;

import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.bo.sign.EcontractBatchMiddleBo;
import com.sankuai.meituan.waimai.customer.constant.sign.DeliveryPdfDataTypeEnum;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.PdfSpliter;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateEnum;
import com.sankuai.meituan.waimai.heron.poilogistics.thrift.constants.LogisticsFeeModeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryInfoBo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import static com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateConstant.TAB_DELIVERY;

@Slf4j
@Service
@PdfSpliter(deliveryPdfDataType = DeliveryPdfDataTypeEnum.TECHNICAL_SERVICE_FEE_AGENT_ACTUAL_PAYMENT)
public class TechnicalServiceAgentActualPaymentSplit implements DeliveryPdfSplit {
    @Override
    public void split(EcontractDeliveryInfoBo deliveryInfoBo, EcontractBatchMiddleBo middleContext) {
        Map<String, Collection<SignTemplateEnum>> tabPdfMap = middleContext.getTabPdfMap();
        Map<String, List<String>> pdfDataMap = middleContext.getPdfDataMap();
        LogisticsFeeModeEnum feeMode = LogisticsFeeModeEnum.codeOf(Integer.valueOf(deliveryInfoBo.getFeeMode()));

        if (feeMode == LogisticsFeeModeEnum.AGENT_ACTUAL_PAYMENT
                && CollectionUtils.isNotEmpty(tabPdfMap.get(TAB_DELIVERY))
                && tabPdfMap.get(TAB_DELIVERY).contains(SignTemplateEnum.TECHNICAL_SERVICE_AGENT_ACTUAL_PAYMENT)) {
            List<String> technicalDefault = pdfDataMap.get(DeliveryPdfDataTypeEnum.TECHNICAL_SERVICE_FEE_AGENT_ACTUAL_PAYMENT.getName());
            if (CollectionUtils.isEmpty(technicalDefault)) {
                technicalDefault = Lists.newArrayList();
                technicalDefault.add(deliveryInfoBo.getDeliveryTypeUUID());
            } else {
                technicalDefault.add(deliveryInfoBo.getDeliveryTypeUUID());
            }
            pdfDataMap.put(DeliveryPdfDataTypeEnum.TECHNICAL_SERVICE_FEE_AGENT_ACTUAL_PAYMENT.getName(), technicalDefault);
            log.info("ADD TO TECHNICAL_SERVICE_FEE_AGENT_ACTUAL_PAYMENT，UUID:{}, wmPoiId:{}，feeMode:{}",
                    deliveryInfoBo.getDeliveryTypeUUID(), deliveryInfoBo.getWmPoiId(), deliveryInfoBo.getFeeMode());
        }
    }
}

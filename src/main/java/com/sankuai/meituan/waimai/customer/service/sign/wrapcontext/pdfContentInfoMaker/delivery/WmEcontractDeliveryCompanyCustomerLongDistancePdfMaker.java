package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.delivery;

import java.util.List;
import java.util.Map;

import com.sankuai.meituan.waimai.customer.constant.sign.DeliveryPdfDataTypeEnum;
import com.sankuai.meituan.waimai.customer.service.sign.gray.WmEcontractSignGrayService;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractBatchTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.util.DateUtil;
import com.sankuai.meituan.waimai.customer.bo.sign.EcontractBatchMiddleBo;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateEnum;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateWrapper;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.AbstractWmEcontractPdfContentInfoBoMaker;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.IWmEcontractPdfContentInfoBoMaker;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.constants.PdfConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.util.MapUtil;

import lombok.extern.slf4j.Slf4j;

@SignTemplateWrapper(wrapperEnum = SignTemplateEnum.DELIVERY_COMPANY_CUSTOMER_LONG_DISTANCE)
@Slf4j
@Service
public class WmEcontractDeliveryCompanyCustomerLongDistancePdfMaker extends AbstractWmEcontractPdfContentInfoBoMaker
        implements IWmEcontractPdfContentInfoBoMaker {

    @Autowired
    private WmEcontractSignGrayService wmEcontractSignGrayService;

    @Override
    public PdfContentInfoBo makePdfContentInfoBo(EcontractBatchContextBo originContext,
            EcontractBatchMiddleBo middleContext) throws WmCustomerException, IllegalAccessException, TException {
        log.info("#WmEcontractDeliveryCompanyCustomerLongDistancePdfMaker");
        SignTemplateEnum signTemplateEnum = extractSignTemplateEnum();

        EcontractTaskBo taskBo;
        if (EcontractBatchTypeEnum.isNationalSubsidyDeliveryType(originContext.getBatchTypeEnum())) {
            taskBo = WmEcontractContextUtil.selectDeliveryEcontractTaskBo(originContext, middleContext.getSignDataFactor());
        } else {
            taskBo = WmEcontractContextUtil.selectByApplyType(originContext, EcontractTaskApplyTypeEnum.POIFEE);
        }
        EcontractDeliveryInfoBo deliveryInfoBo;
        if(wmEcontractSignGrayService.poifeeTempletTransferNewPlatform(originContext.getCustomerId())){
            List<EcontractDeliveryInfoBo> deliveryInfoList = extractDeliveryInfo(originContext, middleContext, DeliveryPdfDataTypeEnum.COMPANYCUSTOMER_LONGDISTANCE_DEFAULT.getName());
            deliveryInfoBo = deliveryInfoList.get(0);
        } else{
            deliveryInfoBo = JSON.parseObject(taskBo.getApplyContext(), EcontractDeliveryInfoBo.class);
        }

        if(deliveryInfoBo == null){
            log.warn("WmEcontractDeliveryCompanyCustomerLongDistancePdfMaker deliveryInfoBo为空，流程中止，customerId:{}", originContext.getCustomerId());
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "原始数据缺失，pdf封装失败");
        }

        EcontractDeliveryCompanyCustomerLongDistanceInfoBo econtractDeliveryCompanyCustomerLongDistanceInfoBo = deliveryInfoBo.getEcontractDeliveryCompanyCustomerLongDistanceInfoBo();

        // PDF数据无需配送范围信息
        econtractDeliveryCompanyCustomerLongDistanceInfoBo.setDeliveryArea(null);
        Map<String, String> map = MapUtil.Object2Map(econtractDeliveryCompanyCustomerLongDistanceInfoBo);

        String signTime = DateUtil.secondsToString(DateUtil.unixTime());
        map.put("partA", StringUtils.defaultIfEmpty(originContext.getCustomerInfoBo().getCustomerName(), StringUtils.EMPTY));
        map.put("partAStampName", StringUtils.defaultIfEmpty(WmEcontractContextUtil.getCustomerName(originContext), StringUtils.EMPTY));
        map.put("partAEstamp", PdfConstant.POI_SIGNKEY);
        map.put("partASignTime", StringUtils.defaultIfEmpty(signTime, StringUtils.EMPTY));

        PdfContentInfoBo pdfInfoBo = new PdfContentInfoBo();
        pdfInfoBo.setPdfTemplateName(signTemplateEnum.getName());
        pdfInfoBo.setPdfMetaContent(map);
        log.info("#WmEcontractDeliveryCompanyCustomerLongDistancePdfMaker, pdfInfoBo={}", JSONObject.toJSONString(pdfInfoBo));
        return pdfInfoBo;
    }

}

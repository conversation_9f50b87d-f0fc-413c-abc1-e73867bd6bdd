package com.sankuai.meituan.waimai.customer.service.kp.statemachine.context;

import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 20230821
 * @desc 签约人KP状态模型上下文
 */
@Data
@Builder
public class KpLegalStatusMachineContext {

    /**
     * 操作类型：新增、修改、删除
     */
    private Integer opType;
    /**
     * 客户ID
     */
    private Integer customerId;

    /**
     * 客户信息
     */
    private WmCustomerDB wmCustomer;

    /**
     * 当前操作的KP：新增或修改的对象
     */
    private WmCustomerKp wmCustomerKp;

    /**
     * 原KP信息
     */
    private WmCustomerKp oldCustomerKp;

    /**
     * 是否生效过
     */
    private Boolean existEffectiveFlag;

    /**
     * 操作人
     */
    private Integer opUid;

    /**
     * 操作人名称
     */
    private String opUName;

}

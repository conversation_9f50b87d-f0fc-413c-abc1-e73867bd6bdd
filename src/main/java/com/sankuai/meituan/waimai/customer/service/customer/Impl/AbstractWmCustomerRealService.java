package com.sankuai.meituan.waimai.customer.service.customer.Impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.cat.util.MetricHelper;
import com.google.common.base.Function;
import com.google.common.base.Joiner;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.google.common.util.concurrent.Uninterruptibles;
import com.meituan.mafka.client.bean.MafkaProducer;
import com.meituan.mtrace.Tracer;
import com.meituan.mtrace.thread.TraceRunnable;
import com.meituan.mtrace.thread.pool.ExecutorServiceTraceWrapper;
import com.meituan.mtrace.thread.pool.TraceExecutors;
import com.meituan.pay.mwallet.thrift.req.SettleInfoBatchQueryReq;
import com.meituan.pay.mwallet.thrift.resp.data.SettleInfoData;
import com.meituan.service.mobile.mtthrift.util.ClientInfoUtil;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.channel.beacon.lib.enums.CustomerModuleStateEnum;
import com.sankuai.meituan.waimai.customer.adapter.*;
import com.sankuai.meituan.waimai.customer.adapter.uac.UacAuthRemoteServiceAdapter;
import com.sankuai.meituan.waimai.customer.bo.WmCustomerRealTypeResult;
import com.sankuai.meituan.waimai.customer.bo.WmPoiDomain;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.constant.KmsKeyNameEnum;
import com.sankuai.meituan.waimai.customer.constant.WmCustomerConstant;
import com.sankuai.meituan.waimai.customer.constant.customer.CertificateDaXiangTypeEnum;
import com.sankuai.meituan.waimai.customer.constant.customer.CustomerMetricEnum;
import com.sankuai.meituan.waimai.customer.constant.customer.MetricConstant;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmContractService;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmCustomerOplogService;
import com.sankuai.meituan.waimai.customer.dao.*;
import com.sankuai.meituan.waimai.customer.ddd.adapter.MtCustomerThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.ddd.adapter.MtCustomerThriftServiceAdapterImpl;
import com.sankuai.meituan.waimai.customer.ddd.domain.customer.aggre.WmCustomerPoiAggre;
import com.sankuai.meituan.waimai.customer.domain.*;
import com.sankuai.meituan.waimai.customer.mq.service.MafkaMessageSendManager;
import com.sankuai.meituan.waimai.customer.service.common.WmEmployeeService;
import com.sankuai.meituan.waimai.customer.service.customer.*;
import com.sankuai.meituan.waimai.customer.service.customer.check.WmCustomerAuthService;
import com.sankuai.meituan.waimai.customer.service.customer.check.customer.SignModeSwitchCheckerAsy;
import com.sankuai.meituan.waimai.customer.service.gray.WmCustomerGrayService;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpRealAuthService;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpService;
import com.sankuai.meituan.waimai.customer.service.sc.WmContractorService;
import com.sankuai.meituan.waimai.customer.service.version.VersionCheckUtil;
import com.sankuai.meituan.waimai.customer.settle.domain.WmPoiSettleAuditedDB;
import com.sankuai.meituan.waimai.customer.settle.service.WmSettleService;
import com.sankuai.meituan.waimai.customer.util.PageUtil;
import com.sankuai.meituan.waimai.customer.util.business.WmCustomerUtil;
import com.sankuai.meituan.waimai.customer.util.diff.BeanDiffUtil;
import com.sankuai.meituan.waimai.customer.util.diff.WmLogDiffConstant;
import com.sankuai.meituan.waimai.customer.util.trans.WmCustomerTransUtil;
import com.sankuai.meituan.waimai.infra.constants.WmOrgConstant;
import com.sankuai.meituan.waimai.infra.constants.WmUniAorType;
import com.sankuai.meituan.waimai.infra.constants.WmVirtualOrgRecursiveTypeEnum;
import com.sankuai.meituan.waimai.infra.domain.*;
import com.sankuai.meituan.waimai.infra.domain.WmOpenCity;
import com.sankuai.meituan.waimai.infra.service.WmEmployService;
import com.sankuai.meituan.waimai.label.constants.LabelSubjectTypeEnum;
import com.sankuai.meituan.waimai.label.thrift.exception.WmLabelException;
import com.sankuai.meituan.waimai.poiaudit.thrift.constants.WmPoiProduceSection;
import com.sankuai.meituan.waimai.poiaudit.thrift.constants.WmPoiProduceStatus;
import com.sankuai.meituan.waimai.poiaudit.thrift.constants.WmPoiValidEnum;
import com.sankuai.meituan.waimai.poiaudit.thrift.domain.WmPoiProduceOplog;
import com.sankuai.meituan.waimai.poiaudit.thrift.service.WmPoiProduceOplogThriftService;
import com.sankuai.meituan.waimai.poibizflow.thrift.exception.WmPoiBizException;
import com.sankuai.meituan.waimai.poibizflow.thrift.switcher.WmPoiSwitchThriftService;
import com.sankuai.meituan.waimai.qualification.WmQualificationThriftService;
import com.sankuai.meituan.waimai.thrift.bo.WmCustomerDiffCellBo;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.constants.WmPoiFieldQueryConstant;
import com.sankuai.meituan.waimai.thrift.constatnt.WmAuditSubmitUidConstant;
import com.sankuai.meituan.waimai.thrift.constatnt.WmAuditTaskBizTypeConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.*;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.PageData;
import com.sankuai.meituan.waimai.thrift.customer.domain.WmCustomerOplogBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.BooleanResult;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.mq.CustomerMQBody;
import com.sankuai.meituan.waimai.thrift.customer.domain.mq.CustomerMQEventEnum;
import com.sankuai.meituan.waimai.thrift.domain.*;
import com.sankuai.meituan.waimai.thrift.exception.MtCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import com.sankuai.meituan.waimai.thrift.service.WmAuditApiService;
import com.sankuai.meituan.waimai.thrift.service.WmPoiFlowlineLabelThriftService;
import com.sankuai.meituan.waimai.thrift.service.WmPoiQueryThriftService;
import com.sankuai.meituan.waimai.thrift.util.DiffUtil;
import com.sankuai.meituan.waimai.thrift.vo.WmAuditQualificationObj;
import com.sankuai.meituan.waimai.util.DateUtil;
import com.sankuai.meituan.waimai.util.DaxiangUtil;
import com.sankuai.meituan.waimai.util.DaxiangUtilV2;
import com.sankuai.nibcus.inf.customer.client.enums.BusinessLineEnum;
import com.sankuai.nibcus.inf.customer.client.enums.RegionEnum;
import com.sankuai.nibcus.inf.customer.client.request.QualificationNumTypeGetCustomerIdRequest;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import org.apache.commons.collections4.MapUtils;

import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

import static com.sankuai.meituan.waimai.thrift.constants.WmPoiFieldQueryConstant.*;


public abstract class AbstractWmCustomerRealService implements IWmCustomerRealService {
    private static Logger logger = LoggerFactory.getLogger(AbstractWmCustomerRealService.class);

    @Autowired
    protected WmCustomerAuditDBMapper wmCustomerAuditDBMapper;

    @Autowired
    private WmCustomerOplogService wmCustomerOplogService;

    @Autowired
    private WmCustomerCommonQuaDBMapper wmCustomerCommonQuaDBMapper;

    @Autowired
    private WmAuditApiService.Iface wmAuditApiService;

    @Autowired
    protected WmCustomerPoiService wmCustomerPoiService;

    @Autowired
    private MafkaMessageSendManager mafkaMessageSendManager;

    @Resource(name = "customerAuditResultNoticeProducer")
    private MafkaProducer customerAuditResultNoticeProducer;

    @Autowired
    private WmPoiProduceOplogThriftService.Iface wmPoiProduceOplogThriftService;

    @Autowired
    private WmPoiQueryThriftService.Iface wmPoiQueryThriftService;

    @Autowired
    private WmCustomerESService wmCustomerESService;

    @Autowired
    WmEmployClient wmEmployClient;

    @Autowired
    protected WmCustomerValidateService wmCustomerValidateService;

    @Autowired
    private WmEmployService.Iface wmEmployService;

    @Autowired
    private WmCustomerRealTypeService customerRealTypeService;


    @Autowired
    private WmPoiSwitchThriftService wmPoiSwitchThriftService;

    @Autowired
    protected MtCustomerThriftServiceAdapter mtCustomerThriftServiceAdapter;

    @Autowired
    private WmSettleService wmSettleService;


    @Autowired
    protected WmCustomerPlatformDataParseService wmCustomerPlatformDataParseService;

    @Autowired
    private WmCustomerDBMapper wmCustomerDBMapper;

    @Autowired
    WmCustomerPoiDBMapper wmCustomerPoiDBMapper;

    @Autowired
    WmPoiFlowlineLabelThriftService.Iface wmPoiFlowlineLabelThriftService;

    @Autowired
    WmCustomerRelMapper wmCustomerRelMapper;

    @Autowired
    private WmCustomerPoiRelExtensionMapper wmCustomerPoiRelExtensionMapper;

    @Autowired
    private WmContractService wmContractService;

    @Autowired
    private CityCommonServiceAdapter cityCommonServiceAdapter;

    @Autowired
    private WmEmployeeService wmEmployeeService;
    @Autowired
    private WmPoiClient wmPoiClient;

    @Autowired
    private WmCustomerBrandService wmCustomerBrandService;

    @Autowired
    private WmCustomerKpService wmCustomerKpService;

    @Autowired
    private WmQualificationThriftService.Iface wmQuaThriftService;

    @Autowired
    private BusinessCertificationAdapter businessCertificationAdapter;

    @Autowired
    private WmCustomerCertificateService wmCustomerCertificateService;

    @Autowired
    WmCustomerGrayService wmCustomerGrayService;

    @Autowired
    WmPoiFlowlineLabelThriftServiceAdapter wmPoiFlowlineLabelThriftServiceAdapter;

    @Autowired
    private WmCustomerAuthService wmCustomerAuthService;

    @Autowired
    private WmVirtualOrgServiceAdaptor wmVirtualOrgServiceAdaptor;

    @Autowired
    private WmCustomerSensitiveWordsService wmCustomerSensitiveWordsService;

    @Autowired
    private WmCustomerService wmCustomerService;

    @Autowired
    private WmCustomerKpRealAuthService wmCustomerKpRealAuthService;

    @Autowired
    private MtCustomerThriftServiceAdapterImpl mtCustomerThriftServiceAdapterImpl;

    @Autowired
    private WmAorServiceAdapter wmAorServiceAdapter;

    @Autowired
    private WmCustomerMultiplexService wmCustomerMultiplexService;

    @Autowired
    private WmContractorService wmContractorService;

    @Autowired
    private WmCustomerLabelService wmCustomerLabelService;

    public static final String CUSTOMER_LOG_COMMON_QUA_ADD = "【资质复用证】新增:";
    public static final String CUSTOMER_LOG_COMMON_QUA_OTHER_ADD = "【资质其他附件】新增:";
    
    public static final String CUSTOMER_LOG_CUSTOMER_SOURCE_REBUILD = "系统来源：电销-重新建店\n";

    public static final String CUSTOMER_LOG_FORCE_UPDATE = "生效方式：强制生效\n";

    public static final String CUSTOMER_LOG_NOT_FORCE_UPDATE = "生效方式：非强制生效\n";

    @Autowired
    private WmCustomerMSCLabelService wmCustomerMSCLabelService;

    @Autowired
    private MerchantSettleQueryProxyServiceAdaptor merchantSettleQueryProxyServiceAdaptor;

    @Autowired
    private SpecialApprovalService specialApprovalService;

    @Autowired
    private UpmAuthCheckService upmAuthCheckService;

    @Autowired
    private UacAuthRemoteServiceAdapter uacAuthRemoteServiceAdapter;

    @Autowired
    private DcCustomerService dcCustomerService;

    public static final int BATCH_SIZE = 50;

    private int LABEL_RETRY_CNT = 5;

    public static final int ES_PAGE_SIZE = 1000;

    public static final int ES_FOR_MAX = 10;

    /**
     * 客户特批或审核流程中的状态：审核中、审核驳回、特批中，特批驳回
     */
    private static final List<Integer> customerSpecialAndAuditStatusList = Lists.newArrayList(CustomerAuditStatus.CUSTOMER_AUDIT_AUDITING.getCode(),
            CustomerAuditStatus.CUSTOMER_AUDIT_REJECT.getCode(), CustomerAuditStatus.CUSTOMER_SPECIAL_AUDIT_REJECT.getCode(), CustomerAuditStatus.CUSTOMER_SPECIAL_AUDITING.getCode());

    private static ExecutorService executorService = TraceExecutors.getTraceExecutorService(
            new ThreadPoolExecutor(10, 100, 60L, TimeUnit.SECONDS, new ArrayBlockingQueue<Runnable>(100), Executors.defaultThreadFactory()));

    private static String URL_HTTP_CONSTANT = "http";

    private static final int CUSTOMER_ID_SIZE = 1000_0000;

    private final static ExecutorService handleService = new ExecutorServiceTraceWrapper(new ThreadPoolExecutor(10, 200,
            0L, TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue(1024), new ThreadPoolExecutor.AbortPolicy()));

    private static ExecutorService ownerExecutorService = TraceExecutors.getTraceExecutorService(
            new ThreadPoolExecutor(10, 30, 30L, TimeUnit.MINUTES, new ArrayBlockingQueue<Runnable>(200),
                    new RejectedExecutionHandler() {
                        @Override
                        public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
                            if (!ownerExecutorService.isShutdown()) {
                                try {
                                    executor.getQueue().put(r);
                                } catch (InterruptedException e) {
                                    throw new RejectedExecutionException("Reject from " + executor);
                                }
                            } else {
                                throw new RejectedExecutionException("Executor " + executor + " have shutdown.");
                            }
                        }
                    }));

    private static ThreadFactory threadFactory = new ThreadFactoryBuilder().setNameFormat("customer_sign_mode_pool_%d").build();
    private static ExecutorService customerExecutorService = TraceExecutors.getTraceExecutorService(
            new ThreadPoolExecutor(10, 30, 30L, TimeUnit.MINUTES, new ArrayBlockingQueue<Runnable>(1000), threadFactory, new RejectedExecutionHandler() {
                @Override
                public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
                    if (!customerExecutorService.isShutdown()) {
                        try {
                            executor.getQueue().put(r);
                        } catch (InterruptedException e) {
                            throw new RejectedExecutionException("Reject from " + executor.toString());
                        }
                    } else {
                        throw new RejectedExecutionException("Executor " + executor.toString() + " have shutdown.");
                    }
                }
            }));

    /**
     * 保存或修改客户
     *
     * @param wmCustomerBasicBo
     * @param force
     * @param opUid
     * @param opName
     * @throws WmCustomerException
     * @throws TException
     */
    @Override
    public ValidateResultBo saveOrUpdateCustomer(WmCustomerBasicBo wmCustomerBasicBo, Boolean force, Integer opUid, String opName, int channel) throws WmCustomerException, TException {
        logger.info("保存客户,参数wmCustomerBasicBo={},force={},opUid={},opName={},channel={}", JSONObject.toJSONString(wmCustomerBasicBo), force, opUid, opName, channel);
        ValidateResultBo validateResultBo = wmCustomerValidateService.validate(wmCustomerBasicBo, force, false, opUid);
        //如果验真不通过返回
        if (validateResultBo.getCode() != CustomerConstants.RESULT_CODE_PASS) {
            logger.info("客户校验没通过,validateResultBo={}", JSONObject.toJSONString(validateResultBo));
            return validateResultBo;
        }

        logger.info("wmCustomerBasicBo={}", JSONObject.toJSONString(wmCustomerBasicBo));
        //id=0为新增
        WmCustomerDB wmCustomerDB = null;
        Integer newCustomerId = wmCustomerBasicBo.getId();
        boolean isNew = false;
        boolean isAsyChangeSignMode = false;
        Integer signMode = wmCustomerBasicBo.getSignMode();
        // 处理是否需要和当前外卖系统内的到餐客户复用
        WmCustomerDB currDcCustomer = getDcCustomerByQua(wmCustomerBasicBo);
        if (wmCustomerBasicBo.getId() == 0) {

            wmCustomerCertificateService.setCustomerCertificate(wmCustomerBasicBo, wmCustomerDB);
            wmCustomerMultiplexService.dealCustomerMultiplexFieldWhenCustomerNew(wmCustomerBasicBo);
            newCustomerId = saveCustomer(wmCustomerBasicBo, validateResultBo, opUid, opName, channel,currDcCustomer);
            wmCustomerBasicBo.setId(newCustomerId);
            addNewSettleCustomerTag(wmCustomerBasicBo, opUid, opName);
            isNew = true;
        } else {
            WmCustomerDB afterCheckWmCustomerDB = checkUpdateCustomerNumberForDC(wmCustomerBasicBo,currDcCustomer);
            if (afterCheckWmCustomerDB != null){
                ValidateResultBo resultBo = new ValidateResultBo();
                resultBo.setCode(CustomerConstants.RESULT_CODE_DUPLICATE_ERROR);
                resultBo.setMsg("该客户资质与到餐客户ID：" + afterCheckWmCustomerDB.getId() + "一致，无法进行修改，请使用该资质新建客户");
                return resultBo;
            }
            // 后续此校验逻辑删除
            if (currDcCustomer != null && !MccCustomerConfig.getCheckUpdateCustomerNumberForDCSwitch()){
                ValidateResultBo resultBo = new ValidateResultBo();
                resultBo.setCode(CustomerConstants.RESULT_CODE_DUPLICATE_ERROR);
                resultBo.setMsg("该客户资质与到餐客户ID：" + currDcCustomer.getId() + "一致，无法进行修改，请使用该资质新建客户");
                return resultBo;
            }
            // 记录更新前的客户信息
            wmCustomerDB = wmCustomerPlatformDataParseService.selectCustomerFromPlatformById(wmCustomerBasicBo.getId());
            wmCustomerCertificateService.setCustomerCertificate(wmCustomerBasicBo, wmCustomerDB);
            wmCustomerMultiplexService.dealCustomerMultiplexFieldWhenCustomerUpdate(wmCustomerBasicBo, wmCustomerDB);

            if (wmCustomerBasicBo.getSignMode() == null) {
                wmCustomerBasicBo.setSignMode(wmCustomerDB.getSignMode());
            }
            //签约模式变更，记录原值
            if (!wmCustomerBasicBo.getSignMode().equals(wmCustomerDB.getSignMode()) && channel == CustomerSource.WAIMAI_BD.getCode()) {
                logger.info("签约模式变更，signMode:{},wmCustomerBasicBo={}", wmCustomerBasicBo.getSignMode(), JSONObject.toJSONString(wmCustomerBasicBo));
                wmCustomerBasicBo.setSignMode(wmCustomerDB.getSignMode());
                isAsyChangeSignMode = true;
            }

            boolean isUpdateNotAudit = updateCustomer(wmCustomerBasicBo, validateResultBo, opUid, opName, channel);
            // 直接修改生效
            if (isUpdateNotAudit) {
                // 客户信息法人或个人变更，则签约人KP信息、法人信息自动删除，需要补录
                WmCustomerDB after = WmCustomerTransUtil.customerBoToDB(wmCustomerBasicBo);
                if (wmCustomerBasicBo.getOwnerUid() <= 0 && wmCustomerDB.getOwnerUid() != null && wmCustomerDB.getOwnerUid() > 0) {
                    after.setOwnerUid(wmCustomerDB.getOwnerUid());
                }
                wmCustomerKpService.customerEffectForKpOperate(wmCustomerDB.getCustomerName(), wmCustomerDB.getLegalPerson(), after);
                //客户类型变更+修改后为美食城客户类型校验档口数
                if (wmCustomerDB.getCustomerRealType() != after.getCustomerRealType()
                        && after.getCustomerRealType() == CustomerRealTypeEnum.MEISHICHENG.getValue()) {
                    checkMscPoiCntRuleAndSendMsg(after, opUid);
                }
                //删除客户场景标签
                delCustomerSceneTag(wmCustomerDB, after);
            }

            //客户类型变更+修改前为美食城客户类型，则处理资质共用标
            if (wmCustomerDB.getCustomerRealType() != wmCustomerBasicBo.getCustomerRealType()
                    && wmCustomerDB.getCustomerRealType() == CustomerRealTypeEnum.MEISHICHENG.getValue()) {
                deleteQuaComLabelByCustomerId(wmCustomerDB);
            }
        }

        //复用客户签约人处理
        wmCustomerMultiplexService.synCustomerMultiplexSigner(wmCustomerBasicBo, isNew, wmCustomerDB, opUid, opName);
        // 异步发送绑定/解绑消息
        if (validateResultBo.getCode() == CustomerConstants.RESULT_CODE_PASS) {
            WmEmploy operator = wmEmployeeService.getWmEmployById(opUid);
            sendBindUnBindMessageAndInsertOpLogAsyn(wmCustomerBasicBo, wmCustomerDB, operator, newCustomerId);
        }
        //客户状态后置处理
        doPostByCustomerStatus(wmCustomerBasicBo, wmCustomerDB, newCustomerId, opUid, opName);
        //签约模式变更，走异步
        if (isAsyChangeSignMode) {
            logger.info("签约模式变更，走异步流程 signMode:{},wmCustomerBasicBo={}", signMode, JSONObject.toJSONString(wmCustomerBasicBo));
            changeCustomerSignModeAsy(signMode, wmCustomerDB, opUid, opName, channel);
        }
        return validateResultBo;
    }

    private WmCustomerDB checkUpdateCustomerNumberForDC(WmCustomerBasicBo wmCustomerBasicBo,WmCustomerDB currentWmCustomerDb) throws WmCustomerException, TException {
        if (!MccCustomerConfig.getCheckUpdateCustomerNumberForDCSwitch()){
            return null;
        }
        WmCustomerDB wmCustomerDB = wmCustomerPlatformDataParseService.selectCustomerFromPlatformById(wmCustomerBasicBo.getId());
        if (wmCustomerDB.getCustomerNumber().equals(wmCustomerBasicBo.getCustomerNumber())){
            return null;
        }
        return currentWmCustomerDb;
    }

    public WmCustomerDB getDcCustomerByQua(WmCustomerBasicBo wmCustomerBasicBo) throws TException, WmCustomerException {
        CustomerRealTypeEnum customerRealTypeEnum = CustomerRealTypeEnum.getByValue(wmCustomerBasicBo.getCustomerRealType());
        boolean isWaiMaiBizOrgCode = customerRealTypeEnum != null && customerRealTypeEnum.getBizOrgCode() == CustomerBizOrgEnum.WAI_MAI.getCode();
        if (MccCustomerConfig.getWdGray() && isWaiMaiBizOrgCode){
            return queryDCCustomerByCustomerNumber(wmCustomerBasicBo.getCustomerNumber());
        }
        return null;
    }

    /**
     * 删除客户场景标签
     *
     * @param before
     * @param after
     */
    private void delCustomerSceneTag(WmCustomerDB before, WmCustomerDB after) {
        //修改前客户类型非外卖单店或非个人资质，则直接过滤
        if (before.getCustomerRealType() != CustomerRealTypeEnum.DANDIAN.getValue()
                || before.getCustomerType() != CustomerType.CUSTOMER_TYPE_IDCARD.getCode()) {
            return;
        }

        //原客户信息有场景字段 && （修改后的客户类型非外卖单店 或 修改后的资质类型非个人资质）
        if ((before.getCustomerRealType() != after.getCustomerRealType()
                || after.getCustomerType() != CustomerType.CUSTOMER_TYPE_IDCARD.getCode())) {
            //需要删除场景标签
            wmCustomerLabelService.deleteWmSingleCustomerSceneTag(before.getMtCustomerId(), 0, "系统");
            logger.info("delCustomerSceneTag,客户修改客户类型变更或资质类型变更，删除场景标签完成,customerId={},mtCustomerId={}", before.getId(), before.getMtCustomerId());
        }
    }

    /**
     * 异步修改签约模式
     * @param signMode
     * @param wmCustomerDB
     * @param opUid
     * @param opName
     * @param channel
     */
    private void changeCustomerSignModeAsy(Integer signMode, WmCustomerDB wmCustomerDB, Integer opUid, String opName, int channel) {
        CustomerSource customerSource = CustomerSource.of(channel);
        customerExecutorService.execute(new TraceRunnable(new Runnable() {
            @Override
            public void run() {
                String status = WmCustomerConstant.SUCCESS;
                String reason = "";
                try {
                    logger.info("changeCustomerSignModeAsy signMode={},wmCustomerDB={},opUid={},opName={},channel={}", signMode, JSONObject.toJSONString(wmCustomerDB), opUid, opName, channel);
                    WmCustomerAuditDB wmCustomerAuditDB = wmCustomerAuditDBMapper.selectCustomerAuditByCustomerId(wmCustomerDB.getId());
                    String errMsg = new SignModeSwitchCheckerAsy(signMode, wmCustomerDB, wmCustomerAuditDB).check();
                    String customerOwnerMis = wmEmployeeService.getMisId(opUid);
                    if (StringUtils.isNotBlank(errMsg)) {
                        logger.info("changeCustomerSignModeAsy check signMode={},wmCustomerDB={},errMsg={}", signMode, JSONObject.toJSONString(wmCustomerDB), JSONObject.toJSONString(errMsg));
                        if (StringUtils.isNotBlank(customerOwnerMis)) {
                            DaxiangUtilV2.push(errMsg, customerOwnerMis);
                        }
                        status = WmCustomerConstant.BUSINESS_EXCEPTION;
                        reason = errMsg;
                    } else {
                        logger.info("changeCustomerSignModeAsy check succ signMode={},wmCustomerDB={}", signMode, JSONObject.toJSONString(wmCustomerDB));
                        if (StringUtils.isNotBlank(customerOwnerMis)) {
                            DaxiangUtilV2.push(String.format("「客户id%s%s」系统已完成切换签约形式，请知悉", wmCustomerDB.getMtCustomerId(), wmCustomerDB.getCustomerName()), customerOwnerMis);
                        }
                        // 更新库
                        wmCustomerPlatformDataParseService.updateSignMode(wmCustomerDB.getId(), signMode);
                        CustomerSignMode oldSignMode = CustomerSignMode.getByCode(wmCustomerDB.getSignMode());
                        CustomerSignMode newSignMode = CustomerSignMode.getByCode(signMode);
                        String log = String.format("[字段变更] 签约模式: \"%s\" => \"%s\"", oldSignMode.getDesc(), newSignMode.getDesc());
                        // 记录操作日志
                        insertCustomerOpLog(wmCustomerDB.getId(), opUid, opName, WmCustomerOplogBo.OpType.UPDATE, log);
                    }
                } catch (WmCustomerException e) {
                    logger.error("异步更新客户签约模式失败 signMode={},wmCustomerDB={},opUid={},opName={}", signMode, JSONObject.toJSONString(wmCustomerDB), opUid, opName, e);
                    status = WmCustomerConstant.BUSINESS_EXCEPTION;
                    reason = e.getMsg();
                } catch (Exception e) {
                    status = WmCustomerConstant.SYSTEM_EXCEPTION;
                    reason = WmCustomerConstant.SYSTEM_EXCEPTION_MSG;
                } finally {
                    Cat.logEvent(CustomerMetricEnum.CUSTOMER_SIGN_MODE_CHANGE.getName(), customerSource.name(), status, reason);
                    MetricHelper.build().name(CustomerMetricEnum.CUSTOMER_SIGN_MODE_CHANGE.getName()).tag(CustomerMetricEnum.CUSTOMER_SIGN_MODE_CHANGE.getTag(), customerSource.name())
                            .tag(CustomerMetricEnum.CUSTOMER_SIGN_MODE_CHANGE.getStatus(), status).count();
                }
            }
        }));
    }


    private void addNewSettleCustomerTag(WmCustomerBasicBo wmCustomerBasicBo, Integer opUid, String opName) {
        logger.info("addNewSettleCustomerTag wmCustomerBasicBo={},opUid={},opName={}", JSONObject.toJSONString(wmCustomerBasicBo), opUid, opName);
        if (wmCustomerBasicBo.getIsLeaf() == CustomerConstants.CUSTOMER_IS_LEAF_NO) {
            return;
        }

        if (wmCustomerBasicBo.getCustomerRealType() == CustomerRealTypeEnum.CONTRACTOR.getValue() ||
                wmCustomerBasicBo.getCustomerRealType() == CustomerRealTypeEnum.AGGREGATE_DISTRIBUTOR.getValue()) {
            return;
        }

        boolean isGray = wmCustomerGrayService.isGrayForGoddess(wmCustomerBasicBo.getOwnerUid());
        if (!isGray) {
            return;
        }

        long lableId = MccCustomerConfig.getNewSettleForCustomerTag();
        long mtCustomerId = Long.valueOf(wmCustomerBasicBo.getMtCustomerId());
        wmPoiFlowlineLabelThriftServiceAdapter.batchAddLabelRel(lableId, Lists.newArrayList(mtCustomerId), opUid, opName, LabelSubjectTypeEnum.CUSTOMER.getCode());
    }


    /**
     * 客户后置状态处理（暂时支持电子营业执照后置处理逻辑）
     *
     * @param wmCustomerBasicBo 修改的客户对象
     * @param oldWmCustomerDB   修改之前的客户对象
     * @param customerId        客户ID
     * @param opUid             操作人ID
     * @param opName            操作人名字
     */
    @Override
    public void doPostByCustomerStatus(WmCustomerBasicBo wmCustomerBasicBo, WmCustomerDB oldWmCustomerDB, Integer customerId, Integer opUid, String opName) {
        try {
            if (wmCustomerBasicBo == null || customerId == null) {
                return;
            }
            WmCustomerDB wmCustomerDB = wmCustomerService.selectCustomerLocalByIdRT(customerId);
            if (wmCustomerDB == null) {
                return;
            }
            //非电子营业执照不处理
            if (wmCustomerBasicBo.getCustomerType() != CustomerType.CUSTOMER_TYPE_BUSINESS.getCode()
                    || wmCustomerDB.getCertificateType() != CertificateTypeEnum.ELECTRONIC.getType()) {
                return;
            }
            /**
             * 是否插入上单状态机和发送客户状态MQ
             * 1.新建电子营业执照
             * 2.纸质营业执照改为电子营业执照
             */
            boolean flag = false;
            if (oldWmCustomerDB == null) {
                // 新建电子执照
                flag = true;
            } else {
                if (wmCustomerDB.getCertificateType() != null
                        && !wmCustomerDB.getCertificateType().equals(oldWmCustomerDB.getCertificateType())) {
                    //纸制修改为电子
                    flag = true;
                }
            }

            //美食城客户类型，判断客户当前是否在审核中，审核中客户生效变更
            if (wmCustomerBasicBo.getCustomerRealType() == CustomerRealTypeEnum.MEISHICHENG.getValue() &&
                    wmCustomerDB.getAuditStatus() == CustomerAuditStatus.CUSTOMER_AUDIT_AUDITING.getCode()) {
                flag = false;
            }

            if (flag) {
                //插入上单状态机通知
                wmCustomerPoiService.insertPoiStateCenterByCustomerId(wmCustomerDB.getId(), CustomerModuleStateEnum.PASS, opUid, opName);
                //发送客户状态MQ
                sendCustomerStatusNoticeMQ(wmCustomerDB.getId(), CustomerMQEventEnum.CUSTOMER_EFFECTIVE, null);
            }
        } catch (Exception e) {
            logger.error("doPostByCustomerStatus::customerId = {}, opUid = {}, opName = {}", customerId, opUid, opName);
        }
    }

    /**
     * 给客户责任人发送绑定/解绑消息
     *
     * @param wmCustomerBasicBo
     * @param oldWmCustomerDb
     * @return
     */
    public void sendBindUnBindMessageAndInsertOpLogAsyn(WmCustomerBasicBo wmCustomerBasicBo, WmCustomerDB oldWmCustomerDb, WmEmploy operator, Integer newCustomerId) {
        executorService.execute(new TraceRunnable(() -> {
            logger.info("sendBindUnBindMessageAsyn(), wmCustomerBasicBo={},  oldWmCustomerDb={},operator:{}, newCustomerId:{}", JSON.toJSONString(wmCustomerBasicBo), JSON.toJSONString(oldWmCustomerDb), JSON.toJSONString(operator), newCustomerId);

            try {
                sendBindUnBindAndInsertOpLogMessage(wmCustomerBasicBo, oldWmCustomerDb, operator, newCustomerId);
            } catch (Exception e) {
                logger.error("给客户责任人发送绑定/解绑消息异常, wmCustomerId:{}", newCustomerId);
            }
        }));
    }

    /**
     * 给客户责任人发送绑定/解绑消息
     *
     * @param wmCustomerBasicBo
     * @param oldWmCustomerDB   更新前的客户信息
     * @param newCustomerId     更新后的客户id
     * @return
     */
    private void sendBindUnBindAndInsertOpLogMessage(WmCustomerBasicBo wmCustomerBasicBo, WmCustomerDB oldWmCustomerDB, WmEmploy operator, Integer newCustomerId) throws WmCustomerException {
        Integer newSuperWmCustomerId = Long.valueOf(wmCustomerBasicBo.getSuperCustomerId()).intValue();
        Integer oldSuperCustomerId = 0;

        if (wmCustomerBasicBo.getId() > 0) {

            if (oldWmCustomerDB == null) {
                logger.warn("saveOrUpdateCustomer##sendBindUnBindMessage,发送绑定/解绑消息时 更新场景未查询到客户信息! id:{}", wmCustomerBasicBo.getId());
                return;
            }
            oldSuperCustomerId = (int) oldWmCustomerDB.getSuperCustomerId();
        }

        sendBindOrUnBindMessageToOwnersAndInsertOpLog(operator, newCustomerId, newSuperWmCustomerId, oldSuperCustomerId);

    }

    /**
     * 给客户责任人发送绑定/解绑消息并记录操作日志
     *
     * @param operator             操作人信息
     * @param customerId           客户id
     * @param newSuperWmCustomerId 新的上级客户id
     * @param oldSuperCustomerId   旧的上级客户id
     * @throws WmCustomerException
     */
    @Override
    public void sendBindOrUnBindMessageToOwnersAndInsertOpLog(WmEmploy operator, Integer customerId, Integer newSuperWmCustomerId, Integer oldSuperCustomerId) throws WmCustomerException {
        logger.info("saveOrUpdateCustomer##sendBindUnBindMessage, id:{},oldSuperCustomerId:{},newSuperWmCustomerId:{}", customerId, oldSuperCustomerId, newSuperWmCustomerId);
        if (newSuperWmCustomerId.equals(oldSuperCustomerId)) {
            // 绑定关系未发生变化 不处理
            return;
        }

        //更新后的客户信息
        WmCustomerDB newCustomerDB = wmCustomerPlatformDataParseService.selectCustomerFromPlatformById(customerId);
        if (newCustomerDB == null) {
            logger.warn("saveOrUpdateCustomer##sendBindUnBindMessage,发送绑定/解绑消息时 未查询到更新后的客户信息! id:{}", customerId);
            return;
        }
        // 原上级客户存在 发送解绑消息
        if (oldSuperCustomerId > 0) {
            StringBuffer log = new StringBuffer();
            log.append("解绑普通客户:").append(newCustomerDB.getCustomerName()).append(newCustomerDB.getMtCustomerId());
            insertCustomerOpLog(oldSuperCustomerId, operator.getUid(), operator.getName(), WmCustomerOplogBo.OpType.UPDATE, log.toString());

            logger.info("saveOrUpdateCustomer##sendBindUnBindMessage, oldSuperCustomerId > 0,发送解绑消息,id:{},oldSuperCustomerId:{}", customerId, oldSuperCustomerId);
            sendBindOrUnBindMessage2Owners(newCustomerDB, operator, oldSuperCustomerId, CustomerConstants.IS_UNBINDING_YES);
        }
        // 绑定到新上级客户 发送绑定消息
        if (newSuperWmCustomerId > 0) {
            logger.info("saveOrUpdateCustomer##sendBindUnBindMessage, newSuperWmCustomerId > 0,发送绑定消息,id:{},newSuperWmCustomerId:{}", customerId, newSuperWmCustomerId);
            StringBuffer log = new StringBuffer();

            log.append("关联普通客户:").append(newCustomerDB.getCustomerName()).append(newCustomerDB.getMtCustomerId());
            insertCustomerOpLog(newSuperWmCustomerId, operator.getUid(), operator.getName(), WmCustomerOplogBo.OpType.UPDATE, log.toString());
            sendBindOrUnBindMessage2Owners(newCustomerDB, operator, newSuperWmCustomerId, CustomerConstants.IS_UNBINDING_NO);
        }
    }

    /**
     * 发送绑定/解绑消息给客户责任人
     *
     * @param wmCustomerDB
     * @param operator
     * @param superCustomerId
     * @param bindType
     * @throws WmCustomerException
     */
    private void sendBindOrUnBindMessage2Owners(WmCustomerDB wmCustomerDB, WmEmploy operator, Integer superCustomerId, Integer bindType) throws WmCustomerException {
        WmCustomerDB superCustomerDB = wmCustomerPlatformDataParseService.selectCustomerFromPlatformById(superCustomerId);
        if (superCustomerDB == null) {
            logger.warn("saveOrUpdateCustomer##sendBindUnBindMessage,发送绑定/解绑消息时 未查询到上级客户信息! id:{},oldSuperCustomerId:{}", wmCustomerDB.getId(), superCustomerId);
        } else {
            List<String> ownerMmis = Lists.newArrayList();
            String customerOwnerMis = wmEmployeeService.getMisId(wmCustomerDB.getOwnerUid());
            String superOwnerMis = wmEmployeeService.getMisId(superCustomerDB.getOwnerUid());

            if (StringUtils.isNotEmpty(customerOwnerMis)) {
                ownerMmis.add(customerOwnerMis);
            }
            if (StringUtils.isNotEmpty(superOwnerMis) && !ownerMmis.contains(superOwnerMis)) {
                ownerMmis.add(superOwnerMis);
            }
            StringBuffer msgBuffer = new StringBuffer();
            if (CustomerConstants.IS_UNBINDING_YES == bindType) {
                msgBuffer.append("【上级客户解绑】").append("\n");
                msgBuffer.append(operator.getName()).append("(").append(operator.getMisId()).append(")").append("已经将普通客户:").append(wmCustomerDB.getCustomerName()).append("(").append(wmCustomerDB.getMtCustomerId()).append(")").append("从上级客户:").append(superCustomerDB.getCustomerName()).append("(").append(superCustomerDB.getMtCustomerId()).append(")中解绑");
            } else {
                msgBuffer.append("【上级客户绑定】").append("\n");
                msgBuffer.append(operator.getName()).append("(").append(operator.getMisId()).append(")").append("已经将普通客户:").append(wmCustomerDB.getCustomerName()).append("(").append(wmCustomerDB.getMtCustomerId()).append(")").append("绑定到上级客户:").append(superCustomerDB.getCustomerName()).append("(").append(superCustomerDB.getMtCustomerId()).append(")");

            }

            if (!CollectionUtils.isEmpty(ownerMmis)) {
                DaxiangUtil.push("<EMAIL>", msgBuffer.toString(), Lists.newArrayList(customerOwnerMis, superOwnerMis));
            }

            logger.info("客户绑定解绑变更通知：mis = {},msg = {}", JSON.toJSONString(Lists.newArrayList(customerOwnerMis, superOwnerMis)), msgBuffer.toString());

        }
    }


    protected abstract boolean updateCustomer(WmCustomerBasicBo wmCustomerBasicBo, ValidateResultBo validateResultBo, Integer opUid, String opName, int channel) throws WmCustomerException, TException;

    /**
     * 根据客户类型，判断是否不需要提审。
     * 需要提审的条件：
     * 1、大于一个属性变更；
     * 2、属性变更了一个，但是类型无变更；
     * 3、只有类型进行了变更，且 客户类型是单店切换为【美食城/食堂】,即 （A && B）
     * 4、所有切换到美食城的场景都需要提审
     *
     * @param wmCustomerBasicBo
     * @return true：不需要提审；false：需要提审
     */
    protected boolean notAuditByCustomerRealType(WmCustomerBasicBo wmCustomerBasicBo, List diffList) throws WmCustomerException {
        /**
         * 需要提审的条件：1、大于一个属性变更，
         *              2、属性变更了一个，但是类型无变更
         *              3、只有类型进行了变更，且 客户类型是单店切换为【美食城/食堂】,即 （A && B）
         *              4、所有切换到美食城的场景都需要提审
         */

        WmCustomerDB wmCustomerDB = selectCustomerById(wmCustomerBasicBo.getId());

        if (diffList.size() == 0) {
            //属性无变更，则不需要提审
            return true;
        } else if (diffList.size() > 1) {
            //1、大于一个属性变更，需要提审
            return false;
        } else {//diffList.size() == 1
            if (wmCustomerBasicBo.getCustomerRealType() == wmCustomerDB.getCustomerRealType()) {
                //2、类型无变更，则需要提审
                return false;
            } else {
                //3、只有类型进行了变更，且 客户类型是单店切换为【美食城/食堂】

                //若由【单店】切换为【美食城/食堂】则需要在客户门店列表中上传“美食城档口列表”，并触发审核
                if (wmCustomerBasicBo.getCustomerRealType() == CustomerRealTypeEnum.MEISHICHENG.getValue() ||
                        wmCustomerBasicBo.getCustomerRealType() == CustomerRealTypeEnum.SHITANG.getValue()) {
                    if (wmCustomerDB.getCustomerRealType() == CustomerRealTypeEnum.DANDIAN.getValue()) {
                        return false;
                    }
                }// end if

                //4、所有切换到美食城的场景都需要提审
                if (wmCustomerBasicBo.getCustomerRealType() == CustomerRealTypeEnum.MEISHICHENG.getValue() &&
                        wmCustomerDB.getCustomerRealType() != CustomerRealTypeEnum.MEISHICHENG.getValue() &&
                        wmCustomerGrayService.isGrayForNewMsc(wmCustomerBasicBo)) {
                    return false;
                }

                //5、特批驳回&原客户类型为单店&修改后为纸质或电子非美食城-需要提审
                if (wmCustomerDB.getAuditStatus() == CustomerAuditStatus.CUSTOMER_SPECIAL_AUDIT_REJECT.getCode()
                        && wmCustomerDB.getCustomerRealType() == CustomerRealTypeEnum.DANDIAN.getValue()
                        && checkPaperOrElectronicMsc(wmCustomerBasicBo)) {
                    return false;
                }

            }

            return true;//不需要提审

        }

    }

    /**
     * 纸质 或 电子且美食城
     *
     * @param wmCustomerBasicBo
     * @return
     */
    private boolean checkPaperOrElectronicMsc(WmCustomerBasicBo wmCustomerBasicBo) {
        //纸质营业执照需要提审
        if (wmCustomerBasicBo.getCertificateType() == CertificateTypeEnum.PAPER.getType()) {
            return true;
        }
        //电子&美食城需要提审
        if (wmCustomerBasicBo.getCertificateType() == CertificateTypeEnum.ELECTRONIC.getType()
                && wmCustomerBasicBo.getCustomerRealType() == CustomerRealTypeEnum.MEISHICHENG.getValue()
                && wmCustomerBasicBo.getCustomerRealTypeSpInfoBo() != null) {
            return true;
        }
        return false;
    }

    /**
     * 新增客户调用
     *
     * @param wmCustomerBasicBo
     * @param validateResultBo
     * @param opUid
     * @param opName
     * @param channel
     * @return
     * @throws WmCustomerException
     * @throws TException
     */
    protected int saveCustomer(WmCustomerBasicBo wmCustomerBasicBo, ValidateResultBo validateResultBo, Integer opUid, String opName, int channel,WmCustomerDB orgDcCustomer) throws WmCustomerException, TException {
        // todo xiaoyao 新加的入参可以考虑下扩展性，类似于策略模式，执行不同的保存客户的策略
        // todo xiaoyao 判断下仅到餐客户同步过来大概的数量级，找朝伟
        int customerId;
        wmCustomerBasicBo.setOwnerUid(opUid);
        if(orgDcCustomer != null){
            logger.info("#saveCustomer-客户新增-复用到餐客户执行修改逻辑:{},原客户ID:{}",JSON.toJSONString(wmCustomerBasicBo),orgDcCustomer.getId());
            customerId = updateDaoCanCustomer(wmCustomerBasicBo,orgDcCustomer.getId(),opUid,channel);
        }else {
            if (wmCustomerBasicBo.getSignMode() == null) {
                wmCustomerBasicBo.setSignMode(CustomerSignMode.ELECTTRONIC.getCode());
            }
            customerId = insertCustomer(wmCustomerBasicBo, channel, opUid);

        }

        wmCustomerBasicBo.setId(customerId);
        logger.info("客户新增，客户ID={}", customerId);
        validateResultBo.setCustomerId(customerId);
        validateResultBo.setMsg(wmCustomerBasicBo.getIsLeaf() == CustomerConstants.CUSTOMER_IS_LEAF_NO ? "保存成功" : "保存成功,将与门店资质一同提审");
        insertCustomerOpLog(customerId, opUid, opName, wmCustomerBasicBo, null, WmCustomerOplogBo.OpType.INSERT);

        //如果营业执照类型为电子类型时(新增:非美食城客户类型场景下)保存即生效，不需要提审
        if (wmCustomerBasicBo.getCertificateType() == CertificateTypeEnum.ELECTRONIC.getType()
                && (wmCustomerBasicBo.getCustomerRealType() != CustomerRealTypeEnum.MEISHICHENG.getValue() || !wmCustomerGrayService.isGrayForNewMsc(wmCustomerBasicBo))) {
            validateResultBo.setMsg("保存成功");
            return customerId;
        }

        //当前审核状态为 待发起特批，则需要发起特批
        if (wmCustomerBasicBo.getAuditStatus() != null
                && wmCustomerBasicBo.getAuditStatus() == CustomerAuditStatus.CUSTOMER_TO_APPLY_SPECIAL_AUDIT.getCode()) {
            //发起特批&更新状态为特批中
            specialApprovalService.commitSpecialApproval(wmCustomerBasicBo, opUid, opName);
            return customerId;
        }

        //客户提审处理
        if (wmCustomerService.checkMscCustomerDir2Audit(wmCustomerBasicBo)
                && wmCustomerService.isCommitAuditWhenUpdate(wmCustomerBasicBo.getIsLeaf(), wmCustomerBasicBo.getCustomerRealType())) {
            Integer bizId = insertCustomerAudit(wmCustomerBasicBo, CustomerConstants.UNEFFECT, wmCustomerBasicBo.getOwnerUid(), CustomerConstants.BATCH_SUBMIT_NO, opUid, opName);
            WmCustomerDB wmCustomerDBNew = WmCustomerTransUtil.customerBoToDB(wmCustomerBasicBo);
            wmCustomerDBNew.setAuditStatus(CustomerAuditStatus.CUSTOMER_AUDIT_AUDITING.getCode());
            commitAudit(wmCustomerDBNew, bizId, opUid, opName, true);
            validateResultBo.setMsg(CustomerConstants.SUBMIT_AUDIT_SUCCESS_MSG);
            insertCustomerOpLog(customerId, opUid, opName, WmCustomerOplogBo.OpType.CHANGESTATUS, CustomerConstants.CUSTOMER_LOG_TEMPLATE_COMMIT_AUDIT);
        }
        return customerId;
    }

    protected Integer updateDaoCanCustomer(WmCustomerBasicBo wmCustomerBasicBo,Integer orgCustomerId,Integer opUid,int channel) throws WmCustomerException {
        logger.info("#updateDaoCanCustomer-新建外卖客户与当前系统内到餐客户资质重复，执行更新和复用逻辑:{}",JSON.toJSONString(wmCustomerBasicBo));
        WmCustomerDB updateWmCustomerDB = WmCustomerTransUtil.customerBoToDB(wmCustomerBasicBo);
        //调用不同子类的方法初始化状态
        initAuditStatus(updateWmCustomerDB, opUid, wmCustomerBasicBo);
        updateWmCustomerDB.setValid(CustomerConstants.VALID);
        int currentSeconds = getCurrentTimeSeconds();
        updateWmCustomerDB.setCtime(currentSeconds);
        updateWmCustomerDB.setUtime(currentSeconds);
        updateWmCustomerDB.setId(orgCustomerId);
        updateWmCustomerDB.setOwnerUid(opUid);
        // 先同步平台
        long mtCustomerId = 0;
        try {
            boolean isUploadImg = true;
            if (channel == CustomerSource.WAIMAI_BD.getCode() && !wmCustomerBasicBo.getPicUrl().contains(URL_HTTP_CONSTANT)) {
                isUploadImg = false;
            }
            mtCustomerId = mtCustomerThriftServiceAdapter.createMtCustomer(updateWmCustomerDB, isUploadImg);
            wmCustomerBasicBo.setMtCustomerId(mtCustomerId);
            if (isUploadImg) {
                // 图片地址有修改（http->S3）
                logger.info("insertCustomer uploadImg wmCustomerDB={}", JSONObject.toJSONString(updateWmCustomerDB));
                wmCustomerBasicBo.setPicUrl(updateWmCustomerDB.getPicUrl());
            }
            updateWmCustomerDB.setMtCustomerId(mtCustomerId);
        } catch (MtCustomerException e) {
            logger.error("美团客户平台创建客户信息失败, oldWmCustomerDb={},MtCustomerException={}", JSONObject.toJSONString(updateWmCustomerDB), e, e);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "客户平台创建用户异常");
        }
        // 更新
        wmCustomerService.updateCustomerInfoById(updateWmCustomerDB);
        //重置审核状态
        wmCustomerBasicBo.setAuditStatus(updateWmCustomerDB.getAuditStatus());
        return orgCustomerId;
    }


    protected WmCustomerDB queryDCCustomerByCustomerNumber(String customerNumber) throws TException, WmCustomerException {
        logger.info("#queryDCCustomerByCustomerNumber-根据资质编码查询仅到餐客户：{}",customerNumber);
        if(StringUtils.isBlank(customerNumber)){
            return null;
        }
        // 先查询客户平台侧，此资质是否存在到餐业务线下的客户
        QualificationNumTypeGetCustomerIdRequest request = new QualificationNumTypeGetCustomerIdRequest();
        request.setBusinessLineId(BusinessLineEnum.NIB_FOOD.getCode());
        request.setQualificationNum(customerNumber);
        List<Long> customerList = mtCustomerThriftServiceAdapter.getCustomerIdByQualificationNum(request);
        if (CollectionUtils.isEmpty(customerList)){
            return null;
        }
        // 再判断资质对应的到餐业务线下的平台ID是否在外卖业务线下有客户
        List<WmCustomerDB> wmCustomerDBList = wmCustomerPlatformDataParseService.selectCustomerFromPlatformByIdOrMtCustomerId(new HashSet<>(customerList));
        if (CollectionUtils.isEmpty(wmCustomerDBList)){
            return null;
        }
        // 找出客户类型为仅到餐的客户
        return wmCustomerDBList.stream().filter(customer -> customer.getCustomerRealType() == CustomerRealTypeEnum.DAOCAN.getValue()).findFirst().orElse(null);
    }

    /**
     * 解绑该客户的上级客户信息
     *
     * @param customerId
     * @return
     * @throws TException
     * @throws WmCustomerException
     */
    @Override
    public boolean unBindSuperCustomer(Integer customerId, Integer opUid, String opName) throws TException, WmCustomerException {
        wmCustomerPlatformDataParseService.unBindSuperCustomer(customerId);

        insertCustomerOpLog(customerId, opUid, opName, WmCustomerOplogBo.OpType.UPDATE, "解绑上级客户");
        return true;
    }


    /**
     * 获取客户详细信息，包括审核diff
     *
     * @param customerId
     * @return
     * @throws TException
     * @throws WmCustomerException
     */
    @Override
    public WmCustomerDetailBo getCustomerWithAuditById(Integer customerId) throws TException, WmCustomerException {
        WmCustomerDB wmCustomerDB;

        if (customerId % 100 < MccCustomerConfig.platformCustomerQueryPercent()) {
            wmCustomerDB = wmCustomerPlatformDataParseService.selectCustomerFromPlatformByIdV2(customerId);
        }else {
            wmCustomerDB = wmCustomerPlatformDataParseService.selectCustomerFromPlatformById(customerId);
        }

        if (wmCustomerDB == null) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "客户ID无法查到有效数据");
        }
        WmCustomerDetailBo wmCustomerDetailBo = WmCustomerTransUtil.customerDBToDetailBo(wmCustomerDB);
        //如果状态为审核中或审核拒绝，需要比对审核表
        if (customerSpecialAndAuditStatusList.contains(wmCustomerDetailBo.getAuditStatus())) {
            WmCustomerAuditDB wmCustomerAuditDB = wmCustomerAuditDBMapper.selectCustomerAuditByCustomerId(customerId);
            if (wmCustomerAuditDB != null) {
                //如果注册国家/地区为null则设置默认值0
                wmCustomerDB.setRegistryState(wmCustomerDB.getRegistryState() == null ? 0 : wmCustomerDB.getRegistryState());
                //获取比对内容
                DiffInfo diffInfo = compareDiff(wmCustomerDB, wmCustomerAuditDB);
                wmCustomerDetailBo.setDiffInfo(diffInfo);
                if (wmCustomerDetailBo.getAuditStatus() == CustomerAuditStatus.CUSTOMER_AUDIT_REJECT.getCode()
                        || wmCustomerDetailBo.getAuditStatus() == CustomerAuditStatus.CUSTOMER_SPECIAL_AUDIT_REJECT.getCode()) {
                    AuditInfo auditInfo = new AuditInfo(wmCustomerAuditDB.getAuditTime(), wmCustomerAuditDB.getAuditResult());
                    wmCustomerDetailBo.setAuditInfo(auditInfo);
                }

                //特批驳回或特批中需要返回任务ID
                if (wmCustomerDetailBo.getAuditStatus() == CustomerAuditStatus.CUSTOMER_SPECIAL_AUDIT_REJECT.getCode()
                        || wmCustomerDetailBo.getAuditStatus() == CustomerAuditStatus.CUSTOMER_SPECIAL_AUDITING.getCode()) {
                    wmCustomerDetailBo.setBizTaskId(wmCustomerAuditDB.getBizTaskId());
                }
            }
        }
//        // 封装客户标签名称
//        List<WmCustomerLabelBo> customerLabelNames = queryCustomerLabelNamesByCustomerId(customerId, oldWmCustomerDb.getOwnerUid());
//        wmCustomerDetailBo.setLabelNames(customerLabelNames);
//        logger.info("查询客户详细信息,wmCustomerDetailBo={}", JSONObject.toJSONString(wmCustomerDetailBo));
        setWmCustomerDetailBoDesc(wmCustomerDetailBo);
        return wmCustomerDetailBo;
    }

    /**
     * 设置新增字段文案——证件形式（营业执照） 1-纸质,2-电子
     * 证件状态（营业执照） 0-无,1-存续,2-注销,3-吊销
     * 证件是否过期 0-未过期，1-过期
     * 法人变更 0-否，1-是
     *
     * @param wmCustomerDetailBo
     */
    private void setWmCustomerDetailBoDesc(WmCustomerDetailBo wmCustomerDetailBo) {
        //证件形式（营业执照） 1-纸质,2-电子
        if (wmCustomerDetailBo.getCertificateType() != null && CertificateTypeEnum.getByType(wmCustomerDetailBo.getCertificateType()) != null) {
            wmCustomerDetailBo.setCertificateTypeDesc(CertificateTypeEnum.getByType(wmCustomerDetailBo.getCertificateType()).getName());
        }
        //证件状态（营业执照） 0-无,1-存续,2-注销,3-吊销
        if (wmCustomerDetailBo.getCertificateStatus() != null && CertificateStatusEnum.getByType(wmCustomerDetailBo.getCertificateStatus()) != null) {
            wmCustomerDetailBo.setCertificateStatusDesc(CertificateStatusEnum.getByType(wmCustomerDetailBo.getCertificateStatus()).getName());
        }
        //证件是否过期 0-未过期，1-过期
        if (wmCustomerDetailBo.getCertificateOverdue() != null && CertificateOverdueEnum.getByType(wmCustomerDetailBo.getCertificateOverdue()) != null) {
            wmCustomerDetailBo.setCertificateOverdueDesc(CertificateOverdueEnum.getByType(wmCustomerDetailBo.getCertificateOverdue()).getName());
        }
        //法人变更 0-否，1-是
        if (wmCustomerDetailBo.getLegalPersonChange() != null && LegalPersonChangeEnum.getByType(wmCustomerDetailBo.getLegalPersonChange()) != null) {
            wmCustomerDetailBo.setLegalPersonChangeDesc(LegalPersonChangeEnum.getByType(wmCustomerDetailBo.getLegalPersonChange()).getName());
        }
    }


    /**
     * 获取客户标签信息
     *
     * @param customerId
     * @param userId
     * @return
     * @throws TException
     * @throws WmCustomerException
     */
    @Override
    public List<WmCustomerLabelBo> getCustomerLabelInfo(int customerId, int userId) throws TException, WmCustomerException {
        // 封装客户标签名称
        List<WmCustomerLabelBo> customerLabelNames = queryCustomerLabelNamesByCustomerId(customerId, userId);
        logger.info("查询客户详细信息,customerLabelNames={}", JSONObject.toJSONString(customerLabelNames));
        return customerLabelNames;
    }

    @Override
    public Map<Integer, List<WmCustomerLabelBo>> getCustomerLabelInfoBatch(List<Integer> customerIdList, int userId) throws TException, WmCustomerException {
        Map<Integer, List<WmCustomerLabelBo>> map = new HashMap<>();
        customerIdList.stream().forEach(customerId -> {
            List<WmCustomerLabelBo> customerLabelNames = null;
            try {
                customerLabelNames = queryCustomerLabelNamesByCustomerId(customerId, userId);
            } catch (Exception e) {
                logger.warn("getWmCustomerPageData#客户标签信息查询异常", e);
            }
            map.put(customerId, customerLabelNames);
        });
        return map;
    }

    /**
     * 是否展示美团美团客户平台的客户信息
     *
     * @return
     */
    private boolean isDisplayMtCustomer(int customerId) {
        if (MccConfig.isDispalyMtCustomer()) {
            return true;
        }

        int beginId = MccConfig.getBeginIdOfDisplayMtCustomer();
        int endId = MccConfig.getEndIdOfDisplayMtCustomer();

        if (beginId <= customerId && endId >= customerId) {
            return true;
        }

        return false;

    }

    /**
     * 获取客户列表
     *
     * @param wmCustomerFormBo
     * @return
     */
    @Override
    public WmCustomerPageDate getCustomerList(WmCustomerFormBo wmCustomerFormBo) throws WmCustomerException, TException {
        // 参数校验
        validateGetCustomerListParam(wmCustomerFormBo);
        WmCustomerEsQueryVo wmCustomerEsQueryVo = WmCustomerTransUtil.wmCustomerEsQueryVo(wmCustomerFormBo);
        // 数据权限控制
        if (MccCustomerConfig.openHQAuthSplitForCustomer()) {
            buildDataLimit(wmCustomerFormBo, wmCustomerEsQueryVo);
        }
        // 校验签约人手机号与签约人证件号是否能生成token
        wmCustomerESService.checkPhoneNumTokenAndCertNumTokenIsEmpty(wmCustomerFormBo.getSignerPhoneNum(), wmCustomerFormBo.getSignerCertNumber(), wmCustomerEsQueryVo.getSignerCertType());

        if (StringUtils.isNotBlank(wmCustomerFormBo.getSignerPhoneNum())) {
            String phoneNumToken = wmCustomerSensitiveWordsService.encryption(wmCustomerFormBo.getSignerPhoneNum(), KmsKeyNameEnum.PHONE_NO, 0);
            if (StringUtils.isNotBlank(phoneNumToken)) {
                wmCustomerEsQueryVo.setSignerPhoneNumToken(phoneNumToken);
            }
        }

        if (StringUtils.isNotBlank(wmCustomerFormBo.getSignerCertNumber()) && wmCustomerEsQueryVo.getSignerCertType() != null) {
            String certNumberToken = wmCustomerSensitiveWordsService.encryption(wmCustomerFormBo.getSignerCertNumber(), KmsKeyNameEnum.IDENTIFY_ID, wmCustomerEsQueryVo.getSignerCertType());
            if (StringUtils.isNotBlank(certNumberToken)) {
                wmCustomerEsQueryVo.setSignerCertNumberToken(certNumberToken);
            }
        }

        if (wmCustomerFormBo.getWmPoiId() > 0 || !CollectionUtils.isEmpty(wmCustomerFormBo.getBrandIds())) {
            return getWmCustomerPageDataWithPoiIdAndBrandIds(wmCustomerFormBo, wmCustomerEsQueryVo);
        }

        PageData<WmCustomerListBo> page = getWmCustomerPageData(wmCustomerEsQueryVo);
        if (page == null) {
            return getDefaultPageIfNoResult();
        }

        return new WmCustomerPageDate(page.getPageInfo(), page.getList());
    }

    @Override
    public WmCustomerPageDate getDcCustomerList(WmCustomerFormBo wmCustomerFormBo) throws WmCustomerException, TException{
        // 参数校验
        validateGetCustomerListParam(wmCustomerFormBo);
        WmCustomerEsQueryVo wmCustomerEsQueryVo = WmCustomerTransUtil.wmCustomerEsQueryVo(wmCustomerFormBo);
        Set<Long> mtCustomerIdSet = dcCustomerService.getAllMtCustomerIdByUid(wmCustomerFormBo.getOpUid());
        if(mtCustomerIdSet.isEmpty()){
            return getDefaultPageIfNoResult();
        }
        // 根据权限过滤后的客户ID查询资质，最终使用资质查询外卖库
        buildDcQuaLimit(mtCustomerIdSet,wmCustomerEsQueryVo);
        PageData<WmCustomerListBo> page = getDcCustomerPageData(wmCustomerEsQueryVo);
        if (page == null) {
            return getDefaultPageIfNoResult();
        }

        return new WmCustomerPageDate(page.getPageInfo(), page.getList());
    }

    private void buildDcQuaLimit(Set<Long> mtCustomerIdSet,WmCustomerEsQueryVo wmCustomerEsQueryVo) throws TException, WmCustomerException {
        List<WmCustomerDB> wmCustomerDBList = mtCustomerThriftServiceAdapter.getCustomerByMtCustomerIds(new ArrayList<>(mtCustomerIdSet),BusinessLineEnum.NIB_FOOD);
        if (CollectionUtil.isNotEmpty(wmCustomerDBList)){
            wmCustomerEsQueryVo.setCustomerNumberList(wmCustomerDBList.stream().map(WmCustomerDB::getCustomerNumber).collect(Collectors.toList()));
        }else {
            wmCustomerEsQueryVo.setCustomerNumberList(new ArrayList<>());
        }
        // 到餐角色下只能看到外卖业务线下的客户类型
        wmCustomerEsQueryVo.setCustomerRealTypeList(
                Arrays.stream(CustomerRealTypeEnum.values())
                        .filter(e -> e.getBizOrgCode()!= null && e.getBizOrgCode() == CustomerBizOrgEnum.WAI_MAI.getCode())
                        .map(CustomerRealTypeEnum::getValue)
                        .collect(Collectors.toList()));
        // 到餐客户查询场景下，不需要用责任人
        wmCustomerEsQueryVo.setHqAuthLimit(true);
    }



    private void validateGetCustomerListParam(WmCustomerFormBo wmCustomerFormBo) throws WmCustomerException {
        if (wmCustomerFormBo != null && !CollectionUtils.isEmpty(wmCustomerFormBo.getBrandIds()) && wmCustomerFormBo.getBrandIds().size() > 3) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "查询的品牌不能超过3个");
        }

        boolean isNoOpUid = wmCustomerFormBo.getOpUid() == null || wmCustomerFormBo.getOpUid() <= 0;
        if (MccCustomerConfig.openHQAuthSplitForCustomer() && isNoOpUid) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "操作人不能为空");
        }
    }


    /**
     * 数据权限限(new：满足总部权限就不看责任人下级限定了)：
     * 1、登录人是[总部白名单]且不是[外卖总部]、[外卖总部只读]、[闪购总部]、[闪购总部只读]、[医药总部]、[医药总部只读]角色：全部客户数据。
     * 2、NEW 登录人是[外卖总部]、[外卖总部只读]角色：客户类型为空、单店、美食城、食堂、食堂承包商、大连锁、小连锁、流量连锁、聚合配送商（共9个）的全部客户数据。
     * 3、NEW 登录人是[闪购总部]、[闪购总部只读]角色：客户类型为空、单店、小连锁、总部商超连锁、总部生鲜连锁、总部鲜花连锁、总部母婴连锁、总部美妆连锁、总部服饰鞋帽连锁、总部日用品连锁（共10个）的全部客户数据。
     * 4、NEW 登录人是[医药总部]、[医药总部只读]角色：客户类型为空、单店药品、总部药品连锁（共3个）的全部客户数据。
     * 5、登录人是客户责任人或责任人上级：本人及下级的客户数据。
     *
     * @param wmCustomerFormBo
     * @param vo
     */
    private void buildDataLimit(WmCustomerFormBo wmCustomerFormBo, WmCustomerEsQueryVo vo) throws WmCustomerException {
        int opUid = wmCustomerFormBo.getOpUid();
        // 判断是否总部权限
        List<Integer> customerRealTypes = wmCustomerAuthService.getAuthCustomerRealType(opUid, WmCustomerAuthTypeEnum.READ_ONLY.getCode());
        if (!CollectionUtils.isEmpty(customerRealTypes)) {
            // 只有PC才允许查看跨境B2C药品
            if (!vo.isPc() && customerRealTypes.contains(CustomerRealTypeEnum.B2C_DRUG.getValue())) {
                customerRealTypes.remove(Integer.valueOf(CustomerRealTypeEnum.B2C_DRUG.getValue()));
            }
            vo.setHqAuthLimit(true);
            vo.setCustomerRealTypeList(customerRealTypes);
            //到家角色不允许查看到餐客户类型的客户
            vo.setNotCustomerRealTypeList(Collections.singletonList(CustomerRealTypeEnum.DAOCAN.getValue()));
            return;
        }

        List<Integer> hasAuthOwnerUidList = Lists.newArrayList(opUid);

        // 获取下级
        List<Integer> downUids = wmVirtualOrgServiceAdaptor.getUidsByUid(opUid, WmOrgConstant.QUERY_ALL_SOURCE, WmVirtualOrgRecursiveTypeEnum.TOP_DOWN_NO_SELF.getType());
        if (!CollectionUtils.isEmpty(downUids)) {
            hasAuthOwnerUidList.addAll(downUids);
        }
        List<Integer> ownerUids = wmCustomerFormBo.getOwnerUidList();
        if (CollectionUtils.isEmpty(ownerUids)) {
            int maxSupportCount = MccCustomerConfig.getESQueryMaxSupportParamCount();
            if (hasAuthOwnerUidList.size() > maxSupportCount) {
                List<Integer> newDownUids = hasAuthOwnerUidList.subList(0, maxSupportCount - 1);
                logger.info("buildDataLimit hasAuthOwnerUidList.size() > maxSupportCount wmCustomerFormBo={}", JSONObject.toJSONString(wmCustomerFormBo));
                hasAuthOwnerUidList = newDownUids;
            }
        } else {
            if (hasAuthOwnerUidList.containsAll(ownerUids)) {
                hasAuthOwnerUidList = ownerUids;
            } else {
                hasAuthOwnerUidList = Lists.newArrayList(-1);
            }
        }
        vo.setOwnerUidList(hasAuthOwnerUidList);
        vo.setHqAuthLimit(true);
    }


    /**
     * 获取客户列表分页数据
     *
     * @param vo
     * @return
     */
    private PageData<WmCustomerListBo> getWmCustomerPageData(WmCustomerEsQueryVo vo) throws WmCustomerException {
        List<WmCustomerListDB> wmCustomerListDBList = Lists.newArrayList();
        if ((vo.getPoiCountStart() == null && vo.getPoiCountEnd() != null) || (vo.getPoiCountStart() != null && vo.getPoiCountEnd() == null)) {
            logger.warn("[getWmCustomerPageData] 门店数量输入不完整");
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_VERIFY_INPUT_ERROR, "门店数量输入不完整");
        }
        if (vo.getPoiCountStart() != null) {
           if (vo.getPoiCountStart() < 0 || vo.getPoiCountEnd() < 0 || vo.getPoiCountStart() > vo.getPoiCountEnd()) {
               logger.warn("[getWmCustomerPageData] 门店数量输入不合法, poiCountStart={}, poiCountEnd={}", vo.getPoiCountStart(), vo.getPoiCountEnd());
               throw new WmCustomerException(CustomerConstants.RESULT_CODE_VERIFY_INPUT_ERROR, "门店数量输入不合法");
           }
        }

        try {
            wmCustomerListDBList = wmCustomerESService.queryCustomerPage(vo);
        } catch (Exception e) {
            logger.error("[getWmCustomerPageData] 查询es失败 vo={}", JSONObject.toJSONString(vo), e);
        }

        if (CollectionUtils.isEmpty(wmCustomerListDBList)) {
            return null;
        }
        // 客户信息覆盖
        List<Integer> customerIds = wmCustomerListDBList.stream().map(WmCustomerListDB::getId).collect(Collectors.toList());
        Set<Integer> customerIdsSet = new HashSet<>(customerIds);
        List<WmCustomerDB> customerList = wmCustomerPlatformDataParseService.selectCustomerFromPlatformByIds(customerIdsSet);
        // 获取db对应信息
        Map<Integer, WmCustomerDB> customerDBMap = customerList.stream().collect(Collectors.toMap(WmCustomerDB::getId, wmCustomerDB -> wmCustomerDB));
        Map<Integer, WmCustomerListDB> customerListDBMap = new HashMap<>();
        if (MccCustomerConfig.getCustomerListPoiCountSwitch()) {
            // 查询关联的门店数量
            customerListDBMap = wmCustomerPoiService.countCustomerPoiList(customerIds);
        }
        // 查询关联的下级客户数量
        Map<Integer, WmCustomerListDB> subCustomerListMap = countSubCustomerList(customerIds);
        // 查询上级客户名称
        Map<Integer, WmCustomerListDB> superCustomerNameListMap = getSuperCustomerNameList(customerIds);
        // 查询关联品牌数
        Map<Integer, Set<Integer>> customerBrandIdMap = getCustomerBrandMap(customerIds);
        // 查询客户资质在客户平台合作的业务线信息
        Map<Integer, Set<Long>> customerBusinessLineMap = getCustomerBusinessLineMap(customerList);

        List<WmCustomerListBo> wmCustomerListBoList = Lists.newArrayList();
        for (WmCustomerListDB customerListDB : wmCustomerListDBList) {
            WmCustomerDB customerDB = customerDBMap.get(customerListDB.getId());
            if (null == customerDB) {
                logger.info("客户{},ES与DB数据不一致", customerListDB.getId());
                continue;
            }
            // 转换wmCustomerListBo
            WmCustomerListBo wmCustomerListBo = WmCustomerTransUtil.transferToListBo(customerDB, customerListDB);
            if (MccCustomerConfig.getCustomerListPoiCountSwitch()) {
                //1、关联门店数
                if (customerListDBMap.containsKey(wmCustomerListBo.getId())) {
                    wmCustomerListBo.setWmPoiCount(customerListDBMap.get(wmCustomerListBo.getId()).getWmPoiCount());
                }
            }
            //2、关联下级客户数
            if (subCustomerListMap.containsKey(wmCustomerListBo.getId())) {
                wmCustomerListBo.setSubCustomerCount(subCustomerListMap.get(wmCustomerListBo.getId()).getSubCustomerCount());
            }
            //3、关联上级客户名称
            if (superCustomerNameListMap.containsKey(wmCustomerListBo.getId())) {
                wmCustomerListBo.setSuperCustomerName(superCustomerNameListMap.get(wmCustomerListBo.getId()).getSuperCustomerName());
            }
            //4、关联品牌信息
            if (customerBrandIdMap.containsKey(wmCustomerListBo.getId())) {
                wmCustomerListBo.setBrandIds(Lists.newArrayList(customerBrandIdMap.getOrDefault(wmCustomerListBo.getId(), Sets.newHashSet())));
            }
            //5、经营形式信息
            if (customerBusinessLineMap.containsKey(wmCustomerListBo.getId())) {
                Set<Long> businessLineIdSet = customerBusinessLineMap.get(wmCustomerListBo.getId());
                wmCustomerListBo.setBusinessLine(StringUtils.join(businessLineIdSet, ","));
                wmCustomerListBo.setBusinessLineDesc(businessLineIdSet.stream()
                        .map(BusinessLineEnum::getByCode) // 将ID映射为枚举
                        .map(BusinessLineEnum::getName) // 获取枚举的name字段
                        .collect(Collectors.joining(","))); // 逗号分隔);
            }
            wmCustomerListBoList.add(wmCustomerListBo);
        }

        PageData<WmCustomerListBo> page = PageUtil.page(wmCustomerListDBList, wmCustomerListBoList);
        logger.info("获取客户列表,返回page={}", JSONObject.toJSONString(page));
        return page;
    }

    private PageData<WmCustomerListBo> getDcCustomerPageData(WmCustomerEsQueryVo vo) throws WmCustomerException{
        List<WmCustomerListDB> wmCustomerListDBList = Lists.newArrayList();

        try {
            wmCustomerListDBList = wmCustomerESService.queryCustomerPage(vo);
        } catch (Exception e) {
            logger.error("[getDcCustomerPageData] 查询es失败 vo={}", JSONObject.toJSONString(vo), e);
        }

        if (CollectionUtils.isEmpty(wmCustomerListDBList)) {
            return null;
        }
        // 客户信息覆盖
        List<Integer> customerIds = wmCustomerListDBList.stream().map(WmCustomerListDB::getId).collect(Collectors.toList());
        Set<Integer> customerIdsSet = new HashSet<>(customerIds);
        List<WmCustomerDB> customerList = wmCustomerPlatformDataParseService.selectCustomerFromPlatformByIds(customerIdsSet);
        // 获取db对应信息
        Map<Integer, WmCustomerDB> customerDBMap = customerList.stream().collect(Collectors.toMap(WmCustomerDB::getId, wmCustomerDB -> wmCustomerDB));
        // 查询客户资质在客户平台合作的业务线信息
        Map<Integer, Set<Long>> customerBusinessLineMap = getCustomerBusinessLineMap(customerList);

        List<WmCustomerListBo> wmCustomerListBoList = Lists.newArrayList();
        for (WmCustomerListDB customerListDB : wmCustomerListDBList) {
            WmCustomerDB customerDB = customerDBMap.get(customerListDB.getId());
            if (null == customerDB) {
                logger.info("到餐客户{},ES与DB数据不一致", customerListDB.getId());
                continue;
            }
            // 转换wmCustomerListBo
            WmCustomerListBo wmCustomerListBo = WmCustomerTransUtil.transferToListBo(customerDB, customerListDB);

            //5、经营形式信息
            if (customerBusinessLineMap.containsKey(wmCustomerListBo.getId())) {
                Set<Long> businessLineIdSet = customerBusinessLineMap.get(wmCustomerListBo.getId());
                wmCustomerListBo.setBusinessLine(StringUtils.join(businessLineIdSet, ","));
                wmCustomerListBo.setBusinessLineDesc(businessLineIdSet.stream()
                        .map(BusinessLineEnum::getByCode) // 将ID映射为枚举
                        .map(BusinessLineEnum::getName) // 获取枚举的name字段
                        .collect(Collectors.joining(","))); // 逗号分隔);
            }
            wmCustomerListBoList.add(wmCustomerListBo);
        }

        PageData<WmCustomerListBo> page = PageUtil.page(wmCustomerListDBList, wmCustomerListBoList);
        logger.info("获取到餐客户列表,返回page={}", JSONObject.toJSONString(page));
        return page;
    }

    /**
     * 根据客户资质查询在客户平台合作的业务线（只关注到餐和外卖）
     */

    private Map<Integer, Set<Long>> getCustomerBusinessLineMap(List<WmCustomerDB> customerDBList) throws WmCustomerException {
        if (CollectionUtils.isEmpty(customerDBList)) {
            return new HashMap<>();
        }
        if (!MccCustomerConfig.getWdGray()){
            // 没有开灰度的时候，不计算客户的经营形式
            return new HashMap<>();
        }
        Map<Integer, Set<Long>> customerBusinessLineMap = new HashMap<>();
        for (WmCustomerDB customerDB : customerDBList) {
            Set<Long> businessLineSet = getCustomerBusinessLine(customerDB);
            if (businessLineSet != null){
                customerBusinessLineMap.put(customerDB.getId(),businessLineSet);
            }
        }
        return customerBusinessLineMap;
    }

    @Override
    public Set<Long> getCustomerBusinessLine(WmCustomerDB customerDB){
        CustomerRealTypeEnum customerRealTypeEnum = CustomerRealTypeEnum.getByValue(customerDB.getCustomerRealType());
        if(StringUtils.isBlank(customerDB.getCustomerNumber()) || CustomerBizOrgEnum.WAI_MAI.getCode() != customerRealTypeEnum.getBizOrgCode()){
            return null;
        }
        if (CustomerRealTypeEnum.DAOCAN == customerRealTypeEnum){
            // 到餐客户仅展示到餐经营形式
            return Sets.newHashSet(BusinessLineEnum.NIB_FOOD.getCode());
        }
        QualificationNumTypeGetCustomerIdRequest request = new QualificationNumTypeGetCustomerIdRequest();
        // 外卖类型的客户，查询资质在客户平台侧是否有到餐业务线
        request.setBusinessLineId(BusinessLineEnum.NIB_FOOD.getCode());
        request.setQualificationNum(customerDB.getCustomerNumber());
        try{
            List<Long> mtCustomerIds = mtCustomerThriftServiceAdapter.getCustomerIdByQualificationNum(request);
            if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(mtCustomerIds)){
                //说明当前客户资质外卖，到餐业务线都合作
                return Sets.newHashSet(BusinessLineEnum.WAI_MAI.getCode(),BusinessLineEnum.NIB_FOOD.getCode());
            }
            //默认具有当前客户类型所属的经营形式
            return Sets.newHashSet(BusinessLineEnum.WAI_MAI.getCode());
        }catch (Exception e){
            logger.error("#getCustomerBusinessLineMap-根据资质查询客户合作业务线异常:{}",customerDB.getMtCustomerId(),e);
        }
        return null;
    }

    /**
     * 根据客户ID列表获取绑定的下级客户数量
     *
     * @param
     * @return
     */
    private Map<Integer, WmCustomerListDB> countSubCustomerList(List<Integer> customerIds) throws WmCustomerException {
        List<WmCustomerListDB> wmCustomerListBoList = wmCustomerPlatformDataParseService.countsubCustomerList(customerIds);
        Map<Integer, WmCustomerListDB> customerListToMap =
                Maps.uniqueIndex(wmCustomerListBoList, new Function<WmCustomerListDB, Integer>() {
                    @Nullable
                    @Override
                    public Integer apply(@Nullable WmCustomerListDB input) {
                        return input.getId();
                    }
                });
        return customerListToMap;
    }

    private Map<Integer, WmCustomerListDB> getSuperCustomerNameList(List<Integer> customerIds) throws WmCustomerException {
        List<WmCustomerListDB> wmCustomerListBoList = wmCustomerPlatformDataParseService.getSuperCustomerNameList(customerIds);
        Map<Integer, WmCustomerListDB> customerListToMap =
                Maps.uniqueIndex(wmCustomerListBoList, new Function<WmCustomerListDB, Integer>() {
                    @Nullable
                    @Override
                    public Integer apply(@Nullable WmCustomerListDB input) {
                        return input.getId();
                    }
                });
        return customerListToMap;
    }


    private WmCustomerPageDate getWmCustomerPageDataWithPoiIdAndBrandIds(WmCustomerFormBo wmCustomerFormBo, WmCustomerEsQueryVo wmCustomerEsQueryVo) throws WmCustomerException {
        //通过门店ID获取关联该门店的客户集合
        //后期可能会出现一个门店对应多个客户的情况
        Set<Integer> customerIdSet = null;
        if (wmCustomerFormBo.getWmPoiId() > 0) {
            customerIdSet = wmCustomerPoiService.selectCustomerIdByPoiId(Sets.newHashSet(wmCustomerFormBo.getWmPoiId()));
            if (CollectionUtils.isEmpty(customerIdSet)) {
                return getDefaultPageIfNoResult();
            }
        }

        // 根据关联品牌id和已根据门店id查询出的客户id过滤出最终的客户id列表
        if (!CollectionUtils.isEmpty(wmCustomerFormBo.getBrandIds())) {
            if (wmCustomerFormBo.getWmPoiId() > 0) {
                // 门店id和品牌id同时查询的情况需要取交集
                customerIdSet = wmCustomerBrandService.selectCustomerIdByBrandIdsAndCustomerIds(wmCustomerFormBo.getBrandIds(), customerIdSet);
            } else {
                // 只根据品牌id单独查询的情况
                customerIdSet = wmCustomerBrandService.selectCustomerIdByBrandIds(wmCustomerFormBo.getBrandIds());
            }
        }

        if (CollectionUtils.isEmpty(customerIdSet)) {
            return getDefaultPageIfNoResult();
        }

        if (wmCustomerFormBo.getCustomerId() > 0 && !customerIdSet.contains(wmCustomerFormBo.getCustomerId())) {
            return getDefaultPageIfNoResult();
        }

        if (wmCustomerFormBo.getCustomerId() <= 0) {
            wmCustomerEsQueryVo.setCustomerIdList(Lists.newArrayList(customerIdSet));
        }

        PageData<WmCustomerListBo> pageData = getWmCustomerPageData(wmCustomerEsQueryVo);
        if (pageData == null) {
            return getDefaultPageIfNoResult();
        }
        return new WmCustomerPageDate(pageData.getPageInfo(), pageData.getList());
    }


    private Map<Integer, Set<Integer>> getCustomerBrandMap(List<Integer> customerIds) throws WmCustomerException {
        Map<Integer, Set<Integer>> customerBrandMap = Maps.newHashMap();
        WmCustomerBrandCondition condition = new WmCustomerBrandCondition();
        condition.setCustomerIds(customerIds);
        List<WmCustomerBrandDB> wmCustomerListBoList = wmCustomerBrandService.listByCondition(condition);
        if (CollectionUtils.isEmpty(wmCustomerListBoList)) {
            return customerBrandMap;
        }

        for (WmCustomerBrandDB wmCustomerBrandDB : wmCustomerListBoList) {
            if (!customerBrandMap.containsKey(wmCustomerBrandDB.getCustomerId())) {
                customerBrandMap.put(wmCustomerBrandDB.getCustomerId(), Sets.newHashSet());
            }
            customerBrandMap.get(wmCustomerBrandDB.getCustomerId()).add(wmCustomerBrandDB.getBrandId());
        }
        return customerBrandMap;
    }

    /**
     * 没有查询到设置默认值
     *
     * @return
     */
    private WmCustomerPageDate getDefaultPageIfNoResult() {
        Map<String, Long> pageInfo = new HashMap<>();
        pageInfo.put("total", 0L);
        pageInfo.put("pages", 1L);
        pageInfo.put("pageNo", 1L);
        return new WmCustomerPageDate(pageInfo, Lists.<WmCustomerListBo>newArrayList());
    }

    @Override
    public boolean checkPoiQuaForCustomerAudit(int customerId, int opUid, String opName) throws WmCustomerException, TException {
        logger.info("检查门店资质,判断是否单独提审customerId={},opUid={},opName={}", customerId, opUid, opName);
        Boolean isCommitCustomerAudit = false;
        List<Long> wmPoiIds = WmCustomerPoiAggre.Factory.make().selectWmPoiIdsByCustomerId(customerId);
        if (!CollectionUtils.isEmpty(wmPoiIds)) {
            if (checkCustomerAuditStatus(customerId) && ConfigUtilAdapter.getBoolean("checkPoiQuaForCustomerAudit_gray",false)){
                //客户不是待审核并且不是为生效
                return true;
            }
            List<WmPoiAggre> wmPoiAggreList = Lists.newArrayList();
            try {
                List<List<Long>> partitionLists = Lists.partition(wmPoiIds, CustomerConstants.POI_QUERY_BATCH_NUM);
                for (List<Long> partitionList : partitionLists) {
                    List<WmPoiAggre> wmPoiAggres = wmPoiQueryThriftService.mgetWmPoiAggreByWmPoiIdWithSpecificField(partitionList, Sets.newHashSet(WmPoiFieldQueryConstant.WM_POI_FIELD_VALID));
                    wmPoiAggreList.addAll(wmPoiAggres);
                }
            } catch (WmServerException e) {
                logger.error("查询门店状态失败", e.toString(), e);
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, e.getMsg());
            }
            Map<Long, Integer> wmPoiValidMap = Maps.newHashMap();
            for (WmPoiAggre wmPoiAggre : wmPoiAggreList) {
                wmPoiValidMap.put(wmPoiAggre.getWm_poi_id(), wmPoiAggre.getValid());
            }
            for (Long wmPoiId : wmPoiIds) {
                Integer valid = wmPoiValidMap.get(wmPoiId);
                //为兼容上单1.0数据问题,如果门店状态非上单中，可直接提审客户
                if (valid != null && (valid == WmPoiValidEnum.READY.getValue() || valid == WmPoiValidEnum.OFFLINE.getValue() || valid == WmPoiValidEnum.ONLINE.getValue())) {
                    logger.info("检查到门店ID:{}状态valid={}，则客户ID:{}需要单独提审", wmPoiId, valid, customerId);
                    isCommitCustomerAudit = true;
                    break;
                }
                WmPoiProduceOplog last = wmPoiProduceOplogThriftService.getLatestRecordByPoint(wmPoiId, WmPoiProduceSection.QUALIFICATIONS);
                //如果有任一绑定门店的资质提审过,则客户需要单独提审
                if (last == null) {
                    continue;
                }

                //获取的op_type需要计算余数
                int remainder = 10;
                if (last.getOp_type() % remainder > WmPoiProduceStatus.INPUT) {
                    logger.info("检查到门店ID:{}资质已提交过审核，则客户ID:{}需要单独提审", wmPoiId, customerId);
                    isCommitCustomerAudit = true;
                    break;
                }
            }
        }
        if (isCommitCustomerAudit) {
            try {
                commitAudit(customerId, opUid, opName, true);
                return true;
            } catch (WmCustomerException wmCustomerException) {
                //如果是因为校验版本所导致的异常，则放过，不影响其他消费
                //因为存在同步场景，门店还是2.0,这时触发的提审，会被因为版本问题报异常影响其他消费者
                if (wmCustomerException.getCode() == 405) {
                    logger.info("消费客户门店绑定关系,检查客户绑定的门店有2.0版本");
                } else {
                    throw wmCustomerException;
                }
            }
        }
        return false;
    }

    /**
     * 为了防止大客户绑门店，每次都扫客户下全量门店数据，增加对客户的审核状态的前置校验
     * 判断客户当前审核状态是否为待审核，且客户生效状态为未生效
     * 如果非上述状态，说明已经提审过。则不需要单独提审，直接返回，不在校验全量门店
     * @param wmCustomerId
     * @return
     */
    private boolean checkCustomerAuditStatus(Integer wmCustomerId) throws WmCustomerException {
        WmCustomerDB wmCustomerDB = wmCustomerPlatformDataParseService.selectCustomerFromPlatformByIdRT(wmCustomerId);
        if (wmCustomerDB == null) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "客户ID无法查到有效数据");
        }
        logger.info("检查门店资质,判断是否单独提审customerId:{},审核状态:{}",wmCustomerId,wmCustomerDB.getAuditStatus());
        return !(wmCustomerDB.getAuditStatus() == CustomerAuditStatus.CUSTOMER_AUDIT_NEW.getCode() && wmCustomerDB.getEffective() == CustomerConstants.UNEFFECT);
    }

    /**
     * 审核回调处理
     */
    @Override
    public void auditCustomerCallBack(WmCustomerAuditBo wmCustomerAuditBo) throws WmCustomerException {
        Boolean needMQ = false;
        WmCustomerDB wmCustomerDB = wmCustomerPlatformDataParseService.selectCustomerFromPlatformByIdRT(wmCustomerAuditBo.getCustomerId());
        //幂等，如果审核状态不为审核中，则返回默认值
        if (wmCustomerDB == null || wmCustomerDB.getAuditStatus() != CustomerAuditStatus.CUSTOMER_AUDIT_AUDITING.getCode()) {
            logger.info("客户无效或不是提审中，无需处理");
            return;
        }
        /** 1.获取变更前法人/姓名信息 **/
        String customerName = wmCustomerDB.getCustomerName();
        String legalPerson = wmCustomerDB.getLegalPerson();
        boolean isCustomerEffect = wmCustomerDB.isEffectived();
        boolean isChangeBizOrgCode = false;
        boolean deleteCustomerSceneTag = false;

        WmCustomerAuditDB wmCustomerAuditDB = new WmCustomerAuditDB();
        String opLog = "";//操作日志内容
        CustomerModuleStateEnum stateEnum = null;//上单状态机状态
        if (wmCustomerAuditBo.getAuditStatus().getCode() == WmCustomerAuditBo.AuditStatusEnum.AUDIT_STATUS_PASS.getCode()) {
            //每次审核通过都需要发生效MQ
            needMQ = true;
            //成功需要把审核表数据覆盖到正式表
            wmCustomerAuditDB = wmCustomerAuditDBMapper.selectCustomerAuditByCustomerIdRT(wmCustomerAuditBo.getCustomerId());
            if (wmCustomerDB.getCustomerRealType() != wmCustomerAuditDB.getCustomerRealType() || wmCustomerDB.getBizOrgCode() == null || wmCustomerDB.getBizOrgCode().intValue() == 0) {
                isChangeBizOrgCode = true;
            }
            // 客户数据生效时校验是否符合不重复要求，如果发现重复，此次提交数据不生效，状态回归到已生效，记录日志，发出大象消息
            boolean allowUpdateCustomerInfo = customerInfoUpdateCheckOperate(wmCustomerDB, wmCustomerAuditDB);
            if (allowUpdateCustomerInfo) {
                // 客户平台资质图片特殊处理
                wmCustomerAuditDB.setPicUrl(mtCustomerThriftServiceAdapterImpl.transferMtCustomerPicUrl(wmCustomerAuditDB.getPicUrl()));
                WmCustomerTransUtil.copyCustomerAuditDBtoDB(wmCustomerAuditDB, wmCustomerDB);
            }
            if (wmCustomerAuditDB.getCustomerType() != null && wmCustomerAuditDB.getCustomerType().intValue() == CustomerType.CUSTOMER_TYPE_BUSINESS.getCode()) {
                wmCustomerDB.setCertificateStatus(businessCertificationAdapter.getCertificateStatus(wmCustomerAuditDB.getCustomerNumber()));
            }
            wmCustomerDB.setAuditStatus(CustomerAuditStatus.CUSTOMER_AUDIT_PASS.getCode());
            wmCustomerDB.setEffective(CustomerConstants.EFFECT);
            wmCustomerAuditDB.setAuditStatus(CustomerAuditStatus.CUSTOMER_AUDIT_PASS.getCode());
            wmCustomerAuditDB.setValid(CustomerConstants.UNVALID);
            opLog = CustomerConstants.CUSTOMER_LOG_TEMPLATE_AUDIT_PASS;
            stateEnum = CustomerModuleStateEnum.PASS;

            //客户类型发生变更&修改前为外卖单店&个人资质，则需要删除场景标签
            deleteCustomerSceneTag = calDeleteCustomerSceneTag(wmCustomerDB, wmCustomerAuditDB);

        } else if (wmCustomerAuditBo.getAuditStatus().getCode() == WmCustomerAuditBo.AuditStatusEnum.AUDIT_STATUS_REJECT.getCode()) {
            wmCustomerDB.setAuditStatus(CustomerAuditStatus.CUSTOMER_AUDIT_REJECT.getCode());
            wmCustomerAuditDB.setAuditStatus(CustomerAuditStatus.CUSTOMER_AUDIT_REJECT.getCode());
            opLog = CustomerConstants.CUSTOMER_LOG_TEMPLATE_AUDIT_REJECT;
            stateEnum = CustomerModuleStateEnum.REJECT;
        }
        wmCustomerAuditDB.setId(wmCustomerAuditBo.getBizId());
        if (StringUtils.isNotEmpty(wmCustomerAuditBo.getAuditResult())) {
            String auditResult = wmCustomerAuditBo.getAuditResult();
            if (auditResult.length() > WmCustomerConstant.MAX_SUPPORT_REJECT_REASON_LENGTH_FOR_CUSTOMER) {
                logger.warn("客户信息审核回调结果太长 auditResult={}", auditResult);
                auditResult = auditResult.substring(0, WmCustomerConstant.MAX_SUPPORT_REJECT_REASON_LENGTH_FOR_CUSTOMER - WmCustomerConstant.TOO_LONG_MSG.length()) + WmCustomerConstant.TOO_LONG_MSG;
            }
            wmCustomerAuditDB.setAuditResult(auditResult);
        }

        //更新客户信息到美团客户平台
        try {
            long mtCustomerId = updateMtCustomer(wmCustomerDB, false);
            if (mtCustomerId != wmCustomerDB.getMtCustomerId()) {
                wmCustomerDB.setMtCustomerId(mtCustomerId);
            }
        } catch (Exception e) {
            logger.error("美团客户平台更新客户信息失败, wmCustomerId={}，e={}", wmCustomerDB.getId(), e);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "客户平台更新异常");
        }
        if (isChangeBizOrgCode) {
            // 计算业务线
            Integer bizOrgCode = CustomerRealTypeEnum.getBizOrgCodeByValue(wmCustomerDB.getCustomerRealType());
            wmCustomerDB.setBizOrgCode(bizOrgCode);
            mtCustomerThriftServiceAdapter.saveMtCustomerBizOrgCode(wmCustomerDB.getMtCustomerId(), wmCustomerDB.getBizOrgCode());
        }
        //删除场景标签为TRUE则需要删除标签
        if (deleteCustomerSceneTag) {
            wmCustomerLabelService.deleteWmSingleCustomerSceneTag(wmCustomerDB.getMtCustomerId(), 0, "系统");
        }
        //更新客户正式表状态
        wmCustomerService.updateCustomer(wmCustomerDB, 0);
        //更新客户审核表内容
        wmCustomerAuditDBMapper.updateCustomerAudit(wmCustomerAuditDB);
        //插入客户日志
        insertCustomerOpLog(wmCustomerAuditBo.getCustomerId(), wmCustomerAuditBo.getOpUid(), wmCustomerAuditBo.getOpName(), WmCustomerOplogBo.OpType.CHANGESTATUS, opLog);
        //插入上单状态机通知
        wmCustomerPoiService.insertPoiStateCenterByCustomerId(wmCustomerDB.getId(), stateEnum, wmCustomerAuditBo.getOpUid(), wmCustomerAuditBo.getOpName());
        if (needMQ) {
            sendCustomerStatusNoticeMQ(wmCustomerDB.getId(), CustomerMQEventEnum.CUSTOMER_EFFECTIVE, null);
        }
        if (wmCustomerAuditBo.getAuditStatus().getCode() == WmCustomerAuditBo.AuditStatusEnum.AUDIT_STATUS_PASS.getCode() && isCustomerEffect) {
            // 客户信息法人或个人变更，则签约人KP信息、法人信息自动删除，需要补录
            wmCustomerKpService.customerEffectForKpOperate(customerName, legalPerson, wmCustomerDB);
        }
    }

    /**
     * 判断是否删除场景标签
     *
     * @param wmCustomerDB
     * @param wmCustomerAuditDB
     * @return
     */
    private boolean calDeleteCustomerSceneTag(WmCustomerDB wmCustomerDB, WmCustomerAuditDB wmCustomerAuditDB) {

        //修改前客户非外卖单店
        if (wmCustomerDB.getCustomerRealType() != CustomerRealTypeEnum.DANDIAN.getValue()) {
            return false;
        }
        //修改前客户非个人资质
        if (wmCustomerDB.getCustomerType() != CustomerType.CUSTOMER_TYPE_IDCARD.getCode()) {
            return false;
        }
        //客户类型变更
        if (wmCustomerAuditDB.getCustomerRealType() != wmCustomerDB.getCustomerRealType()) {
            return true;
        }
        //客户类型不变&资质类型变更
        if (wmCustomerAuditDB.getCustomerRealType() == wmCustomerDB.getCustomerRealType()
                && wmCustomerAuditDB.getCustomerType() != wmCustomerDB.getCustomerType()) {
            return true;
        }
        return false;
    }

    /**
     * 客户数据生效时校验是否符合不重复要求，如果发现重复，此次提交数据不生效，状态回归到已生效，记录日志，发出大象消息
     *
     * @param wmCustomerDB
     * @param wmCustomerAuditDB
     * @return
     * @throws WmCustomerException
     */
    private boolean customerInfoUpdateCheckOperate(WmCustomerDB wmCustomerDB, WmCustomerAuditDB wmCustomerAuditDB) throws WmCustomerException {
        WmCustomerNumberRepeatVo wmCustomerNumberRepeatVo = new WmCustomerNumberRepeatVo(wmCustomerAuditDB.getCustomerId(), wmCustomerAuditDB.getCustomerType(),
                wmCustomerAuditDB.getCustomerNumber(), wmCustomerDB.getIsLeaf(), wmCustomerAuditDB.getRegistryState());
        WmCustomerDB duplicateCustomer = null;
        // 仅校验当前业务线是否有重复客户
        if (MccCustomerConfig.getCheckRuleSwitch()) {
            wmCustomerNumberRepeatVo.setBizOrgCode(CustomerRealTypeEnum.getBizOrgCodeByValue(wmCustomerAuditDB.getCustomerRealType()));
            List<WmCustomerDB> duplicateCustomerList = wmCustomerService.validateCustomerNumberNew(wmCustomerNumberRepeatVo);
            if (!CollectionUtils.isEmpty(duplicateCustomerList)) {
                duplicateCustomer = duplicateCustomerList.get(0);
            }
        } else {
            duplicateCustomer = wmCustomerService.validateCustomerNumber(new WmCustomerNumberRepeatVo(wmCustomerAuditDB.getCustomerId(),
                    wmCustomerAuditDB.getCustomerType(), wmCustomerAuditDB.getCustomerNumber(), wmCustomerDB.getIsLeaf(), wmCustomerAuditDB.getRegistryState()));
        }
        // 客户数据生效时校验是否符合不重复要求，如果发现重复，此次提交数据不生效，状态回归到已生效，记录日志，发出大象消息
        if (duplicateCustomer != null && duplicateCustomer.getId() > 0) {
            String log = String.format("修改后数据与客户（%s%s）资质重复，修改后数据无法生效。", duplicateCustomer.getCustomerName(), duplicateCustomer.getMtCustomerId());
            insertCustomerOpLog(wmCustomerDB.getId(), 0, "系统", WmCustomerOplogBo.OpType.CHANGESTATUS, log);
            String msg = String.format("请注意您的客户（%s%s）：修改后数据与客户（%s%s）资质重复，修改后数据无法生效。", wmCustomerDB.getCustomerName(),
                    wmCustomerDB.getMtCustomerId(), duplicateCustomer.getCustomerName(), duplicateCustomer.getMtCustomerId());
            int opUid = wmCustomerAuditDB.getOpUid();
            WmEmploy wmEmploy = wmEmployClient.getEmployById(opUid);
            if (wmEmploy == null) {
                logger.error("sendCustomerInfoUpdate失败，修改提交获取为空 wmCustomerDB={}", JSONObject.toJSONString(wmCustomerDB));
                return false;
            }
            DaxiangUtil.push("<EMAIL>", msg, Lists.newArrayList(wmEmploy.getMisId()));
            return false;
        } else {
            return true;
        }
    }

    /**
     * 更新客户信息到美团客户平台
     */
    @Override
    public long updateMtCustomer(WmCustomerDB wmCustomerDB, boolean isUploadImg) throws MtCustomerException {
        return mtCustomerThriftServiceAdapter.updateMtCustomer(wmCustomerDB, isUploadImg);
    }

    /**
     * 检查客户有效性
     */
    @Override
    public Boolean checkCustomerEffect(Integer id) throws WmCustomerException {
        Integer effect = wmCustomerPlatformDataParseService.checkCustomerEffect(id);
        if (effect == null) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "客户ID无法查到有效数据");
        }
        return effect == 1;
    }

    /**
     * 插入客户信息
     */
    @Override
    public Integer insertCustomer(WmCustomerBasicBo wmCustomerBasicBo, int channel, Integer opUid) throws WmCustomerException {
        WmCustomerDB wmCustomerDB = WmCustomerTransUtil.customerBoToDB(wmCustomerBasicBo);
        //调用不同子类的方法初始化状态
        initAuditStatus(wmCustomerDB, opUid, wmCustomerBasicBo);
        wmCustomerDB.setValid(CustomerConstants.VALID);
        int currentSeconds = getCurrentTimeSeconds();
        wmCustomerDB.setCtime(currentSeconds);
        wmCustomerDB.setUtime(currentSeconds);

        // 先同步平台
        long mtCustomerId = 0;
        try {
            boolean isUploadImg = true;
            if (channel == CustomerSource.WAIMAI_BD.getCode() && !wmCustomerBasicBo.getPicUrl().contains(URL_HTTP_CONSTANT)) {
                isUploadImg = false;
            }
            mtCustomerId = mtCustomerThriftServiceAdapter.createMtCustomer(wmCustomerDB, isUploadImg);
            wmCustomerBasicBo.setMtCustomerId(mtCustomerId);
            if (isUploadImg) {
                // 图片地址有修改（http->S3）
                logger.info("insertCustomer uploadImg wmCustomerDB={}", JSONObject.toJSONString(wmCustomerDB));
                wmCustomerBasicBo.setPicUrl(wmCustomerDB.getPicUrl());
            }
            wmCustomerDB.setMtCustomerId(mtCustomerId);
        } catch (MtCustomerException e) {
            logger.error("美团客户平台创建客户信息失败, oldWmCustomerDb={},MtCustomerException={}", JSONObject.toJSONString(wmCustomerDB), e, e);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "客户平台创建用户异常");
        }
        Integer customerId = wmCustomerService.insertCustomer(wmCustomerDB);
        if (customerId == null || customerId == 0) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "本地新增客户异常");
        }

        //重置审核状态
        wmCustomerBasicBo.setAuditStatus(wmCustomerDB.getAuditStatus());
        return customerId;
    }

    //得到当前时间的秒
    private static int getCurrentTimeSeconds() {
        long millis = System.currentTimeMillis();
        return (int) (millis / 1000);
    }

    /**
     * 初始化插入客户时的状态
     */
    protected abstract void initAuditStatus(WmCustomerDB wmCustomerDB, Integer opUid, WmCustomerBasicBo wmCustomerBasicBo) throws WmCustomerException;

    /**
     * 删除客户信息
     */
    @Override
    public abstract void deleteCustomer(Integer customerId, Integer opUid, String opName) throws WmCustomerException, TException;


    /**
     * 根据客户ID查询客户信息
     */
    @Override
    public WmCustomerDB selectCustomerById(Integer id) throws WmCustomerException {
        return wmCustomerPlatformDataParseService.selectCustomerFromPlatformById(id);
    }

    @Override
    public WmCustomerDB selectPlatformCustomerByIdFromSlave(Integer id) throws WmCustomerException {
        return wmCustomerPlatformDataParseService.selectCustomerFromPlatformByIdFromSlave(id);
    }

    @Override
    public Integer selectCustomerById4Grey(Integer id) {
        return wmCustomerPlatformDataParseService.selectCustomerById4Grey(id);
    }

    @Override
    public WmCustomerAddressBo getCustomerAddressForAutoBringIn(int customerId, int wmPoiId) throws TException, WmCustomerException {
        logger.info("getCustomerAddressForAudoBringIn：customerId={}, wmPoiId={}", customerId, wmPoiId);
        WmCustomerAddressBo wmCustomerAddressBo = new WmCustomerAddressBo();
        WmCustomerDB wmCustomerDB = selectCustomerById(customerId);
        if (wmCustomerDB == null) {
            logger.warn("通过客户ID查询客户为空了，" + customerId);
            return wmCustomerAddressBo;
        }

        //客户类型为单店
        if (wmCustomerDB.getCustomerRealType() == CustomerRealTypeEnum.DANDIAN.getValue()) {
            if (wmCustomerDB.getCustomerType() == CustomerType.CUSTOMER_TYPE_BUSINESS.getCode()) {
                wmCustomerAddressBo.setPoiBaseAddress(wmCustomerDB.getAddress());
                wmCustomerAddressBo.setPoiQuaAddress(wmCustomerDB.getAddress());
            } else if (wmCustomerDB.getCustomerType() == CustomerType.CUSTOMER_TYPE_IDCARD.getCode()) {
                //个人类型仅带入资质信息
                wmCustomerAddressBo.setPoiQuaAddress(wmCustomerDB.getAddress());
            }

        } else if (wmCustomerDB.getCustomerRealType() == CustomerRealTypeEnum.MEISHICHENG.getValue()) {
            //客户类型为美食城
            if (wmCustomerDB.getCustomerType() == CustomerType.CUSTOMER_TYPE_BUSINESS.getCode()) {
                wmCustomerAddressBo.setPoiBaseAddress(wmCustomerDB.getAddress());
                wmCustomerAddressBo.setPoiQuaAddress(wmCustomerDB.getAddress());
            }

        } else if (wmCustomerDB.getCustomerRealType() == CustomerRealTypeEnum.SHITANG.getValue()) {
            //客户类型为食堂
            if (wmCustomerDB.getCustomerType() == CustomerType.CUSTOMER_TYPE_BUSINESS.getCode()) {
                wmCustomerAddressBo.setPoiBaseAddress(wmCustomerDB.getAddress());
                wmCustomerAddressBo.setPoiQuaAddress(wmCustomerDB.getAddress());
            }

        }

        //代表基础信息非首次创建，则不带入地址信息，详情见prd
        if (wmPoiId > 0) {
            wmCustomerAddressBo.setPoiBaseAddress("");
        }
        return wmCustomerAddressBo;
    }


    /**
     * 根据id批量查询客户信息
     */
    @Override
    public List<WmCustomerDB> selectCustomerByIds(Set<Integer> customerIdSet) throws WmCustomerException {
        return wmCustomerPlatformDataParseService.selectCustomerFromPlatformByIds(customerIdSet);
    }

    /**
     * 查找该上级客户Id下的所有ID。
     * 列表数量不会太大
     */
    @Override
    public List<WmCustomerDB> selectCustomerBySuperCustomerId(Integer superCustomerId) throws WmCustomerException {
        return wmCustomerPlatformDataParseService.selectCustomerFromPlatformBySuperCustomerId(superCustomerId);
    }

    /**
     * 根据客户ID查询客户信息(查主库)
     */
    @Override
    public WmCustomerDB selectCustomerByIdRT(Integer id) throws WmCustomerException {
        return wmCustomerPlatformDataParseService.selectCustomerFromPlatformByIdRT(id);
    }


    /**
     * 根据客户ID查询生效客户数据
     */
    @Override
    public WmCustomerDB selectEffectCustomerById(Integer id) throws WmCustomerException {
        WmCustomerDB wmCustomerDB = wmCustomerPlatformDataParseService.selectCustomerFromPlatformById(id);
        if (wmCustomerDB == null || CustomerConstants.UNEFFECT == wmCustomerDB.getEffective()) {
            return null;
        }
        return wmCustomerDB;
    }

    /**
     * 根据门店ID查询客户信息
     */
    @Override
    public WmCustomerDB selectCustomerByWmPoiId(Long wmPoiId) throws WmCustomerException {
        return wmCustomerPlatformDataParseService.selectCustomerFromPlatformByWmPoiId(wmPoiId);
    }


    /**
     * 插入客户审核信息
     */
    @Override
    public Integer insertCustomerAudit(WmCustomerBasicBo wmCustomerBasicBo, Integer effective, Integer ownerUid, Integer batchSubmit, Integer opUid, String opName) throws WmCustomerException {
        WmCustomerAuditDB wmCustomerAuditDB = WmCustomerTransUtil.customerBoToAuditDB(wmCustomerBasicBo);
        wmCustomerAuditDB.setOwnerUid(ownerUid);
        wmCustomerAuditDB.setAuditStatus(CustomerAuditStatus.CUSTOMER_AUDIT_AUDITING.getCode());
        wmCustomerAuditDB.setEffective(effective);
        wmCustomerAuditDB.setValid(CustomerConstants.VALID);
        wmCustomerAuditDB.setBatchSubmit(batchSubmit);
        wmCustomerAuditDB.setOpUid(opUid);
        wmCustomerAuditDBMapper.insertCustomerAudit(wmCustomerAuditDB);

        //写入状态中心，与历史保持一致，已录入状态不写入状态机
//        wmCustomerPoiService.insertPoiStateCenterByCustomerId(wmCustomerBasicBo.getId(), CustomerModuleStateEnum.NEW, opUid, opName);
        return wmCustomerAuditDB.getId();
    }

    /**
     * 插入客户审核信息
     */
    @Override
    public Integer insertCustomerAudit(WmCustomerDB wmCustomerDB, Integer batchSubmit, Integer opUid, String opName) throws WmCustomerException {
        WmCustomerAuditDB wmCustomerAuditDB = new WmCustomerAuditDB();
        BeanUtils.copyProperties(wmCustomerDB, wmCustomerAuditDB);
        wmCustomerAuditDB.setId(null);
        wmCustomerAuditDB.setCustomerId(wmCustomerDB.getId());
        wmCustomerAuditDB.setAuditStatus(CustomerAuditStatus.CUSTOMER_AUDIT_AUDITING.getCode());
        wmCustomerAuditDB.setBatchSubmit(batchSubmit);
        //插入营业执照信息字段
        wmCustomerAuditDB.setCertificateType(wmCustomerDB.getCertificateType() == null ? CertificateTypeEnum.PAPER.getType() : wmCustomerDB.getCertificateType());
        wmCustomerAuditDB.setCertificateStatus(wmCustomerDB.getCertificateStatus() == null ? CertificateStatusEnum.DURATION.getType() : wmCustomerDB.getCertificateStatus());
        wmCustomerAuditDB.setCertificateOverdue(wmCustomerDB.getCertificateOverdue() == null ? CertificateOverdueEnum.NOEXPIRED.getType() : wmCustomerDB.getCertificateOverdue());
        wmCustomerAuditDB.setLegalPersonChange(wmCustomerDB.getLegalPersonChange() == null ? LegalPersonChangeEnum.CHANGE.getType() : wmCustomerDB.getLegalPersonChange());
        wmCustomerAuditDB.setOpUid(opUid);
        wmCustomerAuditDBMapper.insertCustomerAudit(wmCustomerAuditDB);

        //写入状态中心，与历史保持一致，已录入状态不写入状态机
//        wmCustomerPoiService.insertPoiStateCenterByCustomerId(oldWmCustomerDb.getId(), CustomerModuleStateEnum.NEW, opUid, opName);
        return wmCustomerAuditDB.getId();
    }

    /**
     * 更新提审信息为已打包提审
     */
    @Override
    public void packageCommitAudited(int customerId) throws WmCustomerException {
        wmCustomerAuditDBMapper.updatePackageCommitAudited(customerId);
    }

    @Override
    public WmCustomerBatchAuditBo commitAudit(Integer customerId, Integer opUid, String opName, Boolean force) throws WmCustomerException, TException {
        logger.info("WmCustomerBatchAuditBo commitAudit(), sustomerId = {} , opUid ={}, opName ={} ,force ={} " + customerId, opUid, opName, force);

        VersionCheckUtil.versionCheck(customerId, 0);
        WmCustomerBatchAuditBo wmCustomerBatchAuditBo = new WmCustomerBatchAuditBo();
        //修改为从主库查询客户信息
        WmCustomerDB wmCustomerDB = wmCustomerPlatformDataParseService.selectCustomerFromPlatformByIdRT(customerId);
        logger.info("oldWmCustomerDb = " + JSONObject.toJSONString(wmCustomerDB));
        if (wmCustomerDB == null) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "客户ID无法查到有效数据");
        }
        //存在同步过来的数据没有完善，然后在绑定门店或资质提审时发起的提审，此时判断资质编号，如果为空，则不提审
        boolean isCheckInfo = checkInfo(wmCustomerDB, wmCustomerBatchAuditBo);
        if (!isCheckInfo) {
            return wmCustomerBatchAuditBo;
        }
        if (wmCustomerDB.getAuditStatus() == CustomerAuditStatus.CUSTOMER_AUDIT_NEW.getCode() && wmCustomerDB.getEffective() == CustomerConstants.UNEFFECT) {
            logger.info("客户未提审，开始提审");
            wmCustomerAuditDBMapper.invalidCustomerAudit(customerId);
            // 客户平台图片加工
            String auditPicUrl = mtCustomerThriftServiceAdapterImpl.transferAuditCustomerPicUrl(wmCustomerDB.getPicUrl());
            if (auditPicUrl.contains(WmCustomerConstant.URL_CUSTOMER_PLATFORM_PREFIX)) {
                auditPicUrl = mtCustomerThriftServiceAdapterImpl.uploadImgFromCustomerPlatform(customerId, auditPicUrl);
            }
            wmCustomerDB.setPicUrl(auditPicUrl);
            Integer bizId = insertCustomerAudit(wmCustomerDB, force ? CustomerConstants.BATCH_SUBMIT_NO : CustomerConstants.BATCH_SUBMIT_YES, opUid, opName);
            Integer resultCode = commitAudit(wmCustomerDB, bizId, opUid, opName, force);
            if (resultCode == 0) {
                logger.error("客户信息提审异常,oldWmCustomerDb={}", JSONObject.toJSONString(wmCustomerDB));
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "客户信息提审异常");
            }
            wmCustomerBatchAuditBo.setBatchCommit(!force);
            wmCustomerBatchAuditBo.setBizId(bizId);
            //如果重复提审或异常则不记日志
            if (resultCode != -1) {
                //插入提审日志
                insertCustomerOpLog(wmCustomerDB.getId(), opUid, opName, WmCustomerOplogBo.OpType.CHANGESTATUS, CustomerConstants.CUSTOMER_LOG_TEMPLATE_COMMIT_AUDIT);
            }
        } else {
            //修改为查主库
            WmCustomerAuditDB wmCustomerAuditDB = wmCustomerAuditDBMapper.selectCustomerAuditByCustomerIdRT(customerId);
            if (wmCustomerAuditDB == null) {
                wmCustomerBatchAuditBo.setBatchCommit(false);
            } else {
                wmCustomerBatchAuditBo.setBizId(wmCustomerAuditDB.getId());
                if (wmCustomerAuditDB.getBatchSubmit() == CustomerConstants.BATCH_SUBMIT_YES) {
                    wmCustomerBatchAuditBo.setBatchCommit(true);
                } else {
                    wmCustomerBatchAuditBo.setBatchCommit(false);
                }
                logger.info("客户已提审，返回提审业务号,bizId={},batchSubmit={}", wmCustomerAuditDB.getId(), wmCustomerAuditDB.getBatchSubmit());
            }
        }

        if (wmCustomerDB.getCustomerType() == CustomerType.CUSTOMER_TYPE_IDCARD.getCode()) {
            wmCustomerBatchAuditBo.setBizType(WmAuditTaskBizTypeConstant.CUSTOMER_QUALI_ID_CARD);
        } else if (wmCustomerDB.getCustomerType() == CustomerType.CUSTOMER_TYPE_BUSINESS.getCode()) {
            wmCustomerBatchAuditBo.setBizType(WmAuditTaskBizTypeConstant.CUSTOMER_QUALI_BUSINESS);
        }
        return wmCustomerBatchAuditBo;
    }

    /**
     * 存在同步过来的数据没有完善，然后在绑定门店或资质提审时发起的提审，此时判断资质编号，如果为空，则不提审
     *
     * @param wmCustomerDB
     * @param wmCustomerBatchAuditBo
     * @return
     */
    private boolean checkInfo(WmCustomerDB wmCustomerDB, WmCustomerBatchAuditBo wmCustomerBatchAuditBo) {
        if (CustomerType.CUSTOMER_TYPE_BUSINESS.getCode() == wmCustomerDB.getCustomerType()) {
            boolean isInfoEmpty = StringUtils.isEmpty(wmCustomerDB.getCustomerName()) || StringUtils.isEmpty(wmCustomerDB.getCustomerNumber())
                    || StringUtils.isEmpty(wmCustomerDB.getPicUrl()) || StringUtils.isEmpty(wmCustomerDB.getLegalPerson())
                    || StringUtils.isEmpty(wmCustomerDB.getAddress());
            if (isInfoEmpty) {
                logger.info("客户wmCustomerDB={},类型为营业执照，为同步未完善数据，不能提审", JSONObject.toJSONString(wmCustomerDB));
                wmCustomerBatchAuditBo.setBatchCommit(false);
                return false;
            }
        } else if (CustomerType.CUSTOMER_TYPE_IDCARD.getCode() == wmCustomerDB.getCustomerType()) {
            if (StringUtils.isEmpty(wmCustomerDB.getCustomerName()) || StringUtils.isEmpty(wmCustomerDB.getCustomerNumber())
                    || StringUtils.isEmpty(wmCustomerDB.getPicUrl())) {
                logger.info("客户wmCustomerDB={},类型为身份证，为同步未完善数据，不能提审", JSONObject.toJSONString(wmCustomerDB));
                wmCustomerBatchAuditBo.setBatchCommit(false);
                return false;
            }
        } else if (CustomerType.CUSTOMER_TYPE_BUSINESS_ABROAD.getCode() == wmCustomerDB.getCustomerType()) {
            List<Integer> registryStateList = JSONObject.parseArray(MccCustomerConfig.getB2CRegistryState(), Integer.class);
            boolean isInfoEmpty = StringUtils.isBlank(wmCustomerDB.getPicUrl()) || StringUtils.isBlank(wmCustomerDB.getAddress()) || StringUtils.isBlank(wmCustomerDB.getLegalPerson())
                    || StringUtils.isBlank(wmCustomerDB.getBusinessScope()) || wmCustomerDB.getRegistryState() == null
                    || (!CollectionUtils.isEmpty(registryStateList) && !registryStateList.contains(wmCustomerDB.getRegistryState()))
                    || StringUtils.isBlank(wmCustomerDB.getCustomerName()) || StringUtils.isBlank(wmCustomerDB.getCustomerNumber());
            if (isInfoEmpty) {
                logger.info("客户wmCustomerDB={},类型为海外营业执照，为同步未完善数据，不能提审", JSONObject.toJSONString(wmCustomerDB));
                wmCustomerBatchAuditBo.setBatchCommit(false);
                return false;
            }
        }
        return true;
    }

    /**
     * 提交审核
     */
    @Override
    public Integer commitAuditHeron(Integer customerId, Integer opUid, String opName, Boolean force) throws WmCustomerException {
        try {
            logger.info("千鹭客户提审参数：customerId={},opUid={},opName={},force={}", customerId, opUid, opName, force);
            WmCustomerDB wmCustomerDB = wmCustomerPlatformDataParseService.selectCustomerFromPlatformByIdRT(customerId);
            logger.info("oldWmCustomerDb = " + JSONObject.toJSONString(wmCustomerDB));

            if (wmCustomerDB == null) {
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "客户ID无法查到有效数据");
            }
            // 客户平台图片加工
            String auditPicUrl = mtCustomerThriftServiceAdapterImpl.transferAuditCustomerPicUrl(wmCustomerDB.getPicUrl());
            if (auditPicUrl.contains(WmCustomerConstant.URL_CUSTOMER_PLATFORM_PREFIX)) {
                auditPicUrl = mtCustomerThriftServiceAdapterImpl.uploadImgFromCustomerPlatform(customerId, auditPicUrl);
            }
            wmCustomerDB.setPicUrl(auditPicUrl);
            Integer bizId = insertCustomerAudit(wmCustomerDB, force ? CustomerConstants.BATCH_SUBMIT_NO : CustomerConstants.BATCH_SUBMIT_YES, opUid, opName);

            //提审对象
            WmAuditCommitObj wmAuditCommitObj = new WmAuditCommitObj();
            WmAuditQualificationObj wmAuditQualificationObj = WmCustomerTransUtil.customerDBToCommitData(wmCustomerDB);
            wmAuditQualificationObj.setBatchSubmit(!force);
            //填充客户复用标识
            int customerMultiplexFlag = Optional.ofNullable(wmCustomerDB.getMultiplex()).orElse(CustomerMultiplexEnum.NO.getType());
            wmAuditQualificationObj.setReuseCustomer(customerMultiplexFlag);
            wmAuditCommitObj.setBiz_type(wmAuditQualificationObj.getSubType())
                    .setBiz_id(bizId)
                    .setCustomer_id(wmCustomerDB.getId())
                    .setSubmit_uid(opUid == 0 ? WmAuditSubmitUidConstant.CONTRACT_SYSTEM.getType() : opUid)
                    .setData(JSONObject.toJSONString(wmAuditQualificationObj));

            logger.info("客户提审,wmAuditCommitObj={}", JSONObject.toJSONString(wmAuditCommitObj));
            WmAuditMsg wmAuditMsg = wmAuditApiService.commitAudit(wmAuditCommitObj);
            // 客户提审埋点
            try {
                MetricHelper.build()
                        .name(MetricConstant.METRIC_CUSTOMER_COMMIT_AUDIT_COUNT)
                        .tag("biz_type", String.valueOf(wmAuditQualificationObj.getSubType()))
                        .tag("success", String.valueOf(wmAuditMsg.isResult()))
                        .count();
            }catch (Exception e){
                logger.warn("metricCustomerAudit error", e);
            }
            wmCustomerDB.setAuditStatus(CustomerAuditStatus.CUSTOMER_AUDIT_AUDITING.getCode());

            //提审需要通知上单状态机
            wmCustomerService.updateCustomerAuditStatus(wmCustomerDB);
            wmCustomerPoiService.insertPoiStateCenterByCustomerId(wmCustomerDB.getId(), CustomerModuleStateEnum.AUDITING, opUid, opName);
            return wmAuditMsg.getCode();
        } catch (Exception e) {
            logger.info("客户信息提审异常", e);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "客户信息提审异常");
        }
    }


    /**
     * 提交审核
     */
    @Override
    public Integer commitAudit(WmCustomerDB wmCustomerDB, Integer bizId, Integer opUid, String opName, Boolean force) throws WmCustomerException {
        try {
            // 对重新建店的客户修改直接跳过提审
            String forceUpdate = Tracer.getContext(CustomerConstants.FORCE_UPDATE_KEY);
            Boolean forceUpdateBoolean = new Boolean(forceUpdate);
            if (MccCustomerConfig.getForceUpdateSwitchForReBuild() && forceUpdateBoolean && Objects.nonNull(wmCustomerDB.getCreateSource()) && wmCustomerDB.getCreateSource().equals(CustomerSource.WAIMAI_REBUILD.getCode())) {
                // 命中重新建店 & 强制提审 构造审批通过记录
                logger.info("命中重新建店 & 强制提审, 跳过提审, customerId = {}", wmCustomerDB.getId());
                WmCustomerAuditBo wmCustomerAuditBo = new WmCustomerAuditBo();
                wmCustomerAuditBo.setCustomerId(wmCustomerDB.getId());
                wmCustomerAuditBo.setBizId(bizId);
                wmCustomerAuditBo.setOpUid(opUid);
                wmCustomerAuditBo.setOpName(opName);
                wmCustomerAuditBo.setAuditStatus(WmCustomerAuditBo.AuditStatusEnum.AUDIT_STATUS_PASS);
                wmCustomerDB.setAuditStatus(CustomerAuditStatus.CUSTOMER_AUDIT_AUDITING.getCode());
                wmCustomerService.updateCustomerAuditStatus(wmCustomerDB);

                auditCustomerCallBack(wmCustomerAuditBo);
                return 0;
            }
            WmAuditCommitObj wmAuditCommitObj = new WmAuditCommitObj();
            WmAuditQualificationObj wmAuditQualificationObj = WmCustomerTransUtil.customerDBToCommitData(wmCustomerDB);
            //美食城类型客户提审数据封装
            if (Integer.valueOf(CustomerRealTypeEnum.MEISHICHENG.getValue()).equals(wmCustomerDB.getCustomerRealType())) {
                CustomerRealTypeSpInfoBo customerRealTypeSpInfoBo = JSONObject.parseObject(wmCustomerDB.getCustomerRealTypeSpInfo(), CustomerRealTypeSpInfoBo.class);
                if (customerRealTypeSpInfoBo != null) {
                    wmAuditQualificationObj.setFoodCityUrl(customerRealTypeSpInfoBo.getFoodCityPic());
                    wmAuditQualificationObj.setFoodCityName(customerRealTypeSpInfoBo.getFoodCityName());
                    wmAuditQualificationObj.setAorId(customerRealTypeSpInfoBo.getFoodCityAorId());
                    WmUniAor wmUniAor = wmAorServiceAdapter.getAorInfoById(customerRealTypeSpInfoBo.getFoodCityAorId());
                    if (wmUniAor != null) {
                        wmAuditQualificationObj.setAorName(wmUniAor.getName());
                        WmOpenCity wmOpenCity = wmAorServiceAdapter.getCityInfoWithWmUniAorType(WmUniAorType.fromCode(customerRealTypeSpInfoBo.getFoodCityAorBiz()), wmUniAor.getLocationInfos());
                        wmAuditQualificationObj.setCityName(wmOpenCity == null ? "" : wmOpenCity.getCityName());
                    } else {
                        wmAuditQualificationObj.setAorName("未知蜂窝");
                        wmAuditQualificationObj.setCityName("未知物理城市");
                    }

                    //美食城视频非空则提审需要传
                    if (StringUtils.isNotBlank(customerRealTypeSpInfoBo.getFoodCityVideo())) {
                        wmAuditQualificationObj.setFoodCityVideo(customerRealTypeSpInfoBo.getFoodCityVideo());
                    }
                    //美食城档口数非空则需要传
                    if (customerRealTypeSpInfoBo.getFoodCityPoiCount() != null && customerRealTypeSpInfoBo.getFoodCityPoiCount() > 0) {
                        wmAuditQualificationObj.setFoodCityPoiCount(customerRealTypeSpInfoBo.getFoodCityPoiCount());
                    }
                }
            }
            wmAuditQualificationObj.setBatchSubmit(!force);
            //填充客户复用标识
            int customerMultiplexFlag = Optional.ofNullable(wmCustomerDB.getMultiplex()).orElse(CustomerMultiplexEnum.NO.getType());
            wmAuditQualificationObj.setReuseCustomer(customerMultiplexFlag);
            wmAuditCommitObj.setBiz_type(wmAuditQualificationObj.getSubType())
                    .setBiz_id(bizId)
                    .setCustomer_id(wmCustomerDB.getId())
                    .setSubmit_uid(opUid == 0 ? WmAuditSubmitUidConstant.CONTRACT_SYSTEM.getType() : opUid)//同步任务提审的数据,操作人会为0,审核系统会拒绝该任务，所以改为合同系统提审
                    .setData(JSONObject.toJSONString(wmAuditQualificationObj));
            wmCustomerDB.setAuditStatus(CustomerAuditStatus.CUSTOMER_AUDIT_AUDITING.getCode());
            logger.info("客户提审,wmAuditCommitObj={}", JSONObject.toJSONString(wmAuditCommitObj));
            WmAuditMsg wmAuditMsg = wmAuditApiService.commitAudit(wmAuditCommitObj);
            // 提审埋点
            try{
            MetricHelper.build()
                    .name(MetricConstant.METRIC_CUSTOMER_COMMIT_AUDIT_COUNT)
                    .tag("biz_type", String.valueOf(wmAuditQualificationObj.getSubType()))
                    .tag("success", String.valueOf(wmAuditMsg.isResult()))
                    .count();
            }catch (Exception e){
                logger.warn("metricCustomerAudit error", e);
            }
            wmCustomerDB.setAuditStatus(CustomerAuditStatus.CUSTOMER_AUDIT_AUDITING.getCode());
            wmCustomerService.updateCustomerAuditStatus(wmCustomerDB);
            //提审需要通知上单状态机
            wmCustomerPoiService.insertPoiStateCenterByCustomerId(wmCustomerDB.getId(), CustomerModuleStateEnum.AUDITING, opUid, opName);
            return wmAuditMsg.getCode();
        } catch (Exception e) {
            logger.info("客户信息提审异常", e);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "客户信息提审异常");
        }

    }

    /**
     * 插入客户操作日志
     */
    @Override
    public void insertCustomerOpLog(Integer customerId, Integer userId, String userName, WmCustomerBasicBo wmCustomerBasicBo, WmCustomerDB sourceCustomer, WmCustomerOplogBo.OpType opType) throws WmCustomerException, TException {
        try {
            //如果原客户数据不传，则创建个空对象，比对出新增变更的值
            if (sourceCustomer == null) {
                sourceCustomer = new WmCustomerDB();
            }
            if (wmCustomerBasicBo.getWmCoStatus() == null) {
                wmCustomerBasicBo.setWmCoStatus(sourceCustomer.getWmCoStatus());
            }
            String log = "";
            if (wmCustomerBasicBo.getCustomerType() == CustomerType.CUSTOMER_TYPE_BUSINESS.getCode()) {
                log = BeanDiffUtil.diffContentByMap(sourceCustomer, wmCustomerBasicBo, WmLogDiffConstant.CUSTOMER_BUSINESS_MAP);
                String customerRealTypeSpInfoBoDiffLog = BeanDiffUtil.diffContentByMap(genCustomerRealTypeSpInfoBoDiff(sourceCustomer), genCustomerRealTypeSpInfoBoDiff(wmCustomerBasicBo), WmLogDiffConstant.CUSTOMER_REAL_TYPE_SP_INFO_MAP);
                if (StringUtils.isNotEmpty(customerRealTypeSpInfoBoDiffLog)) {
                    log = new StringBuilder(log).append(customerRealTypeSpInfoBoDiffLog).toString();
                }
            } else if (wmCustomerBasicBo.getCustomerType() == CustomerType.CUSTOMER_TYPE_IDCARD.getCode()) {
                log = BeanDiffUtil.diffContentByMap(sourceCustomer, wmCustomerBasicBo, WmLogDiffConstant.CUSTOMER_IDCARD_MAP);
                //操作日志拼接客户场景日志
                if (wmCustomerBasicBo.getSceneInfoBO() != null) {
                    String customerSceneDiffLog = BeanDiffUtil.diffContentByMap(StringUtils.isBlank(sourceCustomer.getSceneInfo()) ? new CustomerSceneInfoBODiff() : JSON.parseObject(sourceCustomer.getSceneInfo(), CustomerSceneInfoBODiff.class),
                            wmCustomerBasicBo.getSceneInfoBO() == null ? new CustomerSceneInfoBODiff() : JSON.parseObject(JSON.toJSONString(wmCustomerBasicBo.getSceneInfoBO()), CustomerSceneInfoBODiff.class), WmLogDiffConstant.CUSTOMER_SCENE_INFO_MAP);
                    log = new StringBuilder(log).append(customerSceneDiffLog).toString();
                }
            } else if (wmCustomerBasicBo.getCustomerType() == CustomerType.CUSTOMER_TYPE_BUSINESS_ABROAD.getCode()) {
                log = BeanDiffUtil.diffContentByMap(sourceCustomer, wmCustomerBasicBo, WmLogDiffConstant.CUSTOMER_BUSINESS_ABROAD_MAP);
            }

            if (CustomerSource.WAIMAI_REBUILD == wmCustomerBasicBo.getCustomerSource()) {
                StringBuilder logBuilder = new StringBuilder(log)
                        .append(CUSTOMER_LOG_CUSTOMER_SOURCE_REBUILD);

                boolean isForceUpdate = Boolean.parseBoolean(Tracer.getContext("forceUpdate"));
                logBuilder.append(isForceUpdate ? CUSTOMER_LOG_FORCE_UPDATE : CUSTOMER_LOG_NOT_FORCE_UPDATE);

                log = logBuilder.toString();
            }
            insertCustomerOpLog(customerId, userId, userName, opType, log);
        } catch (Exception e) {
            logger.error("AbstractWmCustomerRealService.insertCustomerOpLog,新增客户操作记录发生异常，customerId={}", customerId, e);
        }

    }

    public CustomerRealTypeSpInfoBoDiff genCustomerRealTypeSpInfoBoDiff(CustomerRealTypeSpInfoBo customerRealTypeSpInfoBo) {
        if (customerRealTypeSpInfoBo == null) {
            return new CustomerRealTypeSpInfoBoDiff();
        }
        CustomerRealTypeSpInfoBoDiff result = new CustomerRealTypeSpInfoBoDiff();
        BeanUtils.copyProperties(customerRealTypeSpInfoBo, result);
        //补全相关信息
        WmUniAor wmUniAor = wmAorServiceAdapter.getAorInfoById(result.getFoodCityAorId());
        if (wmUniAor != null) {
            result.setFoodCityAorName(wmUniAor.getName());
            WmUniAorLocationInfo locationInfos = wmUniAor.getLocationInfos();
            WmOpenCity wmOpenCity = wmAorServiceAdapter.getCityInfoWithWmUniAorType(WmUniAorType.fromCode(result.getFoodCityAorBiz()), locationInfos);
            if (wmOpenCity != null) {
                result.setFoodCityLocation(wmOpenCity.getCityName());
            }
        }
        return result;
    }

    public CustomerRealTypeSpInfoBoDiff genCustomerRealTypeSpInfoBoDiff(WmCustomerDB wmCustomerDB) {
        if (wmCustomerDB == null) {
            return null;
        }
        String customerRealTypeSpInfo = wmCustomerDB.getCustomerRealTypeSpInfo();
        if (StringUtils.isEmpty(customerRealTypeSpInfo)) {
            return new CustomerRealTypeSpInfoBoDiff();
        }
        CustomerRealTypeSpInfoBo customerRealTypeSpInfoBo = JSONObject.parseObject(customerRealTypeSpInfo, CustomerRealTypeSpInfoBo.class);
        return genCustomerRealTypeSpInfoBoDiff(customerRealTypeSpInfoBo);
    }

    public CustomerRealTypeSpInfoBoDiff genCustomerRealTypeSpInfoBoDiff(WmCustomerAuditDB wmCustomerAuditDB) {
        if (wmCustomerAuditDB == null) {
            return null;
        }
        String customerRealTypeSpInfo = wmCustomerAuditDB.getCustomerRealTypeSpInfo();
        if (StringUtils.isEmpty(customerRealTypeSpInfo)) {
            return new CustomerRealTypeSpInfoBoDiff();
        }
        CustomerRealTypeSpInfoBo customerRealTypeSpInfoBo = JSONObject.parseObject(customerRealTypeSpInfo, CustomerRealTypeSpInfoBo.class);
        return genCustomerRealTypeSpInfoBoDiff(customerRealTypeSpInfoBo);
    }

    public CustomerRealTypeSpInfoBoDiff genCustomerRealTypeSpInfoBoDiff(WmCustomerBasicBo wmCustomerBasicBo) {
        if (wmCustomerBasicBo == null) {
            return null;
        }
        return genCustomerRealTypeSpInfoBoDiff(wmCustomerBasicBo.getCustomerRealTypeSpInfoBo());
    }

    /**
     * 插入客户操作日志
     *
     * @param customerId
     * @param userId
     * @param userName
     * @param opType
     * @param log
     * @throws WmCustomerException
     * @throws TException
     */
    @Override
    public void insertCustomerOpLog(Integer customerId, Integer userId, String userName, WmCustomerOplogBo.OpType opType, String log) throws WmCustomerException {
        insertCustomerOpLog(customerId, userId, userName, opType, log, "");
    }

    /**
     * 插入客户操作日志
     */
    @Override
    public void insertCustomerOpLog(Integer customerId, Integer userId, String userName, WmCustomerOplogBo.OpType opType, String log, String remark) throws WmCustomerException {
        if (StringUtils.isBlank(log)) {
            return;
        }
        WmCustomerOplogBo wmCustomerOplogBo = new WmCustomerOplogBo(customerId, WmCustomerOplogBo.OpModuleType.CUSTOMER, customerId);
        wmCustomerOplogBo.setOpType(opType.type);
        wmCustomerOplogBo.setLog(log);
        wmCustomerOplogBo.setOpUid(userId);
        wmCustomerOplogBo.setOpUname(userName);
        wmCustomerOplogBo.setRemark(remark);
        wmCustomerOplogService.insert(wmCustomerOplogBo);
    }


    /**
     * 比较客户正式表和审核表，获取diff
     */
    public DiffInfo compareDiff(WmCustomerDB wmCustomerDB, WmCustomerAuditDB wmCustomerAuditDB) throws WmCustomerException {
        DiffInfo diffInfo = null;
        //未生效的数据驳回无需比对
        if (wmCustomerDB.getEffective() == CustomerConstants.EFFECT) {
            Map<String, String> compareMap = null;
            Map<String, String> customerRealTypeSpInfoCompareMap = Maps.newHashMap();
            if (wmCustomerDB.getCustomerType() == CustomerType.CUSTOMER_TYPE_BUSINESS.getCode()) {
                compareMap = CustomerConstants.customerCompare;
                customerRealTypeSpInfoCompareMap = CustomerConstants.customerCompareCustomerRealTypeSpInfo;
            } else if (wmCustomerDB.getCustomerType() == CustomerType.CUSTOMER_TYPE_BUSINESS_ABROAD.getCode()) {
                compareMap = CustomerConstants.customerCompare;
            } else if (wmCustomerDB.getCustomerType() == CustomerType.CUSTOMER_TYPE_IDCARD.getCode()) {
                compareMap = CustomerConstants.customerIDCARDCompare;
            }
            // 客户平台图片特殊处理
            wmCustomerAuditDB.setPicUrl(mtCustomerThriftServiceAdapterImpl.transferMtCustomerPicUrl(wmCustomerAuditDB.getPicUrl()));
            List<WmCustomerDiffCellBo> diffList = DiffUtil.compare(wmCustomerDB, wmCustomerAuditDB, compareMap);
            List<WmCustomerDiffCellBo> customerRealTypeSpInfoDiffList = DiffUtil.compare(genCustomerRealTypeSpInfoBoDiff(wmCustomerDB), genCustomerRealTypeSpInfoBoDiff(wmCustomerAuditDB), customerRealTypeSpInfoCompareMap);
            diffList.addAll(customerRealTypeSpInfoDiffList);
            if (!diffList.isEmpty()) {
                diffInfo = new DiffInfo();
                diffInfo.setDate(wmCustomerAuditDB.getCtime());
                List<WmCustomerDiffProperty> diffContent = Lists.newArrayList();
                for (WmCustomerDiffCellBo wmCustomerDiffCellBo : diffList) {
                    Integer diffFieldType = CustomerConstants.DIFF_FIELD_TYPE_STRING;
                    if (CustomerConstants.CUSTOMER_FIELD_PICURL.equals(wmCustomerDiffCellBo.getField()) || CustomerConstants.CUSTOMER_FIELD_FOOD_CITY_PIC.equals(wmCustomerDiffCellBo.getField())) {
                        diffFieldType = CustomerConstants.DIFF_FIELD_TYPE_IMAGE;
                    }
                    buidDiffShow(wmCustomerDiffCellBo, diffContent, diffFieldType);
                }
                diffInfo.setContent(diffContent);
            }
        }
        return diffInfo;
    }


    private void buidDiffShow(WmCustomerDiffCellBo wmCustomerDiffCellBo, List<WmCustomerDiffProperty> diffContent, Integer diffFieldType) {
        //有效期需要处理展现
        if (wmCustomerDiffCellBo.getField() == CustomerConstants.CUSTOMER_FIELD_VALIDATEDATE) {
            String value;
            if ("0".equals(wmCustomerDiffCellBo.getAft())) {
                value = "长期";
            } else {
                value = DateUtil.seconds2TimeFormat(Long.parseLong(wmCustomerDiffCellBo.getAft()), "yyyy-MM-dd");
            }
            diffContent.add(new WmCustomerDiffProperty(wmCustomerDiffCellBo.getField(), wmCustomerDiffCellBo.getDesc(), diffFieldType, value));
        } else if (wmCustomerDiffCellBo.getField() == CustomerConstants.CUSTOMER_FIELD_HQ_SECOND_CITY_ID) {
            //查询城市名字
            String value = "";
            if (!"0".equals(wmCustomerDiffCellBo.getAft())) {
                WmOpenCity city = cityCommonServiceAdapter.getCity(Integer.valueOf(wmCustomerDiffCellBo.getAft()));
                value = city == null ? "" : city.getCityName();
            }
            diffContent.add(new WmCustomerDiffProperty(wmCustomerDiffCellBo.getField(), wmCustomerDiffCellBo.getDesc(), diffFieldType, value));
        } else if (wmCustomerDiffCellBo.getField() == CustomerConstants.CUSTOMER_FIELD_CUSTOMERREALTYPE) {
            // 客户类型处理展现
            String value = "";
            if (StringUtils.isNotBlank(wmCustomerDiffCellBo.getAft()) && !"0".equals(wmCustomerDiffCellBo.getAft())) {
                CustomerRealTypeEnum customerRealTypeEnum = CustomerRealTypeEnum.getByValue(Integer.valueOf(wmCustomerDiffCellBo.getAft()));
                value = customerRealTypeEnum == null ? "" : customerRealTypeEnum.getName();
            }
            diffContent.add(new WmCustomerDiffProperty(wmCustomerDiffCellBo.getField(), wmCustomerDiffCellBo.getDesc(), diffFieldType, value));
        } else if (wmCustomerDiffCellBo.getField() == CustomerConstants.CUSTOMER_FIELD_CUSTOMER_TYPE) {
            // 客户资质主体处理展现
            String value = "";
            if (StringUtils.isNotBlank(wmCustomerDiffCellBo.getAft()) && !"0".equals(wmCustomerDiffCellBo.getAft())) {
                CustomerType customerType = CustomerType.getByCode(Integer.valueOf(wmCustomerDiffCellBo.getAft()));
                value = customerType == null ? "" : customerType.getDesc();
            }
            diffContent.add(new WmCustomerDiffProperty(wmCustomerDiffCellBo.getField(), wmCustomerDiffCellBo.getDesc(), diffFieldType, value));
        } else if (wmCustomerDiffCellBo.getField() == CustomerConstants.CUSTOMER_FIELD_CUSTOMER_SECOND_TYPE) {
            // 客户资质主体处理展现
            String value = "";
            if (StringUtils.isNotBlank(wmCustomerDiffCellBo.getAft()) && !"0".equals(wmCustomerDiffCellBo.getAft())) {
                CertTypeEnum certTypeEnum = CertTypeEnum.getByType(Integer.valueOf(wmCustomerDiffCellBo.getAft()));
                value = certTypeEnum == null ? "" : certTypeEnum.getName();
            }
            diffContent.add(new WmCustomerDiffProperty(wmCustomerDiffCellBo.getField(), wmCustomerDiffCellBo.getDesc(), diffFieldType, value));
        } else if (wmCustomerDiffCellBo.getField() == CustomerConstants.CUSTOMER_FIELD_SIGN_MODE) {
            // 客户签约形式处理展现
            String value = "";
            if (StringUtils.isNotBlank(wmCustomerDiffCellBo.getAft()) && !"0".equals(wmCustomerDiffCellBo.getAft())) {
                CustomerSignMode customerSignMode = CustomerSignMode.getByCode(Integer.valueOf(wmCustomerDiffCellBo.getAft()));
                value = customerSignMode == null ? "" : customerSignMode.getDesc();
            }
            diffContent.add(new WmCustomerDiffProperty(wmCustomerDiffCellBo.getField(), wmCustomerDiffCellBo.getDesc(), diffFieldType, value));
        } else if (wmCustomerDiffCellBo.getField() == CustomerConstants.CUSTOMER_FIELD_CERTIFICATE_TYPE) {
            // 客户签约形式处理展现
            String value = "";
            if (StringUtils.isNotBlank(wmCustomerDiffCellBo.getAft()) && !"0".equals(wmCustomerDiffCellBo.getAft())) {
                CertificateTypeEnum certificateTypeEnum = CertificateTypeEnum.getByType(Integer.valueOf(wmCustomerDiffCellBo.getAft()));
                value = certificateTypeEnum == null ? "" : certificateTypeEnum.getName();
            }
            diffContent.add(new WmCustomerDiffProperty(wmCustomerDiffCellBo.getField(), wmCustomerDiffCellBo.getDesc(), diffFieldType, value));
        } else if (wmCustomerDiffCellBo.getField() == CustomerConstants.CUSTOMER_FIELD_REGISTRY_STATE) {
            // 客户注册国家/地区处理展现
            String value = "";
            if (StringUtils.isNotBlank(wmCustomerDiffCellBo.getAft()) && !"0".equals(wmCustomerDiffCellBo.getAft())) {
                RegionEnum regionEnum = RegionEnum.getByCode(Integer.valueOf(wmCustomerDiffCellBo.getAft()));
                value = regionEnum == null ? "" : regionEnum.getRegionName();
            }
            diffContent.add(new WmCustomerDiffProperty(wmCustomerDiffCellBo.getField(), wmCustomerDiffCellBo.getDesc(), diffFieldType, value));
        } else if (wmCustomerDiffCellBo.getField() == CustomerConstants.CUSTOMER_FIELD_FOOD_CITY_AOR_BIZ) {
            // 蜂窝业务线
            String value = "";
            if (StringUtils.isNotBlank(wmCustomerDiffCellBo.getAft()) && !"0".equals(wmCustomerDiffCellBo.getAft())) {
                WmUniAorType wmUniAorType = WmUniAorType.fromCode(Integer.valueOf(wmCustomerDiffCellBo.getAft()));
                value = wmUniAorType == null ? "" : wmUniAorType.aorTypeName();
            }
            diffContent.add(new WmCustomerDiffProperty(wmCustomerDiffCellBo.getField(), wmCustomerDiffCellBo.getDesc(), diffFieldType, value));
        } else {
            diffContent.add(new WmCustomerDiffProperty(wmCustomerDiffCellBo.getField(), wmCustomerDiffCellBo.getDesc(), diffFieldType, wmCustomerDiffCellBo.getAft()));
        }
    }

    /**
     * 分配责任人
     *
     * @param customerIdList
     * @param userId
     */
    @Override
    public void distributeCustomer(List<Integer> customerIdList, int userId, Integer opUid, String opName) throws WmCustomerException {
        logger.info("distributeCustomer(), customerIdList={} , userId={}, opUid={} ,opNanem={}", JSON.toJSONString(customerIdList), userId, opUid, opName);

        List<WmCustomerDB> list = wmCustomerPlatformDataParseService.getCustomerOwnUidList(customerIdList);
        //判断customerIdList 是否都存在，不存在则抛出异常
        List IllegalIdlist = disIllegalCustomerId(customerIdList, list);
        if (!CollectionUtils.isEmpty(IllegalIdlist)) {
            logger.info("客户分配责任人时，存在不合法的customerId={}", IllegalIdlist);
            throw new WmCustomerException(401, "存在不合法的customerId:" + IllegalIdlist);
        }

        wmCustomerPlatformDataParseService.distributeCustomer(customerIdList, userId);
        //批量记录操作日志
        List<WmCustomerOplogBo> wmCustomerOplogBoList = Lists.newArrayList();
        for (WmCustomerDB wmCustomerDB : list) {
            WmCustomerOplogBo wmCustomerOplogBo = new WmCustomerOplogBo(wmCustomerDB.getId(), WmCustomerOplogBo.OpModuleType.CUSTOMER, wmCustomerDB.getId());
            wmCustomerOplogBo.setOpType(WmCustomerOplogBo.OpType.UPDATE.type);
            wmCustomerOplogBo.setLog(String.format(CustomerConstants.CUSTOMER_LOG_TEMPLATE_DISTRIBUTE_CUSTOMER,
                    getUserAndId(wmCustomerDB.getOwnerUid()), getUserAndId(userId)));
            wmCustomerOplogBo.setOpUid(opUid);
            wmCustomerOplogBo.setOpUname(opName);
            wmCustomerOplogBoList.add(wmCustomerOplogBo);
        }
        wmCustomerOplogService.batchInsert(wmCustomerOplogBoList);

        //发送大象消息
        sendOwnerChangeMsgAsyn(list, userId, opUid);
    }

    /**
     * 客户责任人变更发送大象消息
     *
     * @param userId 新责任人
     * @param opUid  操作人id
     * @return
     */
    public boolean sendOwnerChangeMsg(List<WmCustomerDB> customerList, int userId, Integer opUid) {
        logger.info("sendOwnerChangeMsg(), customerList={}, userId={}, opUid={} ", JSON.toJSONString(customerList), userId, opUid);
        try {
            WmEmploy newOwner = wmEmployService.getById(userId);
            WmEmploy operator = wmEmployService.getById(opUid);

            for (WmCustomerDB wmCustomerDB : customerList) {
                sendOwnerChangeMsg(wmCustomerDB, newOwner, operator);
            }
        } catch (Exception e) {
            logger.error("责任人变更发送大象消息异常, customerList={},userId={},opUid={}", JSONObject.toJSONString(customerList), userId, opUid, e);
            return false;
        }
        logger.info("sendOwnerChangeMsg(),  return true");
        return true;
    }

    /**
     * 客户责任人变更发送大象消息, 异步的方式 。
     *
     * @param userId 新责任人
     * @param opUid  操作人id
     * @return
     */
    @Override
    public void sendOwnerChangeMsgAsyn(final List<WmCustomerDB> customerList, final int userId, final Integer opUid) {
        logger.info("sendOwnerChangeMsgAsyn(), customerList={}, userId={}, opUid={} ", JSON.toJSONString(customerList), userId, opUid);
        executorService.execute(new TraceRunnable(new Runnable() {
            @Override
            public void run() {
                try {
                    sendOwnerChangeMsg(customerList, userId, opUid);
                } catch (Exception e) {
                    logger.error("责任人变更发送大象消息异常, customerList={},userId={},opUid={}", JSONObject.toJSONString(customerList), userId, opUid, e);
                }
            }
        }));
    }

    /**
     *
     */
    @Override
    public boolean sendOwnerChangeMsg(WmCustomerDB wmCustomerDB, WmEmploy newOwner, WmEmploy operator) throws TException, WmServerException {
        WmEmploy originalOwner = wmEmployService.getById(wmCustomerDB.getOwnerUid());
        //1、校验参数;
        if (originalOwner == null) {
            logger.warn("sendOwnerChangeMsg 找不到对应的责任人，wmCustomerDB.getOwnerUid()={}", wmCustomerDB.getOwnerUid());
            return false;
        }
        if (originalOwner.getUid() == newOwner.getUid()) {
            //责任人无变更
            return true;
        }
        //2、发送给原门店责任人
        sendToOriginalOwner(wmCustomerDB, originalOwner, newOwner, operator);

        //3、发送给新门店责任人
        sendToNewOwner(wmCustomerDB, originalOwner, newOwner, operator);

        return true;
    }

    /**
     * 发送大象消息到原责任人
     *
     * @param wmCustomerDB
     * @param originalOwner
     * @param newOwner
     * @param operator      操作人
     * @return
     * @throws TException
     * @throws WmServerException
     */
    private boolean sendToOriginalOwner(WmCustomerDB wmCustomerDB, WmEmploy originalOwner, WmEmploy newOwner, WmEmploy operator) {
        String email = originalOwner.getMisId();
        StringBuffer msgBuffer = new StringBuffer();
        msgBuffer.append("【客户责任人变更】").append("\n");
        if (MccConfig.isDisplayMtCustomerId()) {
            msgBuffer.append(operator.getName() + "(" + operator.getMisId() + ")" + "已经将您负责的客户：" + wmCustomerDB.getCustomerName() + "(" + wmCustomerDB.getMtCustomerId() + ")" + "责任人变更为" + newOwner.getName() + "(" + newOwner.getMisId() + ")");
        } else {
            msgBuffer.append(operator.getName() + "(" + operator.getMisId() + ")" + "已经将您负责的客户：" + wmCustomerDB.getCustomerName() + "(" + wmCustomerDB.getId() + ")" + "责任人变更为" + newOwner.getName() + "(" + newOwner.getMisId() + ")");
        }
        DaxiangUtil.push("<EMAIL>", msgBuffer.toString(), email);
        logger.info("【原负责人】客户责任人变更通知：email = {},msg = {}", email, msgBuffer.toString());
        return true;
    }

    /**
     * 发送大象消息到新责任人
     *
     * @param wmCustomerDB
     * @param originalOwner
     * @param newOwner
     * @param operator      操作人
     * @return
     */
    private boolean sendToNewOwner(WmCustomerDB wmCustomerDB, WmEmploy originalOwner, WmEmploy newOwner, WmEmploy operator) {
        String email = newOwner.getMisId();
        StringBuffer msgBuffer = new StringBuffer();
        msgBuffer.append("【客户责任人变更】").append("\n");
        if (MccConfig.isDisplayMtCustomerId()) {
            msgBuffer.append(operator.getName() + "(" + operator.getMisId() + ")" + "已经将客户：" + wmCustomerDB.getCustomerName() + "(" + wmCustomerDB.getMtCustomerId() + ")" + "责任人变更为您，原责任人" + originalOwner.getName() + "(" + originalOwner.getMisId() + ")将不再负责");
        } else {
            msgBuffer.append(operator.getName() + "(" + operator.getMisId() + ")" + "已经将客户：" + wmCustomerDB.getCustomerName() + "(" + wmCustomerDB.getId() + ")" + "责任人变更为您，原责任人" + originalOwner.getName() + "(" + originalOwner.getMisId() + ")将不再负责");
        }
        DaxiangUtil.push("<EMAIL>", msgBuffer.toString(), email);
        logger.info("【新负责人】客户责任人变更通知：email = {},msg = {}", email, msgBuffer.toString());
        return true;
    }

    /**
     * 查找出不合法的customerId
     *
     * @param customerIdList
     * @param customerDBlist 数据库中的customer列表
     * @return
     */
    private List disIllegalCustomerId(List<Integer> customerIdList, List<WmCustomerDB> customerDBlist) {
        List<Integer> listRet = new ArrayList<Integer>();

        //数据库中的customerId列表
        List<Integer> customerIdDBList = new ArrayList();
        for (WmCustomerDB wmCustomerDB : customerDBlist) {
            customerIdDBList.add(wmCustomerDB.getId());
        }

        //查找出不在数据库的customerId列表
        for (Integer customerId : customerIdList) {
            if (!customerIdDBList.contains(customerId)) {
                listRet.add(customerId);
            }
        }
        return listRet;
    }


    /**
     * 变更客户责任人(员工离职)
     *
     * @param oldOwnerUid
     * @param newOwnerUid
     */
    @Override
    public void changeCustomerOwner(Integer oldOwnerUid, Integer newOwnerUid) throws WmCustomerException {
        List<Integer> customerIds = wmCustomerPlatformDataParseService.selectCustomerIdsByOwnerUid(oldOwnerUid);
        logger.info("变更客户责任人,customerIds={}", JSONObject.toJSONString(customerIds));
        batchChangeCustomerOwner(customerIds, oldOwnerUid, newOwnerUid);
    }

    private void batchChangeCustomerOwner(List<Integer> customerIds, Integer oldOwnerUid, Integer newOwnerUid) {
        if (CollectionUtils.isEmpty(customerIds)) {
            return;
        }
        ownerExecutorService.execute(new Runnable() {
            @Override
            public void run() {
                List<List<Integer>> customerList = Lists.partition(customerIds, 100);
                for (List<Integer> data : customerList) {
                    wmCustomerPlatformDataParseService.distributeCustomer(data, newOwnerUid);
                    Uninterruptibles.sleepUninterruptibly(500, TimeUnit.MILLISECONDS);
                }

                List<WmCustomerOplogBo> wmCustomerOplogBoList = Lists.newArrayList();
                for (Integer customerId : customerIds) {
                    WmCustomerOplogBo wmCustomerOplogBo = new WmCustomerOplogBo(customerId, WmCustomerOplogBo.OpModuleType.CUSTOMER, customerId);
                    wmCustomerOplogBo.setOpType(WmCustomerOplogBo.OpType.UPDATE.type);
                    wmCustomerOplogBo.setLog(String.format(CustomerConstants.CUSTOMER_LOG_TEMPLATE_DISTRIBUTE_CUSTOMER,
                            getUserAndId(oldOwnerUid), getUserAndId(newOwnerUid)));
                    wmCustomerOplogBo.setOpUid(0);
                    wmCustomerOplogBo.setOpUname(CustomerConstants.CUSTOMER_LOG_OP_NAME_SYSTEM);
                    wmCustomerOplogBoList.add(wmCustomerOplogBo);
                }
                try {
                    wmCustomerOplogService.batchInsert(wmCustomerOplogBoList);
                } catch (WmCustomerException e) {
                    logger.warn(e.getMessage(), e);
                }
                try {
                    wmPoiSwitchThriftService.changeCreator(oldOwnerUid, newOwnerUid);
                } catch (TException | WmPoiBizException e) {
                    logger.error(e.getMessage(), e);
                }
            }
        });
    }

    private String getUserAndId(Integer uid) {
        if (uid == null || uid == 0) {
            return "(" + uid + ")";
        }
        WmEmploy wmEmploy = wmEmployClient.getByIdWithoutException(uid);
        return wmEmploy.getName() + "(" + wmEmploy.getMisId() + ")";
    }

    @Override
    public void changeCustomerOwnerUidByWmPoi(Long wmPoiId, int ownerUid, int opUid, String opUname) throws WmCustomerException {
        Set<Integer> cusIds = wmCustomerPoiService.selectCustomerIdByPoiId(Sets.newHashSet(wmPoiId));
        if (CollectionUtils.isEmpty(cusIds)) {
            logger.info("该门店未绑定客户，客户不需要变更责任人 wmPoiId：{}", wmPoiId);
            return;
        }
        for (Integer cusId : cusIds) {
            Integer poiCnt = WmCustomerPoiAggre.Factory.make().countCustomerPoi(cusId);
            logger.info("客户门店数：{}  cusId：{}", poiCnt, cusId);
            if (poiCnt != null && Integer.valueOf(1).compareTo(poiCnt) < 0) {
                logger.info("多店客户，不更新责任人 cusId:{}", cusId);
                continue;
            }
            WmCustomerDB customerDB = wmCustomerPlatformDataParseService.selectCustomerFromPlatformById(cusId);
            Integer oldOwnerId = customerDB.getOwnerUid();
            wmCustomerPlatformDataParseService.distributeCustomer(Lists.newArrayList(cusId), ownerUid);
            WmCustomerOplogBo wmCustomerOplogBo = new WmCustomerOplogBo(cusId, WmCustomerOplogBo.OpModuleType.CUSTOMER, cusId);
            wmCustomerOplogBo.setOpType(WmCustomerOplogBo.OpType.UPDATE.type);
            wmCustomerOplogBo.setLog(String.format(CustomerConstants.CUSTOMER_LOG_TEMPLATE_DISTRIBUTE_CUSTOMER,
                    getUserAndId(oldOwnerId), getUserAndId(ownerUid)));
            wmCustomerOplogBo.setOpUid(opUid);
            wmCustomerOplogBo.setOpUname(opUname);
            wmCustomerOplogService.insert(wmCustomerOplogBo);
        }
    }

    /**
     * 通过关键字查询客户列表(用于下拉提示)
     *
     * @param keyword
     * @param searchType
     */
    @Override
    public List<WmCustomerDB> selectCustomerListByKeyword(String keyword, Integer searchType, int isLeaf) throws WmCustomerException {

        boolean byEs = (int) (Math.random() * 100) + 1 < ConfigUtilAdapter.getInt("selectCustomerListByKeyword_es_percent", 0);
        if (byEs) {
            List<WmCustomerDB> wmCustomerDBList;
            try {
                wmCustomerDBList = wmCustomerESService.queryCustomerListByKeyword(searchType, keyword, isLeaf);
            } catch (Exception e) {
                logger.error("查询ES失败", e);
                return wmCustomerPlatformDataParseService.selectCustomerFromPlatformByKeyword(keyword, searchType, isLeaf);
            }
            return wmCustomerDBList;
        }
        return wmCustomerPlatformDataParseService.selectCustomerFromPlatformByKeyword(keyword, searchType, isLeaf);
    }

    /**
     * 通过客户Ids查询客户责任人
     *
     * @param customerIdList
     * @return
     */
    @Override
    public List<WmCustomerDB> selectCustomerOwnUidList(List<Integer> customerIdList) throws WmCustomerException {
        return wmCustomerPlatformDataParseService.getCustomerOwnUidList(customerIdList);
    }

    /**
     * 保存客户共用资质证明
     *
     * @param customerId
     * @param urlSet
     * @param otherUrlSet
     * @param opUid
     * @param opName
     * @throws WmCustomerException
     */
    @Override
    public void saveOrUpdateCustomerCommonQua(int customerId, Set<String> urlSet, Set<String> otherUrlSet, int opUid, String opName) throws WmCustomerException {
        logger.info("保存客户共用资质证明,customerId={},urlSet={},otherUrlSet={},opUid={},opName={}", customerId, JSONObject.toJSONString(urlSet), JSONObject.toJSONString(otherUrlSet), opUid, opName);
        WmCustomerOplogBo.OpType opType = null;
        //比对记录，如果没有修改则不处理
        boolean quaModify = true;
        Set<String> urlSourceSet = wmCustomerCommonQuaDBMapper.selectCommonQuaList(customerId);
        if (!CollectionUtils.isEmpty(urlSourceSet)) {
            opType = WmCustomerOplogBo.OpType.UPDATE;
            if (urlSet.size() == urlSourceSet.size() && CollectionUtils.isEmpty(Sets.difference(urlSourceSet, urlSet))) {
                quaModify = false;
            }
        } else {
            opType = WmCustomerOplogBo.OpType.INSERT;
        }
        WmCustomerOplogBo.OpType otherOpType = null;
        //比对记录，如果没有修改则不处理
        boolean otherModify = true;
        Set<String> urlOtherSourceSet = Sets.newHashSet(wmCustomerCommonQuaDBMapper.selectCommonQuaOtherList(customerId));
        if (!CollectionUtils.isEmpty(urlOtherSourceSet)) {
            otherOpType = WmCustomerOplogBo.OpType.UPDATE;
            if (otherUrlSet.size() == urlOtherSourceSet.size() && CollectionUtils.isEmpty(Sets.difference(urlOtherSourceSet, otherUrlSet))) {
                otherModify = false;
            }
        } else {
            otherOpType = WmCustomerOplogBo.OpType.INSERT;
        }
        //保存共用资质证明由于前端保存不区分新增/修改，所以先清空关联关系,再保存
        saveOrUpdateCustomerCommonQuaToDB(customerId, urlSet, otherUrlSet);
        if (quaModify) {
            if (!CollectionUtils.isEmpty(urlSourceSet) || !CollectionUtils.isEmpty(urlSet)) {
                insertCustomerOpLog(customerId, opUid, opName, opType, String.format(CustomerConstants.CUSTOMER_LOG_TEMPLATE_COMMON_QUA, ObjectUtils.defaultIfNull(StringUtils.join(urlSourceSet, CustomerConstants.SPLIT_SYMBOL), ""), StringUtils.join(urlSet, CustomerConstants.SPLIT_SYMBOL)));
            }
        }
        if (otherModify) {
            if (!CollectionUtils.isEmpty(urlOtherSourceSet) || !CollectionUtils.isEmpty(otherUrlSet)) {
                insertCustomerOpLog(customerId, opUid, opName, otherOpType, String.format(CustomerConstants.CUSTOMER_LOG_TEMPLATE_COMMON_QUA_OTHER, ObjectUtils.defaultIfNull(StringUtils.join(urlOtherSourceSet, CustomerConstants.SPLIT_SYMBOL), ""), StringUtils.join(otherUrlSet, CustomerConstants.SPLIT_SYMBOL)));
            }
        }
    }

    /**
     * 增加客户共用资质证明
     *
     * @param customerId
     * @param commonQuaUrlSet
     * @param otherUrlSet
     * @param opUid
     * @param opName
     * @throws WmCustomerException
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addCustomerCommonQuaAndOther(int customerId, Set<String> commonQuaUrlSet, Set<String> otherUrlSet, int opUid, String opName) throws WmCustomerException {

        //1、资质信息是否有修改
        if (!CollectionUtils.isEmpty(commonQuaUrlSet)) {
            Set<String> urlSourceSet = wmCustomerCommonQuaDBMapper.selectCommonQuaList(customerId);
            commonQuaUrlSet.addAll(urlSourceSet);
            int maxNumQuaUrl = MccConfig.getCustomerCommonQuaUrlMaxNum();
            if (commonQuaUrlSet.size() > maxNumQuaUrl) {
                throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "资质公用证明总量不能大于：" + maxNumQuaUrl);
            }
            if (!CollectionUtils.isEmpty(Sets.difference(commonQuaUrlSet, urlSourceSet))) {
                if(hitNewLogRecordFormat(customerId)) {
                    //获取diff链接拼成字符串
                    logger.info("客户id：{}【资质复用证】启用记录日志新格式", customerId);
                    String diffUrls = commonQuaUrlSet.stream().filter(cur -> !urlSourceSet.contains(cur)).collect(Collectors.joining(CustomerConstants.SPLIT_SYMBOL));
                    String log = CUSTOMER_LOG_COMMON_QUA_ADD + diffUrls;
                    insertCustomerOpLog(customerId, opUid, opName, WmCustomerOplogBo.OpType.UPDATE, log);
                }else {
                    insertCustomerOpLog(customerId, opUid, opName, WmCustomerOplogBo.OpType.UPDATE, String.format(CustomerConstants.CUSTOMER_LOG_TEMPLATE_COMMON_QUA, ObjectUtils.defaultIfNull(StringUtils.join(urlSourceSet, CustomerConstants.SPLIT_SYMBOL), ""), StringUtils.join(commonQuaUrlSet, CustomerConstants.SPLIT_SYMBOL)));

                }
            }
            wmCustomerCommonQuaDBMapper.deleteCommonQua(customerId);
            wmCustomerCommonQuaDBMapper.batchInsertCommonQua(customerId, commonQuaUrlSet);
        }

        //2、其他附件信息是否有修改
        if (!CollectionUtils.isEmpty(otherUrlSet)) {
            Set<String> urlOtherSourceSet = Sets.newHashSet(wmCustomerCommonQuaDBMapper.selectCommonQuaOtherList(customerId));
            otherUrlSet.addAll(urlOtherSourceSet);
            int maxNumOtherUrl = MccConfig.getCustomerOtherQuaUrlMaxNum();
            if (otherUrlSet.size() > maxNumOtherUrl) {
                throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "其他附件总量不能大于：" + maxNumOtherUrl);
            }
            if (!CollectionUtils.isEmpty(Sets.difference(otherUrlSet, urlOtherSourceSet))) {
                if(hitNewLogRecordFormat(customerId)){
                    //获取diff链接拼成字符串
                    logger.info("客户id：{}【资质其他证】启用记录日志新格式", customerId);
                    String diffUrls = otherUrlSet.stream().filter(cur ->!urlOtherSourceSet.contains(cur)).collect(Collectors.joining(CustomerConstants.SPLIT_SYMBOL));
                    String log = CUSTOMER_LOG_COMMON_QUA_OTHER_ADD+ diffUrls;
                    insertCustomerOpLog(customerId, opUid, opName, WmCustomerOplogBo.OpType.UPDATE, log);
                }else {
                    insertCustomerOpLog(customerId, opUid, opName, WmCustomerOplogBo.OpType.UPDATE, String.format(CustomerConstants.CUSTOMER_LOG_TEMPLATE_COMMON_QUA_OTHER, ObjectUtils.defaultIfNull(StringUtils.join(urlOtherSourceSet, CustomerConstants.SPLIT_SYMBOL), ""), StringUtils.join(otherUrlSet, CustomerConstants.SPLIT_SYMBOL)));
                }
            }
            wmCustomerCommonQuaDBMapper.deleteCommonQuaOther(customerId);
            wmCustomerCommonQuaDBMapper.batchInsertCommonQuaOther(customerId, Lists.newArrayList(otherUrlSet));
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateCustomerCommonQuaToDB(int customerId, Set<String> urlSet, Set<String> otherUrlSet) throws WmCustomerException {
        wmCustomerCommonQuaDBMapper.deleteCommonQua(customerId);
        if (!CollectionUtils.isEmpty(urlSet)) {
            wmCustomerCommonQuaDBMapper.batchInsertCommonQua(customerId, urlSet);
        }
        wmCustomerCommonQuaDBMapper.deleteCommonQuaOther(customerId);
        if (!CollectionUtils.isEmpty(otherUrlSet)) {
            wmCustomerCommonQuaDBMapper.batchInsertCommonQuaOther(customerId, Lists.newArrayList(otherUrlSet));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateCustomerCommonQuaOtherToDB(int customerId, Set<String> otherUrlSet) throws WmCustomerException {
        wmCustomerCommonQuaDBMapper.deleteCommonQuaOther(customerId);
        if (!CollectionUtils.isEmpty(otherUrlSet)) {
            wmCustomerCommonQuaDBMapper.batchInsertCommonQuaOther(customerId, Lists.newArrayList(otherUrlSet));
        }
    }


    /**
     * 获取客户共用资质证明列表
     *
     * @param customerId
     * @return
     */
    @Override
    public Set<String> getCustomerQuaList(Integer customerId) {
        return wmCustomerCommonQuaDBMapper.selectCommonQuaList(customerId);
    }

    /**
     * 获取客户其他附件列表
     *
     * @param customerId
     * @return
     */
    @Override
    public List<String> getCustomerQuaOtherList(Integer customerId) {
        return wmCustomerCommonQuaDBMapper.selectCommonQuaOtherList(customerId);
    }

    /**
     * 获取客户其他附件列表
     *
     * @param customerId
     * @return
     */
    @Override
    public Set<String> getCustomerQuaOtherListMaster(Integer customerId) {
        return wmCustomerCommonQuaDBMapper.selectCommonQuaOtherListMaster(customerId);
    }

    /**
     * 发送客户状态MQ
     *
     * @param customerId
     * @param customerMQEventEnum
     * @param wmPoiIdSet
     * @param opUid
     * @param opName
     */
    @Override
    public void sendCustomerStatusNoticeMQ(Integer customerId, CustomerMQEventEnum customerMQEventEnum, Set<Long> wmPoiIdSet, Integer opUid, String opName) {
        sendCustomerStatusNoticeMQWithExtension(customerId, customerMQEventEnum, wmPoiIdSet, opUid, opName, null);
    }

    @Override
    public void sendCustomerStatusNoticeMQWithExtension(Integer customerId, CustomerMQEventEnum customerMQEventEnum, Set<Long> wmPoiIdSet, Integer opUid, String opName, Map<String, Object> extension) {
        JSONObject object = new JSONObject();
        object.put("wmPoiIds", StringUtils.join(wmPoiIdSet, CustomerConstants.SPLIT_SYMBOL));
        object.put("opUid", opUid);
        object.put("opName", opName);
        //如果是解绑门店操作，则把结算id传入
        if (customerMQEventEnum.getCode() == CustomerMQEventEnum.CUSTOMER_UNBIND_POI.getCode() && MccConfig.isSendCustomerUnbindPoiMsgField()) {
            List<WmPoiIdAndSettleId> wmPoiIdAndSettleIdList = getSettleIdBywmPoiId(customerId, wmPoiIdSet);
            object.put("wmPoiIdAndSettleId", wmPoiIdAndSettleIdList);
        }
        if (MapUtils.isNotEmpty(extension)) {
            object.putAll(extension);
        }
        sendCustomerStatusNoticeMQ(customerId, customerMQEventEnum, object);
    }

    /**
     * 根据wmPoidId查询结算Id
     *
     * @param customerId
     * @param wmPoiIdSet
     * @return
     */
    private List<WmPoiIdAndSettleId> getSettleIdBywmPoiId(Integer customerId, Set<Long> wmPoiIdSet)  {
        List<WmPoiIdAndSettleId> listRet = new ArrayList<>();

        if (CollectionUtils.isEmpty(wmPoiIdSet)) {
            return listRet;
        }
        if(customerId % 100< MccCustomerConfig.settleDataTransferPercent()){
            logger.info("customerId:{}命中结算数据迁移灰度,启用金服结算查询接口",customerId);
            SettleInfoBatchQueryReq settleInfoBatchQueryReq = new SettleInfoBatchQueryReq();
            settleInfoBatchQueryReq.setIphPayMerchantNo(MccCustomerConfig.getIphPayMerchantNo());
            settleInfoBatchQueryReq.setCustomerId((long)customerId);
            settleInfoBatchQueryReq.setPoiIdList(new ArrayList<>(wmPoiIdSet));
            List<SettleInfoData> settleInfoData = Lists.newArrayList();
            try {
                 settleInfoData = merchantSettleQueryProxyServiceAdaptor.querySettleInfoByPoiIdListAndCustomerIdIncludeValid(settleInfoBatchQueryReq);
            } catch (WmCustomerException e) {
                logger.error("查询金服结算数据TException,如人工介入排查,入参信息:{}", JSONObject.toJSONString(settleInfoBatchQueryReq),e);
                throw new RuntimeException(e);
            }
            if (CollectionUtil.isNotEmpty(settleInfoData)) {
                settleInfoData.stream().
                        //过滤出当前客户id的数据
                        filter(cur -> cur.getCustomerId().equals((long)customerId))
                        .flatMap(
                                cur -> cur.getRelationPoiList().stream()
                                        .filter(wmPoiIdSet::contains)
                                        .map(poiId -> new WmPoiIdAndSettleId(poiId.intValue(), Integer.parseInt(cur.getBizSettleId())))
                        )
                        .forEach(listRet::add);
            }
            //开启流量对比
            if(MccCustomerConfig.settleDataTransferDiffFlag()){
                List<WmPoiSettleAuditedDB> oldRes = wmSettleService.getAllByContractIdAndWmPoiIdList(customerId, new ArrayList<>(wmPoiIdSet));
                List<WmPoiIdAndSettleId> oldData = Lists.newArrayList();
                for (WmPoiSettleAuditedDB settleAudited : oldRes) {
                    oldData.add(new WmPoiIdAndSettleId(settleAudited.getWm_poi_id(), settleAudited.getWm_settle_id()));
                }
                int oldResSize = CollectionUtils.isEmpty(oldData)? 0:oldData.size();
                int newSize = CollectionUtils.isEmpty(listRet)? 0:listRet.size();
                if(oldResSize != newSize ){
                    Cat.logEvent("settleAllDataTransfer","fail");
                    logger.warn("结算数据迁移,流量对比不通过,新逻辑返回结果:{},老逻辑返回结果:{},逻辑降级,启用老结果",JSONObject.toJSONString(listRet),JSONObject.toJSONString(oldData));
                    return oldData;
                }
                if(oldResSize>0 && newSize>0){
                    if(!oldData.containsAll(listRet) || !listRet.containsAll(oldData)){
                        Cat.logEvent("settleAllDataTransfer","fail");
                        logger.warn("结算数据迁移,流量对比不通过,新逻辑返回结果:{},老逻辑返回结果:{},逻辑降级,启用老结果",JSONObject.toJSONString(listRet),JSONObject.toJSONString(oldData));
                        return oldData;
                    }
                }
                Cat.logEvent("settleAllDataTransfer","success");
            }
            return listRet;
        }else {
            List<WmPoiSettleAuditedDB> settleAuditedList = wmSettleService.getAllByContractIdAndWmPoiIdList(customerId, new ArrayList<>(wmPoiIdSet));
            if (CollectionUtils.isEmpty(settleAuditedList)) {
                return new ArrayList<>();
            }

            List<WmPoiIdAndSettleId> res = new ArrayList<>();
            for (WmPoiSettleAuditedDB settleAudited : settleAuditedList) {
                res.add(new WmPoiIdAndSettleId(settleAudited.getWm_poi_id(), settleAudited.getWm_settle_id()));
            }
            return res;
        }
    }

    class WmPoiIdAndSettleId {


        public int getWmSettleId() {
            return wmSettleId;
        }

        public void setWmSettleId(int wmSettleId) {
            this.wmSettleId = wmSettleId;
        }

        public int getWmPoiId() {
            return wmPoiId;
        }

        public void setWmPoiId(int wmPoiId) {
            this.wmPoiId = wmPoiId;
        }

        public WmPoiIdAndSettleId(int wmPoiId, int wmSettleId) {
            this.wmPoiId = wmPoiId;
            this.wmSettleId = wmSettleId;
        }

        private int wmPoiId;
        private int wmSettleId;

        @Override
        public boolean equals(Object o){
            if(this == o ){
                return true;
            }
            if(o == null || getClass() !=o.getClass()){
                return false;
            }
            WmPoiIdAndSettleId anOtherOne = (WmPoiIdAndSettleId) o;
            return (wmPoiId == anOtherOne.getWmPoiId()) && (wmSettleId == anOtherOne.getWmSettleId());
        }
        @Override
        public int hashCode() {
            return Objects.hash(wmPoiId, wmSettleId);
        }

    }

    /**
     * 发送客户状态MQ
     *
     * @param customerId
     * @param customerMQEventEnum
     * @param extraData
     */
    @Override
    public void sendCustomerStatusNoticeMQ(Integer customerId, CustomerMQEventEnum customerMQEventEnum, JSONObject extraData) {
        CustomerMQBody customerMQBody = new CustomerMQBody(customerId, customerMQEventEnum, null);
        if (extraData != null) {
            customerMQBody.setExtraData(extraData.toJSONString());
        }
        mafkaMessageSendManager.send(customerMQBody);
        logger.info("发送客户状态MQ,customerMQBody={}", JSONObject.toJSONString(customerMQBody));
    }

    @Override
    public boolean saveOrUpdateCustomerOtherScan(Integer customerId, List<String> urlList, int opUid, String opName) throws WmCustomerException {
        logger.info("saveOrUpdateCustomerOtherScan customerId = {}, urlList = {}, opUid = {}, opName = {}",
                customerId, JSON.toJSONString(urlList), opUid, opName);
        if (urlList.size() > BATCH_SIZE) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, String.format("保存协议不能超过%s张", BATCH_SIZE));
        }

        WmCustomerOplogBo.OpType opType;
        //比对记录，如果没有修改则不处理
        boolean otherScanModify = true;
        List<String> urlSourceList = wmCustomerCommonQuaDBMapper.selectCommonQuaOtherList(customerId);
        if (CollectionUtils.isEmpty(urlSourceList)) {
            opType = WmCustomerOplogBo.OpType.INSERT;
        } else {
            opType = WmCustomerOplogBo.OpType.UPDATE;
            if (urlList.size() == urlSourceList.size() &&
                    CollectionUtils.isEmpty(Sets.difference(Sets.newHashSet(urlSourceList), Sets.newHashSet(urlList)))) {
                otherScanModify = false;
            }
        }

        try {
            if (!otherScanModify) {
                return true;
            }

            wmCustomerCommonQuaDBMapper.deleteCommonQuaOther(customerId);
            if (!CollectionUtils.isEmpty(urlList)) {
                wmCustomerCommonQuaDBMapper.batchInsertCommonQuaOther(customerId, urlList);
            }

            String log = String.format(CustomerConstants.CUSTOMER_LOG_TEMPLATE_COMMON_QUA_OTHER,
                    ObjectUtils.defaultIfNull(StringUtils.join(urlSourceList, CustomerConstants.SPLIT_SYMBOL), ""),
                    StringUtils.join(urlList, CustomerConstants.SPLIT_SYMBOL));
            insertCustomerOpLog(customerId, opUid, opName, opType, log);
        } catch (WmCustomerException e) {
            logger.warn("保存资质其他附件错误", e);
            return false;
        }
        return true;
    }

    /**
     * 查询客户类型
     *
     * @param userId
     * @return
     * @throws TException
     * @throws WmCustomerException
     * @throws WmServerException
     */
    @Override
    public WmCustomerRealTypeAggreDTO getCustomerRealType(Integer userId, int authType) throws TException, WmCustomerException, WmServerException {
        WmCustomerRealTypeAggreDTO aggreBo = new WmCustomerRealTypeAggreDTO();
        aggreBo.setDisplay(true);
        //只有在先富系统登录的userId，才会被查询到
        WmEmploy employ = wmEmployService.getById(userId);
        if (employ == null) {
            aggreBo.setCustomerRealTypeList(Lists.newArrayList());
            return aggreBo;
        }
        //2、查询有权限的客户类型
        WmCustomerRealTypeResult result = customerRealTypeService.getCustomerRealType(employ, authType);
        if (result == null) {
            aggreBo.setCustomerRealTypeList(Lists.newArrayList());
            return aggreBo;
        }
        aggreBo.setCustomerRealTypeList(result.getCustomerRealTypeList());
        aggreBo.setDefaultVal(result.getDefaultVal());
        return aggreBo;
    }

    /**
     * 通过编号和客户类型获取id大于1一千万的客户
     *
     * @param customerNum
     * @param customerType
     * @return
     * @throws TException
     * @throws WmCustomerException
     */
    @Override
    public WmCustomerBasicBo getNewCustomerByNumberAndType(String customerNum, int customerType) throws TException, WmCustomerException {
        List<WmCustomerDB> wmCustomerDBList = wmCustomerPlatformDataParseService.getCustomerByCustomerNumberAndType(customerNum, customerType, false);
        for (WmCustomerDB customerDB : wmCustomerDBList) {
            if (customerDB.getId() >= CUSTOMER_ID_SIZE) {
                return WmCustomerTransUtil.customerDBToBasicBo(customerDB);
            }
        }
        return null;
    }

    /**
     * 根据客户资质编号、纸质类型以及业务线查询客户
     *
     * @param customerNum
     * @param customerType
     * @param bizCode
     * @return
     * @throws TException
     * @throws WmCustomerException
     */
    @Override
    public WmCustomerBasicBo getCustomerByNumberAndTypeAndBizCode(String customerNum, int customerType, int bizCode)
            throws TException, WmCustomerException {
        List<WmCustomerDB> wmCustomerDBList = wmCustomerPlatformDataParseService.getCustomerByCustomerNumberAndType(customerNum, customerType, false);
        for (WmCustomerDB customerDB : wmCustomerDBList) {
            if (customerDB.getBizOrgCode() == null || !customerDB.getBizOrgCode().equals(bizCode)) {
                continue;
            }
            if (customerDB.getId() >= CUSTOMER_ID_SIZE) {
                return WmCustomerTransUtil.customerDBToBasicBo(customerDB);
            }
        }
        return null;
    }

    @Override
    public WmCustomerBasicBo getCustomerByIdOrMtCustomerId(long customerId) throws TException, WmCustomerException {
        logger.info("getCustomerByIdOrPlatformId(), customerId={}", customerId);
        WmCustomerBasicBo wmCustomerBasicBo = new WmCustomerBasicBo();
        if (customerId <= 0) {
            return wmCustomerBasicBo;
        }
        WmCustomerDB wmCustomerDB = wmCustomerPlatformDataParseService.getCustomerByIdOrMtCustomerId(customerId);

        String appKey = ClientInfoUtil.getClientAppKey();
        List<String> allowQueryMasterAppKeys = Arrays.asList(MccCustomerConfig.getAllowQueryMasterAppKeys().split(","));
        //如果当前为空
        if (wmCustomerDB == null
                && !CollectionUtils.isEmpty(allowQueryMasterAppKeys)
                && allowQueryMasterAppKeys.contains(appKey)) {
            logger.info("getCustomerByIdOrMtCustomerId,查询信息为空，APPKey允许查询主库再次发起查询,customerId={}", customerId, appKey);
            wmCustomerDB = wmCustomerPlatformDataParseService.getCustomerByIdOrMtCustomerIdRT(customerId);
            logger.info("getCustomerByIdOrMtCustomerId,查询信息为空，APPKey允许查询主库再次发起查询,customerId={},wmCustomerDB={}", customerId, appKey, JSON.toJSONString(wmCustomerDB));
        }
        wmCustomerBasicBo = WmCustomerTransUtil.customerDBToBasicBo(wmCustomerDB);
        logger.info("getCustomerByIdOrPlatformId(), wmCustomerBasicBo={}", JSONObject.toJSONString(wmCustomerBasicBo));
        return wmCustomerBasicBo;
    }

    @Override
    public List<WmCustomerBasicBo> getCustomerListByIdOrMtCustomerId(Set<Long> idSet) throws WmCustomerException {
        logger.info("getCustomerListByIdOrMtCustomerId(), idSet={}", idSet);
        List<WmCustomerBasicBo> listRet = new ArrayList();
        if (CollectionUtils.isEmpty(idSet)) {
            return listRet;
        }
        List<WmCustomerDB> wmCustomerDBList = wmCustomerPlatformDataParseService.selectCustomerFromPlatformByIdOrMtCustomerId(idSet);
        for (WmCustomerDB wmCustomerDB : wmCustomerDBList) {
            WmCustomerBasicBo wmCustomerBasicBo = WmCustomerTransUtil.customerDBToBasicBo(wmCustomerDB);
            listRet.add(wmCustomerBasicBo);
        }
        return listRet;
    }

    @Override
    public boolean updateCustomerToMtCustomer(long customerId) throws TException, WmCustomerException {
        WmCustomerDB wmCustomerDB = wmCustomerPlatformDataParseService.selectCustomerFromPlatformById((int) customerId);
        if (null == wmCustomerDB || wmCustomerDB.getMtCustomerId() <= 0) {
            return false;
        }
        try {
            mtCustomerThriftServiceAdapter.updateMtCustomer(wmCustomerDB, false);
            return true;
        } catch (MtCustomerException e) {
            logger.error("updateCustomerToMtCustomer customerId={},code={},msg={}", customerId, e.getCode(), e.getMsg());
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, e.getMsg());
        }
    }

    /**
     * 根据wmPoiId，获取门店为上单中、审核通过待上线、上线的门店数量。并且对门店进行了去重
     *
     * @param wmPoiIdList
     * @return
     */
    public List<Long> getActivePoiList(List<Long> wmPoiIdList) {
        Set wmPoiSet = new HashSet();
        List<List<Long>> wmPoiIdPartitions = Lists.partition(wmPoiIdList, CustomerConstants.POI_QUERY_BATCH_NUM);
        for (List<Long> wmPoiIds : wmPoiIdPartitions) {
            try {
                List<WmPoiAggre> wmPoiAggreList = wmPoiQueryThriftService.mgetWmPoiAggreByWmPoiIdWithSpecificField(wmPoiIds, Sets.newHashSet(WmPoiFieldQueryConstant.WM_POI_FIELD_VALID));
                for (WmPoiAggre poiAggre : wmPoiAggreList) {
                    //只选取三种状态：上线、上单中、审核通过待上线
                    if (poiAggre.getStatus() == WmPoiValidEnum.ONLINE.getValue()
                            || poiAggre.getStatus() == WmPoiValidEnum.CREATING.getValue()
                            || poiAggre.getStatus() == WmPoiValidEnum.READY.getValue()) {
                        wmPoiSet.add(poiAggre.getWm_poi_id());
                    }
                }

            } catch (Exception e) {
                logger.error("getActivePoiList error");
            }

        }//end for

        return new ArrayList<>(wmPoiSet);
    }

    @Override
    public Map<Long, WmCustomerBasicBo> getCustomerByWmPoiIds(List<Long> wmPoiIds)
            throws WmCustomerException {
        if (CollectionUtils.isEmpty(wmPoiIds)) {
            return Maps.newHashMap();
        }

        Map<Long, Integer> wmPoiIdCustomerIdMap = wmCustomerPoiService.selectByWmPoiIdList(wmPoiIds);
        List<Integer> wmCustomerIdList = Lists.newArrayList();
        wmPoiIdCustomerIdMap.entrySet().stream().forEach(item -> wmCustomerIdList.add(item.getValue()));
        if (CollectionUtils.isEmpty(wmCustomerIdList)) {
            return Maps.newHashMap();
        }

        List<WmCustomerDB> wmCustomerDBList = wmCustomerPlatformDataParseService.getCustomerDBList(wmCustomerIdList);
        List<WmCustomerBasicBo> wmCustomerBasicBoList = WmCustomerTransUtil.customerDBListToBasicBoList(wmCustomerDBList);
        Map<Integer, WmCustomerBasicBo> collect = wmCustomerBasicBoList.stream().collect(Collectors.toMap(item -> item.getId(), item -> item));

        Map<Long, WmCustomerBasicBo> mapResult = Maps.newHashMap();
        wmPoiIds.stream().forEach(item -> {
            Integer wmCustomerId = wmPoiIdCustomerIdMap.get(item);
            WmCustomerBasicBo wmCustomerBasicBo = collect.get(wmCustomerId);
            if (wmCustomerBasicBo != null) {
                mapResult.put(item, wmCustomerBasicBo);
            }
        });
        return mapResult;
    }

    @Override
    public ValidateResultBo validCustomerUpdate(String data, int customerId, int opUid, String opName) throws TException, WmCustomerException {
        logger.info("审核修改客户资质校验参数：data={},customerId={},opUid={},opName={}", data, customerId, opUid, opName);
        //对象转换
        WmAuditQualificationObj qualificationObj = JSON.parseObject(data, WmAuditQualificationObj.class);
        //根据客户id查出其他字段(审核表)
        WmCustomerAuditDB wmCustomerAuditDB = wmCustomerAuditDBMapper.selectCustomerAuditByCustomerIdRT(customerId);
        if (wmCustomerAuditDB == null) {
            throw new WmCustomerException(500, "未找到对应的客户审核任务");
        }
        WmCustomerBasicBo basicBo = WmCustomerTransUtil.customerAuditDBToBasicBo(wmCustomerAuditDB);
        WmCustomerDB wmCustomerDB = wmCustomerPlatformDataParseService.selectCustomerFromPlatformByIdRT(customerId);

        //其他值
        basicBo.setMtCustomerId(wmCustomerDB.getMtCustomerId());
        basicBo.setIsLeaf(ObjectUtils.defaultIfNull(wmCustomerDB.getIsLeaf(), 0));
        basicBo.setSuperCustomerId(wmCustomerDB.getSuperCustomerId());
        basicBo.setCustomerExtPro(wmCustomerDB.getCustomerExtPro());
        //如果场景信息非空则需要设置场景信息
        if (StringUtils.isNotBlank(wmCustomerDB.getSceneInfo())) {
            CustomerSceneInfoBO customerSceneInfoBO = JSONObject.parseObject(wmCustomerDB.getSceneInfo(), CustomerSceneInfoBO.class);
            basicBo.setSceneInfoBO(customerSceneInfoBO);
        }
        //品控修改值覆盖
        WmCustomerTransUtil.customerCommitDataToBasicBo(qualificationObj, basicBo);
        return wmCustomerValidateService.validate(basicBo, true, true, opUid);
    }

    @Override
    public void updateAndCallBack(WmCustomerAuditBo wmCustomerAuditBo, int opUid, String opName) throws TException, WmCustomerException {
        logger.info("审核修改客户资质回调参数：wmCustomerBasicBo={},opUid={},opName={}", JSON.toJSONString(wmCustomerAuditBo), opUid, opName);
        Boolean needMQ = false;
        //正式表
        WmCustomerDB wmCustomerDB = wmCustomerPlatformDataParseService.selectCustomerFromPlatformByIdRT(wmCustomerAuditBo.getCustomerId());
        if (wmCustomerDB == null || wmCustomerDB.getAuditStatus() != CustomerAuditStatus.CUSTOMER_AUDIT_AUDITING.getCode()) {
            logger.info("客户无效或不是提审中，无需处理");
            return;
        }
        //变更之前的客户对象
        WmCustomerDB wmCustomerOld = WmCustomerTransUtil.deepCopyWmCustomerDB(wmCustomerDB);
        /** 获取变更前法人/姓名信息 **/
        String customerName = wmCustomerDB.getCustomerName();
        String legalPerson = wmCustomerDB.getLegalPerson();
        boolean isCustomerEffect = wmCustomerDB.isEffectived();

        WmCustomerAuditDB wmCustomerAuditDB = new WmCustomerAuditDB();
        StringBuilder opLog = new StringBuilder();
        CustomerModuleStateEnum stateEnum = null;
        boolean deleteCustomerSceneTag = false;
        if (wmCustomerAuditBo.getAuditStatus().getCode() == WmCustomerAuditBo.AuditStatusEnum.AUDIT_STATUS_PASS.getCode()) {
            needMQ = true;
            wmCustomerAuditDB = wmCustomerAuditDBMapper.selectCustomerAuditByCustomerIdRT(wmCustomerAuditBo.getCustomerId());
            //判断客户类型是否在医药和非医药之间切换
            int customerRealTypeAudit = wmCustomerAuditDB.getCustomerRealType();
            int customerRealType = wmCustomerDB.getCustomerRealType();
            if (WmCustomerUtil.checkSwitch(customerRealType, customerRealTypeAudit) && !MccCustomerConfig.getCheckRuleSwitch()) {
                List<Long> wmPoiIds = wmCustomerPoiService.selectWmPoiIdsByCustomerIdFromDBAndSwitchCentre(wmCustomerDB.getId());
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(wmPoiIds)) {
                    logger.warn("客户类型在医药与非医药间切换，需要解绑客户下门店:customerId={}", wmCustomerDB.getId());
                    //客户类型不更新
                    wmCustomerAuditDB.setCustomerRealType(wmCustomerDB.getCustomerRealType());
                } else {
                    //切换逻辑
                    wmContractService.invalidContractAndCancleSign(wmCustomerDB.getId(), -99, "系统");
                }
            }
            // 客户数据生效时校验是否符合不重复要求，如果发现重复，此次提交数据不生效，状态回归到已生效，记录日志，发出大象消息
            boolean allowUpdateCustomerInfo = customerInfoUpdateCheckOperate(wmCustomerDB, wmCustomerAuditDB);
            if (allowUpdateCustomerInfo) {
                // 客户平台资质图片特殊处理
                // 客户平台资质图片格式不统一，部分存在尾缀.jpg/.jpeg，判断如果是客户平台的资质图片链接，直接获取客户平台的链接
                wmCustomerAuditDB.setPicUrl(mtCustomerThriftServiceAdapterImpl.transferMtCustomerPicUrlNew(wmCustomerAuditDB.getPicUrl(), wmCustomerDB.getPicUrl()));
                logger.info("[new]特殊处理后的资质图片链接:{}, auditPic:{}, customerPic:{}", wmCustomerAuditDB.getPicUrl(), wmCustomerAuditDB.getPicUrl(), wmCustomerDB.getPicUrl());
                WmCustomerTransUtil.copyCustomerAuditDBtoDB(wmCustomerAuditDB, wmCustomerDB);
            }
            if (wmCustomerAuditDB.getCustomerType() != null && wmCustomerAuditDB.getCustomerType().intValue() == CustomerType.CUSTOMER_TYPE_BUSINESS.getCode()) {
                wmCustomerDB.setCertificateStatus(businessCertificationAdapter.getCertificateStatus(wmCustomerAuditDB.getCustomerNumber()));
            }
            wmCustomerDB.setAuditStatus(CustomerAuditStatus.CUSTOMER_AUDIT_PASS.getCode());
            wmCustomerDB.setEffective(CustomerConstants.EFFECT);
            wmCustomerAuditDB.setAuditStatus(CustomerAuditStatus.CUSTOMER_AUDIT_PASS.getCode());
            wmCustomerAuditDB.setValid(CustomerConstants.UNVALID);
            opLog.append(CustomerConstants.CUSTOMER_LOG_TEMPLATE_AUDIT_PASS);
            stateEnum = CustomerModuleStateEnum.PASS;

            //客户类型发生变更&修改前为外卖单店&个人资质，则需要删除场景标签
            deleteCustomerSceneTag = calDeleteCustomerSceneTag(wmCustomerDB, wmCustomerAuditDB);
        } else if (wmCustomerAuditBo.getAuditStatus().getCode() == WmCustomerAuditBo.AuditStatusEnum.AUDIT_STATUS_REJECT.getCode()) {
            wmCustomerDB.setAuditStatus(CustomerAuditStatus.CUSTOMER_AUDIT_REJECT.getCode());
            wmCustomerAuditDB.setAuditStatus(CustomerAuditStatus.CUSTOMER_AUDIT_REJECT.getCode());
            opLog.append(CustomerConstants.CUSTOMER_LOG_TEMPLATE_AUDIT_REJECT);
            stateEnum = CustomerModuleStateEnum.REJECT;
        }

        wmCustomerAuditDB.setId(wmCustomerAuditBo.getBizId());
        //设置审核结果
        wmCustomerAuditDB.setAuditResult(getAuditResult(wmCustomerAuditBo.getAuditResult()));

        // 修改客户资质
        WmCustomerBasicBo basicBo = wmCustomerAuditBo.getBasicBo();
        //记录品控修改字段内容，审核通过才可以修改审核内容
        if (basicBo != null && wmCustomerAuditBo.getAuditStatus().getCode() == WmCustomerAuditBo.AuditStatusEnum.AUDIT_STATUS_PASS.getCode()) {
            WmCustomerDB wmCustomerTem = WmCustomerTransUtil.deepCopyWmCustomerDB(wmCustomerDB);
            setUpdateCustomerDb(basicBo, wmCustomerTem);
            opLog.append("\n").append("审核修改：").append("\n");

            //根据客户资质类型组装操作记录
            appendOpLogByCustomerType(wmCustomerDB, wmCustomerTem, opLog);
            logger.info("审核修改：diff={}", opLog.toString());
            logger.info("wmCustomerTem:{}", JSONObject.toJSONString(wmCustomerTem));
            // 更新审核修改资质内容
            setUpdateCustomerDb(basicBo, wmCustomerDB);
        }
        // 如果是承包商类型客户, 需重新计算合作状态
        if (CustomerRealTypeEnum.CONTRACTOR.getValue() == wmCustomerDB.getCustomerRealType()) {
            wmContractorService.setContractorWmCoStatus(wmCustomerDB);
        }
        // 更新客户信息到美团客户平台
        boolean uneffectivedMultiplexCustomerFlag = uneffectivedMultiplexCustomer(wmCustomerOld);
        logger.warn("wmCustomerDB:{}", JSONObject.toJSONString(wmCustomerDB));
        syncMtCustomerInfo(wmCustomerDB, uneffectivedMultiplexCustomerFlag);
        // 更新客户正式表状态
        wmCustomerService.updateCustomer(wmCustomerDB, 0);
        // 客户类型变更，修改业务线
        if (wmCustomerAuditBo.getAuditStatus().getCode() == WmCustomerAuditBo.AuditStatusEnum.AUDIT_STATUS_PASS.getCode()
                && (wmCustomerDB.getCustomerRealType() != wmCustomerOld.getCustomerRealType() ||
                wmCustomerOld.getBizOrgCode() == null || wmCustomerOld.getBizOrgCode().intValue() == 0)) {
            // 计算业务线
            Integer bizOrgCode = CustomerRealTypeEnum.getBizOrgCodeByValue(wmCustomerDB.getCustomerRealType());
            wmCustomerDB.setBizOrgCode(bizOrgCode);
            mtCustomerThriftServiceAdapter.saveMtCustomerBizOrgCode(wmCustomerDB.getMtCustomerId(), wmCustomerDB.getBizOrgCode());
        }
        //更新客户审核表内容
        wmCustomerAuditDBMapper.updateCustomerAudit(wmCustomerAuditDB);
        //插入客户日志
        insertCustomerOpLog(wmCustomerAuditBo.getCustomerId(), 0, "品控审核人员", WmCustomerOplogBo.OpType.CHANGESTATUS, opLog.toString());
        //插入上单状态机通知
        wmCustomerPoiService.insertPoiStateCenterByCustomerId(wmCustomerDB.getId(), stateEnum, wmCustomerAuditBo.getOpUid(), wmCustomerAuditBo.getOpName());
        if (needMQ) {
            sendCustomerStatusNoticeMQ(wmCustomerDB.getId(), CustomerMQEventEnum.CUSTOMER_EFFECTIVE, null);
        }
        if (wmCustomerAuditBo.getAuditStatus().getCode() == WmCustomerAuditBo.AuditStatusEnum.AUDIT_STATUS_PASS.getCode() && isCustomerEffect) {
            // 客户信息法人或个人变更，则签约人KP信息、法人信息自动删除，需要补录
            wmCustomerKpService.customerEffectForKpOperate(customerName, legalPerson, wmCustomerDB);
            //客户生效审核通过后处理四要素标签
            wmCustomerKpRealAuthService.customerEffectAuditPassFourEleTag(wmCustomerOld, wmCustomerDB);
        }

        //审核通过处理标签信息
        if (wmCustomerAuditBo.getAuditStatus().getCode() == WmCustomerAuditBo.AuditStatusEnum.AUDIT_STATUS_PASS.getCode()) {
            dealTagWhenAuditSuc(wmCustomerOld, wmCustomerDB, opUid);
        }
        //删除场景标签为TRUE则需要删除标签
        if (deleteCustomerSceneTag) {
            wmCustomerLabelService.deleteWmSingleCustomerSceneTag(wmCustomerDB.getMtCustomerId(), 0, "系统");
        }

        //发出审核通过或驳回的事件
        WmCustomerAuditBo auditEventBo = new WmCustomerAuditBo();
        auditEventBo.setAuditStatus(wmCustomerAuditBo.getAuditStatus());
        auditEventBo.setCustomerId(wmCustomerAuditBo.getCustomerId());
        auditEventBo.setAuditResult(wmCustomerAuditBo.getAuditResult());
        sendCustomerAuditEvent(auditEventBo);
    }

    /**
     * 审核成功后处理标签先关逻辑
     *
     * @param wmCustomerOld
     * @param wmCustomerDB
     * @param opUid
     */
    private void dealTagWhenAuditSuc(WmCustomerDB wmCustomerOld, WmCustomerDB wmCustomerDB, Integer opUid) {
        //1-美食城客户审核通过 && 信息完备 打"已审核美食城"标
        if (wmCustomerDB.getCustomerRealType() == CustomerRealTypeEnum.MEISHICHENG.getValue()) {
            String customerRealTypeSpInfo = wmCustomerDB.getCustomerRealTypeSpInfo();
            if (StringUtils.isNotEmpty(customerRealTypeSpInfo)) {
                //给客户以及门店打标
                addLabelOnCustomerAndPoi(wmCustomerDB.getMtCustomerId(), wmCustomerDB.getId());
            }
            //非美食城客户修改为美食城客户，判断美食城档口数是否符合规范，不符合需要发送大象消息
            if (wmCustomerOld.getCustomerRealType() != CustomerRealTypeEnum.MEISHICHENG.getValue()) {
                checkMscPoiCntRuleAndSendMsg(wmCustomerDB, opUid);
            }
        }

        //2-审核通过并从美食城客户切换到非美食城客户-去除相关标识
        if (wmCustomerDB.getCustomerRealType() != CustomerRealTypeEnum.MEISHICHENG.getValue()
                && wmCustomerOld.getCustomerRealType() == CustomerRealTypeEnum.MEISHICHENG.getValue()) {
            WmPoiLabelRel customerLabel = wmPoiFlowlineLabelThriftServiceAdapter.selectLabelRelCache(wmCustomerDB.getMtCustomerId(), MccCustomerConfig.getAuditedMSCCustomerLabel(), LabelSubjectTypeEnum.CUSTOMER.getCode());
            logger.info("#selectLabelRelCache,customerLabel={}", JSONObject.toJSONString(customerLabel));
            if (customerLabel != null && customerLabel.getId() > 0L) {
                //客户去标
                wmPoiFlowlineLabelThriftServiceAdapter.delCustomerLabelRel(MccCustomerConfig.getAuditedMSCCustomerLabel(), wmCustomerDB.getMtCustomerId(), 0, "系统");
                List<WmCustomerPoiDB> wmCustomerPoiDBList = wmCustomerPoiDBMapper.selectWmCustomerPoiByCustomerId(wmCustomerDB.getId());
                List<Long> wmPoiIdList = wmCustomerPoiDBList.stream().map(x -> x.getWmPoiId()).collect(Collectors.toList());
                //门店去标
                wmPoiFlowlineLabelThriftServiceAdapter.batchDeleteLabelToPois(MccCustomerConfig.getMSCWmPoiLabel(), wmPoiIdList, 0, "系统");
                //如果客户有资质共用特殊-客户标签，则需要掉标
                wmCustomerMSCLabelService.deleteQuaComTagOnCusAndPoi(wmCustomerDB.getMtCustomerId(), wmPoiIdList);
            }
        }

        //3-审核通过，如果需要处理外卖单店场景标签
        deleteWmSingleSceneTag(wmCustomerDB, wmCustomerOld);
    }

    /**
     * 删除外卖单店个人场景标签
     *
     * @param wmCustomerDB
     * @param oldCustomerDB
     */
    private void deleteWmSingleSceneTag(WmCustomerDB wmCustomerDB, WmCustomerDB oldCustomerDB) {

        Long mtCustomerId = oldCustomerDB.getMtCustomerId();
        try {
            //原客户信息的客户类型非外卖单店&个人资质 则不处理
            if (oldCustomerDB.getCustomerRealType() != CustomerRealTypeEnum.DANDIAN.getValue()
                    && oldCustomerDB.getCustomerType() != CustomerType.CUSTOMER_TYPE_IDCARD.getCode()) {
                return;
            }
            //修改后客户 非外卖单店 或 修改后是外卖单店&资质非个人则需要处理
            if (wmCustomerDB.getCustomerRealType() != CustomerRealTypeEnum.DANDIAN.getValue()
                    || (wmCustomerDB.getCustomerRealType() == CustomerRealTypeEnum.DANDIAN.getValue()
                    && wmCustomerDB.getCustomerType() != CustomerType.CUSTOMER_TYPE_IDCARD.getCode())) {
                wmCustomerLabelService.deleteWmSingleCustomerSceneTag(oldCustomerDB.getMtCustomerId(), 0, "系统");
            }
        } catch (Exception e) {
            logger.error("deleteWmSingleSceneTag,审核通过删除客户的场景标签发生异常,mtCustomerId={}", mtCustomerId, e);
        }
    }

    /**
     * 根据客户资质类型组组装操作记录
     *
     * @param wmCustomerDB
     * @param wmCustomerTem
     * @param opLog
     */
    private void appendOpLogByCustomerType(WmCustomerDB wmCustomerDB, WmCustomerDB wmCustomerTem, StringBuilder opLog) {
        if (wmCustomerDB.getCustomerType() == CustomerType.CUSTOMER_TYPE_BUSINESS.getCode()) {
            opLog.append(BeanDiffUtil.diffContentByMap(wmCustomerDB, wmCustomerTem, WmLogDiffConstant.CUSTOMER_BUSINESS_MAP));
        } else if (wmCustomerDB.getCustomerType() == CustomerType.CUSTOMER_TYPE_IDCARD.getCode()) {
            opLog.append(BeanDiffUtil.diffContentByMap(wmCustomerDB, wmCustomerTem, WmLogDiffConstant.CUSTOMER_IDCARD_MAP));
        } else if (wmCustomerDB.getCustomerType() == CustomerType.CUSTOMER_TYPE_BUSINESS_ABROAD.getCode()) {
            opLog.append(BeanDiffUtil.diffContentByMap(wmCustomerDB, wmCustomerTem, WmLogDiffConstant.CUSTOMER_BUSINESS_ABROAD_MAP));
        }
    }

    /**
     * 获取审核结果
     *
     * @param auditResult
     * @return
     */
    private String getAuditResult(String auditResult) {
        if (StringUtils.isNotEmpty(auditResult)) {
            if (auditResult.length() > WmCustomerConstant.MAX_SUPPORT_REJECT_REASON_LENGTH_FOR_CUSTOMER) {
                logger.warn("客户信息审核回调结果太长 auditResult={}", auditResult);
                auditResult = auditResult.substring(0, WmCustomerConstant.MAX_SUPPORT_REJECT_REASON_LENGTH_FOR_CUSTOMER - WmCustomerConstant.TOO_LONG_MSG.length()) + WmCustomerConstant.TOO_LONG_MSG;
            }
            return auditResult;
        }
        return null;
    }

    /**
     * 同步美团客户平台
     *
     * @param wmCustomerDB
     * @param uneffectivedMultiplexCustomerFlag
     */
    private void syncMtCustomerInfo(WmCustomerDB wmCustomerDB, boolean uneffectivedMultiplexCustomerFlag) throws WmCustomerException {
        if (uneffectivedMultiplexCustomerFlag) {
            updateReuseCustomerQualification(wmCustomerDB);
        } else {
            updateMtCustomerInfo(wmCustomerDB);
        }
    }

    /**
     * 客户未生效 && 复用客户
     *
     * @param oldCustomer
     * @return
     */
    private boolean uneffectivedMultiplexCustomer(WmCustomerDB oldCustomer) {
        //是否复用客户
        boolean multiplexCustomer = wmCustomerMultiplexService.isMultiplex(oldCustomer.getMultiplexCustomerId());
        return oldCustomer.isUnEffectived() && multiplexCustomer;
    }

    private void sendCustomerAuditEvent(WmCustomerAuditBo auditEventBo) {
        try {
            logger.info("#sendCustomerAuditEvent,msg={}", JSONObject.toJSONString(auditEventBo));
            customerAuditResultNoticeProducer.sendMessage(JSONObject.toJSONString(auditEventBo));
        } catch (Exception e) {
            logger.error("customerAuditResultNoticeProducer sendMessage exception", e);
        }
    }

    /**
     * 更新客户信息到美团客户平台
     *
     * @param wmCustomerDB
     * @throws WmCustomerException
     */
    private void updateMtCustomerInfo(WmCustomerDB wmCustomerDB) throws WmCustomerException {
        try {
            long mtCustomerId = updateMtCustomer(wmCustomerDB, false);
            if (mtCustomerId != wmCustomerDB.getMtCustomerId()) {
                wmCustomerDB.setMtCustomerId(mtCustomerId);
            }
        } catch (Exception e) {
            logger.error("美团客户平台更新客户信息失败, wmCustomerId={}，e={}", wmCustomerDB.getId(), e);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "客户平台更新异常");
        }
    }

    /**
     * 复用客户第一次审核生效时更新客户信息到美团客户平台(客户复用)
     *
     * @param wmCustomerDB
     * @throws WmCustomerException
     */
    private void updateReuseCustomerQualification(WmCustomerDB wmCustomerDB) throws WmCustomerException {
        try {
            long mtCustomerId = mtCustomerThriftServiceAdapter.updateReuseCustomerQualification(wmCustomerDB, false);
            if (mtCustomerId != wmCustomerDB.getMtCustomerId()) {
                wmCustomerDB.setMtCustomerId(mtCustomerId);
            }
        } catch (Exception e) {
            logger.error("美团客户平台更新客户信息失败, wmCustomerId={}，e={}", wmCustomerDB.getId(), e);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "客户平台更新异常");
        }
    }


    /**
     * 设置审核回调客户正式表和审核表更新字段
     *
     * @param basicBo
     * @param wmCustomerDB
     */
    private void setUpdateCustomerDb(WmCustomerBasicBo basicBo, WmCustomerDB wmCustomerDB) {

        //执照名称
        if (StringUtils.isNotEmpty(basicBo.getCustomerName())) {
            wmCustomerDB.setCustomerName(basicBo.getCustomerName());
        }

        //执照编号
        if (StringUtils.isNotEmpty(basicBo.getCustomerNumber())) {
            wmCustomerDB.setCustomerNumber(basicBo.getCustomerNumber());
        }

        //地址
        if (StringUtils.isNotEmpty(basicBo.getAddress())) {
            wmCustomerDB.setAddress(basicBo.getAddress());
        }

        //有效期
        wmCustomerDB.setValidateDate(basicBo.getValidateDate());

        //经营范围
        wmCustomerDB.setBusinessScope(basicBo.getBusinessScope());

        //法人
        if (StringUtils.isNotEmpty(basicBo.getLegalPerson())) {
            wmCustomerDB.setLegalPerson(basicBo.getLegalPerson());
        }
    }


    @Override
    public void washCustmerRealType(List<Integer> customerIdList, int washNum, String misId) throws WmCustomerException, TException {
        logger.info("washCustmerRealType customerIdList:[{}],washNum:[{}],misId:[{}]", customerIdList, washNum, misId);
        if (!MccConfig.getWashAuthorityMisIdsBoolean(misId)) {
            throw new WmCustomerException(WmContractErrorCodeConstant.INPUT_ERROR, "没权限,请联系薛占刚");
        }
        if (CollectionUtils.isEmpty(customerIdList) && washNum <= 0) {
            throw new WmCustomerException(WmContractErrorCodeConstant.INPUT_ERROR, "清洗数量不可为空或为负");
        }

        List<WmCustomerDB> wmCustomerDBCustomerRealTypeList = wmCustomerDBMapper.queryWmCustomerByCustomerRealTypeList(
                Lists.newArrayList(CustomerRealTypeEnum.QINGXUANZE.getValue(), CustomerRealTypeEnum.DANDIAN.getValue()),
                customerIdList,
                washNum);

        logger.info("wmCustomerDBCustomerRealTypeList.size:[{}]", wmCustomerDBCustomerRealTypeList.size());

        List<Integer> wmCustomerDBCustomerIdList = Lists.newArrayList();
        Set<Integer> wmCustomerDBCustomerIdDanDianSet = Sets.newHashSet();
        Map<Integer, WmCustomerDB> wmCustomerDBMap = Maps.newHashMap();
        for (WmCustomerDB wmCustomerDB : wmCustomerDBCustomerRealTypeList) {
            wmCustomerDBCustomerIdList.add(wmCustomerDB.getId());
            if (wmCustomerDB.getCustomerRealType().intValue() == CustomerRealTypeEnum.DANDIAN.getValue()) {
                wmCustomerDBCustomerIdDanDianSet.add(wmCustomerDB.getId());
            }
            wmCustomerDBMap.put(wmCustomerDB.getId(), wmCustomerDB);
        }

        //医药叶子节点
        Set<String> primaryTagIdSet = Sets.newHashSet("179", "6005", "4012", "181", "6006", "180");

        if (!CollectionUtils.isEmpty(wmCustomerDBCustomerRealTypeList)) {
            List<Long> wmPoiIdList = getSingleWmCustomerWmPoiIdList(wmCustomerDBCustomerIdList);
            logger.info("wmPoiIdList.size[{}]", JSON.toJSONString(wmPoiIdList.size()));
            List<WmPoiDomain> wmPoiDomainList = wmPoiClient.pageGetWmPoiByWmPoiIdList(wmPoiIdList,
                    ImmutableSet.of(WM_POI_FIELD_WM_POI_ID, WM_POI_FIELD_CUSTOMER_ID, WM_POI_FIELD_PRIMARY_TAG_ID));

            //筛选一级品类  医药健康
            List<Integer> customerIdYaoPinList = Lists.newArrayList();
            List<Integer> customerIdDanDianList = Lists.newArrayList();
            for (WmPoiDomain wmPoiDomain : wmPoiDomainList) {
                if (primaryTagIdSet.contains(wmPoiDomain.getPrimaryTagId())) {
                    customerIdYaoPinList.add(wmPoiDomain.getCustomerId());
                } else if (!wmCustomerDBCustomerIdDanDianSet.contains(wmPoiDomain.getCustomerId())) {
                    customerIdDanDianList.add(wmPoiDomain.getCustomerId());
                }
            }

            logger.info("customerIdYaoPinList.size:[{}],customerIdDanDianList.size:[{}]",
                    customerIdYaoPinList.size(), customerIdDanDianList.size());

            //单店药品
            if (!CollectionUtils.isEmpty(customerIdYaoPinList)) {
                List<List<Integer>> yapPinCustomerIdListList = Lists.partition(customerIdYaoPinList, 2000);
                for (List<Integer> customerIds : yapPinCustomerIdListList) {
                    wmCustomerDBMapper.updateCustomerRealTypeByCustomerIds(customerIds,
                            CustomerRealTypeEnum.DANDIAN_YAOPIN.getValue());

                    handleService.submit(() -> yaopinglog(customerIds, wmCustomerDBMap));
                }

                logger.info("单店药品清洗完成");
            }

            //单店
            if (!CollectionUtils.isEmpty(customerIdDanDianList)) {
                List<List<Integer>> danDianCustomerIdListList = Lists.partition(customerIdDanDianList, 2000);
                for (List<Integer> customerIds : danDianCustomerIdListList) {
                    wmCustomerDBMapper.updateCustomerRealTypeByCustomerIds(customerIds, CustomerRealTypeEnum.DANDIAN.getValue());

                    handleService.submit(() -> dandianlog(customerIds, wmCustomerDBMap));
                }

                logger.info("单店清洗完成");
            }
        }

    }

    private void dandianlog(List<Integer> customerIds, Map<Integer, WmCustomerDB> wmCustomerDBMap) {
        //添加日志
        try {
            //添加日志
            for (Integer customerId : customerIds) {
                WmCustomerDB oldWmCustomerDB = wmCustomerDBMap.get(customerId);

                WmCustomerBasicBo newWmCustomerBasicBo = WmCustomerTransUtil.customerDBToBasicBo(oldWmCustomerDB);
                newWmCustomerBasicBo.setCustomerRealType(CustomerRealTypeEnum.DANDIAN.getValue());

                insertCustomerOpLog(customerId, 0, "系统清洗客户数据", newWmCustomerBasicBo, oldWmCustomerDB, WmCustomerOplogBo.OpType.UPDATE);


            }
        } catch (WmCustomerException | TException e) {
            logger.error("dandianlog异常", e);
        }
    }


    private void yaopinglog(List<Integer> customerIds, Map<Integer, WmCustomerDB> wmCustomerDBMap) {
        //添加日志
        try {
            for (Integer customerId : customerIds) {

                WmCustomerDB oldWmCustomerDB = wmCustomerDBMap.get(customerId);

                WmCustomerBasicBo newWmCustomerBasicBo = WmCustomerTransUtil.customerDBToBasicBo(oldWmCustomerDB);
                newWmCustomerBasicBo.setCustomerRealType(CustomerRealTypeEnum.DANDIAN_YAOPIN.getValue());
                insertCustomerOpLog(customerId, 0, "系统清洗客户数据", newWmCustomerBasicBo, oldWmCustomerDB, WmCustomerOplogBo.OpType
                        .UPDATE);
            }

        } catch (WmCustomerException | TException e) {
            logger.error("yaopinglog异常", e);
        }
    }

    private List<Long> getSingleWmCustomerWmPoiIdList(List<Integer> customerId) {
        List<WmCustomerPoiDB> wmCustomerPoiDBList = wmCustomerPoiDBMapper.selectWmCustomerPoiByCustomerIdList(customerId);
        Map<Integer, List<Long>> wmCustomerPoiDBMap = Maps.newHashMap();
        for (WmCustomerPoiDB wmCustomerPoiDB : wmCustomerPoiDBList) {
            if (wmCustomerPoiDBMap.get(wmCustomerPoiDB.getCustomerId()) == null) {
                wmCustomerPoiDBMap.put(wmCustomerPoiDB.getCustomerId(), Lists.newArrayList(wmCustomerPoiDB.getWmPoiId()));
            } else {
                List<Long> wmPoiIdList = wmCustomerPoiDBMap.get(wmCustomerPoiDB.getCustomerId());
                wmPoiIdList.add(wmCustomerPoiDB.getWmPoiId());
                wmCustomerPoiDBMap.put(wmCustomerPoiDB.getCustomerId(), wmPoiIdList);
            }
        }

        List<Long> wmPoiIdList = Lists.newArrayList();
        for (Map.Entry<Integer, List<Long>> entry : wmCustomerPoiDBMap.entrySet()) {
            //只处理关联门店数等于1的
            if (entry.getValue() != null && entry.getValue().size() == 1) {
                wmPoiIdList.addAll(entry.getValue());
            }
        }
        return wmPoiIdList;
    }

    @Override
    public WmCustomerBasicBo getLatestWmCustomerBasicBo(Long wmPoiId) throws TException, WmCustomerException {
        logger.info("查询最新客户信息 getLatestWmCustomerBasicBo wmPoiId = {}", wmPoiId);
        if (wmPoiId == null || wmPoiId <= 0) {
            return null;
        }
        // 获取门店对应的客户id
        Map<Long, Integer> wmPoiIdCustomerIdMap = wmCustomerPoiService.selectByWmPoiIdList(Lists.newArrayList(wmPoiId));
        if (MapUtils.isEmpty(wmPoiIdCustomerIdMap)) {
            return null;
        }
        // 获取客户提审信息
        Integer wmCustomerId = wmPoiIdCustomerIdMap.get(wmPoiId);
        WmCustomerAuditDB wmCustomerAuditDB = wmCustomerAuditDBMapper.selectCustomerAuditByCustomerId(wmCustomerId);
        if (wmCustomerAuditDB != null) {
            return WmCustomerTransUtil.customerAuditDBToBasicBo(wmCustomerAuditDB);
        }
        // 如果无提审信息，查询客户生效信息返回
        WmCustomerDB wmCustomerDB = wmCustomerPlatformDataParseService.selectCustomerFromPlatformById(wmCustomerId);
        return WmCustomerTransUtil.customerDBToBasicBo(wmCustomerDB);
    }

    public long getWmCustomerIdByMtCustomerIdRT(long mtCustomerId) {
        return wmCustomerPlatformDataParseService.getWmCustomerIdByMtCustomerIdRT(mtCustomerId);
    }

    @Override
    public long getWmCustomerIdByMtCustomerId(long mtCustomerId) {
        return wmCustomerPlatformDataParseService.getWmCustomerIdByMtCustomerId(mtCustomerId);
    }

    @Override
    public long getMtCustomerIdByWmCustomerId(long wmCustomerId) {
        return wmCustomerPlatformDataParseService.getMtCustomerIdByWmCustomerId(wmCustomerId);
    }

    /**
     * 客户打标，调用打标平台
     *
     * @param wmCustomerId
     * @param labelId
     * @param opUid
     * @param opName
     * @throws WmCustomerException
     * @throws TException
     */
    @Override
    public void batchAddCustomerLabel(Long wmCustomerId, Long labelId, Integer opUid, String opName) throws WmCustomerException, TException {
        Long mtCustomerId = getMtCustomerIdByWmCustomerId(wmCustomerId);
        List<Long> mtCustomerIdList = new ArrayList<>(Arrays.asList(mtCustomerId));
        try {
            wmPoiFlowlineLabelThriftServiceAdapter.batchAddLabelRelEx(labelId, mtCustomerIdList, opUid, opName, LabelSubjectTypeEnum.CUSTOMER.getCode());
        } catch (WmServerException | WmLabelException exception) {
            logger.info("batchAddCustomerLabel#调用打标平台创建标签时异常，wmCustomerId:{}", wmCustomerId);
            while (LABEL_RETRY_CNT > 0) {
                try {
                    wmPoiFlowlineLabelThriftServiceAdapter.batchAddLabelRelEx(labelId, mtCustomerIdList, opUid, opName, LabelSubjectTypeEnum.CUSTOMER.getCode());
                    return;
                } catch (WmServerException | WmLabelException retryException) {
                    logger.info("batchAddCustomerLabel#调用打标平台创建标签时异常，重试第{}次失败，wmCustomerId:{}", wmCustomerId);
                    LABEL_RETRY_CNT--;
                }
            }
            logger.error("batchAddCustomerLabel#调用打标平台创建标签时异常，重试{}次失败后失败，流程结束，wmCustomerId:{}", wmCustomerId);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "调用打标平台创建标签异常");
        }
    }

    /**
     * 查询客户的标签信息
     *
     * @param wmCustomerId 客户id
     */
    @Override
    public WmPoiLabelRel queryCustomerLabelInfo(Long wmCustomerId, Long labelId) throws WmCustomerException, TException {
        Long mtCustomerId = getMtCustomerIdByWmCustomerId(wmCustomerId);
        if (mtCustomerId == null || mtCustomerId == 0L) {
            return null;
        }
        WmPoiLabelRel wmPoiLabelRel;
        try {
            // 获取某个客户的某个标签
            wmPoiLabelRel = wmPoiFlowlineLabelThriftServiceAdapter.selectLabelRelCacheEx(mtCustomerId, labelId, LabelSubjectTypeEnum.CUSTOMER.getCode());
        } catch (WmServerException | WmLabelException e) {
            logger.info("queryCustomerLabelInfo#打标平台获取客户标签异常，MT客户ID:{}，标签ID:{}", mtCustomerId, labelId);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "调用打标平台查询客户标签异常");
        }
        return wmPoiLabelRel;
    }

    /**
     * 判断用户是否包含某个标签
     *
     * @param wmCustomerId
     * @param labelId
     * @return
     */
    @Override
    public boolean customerLabelTypeJudge(Long wmCustomerId, Long labelId) throws WmCustomerException, TException {
        WmPoiLabelRel wmPoiLabelRel = queryCustomerLabelInfo(wmCustomerId, labelId);
        if (null == wmPoiLabelRel) {
            logger.info("customerLabelTypeJudge#客户ID:{}无标签", wmCustomerId);
            return false;
        }
        if (wmPoiLabelRel.getWmLabelId() == labelId.longValue()) {
            return true;
        }
        logger.info("customerLabelTypeJudge#客户ID:{}对一个的标签ID:{}，与预期值不符", wmCustomerId, wmPoiLabelRel.getWmLabelId());
        return false;
    }

    /**
     * 获取客户标签列表
     */
    @Override
    public List<WmCustomerLabelTypeBo> getCustomerLabelTypeList(Integer opUid) throws TException, WmCustomerException {
        List<WmCustomerLabelTypeBo> wmCustomerLabelTypeBoList = new ArrayList<>();
        try {
            List<WmLabelClassFullFormat> wmLabelClassFullFormatList = wmPoiFlowlineLabelThriftService.getLabelAndClassFormatForCrmPage(LabelSubjectTypeEnum.CUSTOMER.getCode(), opUid);
            for (WmLabelClassFullFormat wmLabelClassFullFormat : wmLabelClassFullFormatList) {
                WmCustomerLabelTypeBo wmCustomerLabelTypeBo = new WmCustomerLabelTypeBo();
                wmCustomerLabelTypeBo.setLabelTypeId(wmLabelClassFullFormat.getWmLabelClassification().getId());
                wmCustomerLabelTypeBo.setLabelTypeName(wmLabelClassFullFormat.getWmLabelClassification().getName());
                List<WmCustomerLabelBo> wmCustomerLabelBoList = new ArrayList<>();
                for (WmPoiLabel wmPoiLabel : wmLabelClassFullFormat.getWmPoiLabelList()) {
                    WmCustomerLabelBo wmCustomerLabelBo = new WmCustomerLabelBo();
                    wmCustomerLabelBo.setLabelId(wmPoiLabel.getId());
                    wmCustomerLabelBo.setLabelName(wmPoiLabel.getName());
                    wmCustomerLabelBoList.add(wmCustomerLabelBo);
                }
                wmCustomerLabelTypeBo.setLabelList(wmCustomerLabelBoList);
                wmCustomerLabelTypeBoList.add(wmCustomerLabelTypeBo);
            }
        } catch (WmServerException e) {
            logger.info("getCustomerLabelTypeList#打标平台获取客户标签列表异常");
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "调用打标平台获取客户标签列表异常");
        }
        return wmCustomerLabelTypeBoList;
    }

    /**
     * 获取客户标签列表，指定客户id
     */
    @Override
    public List<WmCustomerLabelBo> getCustomerLabelList(Long wmCustomerId, Integer opUid) throws TException, WmCustomerException {
        Long mtCustomerId = getMtCustomerIdByWmCustomerId(wmCustomerId);
        List<WmCustomerLabelBo> wmCustomerLabelBoList = new ArrayList<>();
        Map<Long, List<WmLabelClassFullFormat>> labelMap;
        try {
            labelMap = wmPoiFlowlineLabelThriftService.getLabelFormatForCrmPage(Lists.newArrayList(Arrays.asList(mtCustomerId)), LabelSubjectTypeEnum.CUSTOMER.getCode(), opUid);
            if (MapUtils.isNotEmpty(labelMap)) {
                if (labelMap.size() != 1) {
                    logger.info("getCustomerLabelTypeList#打标平台返回客户数量错误，wmCustomerId:{}", wmCustomerId);
                    return wmCustomerLabelBoList;
                }
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(labelMap.get(mtCustomerId))) {
                    List<WmLabelClassFullFormat> wmLabelClassFullFormatList = labelMap.get(mtCustomerId);
                    for (WmLabelClassFullFormat wmLabelClassFullFormat : wmLabelClassFullFormatList) {
                        List<WmPoiLabel> wmPoiLabelList = wmLabelClassFullFormat.getWmPoiLabelList();
                        for (WmPoiLabel wmPoiLabel : wmPoiLabelList) {
                            WmCustomerLabelBo wmCustomerLabelBo = new WmCustomerLabelBo();
                            wmCustomerLabelBo.setLabelId(wmPoiLabel.getId());
                            wmCustomerLabelBo.setLabelName(wmPoiLabel.getName());
                            wmCustomerLabelBo.setLabelDetailContent(wmPoiLabel.getContent());
                            wmCustomerLabelBoList.add(wmCustomerLabelBo);
                        }
                    }
                }
            }
        } catch (WmServerException e) {
            logger.info("getCustomerLabelList#打标平台获取客户标签详情异常");
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "打标平台获取客户标签详情异常");
        }
        return wmCustomerLabelBoList;
    }

    /**
     * 客户标签变更更新es
     */
    @Override
    public void customerLabelSync(Long mtCustomerId, Integer opUid, String opUname, Long wmCustomerLabelId) throws TException, WmCustomerException {
        logger.info("customerLabelSync mtCustomerId:{},wmCustomerLabelId:{}", mtCustomerId, wmCustomerLabelId);
        // 反查打标平台，获取客户对应的所有标签，labelid传0表示获取全部标签
        List<WmPoiLabelRel> wmPoiLabelRelList;
        try {
            wmPoiLabelRelList = wmPoiFlowlineLabelThriftService.selectLabelRelBySubjectIdAndLabelId(mtCustomerId, 0, LabelSubjectTypeEnum.CUSTOMER.getCode());
        } catch (Exception e) {
            logger.error("selectLabelRelBySubjectIdAndLabelId异常，mtCustomerId:{}", mtCustomerId, e);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "客户标签同步至es时异常");
        }
        List<String> labelIdStrList = new ArrayList<>();
        for (WmPoiLabelRel wmPoiLabelRel : wmPoiLabelRelList) {
            if (wmPoiLabelRel.getWmLabelId() > 0 && wmPoiLabelRel.getSubjectId() == mtCustomerId) {
                labelIdStrList.add(String.valueOf(wmPoiLabelRel.getWmLabelId()));
            }
        }
        String customerLabelIdsStr = Joiner.on(" ").join(labelIdStrList);
        Long wmCustomerId = getWmCustomerIdByMtCustomerIdRT(mtCustomerId);
        if (wmCustomerId == null || wmCustomerId.longValue() <= 0l) {
            logger.error("customerLabelSync#客户标签同步至es时异常，找不到对应的客户信息，mtCustomerId:{},wmCustomerId:{},wmCustomerLabelId:{}", mtCustomerId, wmCustomerId, wmCustomerLabelId);
            return;
        }
        try {
            wmCustomerESService.syncCustomerLabelToUpsertEs(wmCustomerId, customerLabelIdsStr);
        } catch (Exception e) {
            logger.error("customerLabelSync#客户标签同步至es时异常，wmCustomerId:{}", wmCustomerId, e);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "客户标签同步至es时异常");
        }
        StringBuffer labelOpLog = new StringBuffer();
        List<Long> labelIdLongList = new ArrayList<>();
        labelIdLongList.add(wmCustomerLabelId);
        logger.info("selectWmPoiLabelAndWmPoiLabelClassificationByLabelIds入参:{}", JSON.toJSONString(labelIdLongList));
        try {
            List<LabelAndLabelClassificationResult> labelAndLabelClassificationResults = wmPoiFlowlineLabelThriftService.selectWmPoiLabelAndWmPoiLabelClassificationByLabelIds(labelIdLongList);
            labelAndLabelClassificationResults.stream()
                    .forEach(LabelAndLabelClassificationResult -> {
                        labelOpLog.append("【")
                                .append(LabelAndLabelClassificationResult.getWmPoiLabel().getName())
                                .append("】");
                    });
        } catch (WmServerException e) {
            String errorLabelNames = Joiner.on(",").join(labelIdStrList);
            labelOpLog.append("未知标签名称，标签id为:").append(errorLabelNames);
        }
        //标签日志
        String labelLogStr = "";
        WmCustomerOplogBo.OpType opType = WmCustomerOplogBo.OpType.INSERT;
        if (isAddLabel(wmPoiLabelRelList, wmCustomerLabelId, mtCustomerId)) {
            labelLogStr = String.format(CustomerConstants.CUSTOMER_CREATE_LABEL, labelOpLog.toString());
        } else {
            labelLogStr = String.format(CustomerConstants.CUSTOMER_DEL_LABEL, labelOpLog.toString());
            opType = WmCustomerOplogBo.OpType.DELETE;
        }
        logger.info("insertCustomerOpLog标签名称:{}", labelOpLog.toString());
        if (StringUtils.isNotBlank(labelOpLog)) {
            insertCustomerOpLog(wmCustomerId.intValue(), opUid, opUname, opType,
                    String.format(labelLogStr, labelOpLog.toString()));
        }
    }

    /**
     * 是否客户新增打标
     *
     * @param wmPoiLabelRelList 客户标签关系集合
     * @param wmCustomerLabelId 标签ID
     * @param mtCustomerId      客户平台ID
     * @return
     */
    private boolean isAddLabel(List<WmPoiLabelRel> wmPoiLabelRelList, Long wmCustomerLabelId, Long mtCustomerId) {
        if (CollectionUtils.isEmpty(wmPoiLabelRelList) || wmCustomerLabelId == null || mtCustomerId == null) {
            return false;
        }
        for (WmPoiLabelRel wmPoiLabelRel : wmPoiLabelRelList) {
            if (wmPoiLabelRel.getSubjectId() == mtCustomerId
                    && wmPoiLabelRel.getWmLabelId() == wmCustomerLabelId) {
                return true;
            }
        }
        return false;
    }

    /**
     * 根据客户id获取客户标签名称
     */
    @Override
    public List<WmCustomerLabelBo> queryCustomerLabelNamesByCustomerId(Integer wmCustomerId, Integer userId) throws TException, WmCustomerException {
        logger.info("queryCustomerLabelNamesByCustomerId#客户id:{}查询标签信息，登录人id:{}", wmCustomerId, userId);
        List<WmCustomerLabelBo> wmCustomerLabelBoList = getCustomerLabelList((long) wmCustomerId, userId);
        logger.info("queryCustomerLabelNamesByCustomerId#客户id:{}标签信息:{}", wmCustomerId, JSON.toJSONString(wmCustomerLabelBoList));
        return wmCustomerLabelBoList;
    }

    @Override
    public Long queryBmCompanyCustomerIdByWmCustomerId(long wmCustomerId) throws TException, WmCustomerException {
        logger.info("queryBmCompanyCustomerIdByWmCustomerId#通过外卖客户查询配送公司客户，wmCustomerId:{}", wmCustomerId);
        WmCustomerRelDB wmCustomerRelDB = wmCustomerRelMapper.selectByWmCustomerIdAndBizType(wmCustomerId, CustomerBizTypeEnum.BUSINESS_CUSTOMER.getCode());
        if (wmCustomerRelDB == null) {
            return null;
        }
        return wmCustomerRelDB.getCustomer_biz_id();
    }

    @Override
    public Long queryBmPoiCustomerIdByWmPoiId(long wmPoiId) throws TException, WmCustomerException {
        logger.info("#queryBmPoiCustomerIdByWmPoiId，wmPoiId:{}", wmPoiId);
        WmCustomerPoiRelExtension wmCustomerPoiRelExtension = wmCustomerPoiRelExtensionMapper
                .selectByWmPoiIdAndBizType(wmPoiId,
                        CustomerBizTypeEnum.BUSINESS_CUSTOMER.getCode());
        if (wmCustomerPoiRelExtension == null) {
            return null;
        }
        return wmCustomerPoiRelExtension.getBizId();
    }

    @Override
    public Integer selectWmCustomerIdByWmPoiId(Long wmPoiId) {
        return wmCustomerPoiDBMapper.selectCustomerIdByOneWmPoiId(wmPoiId);
    }

    /**
     * 获取客户列表
     *
     * @param queryDTO
     * @return
     */
    @Override
    public List<WmCustomerBasicBo> getCustomerListByDB(WmCustomerQueryDTO queryDTO) throws WmCustomerException {
        List<WmCustomerBasicBo> list = Lists.newArrayList();
        WmCustomerQueryVo vo = WmCustomerTransUtil.wmCustomerFormDtoToVo(queryDTO);
        List<WmCustomerDB> wmCustomerDBList = wmCustomerService.getCustomerListByDB(vo);
        logger.info("#getCustomerListByDB，queryDTO={},wmCustomerDBList:{}", JSONObject.toJSONString(queryDTO), JSONObject.toJSONString(wmCustomerDBList));
        if (CollectionUtils.isEmpty(wmCustomerDBList)) {
            return list;
        }
        List<WmCustomerDB> customerList = wmCustomerPlatformDataParseService.selectPlateCustomerByIds(wmCustomerDBList);
        for (WmCustomerDB wmCustomerDB : customerList) {
            WmCustomerBasicBo wmCustomerBasicBo = WmCustomerTransUtil.customerDBToBasicBo(wmCustomerDB);
            list.add(wmCustomerBasicBo);
        }
        return list;
    }

    @Override
    public BooleanResult overdueCustomer(long wmCustomerId, WmCustomerBasicBo wmCustomerBasicBo, Integer opUid, String opName) throws TException, WmCustomerException {
        logger.info("overdueCustomer::wmCustomerId = {}, userId = {}, userName = {},wmCustomerBasicBo={}", wmCustomerId, opUid, opName, JSONObject.toJSONString(wmCustomerBasicBo));
        BooleanResult result = new BooleanResult();
        result.setRes(false);
        if (wmCustomerId <= 0 && wmCustomerBasicBo == null) {
            return result;
        }
        WmCustomerDB wmCustomerDB = new WmCustomerDB();
        if (wmCustomerId != 0) {
            wmCustomerDB = wmCustomerPlatformDataParseService.selectCustomerFromPlatformById((int) wmCustomerId);
        } else {
            wmCustomerDB = WmCustomerTransUtil.customerBoToDB(wmCustomerBasicBo);
        }
        //校验是否需要过期处理
        if (!checkWhenOverdueCustomer(wmCustomerDB)) {
            return result;
        }
        Date nowDate = DateUtil.today();
        Date validateDate = DateUtil.fromUnixTime(wmCustomerDB.getValidateDate());
        WmCustomerDB wmCustomerUpdateDB = new WmCustomerDB();
        BeanUtils.copyProperties(wmCustomerDB, wmCustomerUpdateDB);
        //当前时间在过期时间之前表示未过期判断库中是否过期，
        if (nowDate.before(validateDate) || wmCustomerDB.getValidateDate() == 0l) {
            if (wmCustomerDB.getCertificateOverdue() != null
                    && CertificateOverdueEnum.NOEXPIRED.getType() == wmCustomerDB.getCertificateOverdue()) {
                return result;
            }
            wmCustomerUpdateDB.setCertificateOverdue(CertificateOverdueEnum.NOEXPIRED.getType());
            wmCustomerUpdateDB.setId(wmCustomerDB.getId());
        } else {
            if (wmCustomerDB.getCertificateOverdue() != null
                    && CertificateOverdueEnum.EXPIRED.getType() == wmCustomerDB.getCertificateOverdue()) {
                return result;
            }
            wmCustomerUpdateDB.setCertificateOverdue(CertificateOverdueEnum.EXPIRED.getType());
            wmCustomerUpdateDB.setId(wmCustomerDB.getId());
        }
        int count = wmCustomerDBMapper.updateCertificateOverdue(wmCustomerUpdateDB.getId(), wmCustomerUpdateDB.getCertificateOverdue());
        if (count <= 0) {
            return result;
        }
        //当前时间在过期时间之后标识数据从未过期改为过期，发送大象消息
        if (nowDate.after(validateDate) && wmCustomerDB.getValidateDate() != 0l) {
            String daxaingMsg = CertificateDaXiangTypeEnum.getMessage(wmCustomerDB, null, CertificateDaXiangTypeEnum.EXPIRED.getType());
            WmEmploy originalOwner = wmEmployClient.getEmployById(wmCustomerDB.getOwnerUid());
            if (originalOwner != null) {
                DaxiangUtil.push("<EMAIL>", daxaingMsg, originalOwner.getMisId());
            }
        }
        if (wmCustomerBasicBo == null) {
            wmCustomerBasicBo = WmCustomerTransUtil.customerDBToBasicBo(wmCustomerDB);
        }
        wmCustomerBasicBo.setCertificateOverdue(wmCustomerUpdateDB.getCertificateOverdue());
        insertCustomerOpLog(wmCustomerBasicBo.getId(), opUid, opName, wmCustomerBasicBo, wmCustomerDB, WmCustomerOplogBo.OpType.UPDATE);
        result.setRes(true);
        logger.info("客户打过期标记 customerId", wmCustomerDB.getId());
        return result;
    }

    /**
     * 校验是否需要过期处理
     *
     * @param wmCustomerDB
     * @return
     */
    private boolean checkWhenOverdueCustomer(WmCustomerDB wmCustomerDB) {
        //客户不存在
        if (wmCustomerDB == null) {
            return false;
        }
        //非营业执照不处理
        if (wmCustomerDB.getCustomerType() != null
                && wmCustomerDB.getCustomerType() != CustomerType.CUSTOMER_TYPE_BUSINESS.getCode()) {
            return false;
        }
        //非生效状态不处理
        if (wmCustomerDB.getEffective() != null
                && wmCustomerDB.getEffective() != CustomerConstants.EFFECT) {
            return false;
        }
        //非审核通过状态不处理
        if (wmCustomerDB.getAuditStatus() != null
                && CustomerAuditStatus.CUSTOMER_AUDIT_PASS.getCode() != wmCustomerDB.getAuditStatus()) {
            return false;
        }
        //已经过期的不处理
      /*  if (wmCustomerDB.getCertificateOverdue() != null
                && CertificateOverdueEnum.EXPIRED.getType() == wmCustomerDB.getCertificateOverdue()) {
            return false;
        }*/
        //validateDate=null 数据异常
        if (wmCustomerDB.getValidateDate() == null) {
            return false;
        }
        //validateDate=0表示长期有效，不处理
       /* if (wmCustomerDB.getValidateDate() != null && wmCustomerDB.getValidateDate() == 0) {
            return false;
        }*/
        return true;
    }

    @Override
    public ValidateResultBo validateCustomerRealTypeSpInfo(WmCustomerBasicBo wmCustomerBasicBo, Integer opUid) {
        ValidateResultBo validateResultBo = customerRealTypeService.validateCustomerRealTypeSpInfo(wmCustomerBasicBo, opUid);
        return validateResultBo;
    }

    /**
     * 校验一级类型下客户编号唯一
     *
     * @param wmCustomerNumberRepeatVo
     * @return
     */
    @Override
    public WmCustomerDB validateCustomerNumber(WmCustomerNumberRepeatVo wmCustomerNumberRepeatVo) throws WmCustomerException{

        //在客户编号校验规则需要区分新老数据时，需要判断客户为同步数据，则不进行重复编号校验
        if (wmCustomerNumberRepeatVo.getId() < CustomerConstants.CUSTOMER_DATA_BOUND && wmCustomerNumberRepeatVo.getId() > 0) {
            return null;
        }
        List<WmCustomerDB> wmCustomerDBList = wmCustomerPlatformDataParseService.validateCustomerNumber(wmCustomerNumberRepeatVo);

        if (CollectionUtils.isEmpty(wmCustomerDBList)) {
            logger.info("validateCustomerNumber查询结果为空");
            return null;
        }
        for (WmCustomerDB duplicateCustomerDB : wmCustomerDBList) {
            logger.info("validateCustomerNumber,duplicateCustomerDB={}", JSON.toJSONString(duplicateCustomerDB));
            if (MccConfig.getCustomerDuplicateNumberCheck()
                    || duplicateCustomerDB.getId() >= CustomerConstants.CUSTOMER_DATA_BOUND) {
                return duplicateCustomerDB;
            }
        }
        return null;
    }

    /**
     * 校验一级类型下客户编号唯一
     *
     * @param wmCustomerNumberRepeatVo
     * @return
     */
    @Override
    public List<WmCustomerDB> validateCustomerNumberNew(WmCustomerNumberRepeatVo wmCustomerNumberRepeatVo) throws WmCustomerException {
        List<WmCustomerDB> customerDBList = Lists.newArrayList();
        //在客户编号校验规则需要区分新老数据时，需要判断客户为同步数据，则不进行重复编号校验
        if (wmCustomerNumberRepeatVo.getId() < CustomerConstants.CUSTOMER_DATA_BOUND && wmCustomerNumberRepeatVo.getId() > 0) {
            return customerDBList;
        }
        List<WmCustomerDB> wmCustomerDBList = wmCustomerPlatformDataParseService.validateCustomerNumber(wmCustomerNumberRepeatVo);

        if (CollectionUtils.isEmpty(wmCustomerDBList)) {
            logger.info("validateCustomerNumberNew查询结果为空");
            return customerDBList;
        }
        for (WmCustomerDB duplicateCustomerDB : wmCustomerDBList) {
            if (MccConfig.getCustomerDuplicateNumberCheck() || duplicateCustomerDB.getId() >= CustomerConstants.CUSTOMER_DATA_BOUND) {
                if (wmCustomerNumberRepeatVo.getBizOrgCode() != null && duplicateCustomerDB.getBizOrgCode() != null) {
                    // 判断指定业务线下是否有重复的客户
                    if (wmCustomerNumberRepeatVo.getBizOrgCode().intValue() == duplicateCustomerDB.getBizOrgCode().intValue()) {
                        customerDBList.add(duplicateCustomerDB);
                    }
                } else {
                    customerDBList.add(duplicateCustomerDB);
                }
            }
        }
        return customerDBList;
    }

    @Override
    public CustomerRelationStatusEnum getWmPoiRelationStatus(long wmPoiId) {
        return WmCustomerPoiAggre.Factory.make().getWmPoiRelationStatus(wmPoiId);
    }

    @Override
    public WmCustomerPoiBo getWmCustomerIdAndWmPoiRelationStatus(long wmPoiId) {
        return WmCustomerPoiAggre.Factory.make().getWmCustomerIdAndWmPoiRelationStatus(wmPoiId);
    }

    @Override
    public BooleanResult canReleaseFromPreBindStatus(long wmPoiId) {
        return WmCustomerPoiAggre.Factory.make().canReleaseFromPreBindStatus(wmPoiId);
    }

    @Override
    public BooleanResult hasBindCustomerSuccess(long wmPoiId) {
        return WmCustomerPoiAggre.Factory.make().hasBindCustomerSuccess(wmPoiId);
    }

    @Override
    public WmCustomerBasicBo getPreBindCustomerByWmPoiId(long wmPoiId) throws TException, WmCustomerException {
        Integer wmCustomerId = WmCustomerPoiAggre.Factory.make().getPreBindCustomerIdByWmPoiId(wmPoiId);
        if (wmCustomerId == null) {
            return null;
        }
        return wmCustomerService.getCustomerById(wmCustomerId);
    }

    @Override
    public List<WmCustomerDB> searchCustomerListByCondition(SearchCustomerConditionBO searchCustomerConditionBO) throws WmCustomerException {
        List<WmCustomerDB> wmCustomerDBList;
        try {
            SearchCustomerEsConditionDTO dto = new SearchCustomerEsConditionDTO();
            dto.setCustomerRealType(searchCustomerConditionBO.getCustomerRealType());
            dto.setEffective(searchCustomerConditionBO.getEffective());
            dto.setKeyWord(searchCustomerConditionBO.getKeyWord());
            wmCustomerDBList = wmCustomerESService.queryCustomerListByCondition(dto);
            return wmCustomerDBList;
        } catch (Exception e) {
            logger.error("查询ES失败", e);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR,"查询客户失败");
        }
    }


    /**
     * 客户管理批量分配责任人-筛选出的全部数据
     *
     * @param wmCustomerFormBo
     * @return
     * @throws WmCustomerException
     * @throws TException
     */
    public List<Long> getMtCustomerIds(WmCustomerFormBo wmCustomerFormBo) throws WmCustomerException, TException {
        // 参数校验
        validateGetCustomerListParam(wmCustomerFormBo);

        WmCustomerEsQueryVo wmCustomerEsQueryVo = WmCustomerTransUtil.wmCustomerEsQueryVo(wmCustomerFormBo);

        // 数据权限控制
        if (MccCustomerConfig.openHQAuthSplitForCustomer()) {
            buildDataLimit(wmCustomerFormBo, wmCustomerEsQueryVo);
        }

        // 校验签约人手机号与签约人证件号是否能生成token
        wmCustomerESService.checkPhoneNumTokenAndCertNumTokenIsEmpty(wmCustomerFormBo.getSignerPhoneNum(), wmCustomerFormBo.getSignerCertNumber(), wmCustomerEsQueryVo.getSignerCertType());

        if (StringUtils.isNotBlank(wmCustomerFormBo.getSignerPhoneNum())) {
            String phoneNumToken = wmCustomerSensitiveWordsService.encryption(wmCustomerFormBo.getSignerPhoneNum(), KmsKeyNameEnum.PHONE_NO, 0);
            if (StringUtils.isNotBlank(phoneNumToken)) {
                wmCustomerEsQueryVo.setSignerPhoneNumToken(phoneNumToken);
            }
        }

        if (StringUtils.isNotBlank(wmCustomerFormBo.getSignerCertNumber()) && wmCustomerEsQueryVo.getSignerCertType() != null) {
            String certNumberToken = wmCustomerSensitiveWordsService.encryption(wmCustomerFormBo.getSignerCertNumber(), KmsKeyNameEnum.IDENTIFY_ID, wmCustomerEsQueryVo.getSignerCertType());
            if (StringUtils.isNotBlank(certNumberToken)) {
                wmCustomerEsQueryVo.setSignerCertNumberToken(certNumberToken);
            }
        }

        if (wmCustomerFormBo.getWmPoiId() > 0 || !CollectionUtils.isEmpty(wmCustomerFormBo.getBrandIds())) {
            return getMtCustomerIdsDataWithPoiIdAndBrandIds(wmCustomerFormBo, wmCustomerEsQueryVo);
        }

        List<Long> page = getMtCustomerIdsData(wmCustomerEsQueryVo);
        return page;
    }


    /**
     * 获取客户列表分页数据
     *
     * @param vo
     * @return
     */
    private List<Long> getMtCustomerIdsData(WmCustomerEsQueryVo vo) throws WmCustomerException {
        List<Long> wmCustomerListBoList = Lists.newArrayList();
        for (int i = 1; i <= ES_FOR_MAX; i++) {
            List<WmCustomerListDB> wmCustomerListDBList = Lists.newArrayList();
            vo.setPageNo(i);
            vo.setPageSize(ES_PAGE_SIZE);
            try {
                wmCustomerListDBList = wmCustomerESService.queryCustomerPage(vo);
                if (CollectionUtils.isEmpty(wmCustomerListDBList)) {
                    break;
                }
            } catch (Exception e) {
                logger.error("查询es失败 vo={}", JSONObject.toJSONString(vo), e);
            }
            List<Long> tempList = wmCustomerListDBList.stream().map(WmCustomerListDB::getMtCustomerId).distinct().collect(Collectors.toList());
            wmCustomerListBoList.addAll(tempList);
        }
        return wmCustomerListBoList;
    }


    private List<Long> getMtCustomerIdsDataWithPoiIdAndBrandIds(WmCustomerFormBo wmCustomerFormBo, WmCustomerEsQueryVo wmCustomerEsQueryVo) throws WmCustomerException {
        List<Long> result = new ArrayList<>();
        //通过门店ID获取关联该门店的客户集合
        //后期可能会出现一个门店对应多个客户的情况
        Set<Integer> customerIdSet = null;
        if (wmCustomerFormBo.getWmPoiId() > 0) {
            customerIdSet = wmCustomerPoiService.selectCustomerIdByPoiId(Sets.newHashSet(wmCustomerFormBo.getWmPoiId()));
            if (CollectionUtils.isEmpty(customerIdSet)) {
                return result;
            }
        }

        // 根据关联品牌id和已根据门店id查询出的客户id过滤出最终的客户id列表
        if (!CollectionUtils.isEmpty(wmCustomerFormBo.getBrandIds())) {
            if (wmCustomerFormBo.getWmPoiId() > 0) {
                // 门店id和品牌id同时查询的情况需要取交集
                customerIdSet = wmCustomerBrandService.selectCustomerIdByBrandIdsAndCustomerIds(wmCustomerFormBo.getBrandIds(), customerIdSet);
            } else {
                // 只根据品牌id单独查询的情况
                customerIdSet = wmCustomerBrandService.selectCustomerIdByBrandIds(wmCustomerFormBo.getBrandIds());
            }
        }

        if (CollectionUtils.isEmpty(customerIdSet)) {
            return result;
        }

        if (wmCustomerFormBo.getCustomerId() > 0 && !customerIdSet.contains(wmCustomerFormBo.getCustomerId())) {
            return result;
        }

        if (wmCustomerFormBo.getCustomerId() <= 0) {
            wmCustomerEsQueryVo.setCustomerIdList(Lists.newArrayList(customerIdSet));
        }

        result = getMtCustomerIdsData(wmCustomerEsQueryVo);
        return result;
    }


    /**
     * 给客户以及门店打标
     *
     * @param mtCustomerId
     * @param wmCustomerId
     */
    private void addLabelOnCustomerAndPoi(Long mtCustomerId, Integer wmCustomerId) {
        WmPoiLabelRel customerLabel = wmPoiFlowlineLabelThriftServiceAdapter.selectLabelRelCache(mtCustomerId, MccCustomerConfig.getAuditedMSCCustomerLabel(), LabelSubjectTypeEnum.CUSTOMER.getCode());
        logger.info("#selectLabelRelCache,customerLabel={}", JSONObject.toJSONString(customerLabel));
        if (customerLabel == null || customerLabel.getId() <= 0L) {
            //客户打标
            wmPoiFlowlineLabelThriftServiceAdapter.addCustomerLabelRel(MccCustomerConfig.getAuditedMSCCustomerLabel(), mtCustomerId, 0, "系统");
            List<WmCustomerPoiDB> wmCustomerPoiDBList = wmCustomerPoiDBMapper.selectWmCustomerPoiByCustomerId(wmCustomerId);
            List<Long> wmPoiIdList = wmCustomerPoiDBList.stream().map(x -> x.getWmPoiId()).collect(Collectors.toList());
            //门店打标
            wmPoiFlowlineLabelThriftServiceAdapter.batchAddLabelToPois(MccCustomerConfig.getMSCWmPoiLabel(), wmPoiIdList, 0, "系统");
        }
    }


    /**
     * 客户类型变更为美食城后校验美食城档口数是否符合规则，不符合需要 给责任人发送大象消息
     * 目标客户为美食城的话，需要校验档口数是否小于已占用档口数，小于则需要发送消息
     *
     * @param wmCustomerDB
     * @param opUid
     */
    private void checkMscPoiCntRuleAndSendMsg(WmCustomerDB wmCustomerDB, Integer opUid) {
        try {
            //客户是否有资质共用标,有则不需要处理
            boolean hasQuaComCustomerLabel = wmCustomerMSCLabelService.checkHasQuaComCustomerLabel(wmCustomerDB.getMtCustomerId());
            if (hasQuaComCustomerLabel) {
                return;
            }
            //命中灰度&&有资质共用标
            String customerRealTypeSpInfoStr = wmCustomerDB.getCustomerRealTypeSpInfo();
            CustomerRealTypeSpInfoBo customerRealTypeSpInfoBo = JSON.parseObject(customerRealTypeSpInfoStr, CustomerRealTypeSpInfoBo.class);
            CustomerMscUsedPoiDTO customerMscUsedPoiDTO = wmCustomerService.getMscUsedPoiDTO(wmCustomerDB.getId());
            //已维护，但是档口数小于已占用档口数，则需要发消息
            if (customerRealTypeSpInfoBo != null && customerRealTypeSpInfoBo.getFoodCityPoiCount() != null
                    && customerMscUsedPoiDTO != null
                    && customerMscUsedPoiDTO.getUsedPoiCnt() > customerRealTypeSpInfoBo.getFoodCityPoiCount()) {
                String msg = String.format("您负责的美食城客户“%s”（客户ID：%s），“已占用档口数”大于“档口数”，请进行核实。",
                        wmCustomerDB.getCustomerName(), wmCustomerDB.getMtCustomerId());
                WmEmploy wmEmploy = wmEmployClient.getEmployById(wmCustomerDB.getOwnerUid());
                if (wmEmploy == null) {
                    logger.error("checkMscPoiCntRuleAndSendMsg，根据uid未查询到员工信息，不发送大象消息={}", JSONObject.toJSONString(wmCustomerDB));
                    return;
                }
                DaxiangUtil.push("<EMAIL>", msg, Lists.newArrayList(wmEmploy.getMisId()));
            }
        } catch (Exception e) {
            logger.error("checkMscPoiCntRuleAndSendMsg,客户审核通过修改为美食城客户校验档口数规则发生异常,customerId={}", wmCustomerDB.getId(), e);
        }
    }


    /**
     * 客户如果有资质共用标签则删除客户与门店的标签
     *
     * @param wmCustomerDB
     */
    private void deleteQuaComLabelByCustomerId(WmCustomerDB wmCustomerDB) {
        List<WmCustomerPoiDB> wmCustomerPoiDBList = wmCustomerPoiDBMapper.selectWmCustomerPoiByCustomerId(wmCustomerDB.getId());
        if (!CollectionUtils.isEmpty(wmCustomerPoiDBList)) {
            List<Long> wmPoiIdList = wmCustomerPoiDBList.stream().map(x -> x.getWmPoiId()).collect(Collectors.toList());
            //此处方法内有判断是否有资质共用标
            wmCustomerMSCLabelService.deleteQuaComTagOnCusAndPoi(wmCustomerDB.getMtCustomerId(), wmPoiIdList);
        }
    }
    //命中「新增公用资质证明日志记录新格式」灰度
    private boolean hitNewLogRecordFormat(int customerId){
        //白名单判断
        if(MccCustomerConfig.hitNewLogRecordFormatWitheList().contains(customerId)){
            return true;
        }
        //灰度百分比判断
        if(MccCustomerConfig.hitNewLogRecordFormatGrayPercent() > (customerId%100)){
            return true;
        }
        return false;
    }

}

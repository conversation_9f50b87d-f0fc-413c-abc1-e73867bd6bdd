package com.sankuai.meituan.waimai.customer.domain.sc;

import java.util.List;

public class WmScCanteenPoiTaskDO {
    private Long id;

    private Integer canteenIdFrom;

    private Integer canteenIdTo;

    private Integer auditStatus;

    private Integer auditNodeType;

    private Integer valid;

    private Long utime;

    private Long ctime;

    private Integer taskType;

    private Integer userId;

    private String userName;

    private Integer taskReasonType;

    private String taskReason;

    private Long parentTicketId;

    private String currentNodeCode;
    /**
     * 证明材料图片
     */
    private String proofMaterialImage;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getCanteenIdFrom() {
        return canteenIdFrom;
    }

    public void setCanteenIdFrom(Integer canteenIdFrom) {
        this.canteenIdFrom = canteenIdFrom;
    }

    public Integer getCanteenIdTo() {
        return canteenIdTo;
    }

    public void setCanteenIdTo(Integer canteenIdTo) {
        this.canteenIdTo = canteenIdTo;
    }

    public Integer getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(Integer auditStatus) {
        this.auditStatus = auditStatus;
    }

    public Integer getAuditNodeType() {
        return auditNodeType;
    }

    public void setAuditNodeType(Integer auditNodeType) {
        this.auditNodeType = auditNodeType;
    }

    public Integer getValid() {
        return valid;
    }

    public void setValid(Integer valid) {
        this.valid = valid;
    }

    public Long getUtime() {
        return utime;
    }

    public void setUtime(Long utime) {
        this.utime = utime;
    }

    public Long getCtime() {
        return ctime;
    }

    public void setCtime(Long ctime) {
        this.ctime = ctime;
    }

    public Integer getTaskType() {
        return taskType;
    }

    public void setTaskType(Integer taskType) {
        this.taskType = taskType;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName == null ? null : userName.trim();
    }

    public Integer getTaskReasonType() {
        return taskReasonType;
    }

    public void setTaskReasonType(Integer taskReasonType) {
        this.taskReasonType = taskReasonType;
    }

    public String getTaskReason() {
        return taskReason;
    }

    public void setTaskReason(String taskReason) {
        this.taskReason = taskReason;
    }

    public Long getParentTicketId() {
        return parentTicketId;
    }

    public void setParentTicketId(Long parentTicketId) {
        this.parentTicketId = parentTicketId;
    }

    public String getCurrentNodeCode() {
        return currentNodeCode;
    }

    public void setCurrentNodeCode(String currentNodeCode) {
        this.currentNodeCode = currentNodeCode;
    }


    public String getProofMaterialImage() {
        return proofMaterialImage;
    }

    public void setProofMaterialImage(String proofMaterialImage) {
        this.proofMaterialImage = proofMaterialImage;
    }

}
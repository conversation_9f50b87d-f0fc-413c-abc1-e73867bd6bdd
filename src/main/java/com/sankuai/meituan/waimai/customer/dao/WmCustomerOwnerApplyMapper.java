package com.sankuai.meituan.waimai.customer.dao;

import com.sankuai.meituan.waimai.customer.domain.customer.WmCustomerOwnerApply;
import com.sankuai.meituan.waimai.customer.domain.customer.WmCustomerOwnerApplyListDbQuery;
import com.sankuai.meituan.waimai.customer.domain.customer.WmCustomerOwnerApplyQueryBO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @desc 客户责任人申请记录DAO层
 */
public interface WmCustomerOwnerApplyMapper {

    int insert(WmCustomerOwnerApply apply);

    int countByCustomerOwnerApplyQueryBo(WmCustomerOwnerApplyQueryBO queryBO);

    int countMaxIdByQueryBo(WmCustomerOwnerApplyQueryBO queryBO);

    List<WmCustomerOwnerApply> listCustomerOwnerApplyByQueryBo(WmCustomerOwnerApplyQueryBO queryBO);

    /**
     * 根据客户id查询进行中的申请记录
     * 
     * @param customerId
     * @return
     */
    WmCustomerOwnerApply getDoingApplyByCustomerId(@Param("customerId") Integer customerId);


    /**
     * 根据客户id查询申请记录
     * 
     * @param customerId
     * @return
     */
    List<WmCustomerOwnerApply> listByCustomerId(@Param("customerId") Integer customerId);

    /**
     * 根据id更新状态
     *
     * @param id
     * @param status
     */
    void updateStatusById(@Param("id") Integer id, @Param("status") Integer status,
            @Param("oldStatus") Integer oldStatus);

    /**
     * 根据ID查询
     */
    WmCustomerOwnerApply getWmCustomerOwnerApplyById(@Param("id") Integer id);

    /**
     * ` 根据id更新groupId
     *
     * @param id
     * @param groupId
     */
    void updateGroupIdById(@Param("id") Integer id, @Param("groupId") Long groupId);

    /**
     * 根据id更新客户类型
     * @param ids
     * @param customerRealType
     */
    void updateCustomerRealTypesByIds(@Param("ids") List<Integer> ids, @Param("customerRealType") Integer customerRealType);

    /**
     * 根据时间范围或客户Id查询申请中申请单列表
     * @param queryBO
     * @return
     */
    List<WmCustomerOwnerApply> listDoingApplyByTimeCondition(WmCustomerOwnerApplyListDbQuery queryBO);
}
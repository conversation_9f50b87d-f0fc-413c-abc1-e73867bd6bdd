package com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.strategy.precore.bind;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.domain.cuspoirel.bind.CustomerPoiBindFlowContext;
import com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.strategy.precore.IBindPreCoreStrategy;
import com.sankuai.meituan.waimai.customer.service.customer.task.CustomerTaskService;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @desc 直接绑定前置核心操作策略
 * @date 20240115
 */
@Slf4j
@Service
public class DirectBindPreCoreStrategy implements IBindPreCoreStrategy {

    @Autowired
    private CustomerTaskService customerTaskService;

    /**
     * 策略执行逻辑
     *
     * @param context
     * @throws WmCustomerException
     */
    @Override
    public void execute(CustomerPoiBindFlowContext context) throws WmCustomerException {

        WmCustomerDB wmCustomerDB = context.getWmCustomerDB();
        //步骤2：前置操作，创建客户门店绑定任务
        Map<Long, Integer> poiAndTaskMaps = customerTaskService.batchAddCustomerBindTask(wmCustomerDB.getId(), context.getWmPoiIdSet(), context.getCustomerOperateBO());
        context.setPoiAndTaskMaps(poiAndTaskMaps);
        log.info("DirectBindPreCoreStrategy,直接绑定流程的核心前置操作完成,customerId={},wmPoiIds={}", wmCustomerDB.getId(), JSON.toJSONString(context.getWmPoiIdSet()));
    }

}

package com.sankuai.meituan.waimai.customer.service.sign.compare;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.CertifyH5InfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.SignerInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageBatchInfoBo;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * @description: 短信阶段比较器
 * @author: zhangyuanhao02
 * @create: 2025/1/6 10:34
 */
@Slf4j
public class SmsStageInfoBoComparator implements StageBatchInfoBoComparator{

    public static final String SMS_STAGE_PREFIX = "real_name_auth";

    @Override
    public String compare(StageBatchInfoBo source, StageBatchInfoBo target) {
        String sourceStageName = target.getStageName();
        String targetStageName = source.getStageName();
        if (!targetStageName.startsWith(SMS_STAGE_PREFIX)) {
            log.info("SmsStageInfoBoComparator#compare 跳过当前targetStageName:{}, sourceStageName:{}", targetStageName, sourceStageName);
            return Strings.EMPTY;
        }

        StringBuilder failMsg = new StringBuilder();
        // 比较短信发起类型是否一致
        if (!targetStageName.equals(sourceStageName)) {
            failMsg.append("stageName不一致")
                    .append(concatMsg(sourceStageName, targetStageName));
        }

        // 比较短信接受人信息
        SignerInfoBo sourceSignerInfoBo = source.getSignerInfoBo();
        SignerInfoBo targetSignerInfoBo = target.getSignerInfoBo();
        compareSignerInfoBo(sourceSignerInfoBo, targetSignerInfoBo, failMsg);

        return failMsg.toString();
    }

    /**
     * 比较短信接受人信息
     * @param sourceSignerInfoBo
     * @param targetSignerInfoBo
     * @param failMsg
     */
    private void compareSignerInfoBo(SignerInfoBo sourceSignerInfoBo, SignerInfoBo targetSignerInfoBo, StringBuilder failMsg) {
        log.info("SmsStageInfoBoComparator#compareSignerInfoBo source:{}, target:{}", JSONObject.toJSONString(sourceSignerInfoBo), JSONObject.toJSONString(targetSignerInfoBo));

        if (!Objects.equals(sourceSignerInfoBo.getName(), targetSignerInfoBo.getName())) {
            failMsg.append("短信接受人姓名不一致")
                    .append(concatMsg(sourceSignerInfoBo.getName(), targetSignerInfoBo.getName()));
        }

        if (!Objects.equals(sourceSignerInfoBo.getIdCardNo(), targetSignerInfoBo.getIdCardNo())) {
            failMsg.append("短信接受人idCardNo不一致")
                    .append(concatMsg(sourceSignerInfoBo.getIdCardNo(), targetSignerInfoBo.getIdCardNo()));
        }

        if (!Objects.equals(sourceSignerInfoBo.getPhone(), targetSignerInfoBo.getPhone())) {
            failMsg.append("短信接受人手机号不一致")
                    .append(concatMsg(sourceSignerInfoBo.getPhone(), targetSignerInfoBo.getPhone()));
        }

        if (!Objects.equals(sourceSignerInfoBo.getBankName(), targetSignerInfoBo.getBankName())) {
            failMsg.append("短信接受人银行名称不一致")
                    .append(concatMsg(sourceSignerInfoBo.getBankName(), targetSignerInfoBo.getBankName()));
        }

        if (!Objects.equals(sourceSignerInfoBo.getBankCardNo(), targetSignerInfoBo.getBankCardNo())) {
            failMsg.append("短信接受人银行卡号不一致")
                    .append(concatMsg(sourceSignerInfoBo.getBankCardNo(), targetSignerInfoBo.getBankCardNo()));
        }

        if (!Objects.equals(sourceSignerInfoBo.getClientId(), targetSignerInfoBo.getClientId())) {
            failMsg.append("短信接受人clientId不一致")
                    .append(concatMsg(sourceSignerInfoBo.getClientId(), targetSignerInfoBo.getClientId()));
        }

        if (!Objects.equals(sourceSignerInfoBo.getClientSecret(), targetSignerInfoBo.getClientSecret())) {
            failMsg.append("短信接受人clientSecret不一致")
                    .append(concatMsg(sourceSignerInfoBo.getClientSecret(), targetSignerInfoBo.getClientSecret()));
        }

        if (!Objects.equals(sourceSignerInfoBo.getSmsTemplateId(), targetSignerInfoBo.getSmsTemplateId())) {
            failMsg.append("短信接受人短信模板id不一致")
                    .append(concatMsg(sourceSignerInfoBo.getSmsTemplateId(), targetSignerInfoBo.getSmsTemplateId()));
        }

        if (!Objects.equals(sourceSignerInfoBo.getSmsTempletVersion(), targetSignerInfoBo.getSmsTempletVersion())) {
            failMsg.append("短信接受人短信模板版本不一致")
                    .append(concatMsg(sourceSignerInfoBo.getSmsTempletVersion(), targetSignerInfoBo.getSmsTempletVersion()));
        }

        List<String> sourceMobileList = sourceSignerInfoBo.getMobileList();
        List<String> targetMobileList = targetSignerInfoBo.getMobileList();
        if (!Objects.equals(sourceSignerInfoBo.getMobileList(), targetSignerInfoBo.getMobileList())) {
            failMsg.append("短信接受人手机号列表不一致")
                    .append(concatMsg(JSONObject.toJSONString(sourceMobileList), JSONObject.toJSONString(targetMobileList)));
        }

        List<String> sourceChannelList = sourceSignerInfoBo.getChannelList();
        List<String> targetChannelList = targetSignerInfoBo.getChannelList();
        if (!Objects.equals(sourceChannelList, targetChannelList)) {
            failMsg.append("短信接受人短信渠道不一致")
                    .append(concatMsg(JSONObject.toJSONString(sourceChannelList), JSONObject.toJSONString(targetChannelList)));
        }

        compareCertifyH5Info(sourceSignerInfoBo.getCertifyH5InfoBo(), targetSignerInfoBo.getCertifyH5InfoBo(), failMsg);
    }

    /**
     * 比较短信H5认证信息
     * @param source
     * @param target
     * @param failMsg
     */
    private void compareCertifyH5Info(CertifyH5InfoBo source, CertifyH5InfoBo target, StringBuilder failMsg) {
        log.info("SmsStageInfoBoComparator#compareCertifyH5Info source:{},target:{}",JSONObject.toJSONString(source), JSONObject.toJSONString(target));

        if (!Objects.equals(source.getCertType(), target.getCertType())) {
            failMsg.append("短信H5认证类型不一致")
                    .append(concatMsg(source.getCertType(), target.getCertType()));
        }

        if (!Objects.equals(source.getCertPhone(), target.getCertPhone())) {
            failMsg.append("短信H5认证手机号不一致")
                    .append(concatMsg(source.getCertPhone(), target.getCertPhone()));
        }

        if (!Objects.equals(source.getCompanyName(), target.getCompanyName())) {
            failMsg.append("短信H5认证公司名称不一致")
                    .append(concatMsg(source.getCompanyName(), target.getCompanyName()));
        }

        if (!Objects.equals(source.getCompanyNum(), target.getCompanyNum())) {
            failMsg.append("短信H5认证公司统一信用代码不一致")
                    .append(concatMsg(source.getCompanyNum(), target.getCompanyNum()));
        }

        if (!Objects.equals(source.getSignerName(), target.getSignerName())) {
            failMsg.append("短信H5认证签约人姓名不一致")
                    .append(concatMsg(source.getSignerName(), target.getSignerName()));
        }

        if (!Objects.equals(source.getSignerCardType(), target.getSignerCardType())) {
            failMsg.append("短信H5认证签约人证件类型不一致")
                    .append(concatMsg(source.getSignerCardType(), target.getSignerCardType()));
        }

        if (!Objects.equals(source.getSignerCardNum(), target.getSignerCardNum())) {
            failMsg.append("短信H5认证签约人证件号码不一致")
                    .append(concatMsg(source.getSignerCardNum(), target.getSignerCardNum()));
        }

    }
}

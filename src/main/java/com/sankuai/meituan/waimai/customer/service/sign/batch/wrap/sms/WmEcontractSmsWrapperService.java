package com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.sms;

import com.sankuai.meituan.waimai.customer.adapter.WmEmployClient;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageBatchInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;

import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 *【美团外卖】您与美团外卖的$$module$$($$detail$$）$$other$$，请点击链接进行确认：$$shortlink$$，该链接24小时内有效；有问题请联系客户经理$$name$$（$$phone$$）
 * 短信模板解析参考wiki:https://123.sankuai.com/km/page/16752589
 */
@Service
public class WmEcontractSmsWrapperService extends AbstractWmEcontractSmsWrapperService {

    @Resource
    private WmCustomerService wmCustomerService;

    @Resource
    private WmEmployClient wmEmployClient;

    @Override
    public StageBatchInfoBo wrap(EcontractBatchContextBo contextBo) throws TException, WmCustomerException {
        return super.wrap(contextBo);
    }

}

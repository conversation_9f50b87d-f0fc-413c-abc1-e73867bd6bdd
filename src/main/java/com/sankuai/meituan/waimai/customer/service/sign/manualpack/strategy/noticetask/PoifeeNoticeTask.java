package com.sankuai.meituan.waimai.customer.service.sign.manualpack.strategy.noticetask;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.adapter.WmLogisticsGatewayThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.config.MccSignConfig;
import com.sankuai.meituan.waimai.customer.service.sign.applytask.context.ManualPackNoticeContext;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.HeronContractOperator;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.flow.sign.HeronContractManualBatchSignParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.flow.sign.HeronContractManualSignItem;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.flow.sign.HeronContractSignGrayParam;
import com.sankuai.meituan.waimai.poilogistics.thrift.domain.OperateInfo;
import com.sankuai.meituan.waimai.poilogistics.thrift.domain.sign.BatchManualSignInfo;
import com.sankuai.meituan.waimai.poilogistics.thrift.domain.sign.ManualSignInfo;
import com.sankuai.meituan.waimai.poilogistics.thrift.exception.WmPoiLogisticsException;
import com.sankuai.meituan.waimai.poilogistics.thrift.service.WmLogisticsFeeThriftService;
import com.sankuai.meituan.waimai.poilogistics.thrift.service.WmPoiLogisticsSignThriftService;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Author: 窦旭蒙 MIS: douxumeng Date: 2022-03-14 21:23 Email: <EMAIL> Desc:
 */
@Slf4j
@Service
public class PoifeeNoticeTask implements NoticeTask {

    @Autowired
    private WmLogisticsFeeThriftService.Iface wmLogisticsFeeThriftService;

    @Autowired
    private WmPoiLogisticsSignThriftService.Iface wmPoiLogisticsSignThriftService;

    @Autowired
    private WmLogisticsGatewayThriftServiceAdapter wmLogisticsGatewayThriftServiceAdapter;

    @Override
    public void notice(String module, List<Long> bizIdList, ManualPackNoticeContext context, List<Long> taskIds)
            throws WmCustomerException, TException {
        log.info("notice#module:{}, taskInfo:{}", module, JSON.toJSONString(context.getAllTaskInfo()));
        try {
            if (!MccSignConfig.configPoifeeSignUseWmPoiLogisticsSignThriftService()) {
                wmLogisticsFeeThriftService.signWmPoiAllFeeManualPackageInfo(taskIds, context.getManualBatchId(),
                        new OperateInfo().setOpId(context.getCommitUid()));
            } else {
                Map<Long, Long> deliveryTaskWmPoiIdMap = context.getDeliveryTaskWmPoiIdMap();
                if (CollectionUtils.isEmpty(deliveryTaskWmPoiIdMap)) {
                    log.error("deliveryTaskWmPoiIdMap为空");
                    throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "任务数据组装异常");
                }
                List<HeronContractManualSignItem> manualSignItemList = deliveryTaskWmPoiIdMap.entrySet().stream()
                        .map(x -> {
                            HeronContractManualSignItem signItem = new HeronContractManualSignItem();
                            signItem.setWmPoiId(x.getValue());
                            signItem.setManualConfirmId(x.getKey());
                            return signItem;
                        }).collect(Collectors.toList());

                HeronContractSignGrayParam signGrayParam = new HeronContractSignGrayParam();
                signGrayParam.setBatchManualConfirmId(context.getManualBatchId());
                signGrayParam.setSignItemList(manualSignItemList);
                if (wmLogisticsGatewayThriftServiceAdapter.isDeliverySignOperateUseNewIface(signGrayParam)) {
                    HeronContractOperator operator = HeronContractOperator.builder()
                            .opId((long) context.getCommitUid())
                            .build();
                    EcontractTaskApplyTypeEnum applyTypeEnum = deliveryTaskWmPoiIdMap.size() > 1 ?
                            EcontractTaskApplyTypeEnum.BATCHPOIFEE : EcontractTaskApplyTypeEnum.POIFEE;
                    HeronContractManualBatchSignParam manualBatchSignParam = HeronContractManualBatchSignParam.builder()
                            .batchManualConfirmId(context.getManualBatchId())
                            .signItemList(manualSignItemList)
                            .applyType(applyTypeEnum.getName())
                            .operator(operator).build();
                    wmLogisticsGatewayThriftServiceAdapter.deliveryBatchApplySignUseNewIface(manualBatchSignParam);
                } else {
                    List<ManualSignInfo> signInfoList = deliveryTaskWmPoiIdMap.entrySet().stream()
                            .map(x -> new ManualSignInfo(x.getValue(), x.getKey())).collect(Collectors.toList());
                    wmPoiLogisticsSignThriftService.signWmPoiAllFeeManualPackageInfo(
                            new BatchManualSignInfo(signInfoList, context.getManualBatchId()),
                            new OperateInfo().setOpId(context.getCommitUid()));
                }
            }
        } catch (WmPoiLogisticsException e) {
            log.error("signWmPoiAllFeeManualPackageInfo异常", e);
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, e.getMsg());
        }
    }
}

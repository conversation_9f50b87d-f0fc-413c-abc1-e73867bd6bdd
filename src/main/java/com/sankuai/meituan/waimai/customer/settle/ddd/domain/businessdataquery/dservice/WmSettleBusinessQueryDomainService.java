package com.sankuai.meituan.waimai.customer.settle.ddd.domain.businessdataquery.dservice;

import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettleStatusBo;
import java.util.List;
import java.util.Map;

import org.apache.thrift.TException;

import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;

public interface WmSettleBusinessQueryDomainService {

    Map<Long, WmSettleStatusBo> getWmSettleStatusForSwitchingWmPoiId(List<Long> wmPoiId, Long targetWmCustomerId) throws WmCustomerException, TException;

}

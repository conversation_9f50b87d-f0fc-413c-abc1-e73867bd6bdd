package com.sankuai.meituan.waimai.customer.adapter.daocan;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.customer.thrift.common.TPage;
import com.sankuai.meituan.customer.thrift.dto.TPartnerBasic;
import com.sankuai.meituan.customer.thrift.dto.TQueryPartnerBasicReq;
import com.sankuai.meituan.customer.thrift.dto.TQueryPartnerBasicResp;
import com.sankuai.meituan.customer.thrift.exception.TCustomerException;
import com.sankuai.meituan.customer.thrift.service.CustomerBaseService;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DcCustomerBaseAdapter {

    @Autowired
    private CustomerBaseService.Iface customerBaseService;

    /**
     * 到餐客户系统接口默认成功的返回值
     */
    private static final Integer SUCCESS = 200;

    /**
     * 根据uid查询有权限的到餐客户列表
     */

    public Set<Long> queryPartnerBasicByBdIdAndStatus(TQueryPartnerBasicReq tQueryPartnerBasicReq, TPage tPage) throws TException, WmCustomerException {
        log.info("#queryPartnerBasicByBdIdAndStatus-根据uid查询有权限的到餐客户列表-req:{},page:{}", JSON.toJSONString(tQueryPartnerBasicReq),JSON.toJSONString(tPage));
        TQueryPartnerBasicResp tQueryPartnerBasicResp;
        try{
            tQueryPartnerBasicResp = customerBaseService.queryPartnerBasicByBdIdAndStatus(tQueryPartnerBasicReq, tPage);
            log.info("#queryPartnerBasicByBdIdAndStatus-根据uid查询有权限的到餐客户列表-req:{},resp:{}", JSON.toJSONString(tQueryPartnerBasicReq),JSON.toJSONString(tQueryPartnerBasicResp));
        }catch (TCustomerException e) {
            log.error("#queryPartnerBasicByBdIdAndStatus-根据uid查询有权限的到餐客户列表异常-req:{}", JSON.toJSONString(tQueryPartnerBasicReq),e);
            throw new WmCustomerException(e.getCode(),e.getMessage());
        }
        if (tQueryPartnerBasicResp == null){
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR,"查询结果为空");
        }
        if (SUCCESS != tQueryPartnerBasicResp.getCode()){
            log.error("#queryPartnerBasicByBdIdAndStatus-根据uid查询有权限的到餐客户列表异常-req:{},msg:{}", JSON.toJSONString(tQueryPartnerBasicReq),tQueryPartnerBasicResp.getMsg());
            throw new WmCustomerException(tQueryPartnerBasicResp.getCode(),tQueryPartnerBasicResp.getMsg());
        }

        return tQueryPartnerBasicResp.getPartnerList().stream()
                .map(TPartnerBasic::getCustomerId)
                .collect(Collectors.toSet());
    }
}

package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.c1contract;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.sankuai.meituan.util.DateUtil;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.ContractSignEstampSubjectEnum;
import com.sankuai.meituan.waimai.customer.settle.service.WmSettlePdfTransService;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.constants.PdfConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerType;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractContractInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

/**
 * Author: 窦旭蒙
 * MIS: douxumeng
 * Date: 2021-08-17 15:25
 * Email: <EMAIL>
 * Desc:
 */
@Slf4j
public class WmEcontractC1ContractQDBContentGenerate {
    /**
     * 生成C1合同钱袋宝协议信息数据
     */
    public Map<String, String> generatePdfObject(EcontractBatchContextBo contextBo, EcontractTaskBo taskBo) {
        log.info("WmEcontractC1ContractQDBContentGenerate contextBo = {}", JSON.toJSONString(contextBo));
        String signTime = DateUtil.secondsToString(DateUtil.unixTime());

        Map<String, String> pdfMap = Maps.newHashMap();
        pdfMap.put("partA", StringUtils.defaultIfEmpty(contextBo.getCustomerInfoBo().getCustomerName(), StringUtils.EMPTY));
        pdfMap.put("partAStampName", StringUtils.defaultIfEmpty(WmEcontractContextUtil.getCustomerName(contextBo), StringUtils.EMPTY));
        pdfMap.put("partAAddress", StringUtils.defaultIfEmpty(contextBo.getCustomerInfoBo().getAddress(), StringUtils.EMPTY));
        pdfMap.put("signerEmail", StringUtils.defaultIfEmpty(contextBo.getKpBo().getSignerEmail(), StringUtils.EMPTY));
        pdfMap.put("partAContact", StringUtils.defaultIfEmpty(contextBo.getKpBo().getSignerName(), StringUtils.EMPTY));// 甲方签约人
        pdfMap.put("partAPhone", StringUtils.defaultIfEmpty(contextBo.getKpBo().getSignerPhoneNum(), StringUtils.EMPTY));
        pdfMap.put("partBSignTime", StringUtils.defaultIfEmpty(signTime, StringUtils.EMPTY));
        pdfMap.put("partAEstamp", PdfConstant.POI_SIGNKEY);
        pdfMap.put("qdbEstamp", PdfConstant.QDB_SIGNKEY);
        pdfMap.put("qdbNumber", WmSettlePdfTransService.genQdbNumber(contextBo.getCustomerId()));

        log.info("WmEcontractC1ContractQDBContentGenerate pdfMap = {}", JSON.toJSONString(pdfMap));
        return pdfMap;
    }
}
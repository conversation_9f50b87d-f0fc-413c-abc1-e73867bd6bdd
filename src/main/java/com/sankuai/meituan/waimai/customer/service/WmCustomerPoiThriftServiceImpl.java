package com.sankuai.meituan.waimai.customer.service;

import com.alibaba.fastjson.JSON;
import com.meituan.service.mobile.mtthrift.util.ClientInfoUtil;
import com.sankuai.meituan.waimai.customer.aspect.CustomerDynamicRouter;
import com.sankuai.meituan.waimai.customer.config.MccConfig;

import java.util.List;
import java.util.Map;
import java.util.Set;

import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.constant.customer.CustomerPoiBindTypeEnum;
import com.sankuai.meituan.waimai.customer.constant.customer.CustomerPoiUnBindTypeEnum;
import com.sankuai.meituan.waimai.customer.constant.customer.WmCustomerPoiOplogSourceTypeEnum;
import com.sankuai.meituan.waimai.customer.domain.poi.WmCustomerPoiBindParamBo;
import com.sankuai.meituan.waimai.customer.domain.poi.WmCustomerPoiUnBindParamBo;
import com.sankuai.meituan.waimai.customer.domain.task.CustomerOperateBO;
import com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.flow.PoiBindCustomerFlowService;
import com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.flow.PoiUnBindCustomerFlowService;
import com.sankuai.meituan.waimai.customer.service.gray.CustomerPoiRelFlowGrayService;
import com.sankuai.meituan.waimai.customer.service.customer.*;
import com.sankuai.meituan.waimai.customer.service.customer.monitor.WmCustomerPoiAttributeAgreeMonitor;
import com.sankuai.meituan.waimai.customer.service.customer.monitor.WmCustomerPoiAttributeDataLegalMonitor;
import com.sankuai.meituan.waimai.customer.service.customer.monitor.WmCustomerPoiLabelMonitor;
import com.sankuai.meituan.waimai.customer.service.customer.monitor.WmCustomerPoiMonitor;
import com.sankuai.meituan.waimai.customer.service.customer.monitor.customerPoiList.WmCustomerPoiListMonitor;
import com.sankuai.meituan.waimai.customer.service.customer.poi.CustomerPoiBindService;
import com.sankuai.meituan.waimai.customer.service.customer.poi.CustomerPoiUnBindService;
import com.sankuai.meituan.waimai.datasource.multi.MultipleDataSource;
import com.sankuai.meituan.waimai.poibizflow.thrift.switcher.mq.CustomerSwitchNotifyMsg;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.task.*;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.monitor.MonitorCustomerPoiAttributeAgreeDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.monitor.MonitorCustomerPoiAttributeDataLegalDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.monitor.MonitorCustomerPoiDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.monitor.MonitorCustomerSwitchDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.poirel.CustomerPoiBindDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.poirel.CustomerPoiUnBindDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.github.pagehelper.PageHelper;
import com.sankuai.meituan.waimai.customer.ddd.contract.DDDGrayUtil;
import com.sankuai.meituan.waimai.customer.ddd.domain.customer.aggre.WmCustomerPoiAggre;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiSmsRecordDB;
import com.sankuai.meituan.waimai.customer.util.PageUtil;
import com.sankuai.meituan.waimai.customer.util.trans.WmCustomerTransUtil;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.domain.PageData;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.LongResult;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.*;
import com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerPoiThriftService;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
public class WmCustomerPoiThriftServiceImpl implements WmCustomerPoiThriftService {
    @Autowired
    private WmCustomerPoiService wmCustomerPoiService;

    @Autowired
    private WmCustomerSwitchService wmCustomerSwitchService;

    @Autowired
    private WmCustomerPoiListEsService wmCustomerPoiListEsService;

    @Autowired
    private WmCustomerPoiListMonitor wmCustomerPoiListMonitor;

    @Autowired
    private WmCustomerPoiLabelMonitor wmCustomerPoiLabelMonitor;

    @Autowired
    private WmCustomerPoiAttributeService wmCustomerPoiAttributeService;

    @Autowired
    private WmCustomerPoiAttributeAgreeMonitor wmCustomerPoiAttributeAgreeMonitor;

    @Autowired
    private WmCustomerPoiAttributeDataLegalMonitor wmCustomerPoiAttributeDataLegalMonitor;

    @Autowired
    private WmCustomerPoiMonitor wmCustomerPoiMonitor;

    @Autowired
    private WmCustomerPoiBrandNoticeService wmCustomerPoiBrandNoticeService;

    @Autowired
    private CustomerPoiBindOrUnbindService customerPoiBindOrUnbindService;

    @Autowired
    private CustomerPoiBindService customerPoiBindService;

    @Autowired
    private CustomerPoiUnBindService customerPoiUnBindService;

    @Autowired
    private CustomerPoiRelFlowGrayService flowGrayService;

    @Autowired
    private PoiBindCustomerFlowService poiBindCustomerFlowService;

    @Autowired
    private WmCustomerPoiRelService wmCustomerPoiRelService;

    @Autowired
    private WmCustomerService wmCustomerService;

    @Autowired
    private PoiUnBindCustomerFlowService poiUnBindCustomerFlowService;

    /**
     * 标准API客户门店绑定
     *
     * @param customerPoiBindDTO
     * @throws TException
     * @throws WmCustomerException
     */
    @Override
    public void customerBindPoiByParams(CustomerPoiBindDTO customerPoiBindDTO) throws TException, WmCustomerException {
        log.info("customerBindPoiByParams,customerPoiBindDTO={}", JSON.toJSONString(customerPoiBindDTO));
        //参数非空
        if (customerPoiBindDTO == null) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "参数不合法");
        }
        //客户ID和门店ID列表非空判断
        if (customerPoiBindDTO.getCustomerId() == null || customerPoiBindDTO.getCustomerId() <= 0
                || CollectionUtils.isEmpty(customerPoiBindDTO.getWmPoiIds())) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "客户ID或wmPoiIds参数不合法");
        }

        //渠道非空判断
        if (customerPoiBindDTO.getOpSource() == null || customerPoiBindDTO.getOpSource() < 0) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "opSource参数不合法");
        }
        //详细业务入口来源
        if (StringUtils.isBlank(customerPoiBindDTO.getOpSourceDetail())) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "opSourceDetail参数必传");
        }
        int customerId = customerPoiBindDTO.getCustomerId();
        CustomerOperateBO customerOperateBO = new CustomerOperateBO.Builder()
                .opSource(customerPoiBindDTO.getOpSource())
                .opDetailSource(customerPoiBindDTO.getOpSourceDetail())
                .opSystem(customerPoiBindDTO.getSystemName())
                .opUserId(customerPoiBindDTO.getOpUId())
                .opUserName(customerPoiBindDTO.getOpUName())
                .taskSceneType(customerPoiBindDTO.getSceneType())
                .taskType(customerPoiBindDTO.getTaskType())
                .bizTaskId(customerPoiBindDTO.getBizTaskId())
                .build();

        if (flowGrayService.hitPoiBindCustomerFlowGray(customerId)) {
            poiBindCustomerFlowService.bindByFlowAndRule(customerPoiBindDTO, customerOperateBO);
        } else {
            customerPoiBindService.bindOrRestart(new WmCustomerPoiBindParamBo(customerId, customerPoiBindDTO.getWmPoiIds(),
                    customerPoiBindDTO.getRemark(), customerPoiBindDTO.getOpUId(), customerPoiBindDTO.getOpUName(), customerPoiBindDTO.isCheckPoiVersion(),
                    CustomerTaskSourceEnum.of(customerPoiBindDTO.getOpSource()), customerOperateBO, CustomerPoiBindTypeEnum.DIRECT_BIND));
        }

    }

    /**
     * 标准API客户门店解绑
     *
     * @param customerPoiUnBindDTO
     * @throws TException
     * @throws WmCustomerException
     */
    @Override
    public String customerUnBindPoiByParams(CustomerPoiUnBindDTO customerPoiUnBindDTO) throws TException, WmCustomerException {
        log.info("customerUnBindPoiByParams,customerPoiUnBindDTO={}", JSON.toJSONString(customerPoiUnBindDTO));
        //参数非空
        if (customerPoiUnBindDTO == null) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "参数不合法");
        }
        //客户ID和门店ID列表非空判断
        if (customerPoiUnBindDTO.getCustomerId() == null || customerPoiUnBindDTO.getCustomerId() <= 0
                || CollectionUtils.isEmpty(customerPoiUnBindDTO.getWmPoiIds())) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "客户ID或wmPoiIds参数不合法");
        }
        //渠道非空判断
        if (customerPoiUnBindDTO.getOpSource() == null || customerPoiUnBindDTO.getOpSource() < 0) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "opSource参数不合法");
        }

        CustomerOperateBO customerOperateBO = new CustomerOperateBO.Builder()
                .opSource(customerPoiUnBindDTO.getOpSource())
                .opDetailSource(customerPoiUnBindDTO.getOpSourceDetail())
                .opSystem(customerPoiUnBindDTO.getSystemName())
                .opUserId(customerPoiUnBindDTO.getOpUId())
                .opUserName(customerPoiUnBindDTO.getOpUName())
                .taskSceneType(customerPoiUnBindDTO.getSceneType())
                .taskType(customerPoiUnBindDTO.getTaskType())
                .bizTaskId(customerPoiUnBindDTO.getBizTaskId())
                .build();

        //命中新流程解绑灰度
        if (flowGrayService.hitPoiUnBindCustomerFlowGray(customerPoiUnBindDTO.getCustomerId())) {
            return poiUnBindCustomerFlowService.unBindByFlowAndRule(customerPoiUnBindDTO, customerOperateBO);
        } else {
            //客户门店关系解绑操作
            return customerPoiUnBindService.unBind(new WmCustomerPoiUnBindParamBo(customerPoiUnBindDTO.getCustomerId(), customerPoiUnBindDTO.getWmPoiIds()
                    , customerPoiUnBindDTO.getRemark(), customerPoiUnBindDTO.getOpUId(), customerPoiUnBindDTO.getOpUName(),
                    CustomerTaskSourceEnum.of(customerPoiUnBindDTO.getOpSource()), customerOperateBO, CustomerPoiUnBindTypeEnum.DIRECT_UNBIND));
        }


    }

    /**
     * 绑定门店（基本信息绑定）
     * 接口无流程，后续将下线不在提供服务
     *
     * @param customerId
     * @param wmPoiIdSet
     * @param opUid
     * @param opName
     * @throws TException
     * @throws WmCustomerException
     */
    @Override
    @Deprecated
    public void customerBindPoi(int customerId, Set<Long> wmPoiIdSet, int opUid, String opName) throws TException, WmCustomerException {
        if (MccCustomerConfig.getCustomerPoiBindOfflineSwitch()) {
            log.error("customerBindPoi,接口下线，有请求调用,appKey={},customerId={},wmPoiIdSet={}", ClientInfoUtil.getClientAppKey(), customerId, JSON.toJSONString(wmPoiIdSet));
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "接口已下线不再提供服务");
        }
        customerPoiBindService.bindOrRestart(new WmCustomerPoiBindParamBo(customerId, wmPoiIdSet, "", opUid, opName, true, CustomerTaskSourceEnum.POI_BASE_INFO, null, CustomerPoiBindTypeEnum.DIRECT_BIND));
    }

    /**
     * 客户绑定门店（多店入驻、闪购多店自入驻）
     * 接口无流程，后续将下线不在提供服务
     *
     * @param customerId
     * @param wmPoiIdSet
     * @param opUid
     * @param opName
     * @throws TException
     * @throws WmCustomerException
     */
    @Override
    @Deprecated
    public void customerBindPoiWithoutPoiVersionCheck(int customerId, Set<Long> wmPoiIdSet, int opUid, String opName) throws TException, WmCustomerException {
        if (MccCustomerConfig.getCustomerPoiBindOfflineSwitch()) {
            log.error("customerBindPoiWithoutPoiVersionCheck,接口下线，有请求调用,appKey={},customerId={},wmPoiIdSet={}", ClientInfoUtil.getClientAppKey(), customerId, JSON.toJSONString(wmPoiIdSet));
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "接口已下线不再提供服务");
        }
        customerPoiBindService.bindOrRestart(new WmCustomerPoiBindParamBo(customerId, wmPoiIdSet, "", opUid, opName, false, CustomerTaskSourceEnum.DUODIAN_SETTLE, null, CustomerPoiBindTypeEnum.DIRECT_BIND));
    }

    @Override
    public void customerBindPoiWithSource(int customerId, Set<Long> wmPoiIdSet, int opUid, String opName, int sourceCode) throws TException, WmCustomerException {
        if (MccCustomerConfig.getCustomerPoiBindOfflineSwitch()) {
            log.error("customerBindPoiWithSource,接口下线，有请求调用,appKey={},customerId={},wmPoiIdSet={}", ClientInfoUtil.getClientAppKey(), customerId, JSON.toJSONString(wmPoiIdSet));
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "接口已下线不再提供服务");
        }

        //添加appKey适配电销系统调用请求，目前只有电销调用
        CustomerOperateBO customerOperateBO = customerPoiBindOrUnbindService.getZRZCustomerOperateDTO(ClientInfoUtil.getClientAppKey());
        if (customerOperateBO != null) {
            customerOperateBO.setTaskType(CustomerTaskTypeEnum.CUSTOMER_BIND_POI.getCode());
            customerOperateBO.setTaskSceneType(CustomerTaskSceneType.CUSTOMER_BIND_OR_UNBIND.getCode());
            customerOperateBO.setOpUserId(opUid);
            customerOperateBO.setOpUserName(opName);
        }
        WmCustomerPoiOplogSourceTypeEnum oplogSourceTypeEnum = WmCustomerPoiOplogSourceTypeEnum.findByCode(sourceCode);
        CustomerTaskSourceEnum taskSourceEnum = CustomerTaskSourceEnum.of(oplogSourceTypeEnum.getTaskSource());
        customerPoiBindService.bindOrRestart(new WmCustomerPoiBindParamBo(customerId, wmPoiIdSet, "", opUid, opName, false, taskSourceEnum, customerOperateBO, CustomerPoiBindTypeEnum.DIRECT_BIND));
    }

    /**
     * 绑定门店 二期- (客户门店列表批量绑定)
     * 接口无流程，后续将下线不在提供服务
     *
     * @param customerId
     * @param wmPoiIdSet
     * @param opUid
     * @param opName
     * @param remark
     * @param checkPoiVersion
     * @throws TException
     * @throws WmCustomerException
     */
    @Override
    @Deprecated
    public void customerBindPoiWithRemark(int customerId, Set<Long> wmPoiIdSet, String remark, int opUid, String opName, boolean checkPoiVersion) throws TException, WmCustomerException {
        if (MccCustomerConfig.getCustomerPoiBindOfflineSwitch()) {
            log.error("customerBindPoiWithRemark,接口下线，有请求调用,appKey={},customerId={},wmPoiIdSet={}", ClientInfoUtil.getClientAppKey(), customerId, JSON.toJSONString(wmPoiIdSet));
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "接口已下线不再提供服务");
        }
        customerPoiBindService.bindOrRestart(new WmCustomerPoiBindParamBo(customerId, wmPoiIdSet, remark, opUid, opName, checkPoiVersion, CustomerTaskSourceEnum.BD_SETTLE, null, CustomerPoiBindTypeEnum.DIRECT_BIND));
    }

    /**
     * 只有客户门店列表-关联门店使用，其他场景勿用此接口
     *
     * @param customerId
     * @param wmPoiIdSet
     * @param opUid
     * @param opName
     * @param source
     * @throws TException
     * @throws WmCustomerException
     */
    @Override
    public void reStartCustomerPreBindPoi(int customerId, Set<Long> wmPoiIdSet, int opUid, String opName, int source) throws TException, WmCustomerException {
        CustomerOperateBO customerOperateBO = new CustomerOperateBO.Builder()
                .opUserId(opUid).opUserName(opName)
                .opSource(CustomerTaskSourceEnum.BD_SETTLE.getCode())
                .opDetailSource(CustomerTaskDetailSourceEnum.RE_START_BIND.getDesc())
                .opSystem(CustomerTaskOpSystemEnum.CUSTOMER_WEB.getDesc())
                .taskType(CustomerTaskTypeEnum.CUSTOMER_BIND_POI.getCode())
                .taskSceneType(CustomerTaskSceneType.CUSTOMER_BIND_OR_UNBIND.getCode()).build();
        customerPoiBindService.bindOrRestart(new WmCustomerPoiBindParamBo(customerId, wmPoiIdSet, "", opUid, opName, true, CustomerTaskSourceEnum.BD_SETTLE, customerOperateBO, CustomerPoiBindTypeEnum.RE_START_BIND));
    }

    /**
     * 客户切换流程中-新客下重新发起预绑定门店,其他场景勿用
     *
     * @param switchTaskId
     * @param customerId
     * @param wmPoiIdSet
     * @param opUid
     * @param opName
     * @param source
     * @throws TException
     * @throws WmCustomerException
     */
    @Override
    public void reStartCustomerPreBindPoiForSwitch(long switchTaskId, int customerId, Set<Long> wmPoiIdSet, int opUid, String opName, int source) throws TException, WmCustomerException {
        wmCustomerSwitchService.reStartCustomerPreBindPoiForSwitch(switchTaskId, customerId, wmPoiIdSet, opUid, opName, source);
    }

    /**
     * 解绑门店
     * 接口无流程，后续将下线不在提供服务
     *
     * @param customerId
     * @param wmPoiIdSet
     * @param opUid
     * @param opName
     * @throws TException
     * @throws WmCustomerException
     * @throws WmServerException
     */
    @Override
    @Deprecated
    public String customerUnBindPoi(int customerId, Set<Long> wmPoiIdSet, int opUid, String opName) throws TException, WmCustomerException {
        if (MccCustomerConfig.getCustomerPoiUnBindOfflineSwitch()) {
            log.error("customerUnBindPoi,接口下线，有请求调用,appKey={},customerId={},wmPoiIdSet={}", ClientInfoUtil.getClientAppKey(), customerId, JSON.toJSONString(wmPoiIdSet));
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "接口已下线不再提供服务");
        }
        return customerPoiUnBindService.unBind(new WmCustomerPoiUnBindParamBo(customerId, wmPoiIdSet
                , "", opUid, opName,
                CustomerTaskSourceEnum.UN_KNOWN, null, CustomerPoiUnBindTypeEnum.DIRECT_UNBIND));
    }

    /**
     * 解绑门店与所对应的客户关联。</br>
     * 此方法只是解绑，不需要kp进行确认，不判断门店状态，不把门店置为下线。
     * （门店释放专用）
     *
     * @param wmPoiIdSet
     * @param opUid
     * @param opName
     * @throws TException
     * @throws WmCustomerException
     * @throws WmServerException
     */
    @Override
    public void unBindPoiOfCustomer(Set<Long> wmPoiIdSet, int opUid, String opName) throws TException, WmCustomerException {
        customerPoiUnBindService.poiReleaseUnBind(wmPoiIdSet, opUid, opName);
    }

    /**
     * 客户解绑门店 二期(客户门店列表批量解绑)
     * 接口无流程，后续将下线不在提供服务
     *
     * @param customerId
     * @param wmPoiIdSet
     * @param remark
     * @param opUid
     * @param opName
     * @throws TException
     * @throws WmCustomerException
     */
    @Override
    @Deprecated
    public String customerUnBindPoiWithRemark(int customerId, Set<Long> wmPoiIdSet, String remark, int opUid, String opName) throws TException, WmCustomerException {
        if (MccCustomerConfig.getCustomerPoiUnBindOfflineSwitch()) {
            log.error("customerUnBindPoiWithRemark,接口下线，有请求调用,appKey={},customerId={},wmPoiIdSet={}", ClientInfoUtil.getClientAppKey(), customerId, JSON.toJSONString(wmPoiIdSet));
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "接口已下线不再提供服务");
        }

        return customerPoiUnBindService.unBind(new WmCustomerPoiUnBindParamBo(customerId, wmPoiIdSet,
                remark, opUid, opName, CustomerTaskSourceEnum.BD_SETTLE, null, CustomerPoiUnBindTypeEnum.DIRECT_UNBIND));
    }

    /**
     * 统计绑定门店数
     *
     * @param customerId
     * @return
     * @throws TException
     * @throws WmCustomerException
     */
    @Override
    @CustomerDynamicRouter
    public Integer countCustomerPoi(int customerId) throws TException, WmCustomerException {
        if (customerId == 0) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "客户ID不能为0");
        }
        return WmCustomerPoiAggre.Factory.make().countCustomerPoi(customerId);
    }

    /**
     * 统计解绑中门店数
     *
     * @param customerId
     * @return
     * @throws TException
     * @throws WmCustomerException
     */
    @Override
    public Integer countUnbindingPoi(int customerId) throws TException, WmCustomerException {
        if (customerId == 0) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "客户ID不能为0");
        }
        return WmCustomerPoiAggre.Factory.make().countUnbindingPoi(customerId);
    }

    /**
     * 获取解绑中列表
     *
     * @param customerId
     * @param pageSize
     * @param pageNo
     * @return
     * @throws TException
     * @throws WmCustomerException
     */
    @Override
    public WmUnBindingPoiPageDate unbindingPoiList(int customerId, int pageSize, int pageNo) throws TException, WmCustomerException {
        PageHelper.startPage(pageNo, pageSize, true, false);
        List<WmCustomerPoiSmsRecordDB> list = WmCustomerPoiAggre.Factory.make().selectSmsRecordListByCustomerId(customerId);
        List<WmUnBindingPoiListBo> unBindingPoiList = WmCustomerTransUtil.smsRecordToUnBindingPoiList(list);
        PageData<WmUnBindingPoiListBo> page = PageUtil.page(list, unBindingPoiList);
        return new WmUnBindingPoiPageDate(page.getPageInfo(), page.getList());
    }

    /**
     * 重新发送短信
     *
     * @param customerId
     * @param taskId
     * @param opUid
     * @param opName
     * @throws TException
     * @throws WmCustomerException
     */
    @Override
    @Deprecated
    public void reSendUnBindSms(int customerId, long taskId, int opUid, String opName) throws TException, WmCustomerException {
        log.error("reSendUnBindSms接口已下线,如还有使用请联系liuzhihao09");
        throw new WmCustomerException(CustomerErrorCodeConstants.SYSTEM_ERROR, "接口已下线,如还有使用请联系liuzhihao09");
    }

    /**
     * 取消解绑
     *
     * @param customerId
     * @param taskId
     * @param opUid
     * @param opName
     * @throws TException
     * @throws WmCustomerException
     */
    @Override
    public void cancelUnBind(int customerId, long taskId, int opUid, String opName) throws TException, WmCustomerException {
        wmCustomerPoiService.cancelUnBind(customerId, taskId, opUid, opName);
    }

    /**
     * 强制解绑
     *
     * @param customerId
     * @param taskId
     * @param opUid
     * @param opName
     * @throws TException
     * @throws WmCustomerException
     * @throws WmServerException
     */
    @Override
    public void forceUnbind(int customerId, long taskId, int opUid, String opName) throws TException, WmCustomerException {
        wmCustomerPoiService.forceUnbind(customerId, taskId, opUid, opName);
    }

    /**
     * 根据客户ID查询绑定的门店Ids
     *
     * @param customerId
     * @return
     * @throws TException
     * @throws WmCustomerException
     */
    @Override
    @CustomerDynamicRouter
    public List<Long> selectWmPoiIdsByCustomerId(int customerId) throws TException, WmCustomerException {
        return WmCustomerPoiAggre.Factory.make().selectWmPoiIdsByCustomerId(customerId);
    }


    @Override
    public List<WmCustomerPoiBo> selectWmCustomerPoiByCustomerIds(Set<Integer> customerIdSet) throws TException, WmCustomerException {
        return WmCustomerPoiAggre.Factory.make().selectWmCustomerPoiByCustomerIds(customerIdSet);
    }


    /**
     * 门店生成pdf
     *
     * @param wmCustomerGeneratePdfPoiBo
     * @param opUid
     * @throws TException
     * @throws WmCustomerException
     */
    @Override
    public LongResult generatePdfPoi(WmCustomerGeneratePdfPoiBo wmCustomerGeneratePdfPoiBo, int opUid, String opName)
            throws TException, WmCustomerException {

        if (MccConfig.customerPoiGenPdfUseNewData()) {
            return wmCustomerPoiService.generatePdfPoiNew(wmCustomerGeneratePdfPoiBo, opUid, opName);
        } else {
            return wmCustomerPoiService.generatePdfPoi(wmCustomerGeneratePdfPoiBo, opUid, opName);
        }
    }

    /***
     * 查询客户门店生成pdf列表
     * @param customerId
     * @param pageNo
     * @param pageSize
     * @param id
     * @param name
     * @return
     */
    @Override
    public WmCustomerPoiPdfPage queryPdfPoiList(Integer customerId, Integer pageNo, Integer pageSize, int id, String name) throws TException, WmCustomerException {
        if (DDDGrayUtil.customerUseNewService()) {
            return WmCustomerPoiAggre.Factory.make().queryPdfPoiList(customerId, pageNo, pageSize, id, name);
        }
        return wmCustomerPoiService.queryPdfPoiList(customerId, pageNo, pageSize, id, name);
    }

    @Override
    public Map<Integer, List<Long>> selectWmPoiIdsByCustomerIdList(List<Integer> customerIdList) throws TException, WmCustomerException {
        return WmCustomerPoiAggre.Factory.make().selectWmPoiIdsByCustomerIdList(customerIdList);
    }

    @Override
    public Boolean checkPoiCateChange(long wmPoiId, int customerId, long firstCategory, int opUid, String opName) throws TException, WmCustomerException {
        return WmCustomerPoiAggre.Factory.make().checkPoiCateChange(wmPoiId, customerId, firstCategory, opUid, opName);
//        return wmCustomerPoiService.checkPoiCateChange(wmPoiId, customerId, firstCategory, opUid, opName);
    }

    @Override
    public int getActivePoiSizeByCustomerId(long mtCustomerId) throws TException, WmCustomerException {
        return wmCustomerPoiService.getActivePoiSizeByCustomerId(mtCustomerId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void forceUnbindUnconfirm(WmCustomerForceUnbindUnconfirmDTO data) throws TException, WmCustomerException {
        wmCustomerSwitchService.forceUnbindUnconfirm(data, null);
    }

    @Override
    public List<WmCustomerPoiBo> selectCustomerPoiRelByCondition(WmCustomerPoiQueryConditionDTO queryConditionDTO) throws TException, WmCustomerException {
        return WmCustomerPoiAggre.Factory.make().selectCustomerPoiRelByCondition(queryConditionDTO);
    }

    @Override
    @Deprecated
    public void operateCustomerSwitch(WmCustomerSwitchingBo bo) throws TException, WmCustomerException {
        log.error("operateCustomerSwitch接口已下线,如还有使用请联系liuzhihao09");
        throw new WmCustomerException(CustomerErrorCodeConstants.SYSTEM_ERROR, "接口已下线,如还有使用请联系liuzhihao09");
    }

    @Override
    public WmCustomerPoiListPageData queryCustomerPoiList(WmCustomerPoiListConditionDTO condition) throws TException, WmCustomerException {
        return wmCustomerPoiListEsService.queryData(condition);
    }

    @Override
    public Integer syncCustomerPoiListToEs(int idStart, int idEnd, int opUid, String opName) throws TException, WmCustomerException {
        return WmCustomerPoiAggre.Factory.make().syncCustomerPoiListToEs(idStart, idEnd, opUid, opName);
    }

    @Override
    @Deprecated
    public void syncBrandIdAndOwnerUidToCustomerPoiListEsGray(List<Integer> customerIdList) throws TException, WmCustomerException {
        log.error("syncBrandIdAndOwnerUidToCustomerPoiListEsGray接口已下线,如还有使用请联系liuzhihao09");
        throw new WmCustomerException(CustomerErrorCodeConstants.SYSTEM_ERROR, "接口已下线,如还有使用请联系liuzhihao09");
    }

    @Override
    @Deprecated
    public void syncBrandIdAndOwnerUidToCustomerPoiListEsAll(int idStart, int idEnd) throws WmCustomerException, TException {
        log.error("syncBrandIdAndOwnerUidToCustomerPoiListEsAll接口已下线,如还有使用请联系liuzhihao09");
        throw new WmCustomerException(CustomerErrorCodeConstants.SYSTEM_ERROR, "接口已下线,如还有使用请联系liuzhihao09");
    }

    @Override
    public void syncBrandNoticeToCustomerPoiListEs(CustomerPoiBrandNoticeDto customerPoiBrandNoticeDto) throws TException, WmCustomerException {
        wmCustomerPoiBrandNoticeService.syncBrandNoticeToCustomerPoiListEs(customerPoiBrandNoticeDto);
    }

    @Override
    public String monitorCustomerPoiListEs(WmCustomerPoiListUpdateInfo info) throws TException, WmCustomerException {
        return wmCustomerPoiListMonitor.check(info);
    }

    @Override
    public String monitorCustomerPoiLabel(WmCustomerPoiLabelInfo info) throws TException, WmCustomerException {
        return wmCustomerPoiLabelMonitor.check(info);
    }


    @Override
    public boolean checkCustomerPoiReadyForSettle(Long wmPoiId) throws TException, WmCustomerException {
        return WmCustomerPoiAggre.Factory.make().checkCustomerPoiReadyForSettle(wmPoiId);
    }

    @Override
    public List<WmCustomerPoiListInfoDTO> getCustomerPoiList(WmCustomerPoiListConditionDTO condition) throws TException, WmCustomerException {
        return wmCustomerPoiListEsService.queryList(condition);
    }

    @Override
    @Deprecated
    public void initCustomerPoiAttribute(Integer customerId) {
        log.error("syncBrandIdAndOwnerUidToCustomerPoiListEsGray接口已下线,如还有使用请联系liuzhihao09");
    }

    @Override
    public String monitorCustomerPoiAttributeAgree(MonitorCustomerPoiAttributeAgreeDTO info) {
        return wmCustomerPoiAttributeAgreeMonitor.check(info);
    }

    @Override
    public String monitorCustomerPoiAttributeDataLegal(MonitorCustomerPoiAttributeDataLegalDTO info) {
        return wmCustomerPoiAttributeDataLegalMonitor.check(info);
    }

    @Override
    public List<WmCustomerPoiAttributeResultDTO> selectCustomerPoiAttribute(WmCustomerPoiAttributeSearchConditionDTO info) {
        return wmCustomerPoiAttributeService.selectCustomerPoiAttribute(info);
    }

    @Override
    public void deleteCustomerPoiAttribute(long id) {
        wmCustomerPoiAttributeService.deleteCustomerPoiAttribute(id);
    }

    @Override
    public void cancelCustomerSwitchTask(String msg) {
        try {
            CustomerSwitchNotifyMsg switchTaskMsg = JSON.parseObject(msg, CustomerSwitchNotifyMsg.class);
            wmCustomerSwitchService.cancelSwitch(switchTaskMsg);
        } catch (Exception e) {
            log.error("客户不下线-任务取消 处理失败 msg={}", msg, e);
        }
    }

    @Override
    public String monitorCustomerPoiChange(MonitorCustomerPoiDTO info) {
        return wmCustomerPoiMonitor.check(info);
    }

    @Override
    public boolean checkPoiSwitchCustomerSuc(Long fromCustomerId, Long toCustomerId, Long wmPoiId) throws TException, WmCustomerException {
        return wmCustomerSwitchService.checkPoiSwitchCustomerSuc(fromCustomerId, toCustomerId, wmPoiId);
    }

    @Override
    public boolean checkPoiCancelSwitchCustomerSuc(Long fromCustomerId, Long toCustomerId, Long wmPoiId) throws TException, WmCustomerException {
        return wmCustomerSwitchService.checkPoiCancelSwitchCustomerSuc(fromCustomerId, toCustomerId, wmPoiId);
    }

    @Override
    public boolean checkPoiSwitchCustomerSucV2(MonitorCustomerSwitchDTO monitorCustomerSwitchDTO) throws TException, WmCustomerException {
        return wmCustomerSwitchService.checkPoiSwitchCustomerSucV2(monitorCustomerSwitchDTO);
    }

    @Override
    public boolean checkPoiCancelSwitchCustomerSucV2(MonitorCustomerSwitchDTO monitorCustomerSwitchDTO) throws TException, WmCustomerException {
        return wmCustomerSwitchService.checkPoiCancelSwitchCustomerSucV2(monitorCustomerSwitchDTO);
    }

    @Override
    public String monitorCustomerPoiUnbind(List<Long> wmPoiIdList) throws TException, WmCustomerException {
        return wmCustomerPoiMonitor.monitorUnbind(wmPoiIdList);
    }

    /**
     * 客户绑定门店状态监控[客户类型为单店模式下校验经营门店是否绑定同一物理门店]
     *
     * @param monitorCustomerPoiDTO 数据一致性校验参数
     * @return 告警内容
     */
    @Override
    public String monitorPoiBind(MonitorCustomerPoiDTO monitorCustomerPoiDTO) throws TException{
        return wmCustomerPoiMonitor.monitorPoiBind(monitorCustomerPoiDTO);
    }

    @Override
    @CustomerDynamicRouter
    public Boolean haveMoreThanOnePoi(Long customerId) throws WmCustomerException, TException {
        if (customerId == null || customerId <=0) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "无效客户id");
        }
        WmCustomerBasicBo wmCustomerBasicBo = wmCustomerService.getCustomerById(customerId.intValue());
        if(wmCustomerBasicBo == null){
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "根据客户id查询客户数据为空");
        }
        return wmCustomerPoiRelService.haveMoreThanOnePoi(customerId);
    }

    /**
     * 统计绑定或绑定中门店数
     *
     * @param customerId
     * @return
     * @throws WmCustomerException
     */
    @Override
    public Integer cntBindOrBindingPoiByCustomerId(Long customerId) throws WmCustomerException, TException {
        //参数合法性校验
        if (customerId == null || customerId <= 0) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "无效客户id");
        }
        //查询客户信息
        WmCustomerBasicBo wmCustomerBasicBo = wmCustomerService.getCustomerById(customerId.intValue());
        if (wmCustomerBasicBo == null) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "根据客户id查询客户数据为空");
        }
        return wmCustomerPoiRelService.cntBindOrBindingPoiByCustomerId(customerId.intValue());
    }

}

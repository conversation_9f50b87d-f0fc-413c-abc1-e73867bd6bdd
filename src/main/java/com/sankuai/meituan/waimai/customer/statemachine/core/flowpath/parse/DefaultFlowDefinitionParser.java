package com.sankuai.meituan.waimai.customer.statemachine.core.flowpath.parse;

import com.sankuai.meituan.waimai.customer.statemachine.core.flowpath.FlowConnectionDefinition;
import com.sankuai.meituan.waimai.customer.statemachine.core.flowpath.FlowDefinition;
import com.sankuai.meituan.waimai.customer.statemachine.core.flowpath.FlowStateDefinition;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.ResourceUtils;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.util.Map;

/**
 * Created by jinhu on 16/8/11.
 */
public class DefaultFlowDefinitionParser implements FlowDefinitionParser {

    private static final Logger logger = LoggerFactory.getLogger(DefaultFlowDefinitionParser.class);

    private static final String STATE_ELEMENT_NAME = "state";
    private static final String NAME_ELEMENT_NAME = "name";
    private static final String LABEL_ELEMENT_NAME = "label";
    private static final String CONNECTION_ELEMENT_NAME = "connection";
    private static final String TO_STATE_ELEMENT_NAME = "toState";

    @Override
    public FlowDefinition parse(String file) throws Exception {
        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
        DocumentBuilder builder = factory.newDocumentBuilder();
        Document document = builder.parse(ResourceUtils.getFile(file));
        Element element = document.getDocumentElement();
        NodeList stateNodes = element.getElementsByTagName(STATE_ELEMENT_NAME);
        FlowDefinition flowDefinition = new FlowDefinition();
        for (int i = 0; i < stateNodes.getLength(); i++) {
            Element stateElement = (Element) stateNodes.item(i);
            FlowStateDefinition stateDefinition = parseFlowState(stateElement);
            flowDefinition.getFlowStates().put(stateDefinition.getStateName(), stateDefinition);
        }
        for (int i = 0; i < stateNodes.getLength(); i++) {
            Element stateElement = (Element) stateNodes.item(i);
            parseConnections(flowDefinition.getFlowStates(), stateElement);
        }

        flowDefinition.setFlowLabel(element.getAttribute(LABEL_ELEMENT_NAME));
        return flowDefinition;
    }

    private FlowStateDefinition parseFlowState(Element stateElement) {
        FlowStateDefinition stateDefinition = new FlowStateDefinition();
        stateDefinition.setStateName(stateElement.getAttribute(NAME_ELEMENT_NAME));
        stateDefinition.setStateLabel(stateElement.getAttribute(LABEL_ELEMENT_NAME));
        return stateDefinition;
    }

    private void parseConnections(Map<String, FlowStateDefinition> stateDefinitionMap, Element stateElement)
            throws Exception {
        String fromStateName = stateElement.getAttribute(NAME_ELEMENT_NAME);
        FlowStateDefinition fromStateDefinition = stateDefinitionMap.get(fromStateName);
        if (fromStateDefinition == null) {//
            throw new Exception();
        }

        NodeList connectionNodes = stateElement.getElementsByTagName(CONNECTION_ELEMENT_NAME);
        if (connectionNodes != null && connectionNodes.getLength() > 0) {
            for (int i = 0; i < connectionNodes.getLength(); i++) {
                Element connectionElement = (Element) connectionNodes.item(i);
                String toStateName = connectionElement.getAttribute(TO_STATE_ELEMENT_NAME);
                FlowStateDefinition toStateDefinition = stateDefinitionMap.get(toStateName);
                if (toStateDefinition == null) {//
                    throw new Exception();
                }
                FlowConnectionDefinition connectionDefinition = new FlowConnectionDefinition();
                connectionDefinition.setFromStateDefinition(fromStateDefinition);
                connectionDefinition.setToStateDefinition(toStateDefinition);
                fromStateDefinition.getConnections().put(toStateDefinition, connectionDefinition);
            }
        }
    }

}

package com.sankuai.meituan.waimai.customer.contract.service;

import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.apache.thrift.TException;

public interface IContractValidator {

    boolean valid(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException, TException;

}

package com.sankuai.meituan.waimai.customer.service.sc.dbus;

import com.sankuai.meituan.waimai.customer.constant.sc.WmScTableDbusEnum;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/8/5
 */
@Service
public class TableEventHandleService {

    @Autowired
    private List<ITableEvent> iTableEventHandleList;

    private Map<WmScTableDbusEnum, ITableEvent> iTableEventHandleMap = new HashMap<>();

    @PostConstruct
    public void init() {
        if (CollectionUtils.isEmpty(iTableEventHandleList)) {
            return;
        }
        for (ITableEvent handel : iTableEventHandleList) {
            iTableEventHandleMap.put(handel.getTable(), handel);
        }
    }


    public String handleUpdate(TableEvent event)  {
        if (event == null) {
            return "";
        }
        return iTableEventHandleMap.get(event.getTable()).handleUpdate(event);
    }

    public String handleInsert(TableEvent event)  {
        if (event == null) {
            return "";
        }
        return iTableEventHandleMap.get(event.getTable()).handleInsert(event);
    }

    public String handleDelete(TableEvent event)  {
        if (event == null) {
            return "";
        }
        return iTableEventHandleMap.get(event.getTable()).handleDelete(event);
    }

}

package com.sankuai.meituan.waimai.customer.domain;

import lombok.Data;

@Data
public class WmCustomerPoiLogisticsSubjectDB {

    /**
     * 主键id
     */
    private Integer id;
    /**
     * 客户id
     */
    private Integer customerId;
    /**
     * 门店id
     */
    private Long wmPoiId;
    /**
     * 乙方主体代码
     */
    private Integer partBNum;
    /**
     * 履约服务费主体代码
     */
    private Integer partLogisticsNum;
    /**
     * 生效时间
     */
    private Integer effectTime;
    /**
     * 操作类型
     */
    private Integer tag;
    /**
     * 是否有效
     */
    private Integer valid;
    /**
     * 创建时间
     */
    private Integer ctime;
    /**
     * 修改时间
     */
    private Integer utime;
}

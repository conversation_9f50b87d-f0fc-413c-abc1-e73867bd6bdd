package com.sankuai.meituan.waimai.customer.dao.sc.delivery;

import com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmSchoolDeliveryHistoryDO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 学校交付历史信息Mapper
 * <AUTHOR>
 * @date 2024/02/08
 * @email <EMAIL>
 **/
@Component
public interface WmSchoolDeliveryHistoryMapper {

    /**
     * 根据主键ID查询学校交付历史信息
     * @param id 主键ID
     * @return 学校交付历史信息
     */
    WmSchoolDeliveryHistoryDO selectByPrimaryKey(@Param("id") Long id);

    /**
     * 根据学校主键ID查询学校交付历史信息
     * @param schoolPrimaryId 学校主键ID
     * @return 学校交付历史信息
     */
    List<WmSchoolDeliveryHistoryDO> selectBySchoolPrimaryId(@Param("schoolPrimaryId") Integer schoolPrimaryId);

    /**
     * 根据交付ID查询学校交付历史信息
     * @param deliveryId 交付ID
     * @return 学校交付历史信息
     */
    WmSchoolDeliveryHistoryDO selectByDeliveryId(@Param("deliveryId") Integer deliveryId);

    /**
     * 新增学校交付历史信息
     * @param wmSchoolDeliveryHistoryDO 交付历史信息
     * @return 更新行数
     */
    int insertSelective(WmSchoolDeliveryHistoryDO wmSchoolDeliveryHistoryDO);

}

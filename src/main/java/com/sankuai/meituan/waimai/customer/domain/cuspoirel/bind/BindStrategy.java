package com.sankuai.meituan.waimai.customer.domain.cuspoirel.bind;

import com.sankuai.meituan.waimai.customer.domain.cuspoirel.CustomerPoiRelStrategyBean;
import com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.strategy.after.IBindAfterStrategy;
import com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.strategy.check.IBindCheckStrategy;
import com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.strategy.core.IBindCoreStrategy;
import com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.strategy.precore.IBindPreCoreStrategy;
import com.sankuai.meituan.waimai.customer.util.base.SpringBeanUtil;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;


/**
 * <AUTHOR>
 * @date 20240116
 * @desc 绑定流程策略上下文定义
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BindStrategy {

    @Autowired
    private IBindCheckStrategy bindCheckStrategy;

    @Autowired
    private IBindPreCoreStrategy bindPreCoreStrategy;

    @Autowired
    private IBindCoreStrategy bindCoreStrategy;

    @Autowired
    private IBindAfterStrategy bindAfterStrategy;

    public void execute(CustomerPoiBindFlowContext context) throws WmCustomerException {
        bindCheckStrategy.checkBindByParams(context);
        bindPreCoreStrategy.execute(context);
        bindCoreStrategy.execute(context);
        bindAfterStrategy.execute(context);
    }

    /**
     * 根据参数构建策略类
     *
     * @param poiRelStrategyBean
     * @return
     */
    public static BindStrategy buiLdStrategyWithContext(CustomerPoiRelStrategyBean poiRelStrategyBean) {
        //绑定签约通知的校验策略
        IBindCheckStrategy bindCheckStrategy = (IBindCheckStrategy) SpringBeanUtil.getBean(poiRelStrategyBean.getCheckBeanName());
        //绑定签约通知的核心前置操作策略
        IBindPreCoreStrategy bindPreCoreStrategy = (IBindPreCoreStrategy) SpringBeanUtil.getBean(poiRelStrategyBean.getPreCoreBeanName());
        //绑定的核心操作策略
        IBindCoreStrategy bindCoreStrategy = (IBindCoreStrategy) SpringBeanUtil.getBean(poiRelStrategyBean.getCoreBeanName());
        //绑定的后置操作策略
        IBindAfterStrategy bindAfterStrategy = (IBindAfterStrategy) SpringBeanUtil.getBean(poiRelStrategyBean.getAfterBeanName());

        //构建策略上下文
        BindStrategy strategyContext = BindStrategy.builder()
                .bindCheckStrategy(bindCheckStrategy)
                .bindPreCoreStrategy(bindPreCoreStrategy)
                .bindCoreStrategy(bindCoreStrategy)
                .bindAfterStrategy(bindAfterStrategy)
                .build();
        return strategyContext;
    }

}

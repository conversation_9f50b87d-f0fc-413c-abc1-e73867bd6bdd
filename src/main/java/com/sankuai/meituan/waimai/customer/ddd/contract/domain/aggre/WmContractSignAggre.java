package com.sankuai.meituan.waimai.customer.ddd.contract.domain.aggre;

import com.sankuai.meituan.waimai.customer.contract.dao.WmTempletContractSignAuditedDBMapper;
import com.sankuai.meituan.waimai.customer.contract.dao.WmTempletContractSignDBMapper;
import com.sankuai.meituan.waimai.customer.contract.domain.WmTempletContractSignDB;
import com.sankuai.meituan.waimai.customer.ddd.base.Entity;
import com.sankuai.meituan.waimai.customer.util.trans.WmTempletContractTransUtil;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractSignBo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.List;

@Deprecated
@Slf4j
public class WmContractSignAggre extends Entity<WmContractSignAggre.Context> {

    public WmContractSignAggre(WmContractSignAggre.Context context) {
        super(context);
    }

    /**
     * 合同id
     */
    long templetId;

    List<WmTempletContractSignBo> signBoList;

    public WmTempletContractSignBo selectAuditedPartyBSignerBo() {
        log.info("selectAuditedPartyBSignerBo#contractId:{}", templetId);
        List<WmTempletContractSignDB> templetContractSignDBList = ctx().wmTempletContractSignAuditedDBMapper.selectByWmTempletContractId(templetId);
        List<WmTempletContractSignBo> signBoList = WmTempletContractTransUtil.templetSignDbToBoList(templetContractSignDBList);
        return WmContractSignAggre.Factory.makeWithSignBo(signBoList).getPartyBSignerBo();
    }

    public WmTempletContractSignBo selectPartyBSigner() {
        List<WmTempletContractSignDB> templetContractSignDBList = ctx().wmTempletContractSignDBMapper.selectByWmTempletContractId(templetId);
        List<WmTempletContractSignBo> signBoList = WmTempletContractTransUtil.templetSignDbToBoList(templetContractSignDBList);
        return WmContractSignAggre.Factory.makeWithSignBo(signBoList).getPartyBSignerBo();
    }

    public WmTempletContractSignBo getPartyASignerBo() {
        if (signBoList == null) {
            return null;
        }
        for (WmTempletContractSignBo signBo : signBoList) {
            if ("A".equals(signBo.getSignType())) {
                return signBo;
            }
        }
        return null;
    }

    public WmTempletContractSignBo getPartyBSignerBo() {
        if (signBoList == null) {
            return null;
        }
        for (WmTempletContractSignBo signBo : signBoList) {
            if ("B".equals(signBo.getSignType())) {
                return signBo;
            }
        }
        return null;
    }

    @Lazy(false)
    @Component
    public static class Factory implements Entity.Factory {

        private static Context context = new Context();

        public static WmContractSignAggre make() {
            return new WmContractSignAggre(context);
        }

        public static WmContractSignAggre make(long templetId) {
            WmContractSignAggre wmContractSignAggre = new WmContractSignAggre(context);
            wmContractSignAggre.templetId = templetId;
            return wmContractSignAggre;
        }

        public static WmContractSignAggre makeWithSignBo(List<WmTempletContractSignBo> signBoList) {
            WmContractSignAggre wmContractSignAggre = new WmContractSignAggre(context);
            wmContractSignAggre.signBoList = signBoList;
            return wmContractSignAggre;
        }

        public static WmContractSignAggre makeWithSignBoAndTempletId(List<WmTempletContractSignBo> signBoList, long templetId) {
            WmContractSignAggre wmContractSignAggre = new WmContractSignAggre(context);
            wmContractSignAggre.signBoList = signBoList;
            wmContractSignAggre.templetId = templetId;
            return wmContractSignAggre;
        }

        @Autowired
        public void setWmTempletContractSignAuditedDBMapper(WmTempletContractSignAuditedDBMapper wmTempletContractSignAuditedDBMapper) {
            context.wmTempletContractSignAuditedDBMapper = wmTempletContractSignAuditedDBMapper;
        }

        @Autowired
        public void setWmTempletContractSignDBMapper(WmTempletContractSignDBMapper wmTempletContractSignDBMapper) {
            context.wmTempletContractSignDBMapper = wmTempletContractSignDBMapper;
        }
    }

    static class Context implements Entity.Context {
        WmTempletContractSignAuditedDBMapper wmTempletContractSignAuditedDBMapper;
        WmTempletContractSignDBMapper wmTempletContractSignDBMapper;
    }
}

package com.sankuai.meituan.waimai.customer.contract.service.impl.check;

import com.sankuai.meituan.waimai.customer.contract.dao.WmTempletContractDBMapper;
import com.sankuai.meituan.waimai.customer.contract.domain.WmTempletContractDB;
import com.sankuai.meituan.waimai.customer.contract.service.IContractValidator;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerContractStatus;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Author: 窦旭蒙
 * MIS: douxumeng
 * Date: 2020-12-08 17:19
 * Email: <EMAIL>
 * Desc:
 */
@Slf4j
@Service
public class ContractCancelSignStatusValidator implements IContractValidator {

    @Autowired
    WmTempletContractDBMapper wmTempletContractDBMapper;

    @Override
    public boolean valid(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException {
        Long contractId = contractBo.getBasicBo().getTempletContractId();
        WmTempletContractDB wmTempletContractDB = wmTempletContractDBMapper.selectByPrimaryKey(contractId);
        if (wmTempletContractDB == null) {
            log.info("valid#废除合同校验器，contractId:{}，不存在合同", contractId);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "不存在合同，不可废除");
        }
        // 除了【签约中】两种状态，其他的都不允许操作取消签约
        if (wmTempletContractDB.getStatus() == CustomerContractStatus.SIGNING.getCode()) {
            log.info("valid#合同取消签约校验器，contractId:{}，合同状态为:{}，不允许操作废除", CustomerContractStatus.getByCode(wmTempletContractDB.getStatus()).getDesc());
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "当前合同状态不允许操作废除");
        }
        return true;
    }
}

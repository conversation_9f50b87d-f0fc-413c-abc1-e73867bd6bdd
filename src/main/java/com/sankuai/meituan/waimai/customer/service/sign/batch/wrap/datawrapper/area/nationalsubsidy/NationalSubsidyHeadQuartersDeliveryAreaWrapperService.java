package com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper.area.nationalsubsidy;

import com.sankuai.meituan.waimai.customer.aspect.AreaDataWrapper;
import com.sankuai.meituan.waimai.econtrct.client.domain.biz.EcontractContentBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataWrapperEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @description:
 * @author: liuyunjie05
 * @create: 2025/5/29 14:40
 */
@Slf4j
@Service
@AreaDataWrapper(wrapperEnum = EcontractDataWrapperEnum.NATIONAL_SUBSIDY_HEADQUARTERS_DELIVERY_PERFORMANCE_SERVICE_FEE)
public class NationalSubsidyHeadQuartersDeliveryAreaWrapperService extends AbstractNationalSubsidyDeliveryAreaWrapperService {

    @Override
    public List<EcontractContentBo> wrap(EcontractBatchContextBo contextBo) throws IllegalAccessException, WmCustomerException {
        return super.wrap(contextBo, EcontractDataWrapperEnum.NATIONAL_SUBSIDY_HEADQUARTERS_DELIVERY_PERFORMANCE_SERVICE_FEE);
    }
}

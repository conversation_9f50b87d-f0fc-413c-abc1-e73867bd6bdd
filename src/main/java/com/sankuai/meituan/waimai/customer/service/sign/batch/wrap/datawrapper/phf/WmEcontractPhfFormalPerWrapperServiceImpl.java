package com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper.phf;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.customer.aspect.DataWrapper;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper.IWmEcontractDataWrapperService;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.constants.PdfConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.*;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @description: 拼好送正式版履约合同
 * @author: zhangyuanhao02
 * @create: 2024/12/23 14:36
 */
@Slf4j
@Service
@DataWrapper(wrapperEnum = EcontractDataWrapperEnum.PHF_FORMAL_PER)
public class WmEcontractPhfFormalPerWrapperServiceImpl extends AbstarctWmEcontractPhfDataWrapperService implements IWmEcontractDataWrapperService  {
    // 合同名称
    private static final String CONTRACT_NAME = "拼好送配送服务协议";

    // 描述门店名称的最大数量
    private static final int DESC_POI_NAME_NUM = 3;

    @Override
    public List<PdfContentInfoBo> wrap(EcontractBatchContextBo contextBo) throws IllegalAccessException, WmCustomerException {
        EcontractTaskBo taskBo = WmEcontractContextUtil.selectByApplyType(contextBo, EcontractTaskApplyTypeEnum.PHF_DELIVERY);
        EcontractBatchDeliveryInfoBo batchDeliveryInfoBo = JSON.parseObject(taskBo.getApplyContext(), EcontractBatchDeliveryInfoBo.class);

        if(CollectionUtils.isEmpty(batchDeliveryInfoBo.getEcontractDeliveryInfoBoList())){
            log.warn("WmEcontractPhfFormalPerWrapperServiceImpl deliveryInfoBo为空，流程中止，customerId:{}", contextBo.getCustomerId());
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "原始数据缺失，pdf封装失败");
        }

        String signVersion = ConfigUtilAdapter.getString(EcontractSignTempletConstant.CURRENT_DELIVERY_SIGN_VERSION_FOR_NEW_MODULE);
        EcontractSignVersionBo econtractSignVersionBo = contextBo
                .getEcontractSignVersionBo();
        if(econtractSignVersionBo == null){
            econtractSignVersionBo = new EcontractSignVersionBo();
        }
        econtractSignVersionBo.setDeliverySignVersion(signVersion + "B");
        contextBo.setEcontractSignVersionBo(econtractSignVersionBo);

        // 按门店分批次，一个批次一个合同PDF
        EcontractCustomerInfoBo customerInfoBo = contextBo.getCustomerInfoBo();
        List<PdfContentInfoBo> pdfContentInfoBos = Lists.newArrayList();

        // 获取门店分组信息
        List<EcontractDeliveryInfoBo> allDeliveryInfoBoList = batchDeliveryInfoBo.getEcontractDeliveryInfoBoList();
        Map<Integer, List<List<Long>>> wmPoiIdGroupMap = batchDeliveryInfoBo.getWmPoiIdGroupMap();
        // 履约侧创建合同
        List<List<Long>> wmPoiIdGroupList = wmPoiIdGroupMap.get(EcontractDataSourceEnum.PHF_LOGISTICS_PERFORMANCE_PDF.getType());
        List<List<EcontractDeliveryInfoBo>> wmPoiInfoBoGroupList = extractWmPoiInfoBoGroupList(allDeliveryInfoBoList, wmPoiIdGroupList);

        for (List<EcontractDeliveryInfoBo> econtractDeliveryInfoBos : wmPoiInfoBoGroupList) {
            EcontractDeliveryInfoBo firstDeliveryInfoBo = econtractDeliveryInfoBos.get(0);
            EcontractDeliveryPhfInfoBo econtractDeliveryPhfInfoBo = firstDeliveryInfoBo.getEcontractDeliveryPhfInfoBo();
            Map<Integer, String> perPdfUrlMap = econtractDeliveryPhfInfoBo.getPerPdfUrlMap();
            log.info("WmEcontractPhfFormalPerWrapperServiceImpl# perPdfUrlMap:{}", JSONObject.toJSONString(perPdfUrlMap));

            Map<String,String> pdfMetaContentMap = generatePdfMetaContent(customerInfoBo, null);
            fillWmPoiIdAndInfo(pdfMetaContentMap, econtractDeliveryInfoBos);

            PdfContentInfoBo pdfInfoBo = new PdfContentInfoBo();
            String contractDesc = generateContractDesc(econtractDeliveryInfoBos);
            pdfInfoBo.setPdfMetaContent(pdfMetaContentMap);
            pdfInfoBo.setContractDesc(contractDesc);
            pdfInfoBo.setContractName(CONTRACT_NAME);
            String pdfUrl = perPdfUrlMap.get(EcontractDeliveryTypeEnum.PHS_FORMAL.getType());
            pdfInfoBo.setPdfUrl(pdfUrl);
            pdfContentInfoBos.add(pdfInfoBo);
        }

        log.info("#WmEcontractPhfFormalSelfWrapperServiceImpl, pdfContentInfoBos={}", JSONObject.toJSONString(pdfContentInfoBos));
        return pdfContentInfoBos;
    }

    @Override
    public Map<String, String> generatePdfMetaContent(EcontractCustomerInfoBo customerInfoBo, EcontractDeliveryPhfInfoBo infoBo) throws WmCustomerException {
        Map<String, String> pdfMetaContentMap = new HashMap<>();
        // 商家主体
        pdfMetaContentMap.put("partAName", customerInfoBo.getCustomerName());
        // 美团上海主体
        pdfMetaContentMap.put("partBName", ContractSignSubjectEnum.SHANGHAI_ZHISONG.getDesc());

        return pdfMetaContentMap;
    }


    /**
     * 生成合同描述
     * @param econtractDeliveryInfoBos
     * @return
     */
    private String generateContractDesc(List<EcontractDeliveryInfoBo> econtractDeliveryInfoBos) {
        int pdfDescPoiNameSize = Math.min(econtractDeliveryInfoBos.size(), DESC_POI_NAME_NUM);
        List<String> poiNameList = econtractDeliveryInfoBos.subList(0, pdfDescPoiNameSize).stream()
                .map(e -> e.getEcontractDeliveryPhfInfoBo().getWmPoiName())
                .collect(Collectors.toList());
        String poiNames = String.join(",", poiNameList);
        String contractDesc = "适用于" + poiNames;
        if (econtractDeliveryInfoBos.size() > DESC_POI_NAME_NUM) {
            contractDesc += "...";
        }

        return contractDesc;
    }
}


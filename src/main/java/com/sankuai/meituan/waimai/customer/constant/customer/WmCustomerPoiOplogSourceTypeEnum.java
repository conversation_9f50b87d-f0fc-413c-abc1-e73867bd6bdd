package com.sankuai.meituan.waimai.customer.constant.customer;

import com.sankuai.meituan.waimai.thrift.customer.constant.customer.task.CustomerTaskSourceEnum;

/**
 * wm_customer_poi_oplog的操作来源枚举
 */
public enum WmCustomerPoiOplogSourceTypeEnum {
    UNKNOW(0, "未知", CustomerTaskSourceEnum.UN_KNOWN.getCode()),
    POI_SETTLE(1, "门店入驻", null),
    BASE_BIND(2, "基本信息绑定", CustomerTaskSourceEnum.POI_BASE_INFO.getCode()),
    DUODIAN_SETTLE(3, "多店入驻绑定", CustomerTaskSourceEnum.DUODIAN_SETTLE.getCode()),
    POI_RELEASE(4, "门店释放", CustomerTaskSourceEnum.POI_RELEASE.getCode()),
    DELETE_CUSTOMER(5, "客户删除", CustomerTaskSourceEnum.CUSTOMER_DELETE.getCode()),
    CONTRACT_START(6, "合同发起", CustomerTaskSourceEnum.C1_CONTRACT_DISCARD.getCode()),
    CUSTOMER_SWITCH(7, "客户切换", CustomerTaskSourceEnum.CUSTOMER_SWITCH.getCode()),
    CUSTOMER_POI_LIST(8, "客户门店列表", CustomerTaskSourceEnum.BD_SETTLE.getCode()),

    PRE_BIND(9, "门店预绑定成功", null),

    DIAN_XIAO(10, "电销系统", CustomerTaskSourceEnum.WM_SINGLE_ZRZ.getCode()),
    AGENT_AND_CONTRACT_START(11, "代理商&&合同发起", CustomerTaskSourceEnum.C1_CONTRACT_DISCARD.getCode()),
    FORCE_UNBIND(12, "强制解绑", null),
    CONFIRM_UNBIND(13, "确认解绑", null),

    /**
     * 外卖单店自入驻
     */
    WM_SINGLE_ZRZ(14, "外卖单店自入驻", CustomerTaskSourceEnum.WM_SINGLE_ZRZ.getCode()),

    /**
     * 外卖多店自入驻
     */
    WM_DUODIAN_ZRZ(15, "外卖多店自入驻", CustomerTaskSourceEnum.WM_DUODIAN_ZRZ.getCode()),

    /**
     * 闪购单店自入驻
     */
    SG_SINGLE_ZRZ(16, "闪购单店自入驻", CustomerTaskSourceEnum.SG_SINGLE_ZRZ.getCode()),

    /**
     * 闪购多店自入驻
     */
    SG_DUODIAN_ZRZ(17, "闪购多店自入驻", CustomerTaskSourceEnum.SG_DUODIAN_ZRZ.getCode()),

    /**
     * 医药单店自入驻
     */
    MED_SINGLE_ZRZ(18, "医药单店自入驻", CustomerTaskSourceEnum.MED_SINGLE_ZRZ.getCode()),

    /**
     * 医药多店自入驻
     */
    MED_DUODIAN_ZRZ(19, "医药多店自入驻", CustomerTaskSourceEnum.MED_DUODIAN_ZRZ.getCode()),

    /**
     * 医药直营转代理审核
     */
    MED_DIRECT_2_AGENT_AUDIT(20, "医药直营转代理审核", CustomerTaskSourceEnum.MED_DIRECT_2_AGENT_AUDIT.getCode()),

    /**
     * 闪购多店代运营
     */
    SG_DUODIAN_AGENT_RUN(21, "门店代理商服务", CustomerTaskSourceEnum.SG_DUODIAN_AGENT_RUN.getCode()),

    /**
     * 门店代理商服务
     */
    POI_AGNET_OPERATION(22, "闪购多店代运营", CustomerTaskSourceEnum.POI_AGNET_OPERATION.getCode()),

    /**
     * 子门店入驻
     */
    CHILD_POI_SETTLE(23, "子门店入驻", CustomerTaskSourceEnum.CHILD_POI_SETTLE.getCode()),

    /**
     * 压测专用
     */
    STRESS_TEST(24,"压测",CustomerTaskSourceEnum.STRESS_TEST.getCode())
    ;

    private int code;

    private String desc;

    /**
     * 客户任务渠道
     */
    private Integer taskSource;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public Integer getTaskSource() {
        return taskSource;
    }

    public void setTaskSource(Integer taskSource) {
        this.taskSource = taskSource;
    }

    WmCustomerPoiOplogSourceTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    WmCustomerPoiOplogSourceTypeEnum(int code, String desc, Integer taskSource) {
        this.code = code;
        this.desc = desc;
        this.taskSource = taskSource;
    }

    public static WmCustomerPoiOplogSourceTypeEnum findByCode(int code) {
        for (WmCustomerPoiOplogSourceTypeEnum item : values()) {
            if (item.getCode() == code) {
                return item;
            }
        }
        return UNKNOW;
    }

    /**
     * 根据任务渠道或者操作类型
     *
     * @param taskSource
     * @return
     */
    public static WmCustomerPoiOplogSourceTypeEnum findByOpSource(Integer taskSource) {
        if (taskSource == null || taskSource < 0) {
            return UNKNOW;
        }
        for (WmCustomerPoiOplogSourceTypeEnum item : values()) {
            if (item.getTaskSource() == taskSource) {
                return item;
            }
        }
        return UNKNOW;
    }

}

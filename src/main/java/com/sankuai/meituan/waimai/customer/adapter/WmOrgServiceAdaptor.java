package com.sankuai.meituan.waimai.customer.adapter;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.infra.domain.WmOrgListResult;
import com.sankuai.meituan.waimai.infra.domain.WmOrgSearchParam;
import com.sankuai.meituan.waimai.infra.service.WmOrgService;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class WmOrgServiceAdaptor {
    @Autowired
    private WmOrgService.Iface wmOrgService;

    public WmOrgListResult serach(WmOrgSearchParam builder)throws WmSchCantException {
        try {
            log.info("[wmOrgServiceAdaptor.serach] builder = {}", JSONObject.toJSONString(builder));
            WmOrgListResult orgListResult = wmOrgService.search(builder);
            log.info("[WmVirtualOrgServiceAdaptor.getOrgsByUidWithinOrgIds] orgListResult = {}",JSONObject.toJSONString(orgListResult));
            return orgListResult;
        } catch (Exception e) {
            log.error("[wmOrgServiceAdaptor.serach] Exception. getOrgsByUidWithinOrgIds ={}",
                    JSONObject.toJSONString(builder), e);
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "获取组织节点的主负责人失败");
        }
    }
}

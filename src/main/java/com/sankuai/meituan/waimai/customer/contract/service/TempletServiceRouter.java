package com.sankuai.meituan.waimai.customer.contract.service;

import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.customer.contract.annotations.ServiceTag;
import com.sankuai.meituan.waimai.customer.contract.exception.UnSupportedTempletTypeException;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmCommonConfigContractTemplateService;
import com.sankuai.meituan.waimai.thrift.customer.constant.WmTempletContractTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.config.ContractSourceEnum;
import org.springframework.aop.support.AopUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Service
public class TempletServiceRouter {

    @Resource
    private List<IWmContractTempletService> processes;

    @Resource
    private WmCommonConfigContractTemplateService commonConfigContractTemplateService;

    private static WmCommonConfigContractTemplateService commonConfigContractTemplateServiceStatic;

    static Map<WmTempletContractTypeEnum, IWmContractTempletService> typeAndServiceMap = Maps.newHashMap();

    @PostConstruct
    public void init() {
        commonConfigContractTemplateServiceStatic = commonConfigContractTemplateService;
        for (IWmContractTempletService contractTempletService : processes) {
            ServiceTag annotation = AopUtils
                    .getTargetClass(contractTempletService)
                    .getAnnotation(ServiceTag.class);
            if (annotation == null || annotation.templetTypes() == null) {
                continue;
            }
            for (WmTempletContractTypeEnum bizType : annotation.templetTypes()) {
                typeAndServiceMap.put(bizType, contractTempletService);
            }
        }
    }

    public static IWmContractTempletService getService(WmTempletContractTypeEnum typeEnum) {
        IWmContractTempletService wmContractTempletService = typeAndServiceMap.get(typeEnum);
        if (wmContractTempletService == null) {
            throw new UnSupportedTempletTypeException("该模板类型【" + typeEnum + "】暂未有Service实现");
        }
        return wmContractTempletService;
    }

    public static IWmContractTempletService getDefaultService() {
        return getService(WmTempletContractTypeEnum.C1_E);
    }

    public static IWmContractTempletService getService(int typeCode) {
        WmTempletContractTypeEnum byCode = WmTempletContractTypeEnum.getByCode(typeCode);
        if (byCode == null) {
            throw new UnSupportedTempletTypeException("不支持的模板类型【" + typeCode + "】");
        }
        return getService(byCode);
    }

    public static IWmContractTempletService getService(int typeCode, Integer contractSource) {
        if (ContractSourceEnum.isConfigSource(contractSource)) {
            return commonConfigContractTemplateServiceStatic;
        }
        return getService(typeCode);
    }

}

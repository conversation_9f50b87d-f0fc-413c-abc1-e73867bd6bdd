package com.sankuai.meituan.waimai.customer.constant.common;

/**
 * -@author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * -@description:
 * -@date: 2022/11/18 4:40 PM
 */
public enum StatusCodeEnum {

    /**
     * 状态code
     */

    SUCCESS(0, "请求成功"),
    SYSTEM_ERROR(1, "系统异常"),
    REQUEST_PARAM_ERROR(2, "请求参数错误"),
    BIZ_ERROR(3, "业务异常");

    private final int code;
    private final String errorMsg;

    private StatusCodeEnum(int code, String errorMsg) {
        this.code = code;
        this.errorMsg = errorMsg;
    }

    public int getCode() {
        return this.code;
    }

    public String getErrorMsg() {
        return this.errorMsg;
    }
}

package com.sankuai.meituan.waimai.customer.contract.service.impl.check;

import com.sankuai.meituan.waimai.customer.contract.dao.WmTempletContractDBMapper;
import com.sankuai.meituan.waimai.customer.contract.domain.WmTempletContractDB;
import com.sankuai.meituan.waimai.customer.contract.service.IContractValidator;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmContractSignService;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.SignType;
import com.sankuai.meituan.waimai.thrift.customer.constant.config.ContractSourceEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractBasicBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractSignBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractTypeBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Service
public class DuplicateContractValidator implements IContractValidator {

    @Autowired
    private WmTempletContractDBMapper wmTempletContractDBMapper;

    @Autowired
    WmContractSignService wmContractSignService;

    @Override
    public boolean valid(WmCustomerContractBo contractBo, int opUid, String opName)
            throws WmCustomerException {

        isExistTheSameTypeContract(contractBo);

        if (contractBo.isIgnoreExistAnotherSignTypeContract()) {
            return true;
        }

        isExistAnotherSignTypeContract(contractBo);
        return true;
    }

    /**
     * 是否存在该客户下相同类型合同
     */
    private void isExistTheSameTypeContract(WmCustomerContractBo contractBo) throws WmCustomerException {
        WmTempletContractBasicBo basicBo = contractBo.getBasicBo();
        List<WmTempletContractDB> wmTempletContractDBList = wmTempletContractDBMapper.selectValidByParentIdAndType((long) basicBo.getParentId(),
                basicBo.getType());
        WmTempletContractTypeBo typeBo = new WmTempletContractTypeBo(basicBo.getType());
        if (CollectionUtils.isEmpty(wmTempletContractDBList)) {
            return;
        }
        SignType signType = SignType.getByCode(typeBo.getSignType());
        if (typeBo.getCooperateMode() == WmTempletContractTypeBo.TYPE_C1) {
            String msg = String.format("提交失败，该客户已有与美团的%s合同，请勿重复添加", signType.getDesc());
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, msg);
        }
        if (typeBo.getCooperateMode() == WmTempletContractTypeBo.COOPERATEMODE_C2) {

            WmTempletContractSignBo modifyPartyBSigner = wmContractSignService.getPartyBSigner(contractBo.getSignBoList());

            for (WmTempletContractDB wmTempletContractDB : wmTempletContractDBList) {
                //校验线上表
                WmTempletContractSignBo auditedPartyBSignerInDb = wmContractSignService.getAuditedPartyBSignerWithOutSignPhone(wmTempletContractDB.getId());
                if (auditedPartyBSignerInDb != null && auditedPartyBSignerInDb.getSignId() == modifyPartyBSigner.getSignId()) {
                    String msg = String.format("提交失败，该客户与合作商[%s]已存在%s合同，请勿重复添加", getAgentName(wmTempletContractDB.getId()), signType.getDesc());
                    throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, msg);
                }

                //校验线下表
                WmTempletContractSignBo offlinePartyBSignerInDb = wmContractSignService.getPartyBSignerWithOutSignPhone(wmTempletContractDB.getId());
                if (offlinePartyBSignerInDb != null && offlinePartyBSignerInDb.getSignId() == modifyPartyBSigner.getSignId()) {
                    String msg = String.format("提交失败，该客户与合作商[%s]已存在%s合同，请勿重复添加", getAgentName(wmTempletContractDB.getId()), signType.getDesc());
                    throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, msg);
                }
            }
        }
        if (typeBo.getCooperateMode() == WmTempletContractTypeBo.NATIONAL_SUBSIDY_PURCHASE) {
            nationalSubsidyPurchaseContractDuplicateCheck(contractBo, wmTempletContractDBList, signType);
        }
        if (typeBo.getCooperateMode() == WmTempletContractTypeBo.COOPERATEMODE_QUA_REAL_LETTER
                || typeBo.getCooperateMode() == WmTempletContractTypeBo.COOPERATEMODE_POI_PROMOTION_SERVICE
                || typeBo.getCooperateMode() == WmTempletContractTypeBo.COOPERATEMODE_DELIVERY_SERVICE_CONTRACT
                || typeBo.getCooperateMode() == WmTempletContractTypeBo.COOPERATEMODE_BUSINESS_CUSTOMER
                || typeBo.getCooperateMode() == WmTempletContractTypeBo.COOPERATEMODE_GROUP_MEAL
                || typeBo.getCooperateMode() == WmTempletContractTypeBo.COOPERATEMODE_BAG_SERVICE
                || typeBo.getCooperateMode() == WmTempletContractTypeBo.COOPERATEMODE_FOODCITY_STATEMENT
                || typeBo.getCooperateMode() == WmTempletContractTypeBo.COOPERATEMODE_INTERIM_SELF
                || typeBo.getCooperateMode() == WmTempletContractTypeBo.COOPERATEMODE_MEDIC_ORDER_SPLIT
                || typeBo.getCooperateMode() == WmTempletContractTypeBo.COOPERATEMODE_SUBJECT_CHANGE_SUPPLEMENT
                || typeBo.getCooperateMode() == WmTempletContractTypeBo.COOPERATEMODE_FOUR_WHEEL_PERFORMANCE_SUPPLEMENT_AGREEMENT
                || typeBo.getCooperateMode() == WmTempletContractTypeBo.COOPERATEMODE_SPEEDY_DELIVERY_COOPERATION_AGREEMENT) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "提交失败，该客户已有同类电子合同，请勿重复添加");
        }
        if (ContractSourceEnum.isConfigSource(contractBo.getBasicBo().getContractSource())) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "提交失败，该客户已有同类电子合同，请勿重复添加");
        }
    }

    private void nationalSubsidyPurchaseContractDuplicateCheck(WmCustomerContractBo contractBo,
                                                               List<WmTempletContractDB> wmTempletContractDBList,
                                                               SignType signType) throws WmCustomerException {
        WmTempletContractSignBo modifyPartyBSigner = wmContractSignService.getPartyBSigner(contractBo.getSignBoList());

        for (WmTempletContractDB wmTempletContractDB : wmTempletContractDBList) {
            //校验线上表
            WmTempletContractSignBo auditedPartyBSignerInDb = wmContractSignService.getAuditedPartyBSignerWithOutSignPhone(wmTempletContractDB.getId());
            if (auditedPartyBSignerInDb != null && auditedPartyBSignerInDb.getSignId() == modifyPartyBSigner.getSignId()) {
                String msg = String.format("提交失败，该客户与[%s]的采销协议已存在，请在先富系统重新编辑已有协议并再次发起，不需要重复添加", modifyPartyBSigner.getSignName());
                throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, msg);
            }

            //校验线下表
            WmTempletContractSignBo offlinePartyBSignerInDb = wmContractSignService.getPartyBSignerWithOutSignPhone(wmTempletContractDB.getId());
            if (offlinePartyBSignerInDb != null && offlinePartyBSignerInDb.getSignId() == modifyPartyBSigner.getSignId()) {
                String msg = String.format("提交失败，该客户与[%s]的采销协议已存在，请在先富系统重新编辑已有协议并再次发起，不需要重复添加", modifyPartyBSigner.getSignName());
                throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, msg);
            }
        }
    }

    /**
     * 是否存在该客户下其他签约类型的合同
     */
    private void isExistAnotherSignTypeContract(WmCustomerContractBo contractBo) throws WmCustomerException {
        WmTempletContractBasicBo basicBo = contractBo.getBasicBo();
        WmTempletContractTypeBo contractType = new WmTempletContractTypeBo(basicBo.getType());
        WmTempletContractTypeBo contractTypeForAnotherSignType = toAnotherSignType(basicBo.getType());
        List<WmTempletContractDB> wmTempletContractDBList = wmTempletContractDBMapper.selectValidByParentIdAndType((long) basicBo.getParentId(),
                contractTypeForAnotherSignType.getTypeCode());
        if (CollectionUtils.isEmpty(wmTempletContractDBList)) {
            return;
        }
        for (WmTempletContractDB wmTempletContractDB : wmTempletContractDBList) {
            SignType existSignType = SignType.getByCode(contractTypeForAnotherSignType.getSignType());
            SignType newSignType = SignType.getByCode(contractType.getSignType());
            if (contractType.getCooperateMode() == WmTempletContractTypeBo.COOPERATEMODE_C1) {
                String msg = String.format("该客户已有与美团的%s合同（编号%s），此%s合同生效后，原%s合同将自动作废。是否确认提交？", existSignType.getDesc(),
                        wmTempletContractDB.getNumber(), newSignType.getDesc(), existSignType.getDesc());
                throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_WARN, msg);
            }
            WmTempletContractSignBo partyBSignerInDb = wmContractSignService.getPartyBSignerWithOutSignPhone(wmTempletContractDB.getId());
            WmTempletContractSignBo modifyPartyBSigner = wmContractSignService.getPartyBSigner(contractBo.getSignBoList());
            if (partyBSignerInDb.getSignId() == modifyPartyBSigner.getSignId()) {
                String agentName = getAgentName(wmTempletContractDB.getId());
                String msg = String.format("该客户与合作商[%s]已存在%s合同（编号%s），此%s合同生效后，原%s合同将自动作废。是否确认提交？", agentName, existSignType.getDesc(),
                        wmTempletContractDB.getNumber(), newSignType.getDesc(), existSignType.getDesc());
                throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_WARN, msg);
            }
        }
    }

    private String getAgentName(long templetId) {
        WmTempletContractSignBo partyBSigner = wmContractSignService.getPartyBSignerWithOutSignPhone(templetId);
        return partyBSigner == null ? "" : partyBSigner.getSignName();
    }

    private WmTempletContractTypeBo toAnotherSignType(int contractType) {
        WmTempletContractTypeBo contractTypeForAnotherSignType = new WmTempletContractTypeBo(contractType);
        int signType = (contractTypeForAnotherSignType.getSignType() + 1) / contractTypeForAnotherSignType.getSignType();
        contractTypeForAnotherSignType.setSignType(signType);
        return contractTypeForAnotherSignType;
    }
}

package com.sankuai.meituan.waimai.customer.service.customer.monitor.customerPoiList;

import com.alibaba.fastjson.JSON;
import com.dianping.frog.sdk.data.DmlType;
import com.sankuai.meituan.waimai.customer.constant.WmCustomerPoiListESFields;
import com.sankuai.meituan.waimai.customer.constant.customer.WmPoiRelTableDbusEnum;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiListVo;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiListEsService;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmColumnInfo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerPoiListInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class WmPoiBaseCheck implements InfoUpdateCheck {

    @Autowired
    private WmCustomerPoiListEsService wmCustomerPoiListEsService;

    @Override
    public String check(String tableName, DmlType operateType, Map<String, WmColumnInfo> columnInfoMap) {
        log.info("[WmPoiBaseCheck] check参数: tableName={}, operateType={}, columnInfoMap={}", tableName, operateType, JSON.toJSONString(columnInfoMap));
        if (!tableName.equals(WmPoiRelTableDbusEnum.TABLE_WM_POI_BASE.getAlias())) {
            return null;
        }
        switch (operateType) {
            case INSERT:
            case DELETE:
                return null;
            case UPDATE:
                return checkUpdate(columnInfoMap);
            default:
                return null;
        }
    }


    private String checkUpdate(Map<String, WmColumnInfo> columnInfoMap) {
        String wmPoiId = columnInfoMap.get(WmCustomerPoiListESFields.WM_POI_ID.getDbField()).getNewValue();
        String name = columnInfoMap.get(WmCustomerPoiListESFields.SHOP_NAME.getDbField()).getNewValue();
        String address = columnInfoMap.get(WmCustomerPoiListESFields.ADDRESS.getDbField()).getNewValue();
        String ownerUid = columnInfoMap.get(WmCustomerPoiListESFields.OWNER_UID.getDbField()).getNewValue();

        WmCustomerPoiListVo condition = new WmCustomerPoiListVo();
        condition.setWmPoiId(Integer.valueOf(wmPoiId));
        condition.setPageNo(1);
        condition.setPageSize(10);
        List<WmCustomerPoiListInfoDTO> list = wmCustomerPoiListEsService.queryData(condition);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        StringBuffer errMsg = new StringBuffer();
        for (WmCustomerPoiListInfoDTO dto : list) {
            if (dto.getShopName() == null || !dto.getShopName().equals(name)) {
                errMsg.append(String.format("门店信息不一致:%s:%s,%s:%s(%s);", WmCustomerPoiListESFields.ID.getField(), dto.getId(), WmCustomerPoiListESFields.SHOP_NAME.getField(), name, dto.getShopName()));
            }
            if (dto.getAddress() == null || !dto.getAddress().equals(address)) {
                errMsg.append(String.format("门店信息不一致:%s:%s,%s:%s(%s);", WmCustomerPoiListESFields.ID.getField(), dto.getId(), WmCustomerPoiListESFields.ADDRESS.getField(), address, dto.getAddress()));
            }
            if (dto.getOwnerUid() == null || !dto.getOwnerUid().equals(Integer.valueOf(ownerUid))) {
                errMsg.append(String.format("[门店责任人]信息不一致:%s:%s,%s:%s(%s);", WmCustomerPoiListESFields.ID.getField(), dto.getId(), WmCustomerPoiListESFields.OWNER_UID.getField(), ownerUid, dto.getOwnerUid()));
            }
        }
        return errMsg.toString();
    }
}

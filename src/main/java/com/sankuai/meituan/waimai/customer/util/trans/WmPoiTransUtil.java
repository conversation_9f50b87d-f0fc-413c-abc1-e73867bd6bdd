package com.sankuai.meituan.waimai.customer.util.trans;

import com.google.common.collect.Lists;

import com.sankuai.meituan.common.json.JSONUtil;
import com.sankuai.meituan.waimai.customer.bo.WmPoiDomain;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre;

import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 16-8-18.
 */
public class WmPoiTransUtil {
    /**
     * wmPoiAggre转换WmPoiDomain
     *
     * @param wmPoiAggre 商家聚合对象列表
     * @return 商家
     */
    public static WmPoiDomain transWmPoiAggre(WmPoiAggre wmPoiAggre) {
        if (wmPoiAggre == null) {
            return null;
        }
        WmPoiDomain wmPoiDomain = new WmPoiDomain();
        TransUtil.transfer(wmPoiAggre, wmPoiDomain);
        return wmPoiDomain;
    }

    public static void main(String[] args) {
        WmPoiAggre fromObj = new WmPoiAggre();
        fromObj.setAgent_id(1);
        WmPoiDomain toObj = new WmPoiDomain();
        TransUtil.transfer(fromObj, toObj);
        System.out.println(JSONUtil.toJSONString(toObj));
        long start = System.currentTimeMillis();
        for (int i = 0; i < 300; i ++) {
//            BeanUtils.copyProperties(new WmPoiAggre(), new WmPoiDomain());
            TransUtil.transferAll(new WmPoiAggre(), new WmPoiDomain());
        }
        System.out.println("耗时" + (System.currentTimeMillis() - start));
    }

    /**
     * 批量更新wmPoiAggre转换WmPoiDomain
     *
     * @param wmPoiAggreList 商家聚合对象列表
     * @return 商家
     */
    public static List<WmPoiDomain> batchTransWmPoiAggre(List<WmPoiAggre> wmPoiAggreList) {
        if (CollectionUtils.isEmpty(wmPoiAggreList)) {
            return Lists.newArrayList();
        }

        List<WmPoiDomain> wmPoiDomainList = Lists.newArrayList();
        for (WmPoiAggre wmPoiAggre:wmPoiAggreList) {
            WmPoiDomain wmPoiDomain = transWmPoiAggre(wmPoiAggre);
            wmPoiDomain.setToShopStatus(wmPoiAggre.getSelf_pickup_support());
            CollectionUtils.addIgnoreNull(wmPoiDomainList, wmPoiDomain);
        }
        return wmPoiDomainList;
    }

}

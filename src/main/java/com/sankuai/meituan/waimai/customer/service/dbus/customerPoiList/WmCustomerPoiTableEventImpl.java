package com.sankuai.meituan.waimai.customer.service.dbus.customerPoiList;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.meituan.dbus.common.DbusUtils;
import com.meituan.dbus.thriftV2.utils.ThriftV2Utils;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.channel.beacon.thrift.vo.ModuleStateResVo;
import com.sankuai.meituan.waimai.customer.adapter.BeaconQueryThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.constant.WmCustomerPoiListESFields;
import com.sankuai.meituan.waimai.customer.constant.customer.WmCustomerRelTableDbusEnum;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerDBMapper;
import com.sankuai.meituan.waimai.customer.ddd.adapter.WmPoiQueryAdapter;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiDB;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiAttributeService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiListEsService;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpPoiService;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpService;
import com.sankuai.meituan.waimai.customer.util.trans.WmCustomerPoiListInfoTransUtil;
import com.sankuai.meituan.waimai.thrift.constants.WmPoiFieldQueryConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerRelationStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKpPoi;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.elasticsearch.action.index.IndexResponse;
import org.elasticsearch.action.update.UpdateResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Slf4j
@Service
public class WmCustomerPoiTableEventImpl implements ICustomerRelTableEvent {

    private static final Set<String> WM_POI_FIELDS_AGGRE_BASE = ImmutableSet.of(
            WmPoiFieldQueryConstant.WM_POI_FIELD_WM_POI_ID,
            WmPoiFieldQueryConstant.WM_POI_FIELD_NAME,
            WmPoiFieldQueryConstant.WM_POI_FIELD_ADDRESS,
            WmPoiFieldQueryConstant.WM_POI_FIELD_CUSTOMER_ID,
            WmPoiFieldQueryConstant.WM_POI_FIELD_MT_CUSTOMER_ID,
            WmPoiFieldQueryConstant.WM_POI_FIELD_VALID,
            WmPoiFieldQueryConstant.WM_POI_FIELD_IS_DELETE,
            WmPoiFieldQueryConstant.WM_POI_FIELD_BRAND_ID,
            WmPoiFieldQueryConstant.WM_POI_FIELD_OWNER_UID,
            WmPoiFieldQueryConstant.WM_POI_FIELD_LABEL_IDS,
            WmPoiFieldQueryConstant.WM_POI_FIELD_SUB_WM_POI_TYPE
    );

    private static final Set<Integer> PRE_BIND_STATUS_SET = Sets.newHashSet();
    static{
        PRE_BIND_STATUS_SET.add(CustomerRelationStatusEnum.TO_APPLY_BIND.getCode());
        PRE_BIND_STATUS_SET.add(CustomerRelationStatusEnum.CONFIRM_BINDING.getCode());
        PRE_BIND_STATUS_SET.add(CustomerRelationStatusEnum.CONFIRM_BINDING_FAIL.getCode());
    }

    @Autowired
    private WmPoiQueryAdapter wmPoiQueryAdapter;

    @Autowired
    private BeaconQueryThriftServiceAdapter beaconQueryThriftServiceAdapter;

    @Autowired
    private WmCustomerKpPoiService wmCustomerKpPoiService;

    @Autowired
    private WmCustomerKpService wmCustomerKpService;

    @Autowired
    private WmCustomerPoiListEsService esService;

    @Autowired
    private WmCustomerDBMapper wmCustomerDBMapper;

    @Autowired
    private WmCustomerPoiAttributeService wmCustomerPoiAttributeService;

    private final String VALID_FIELD = "valid";

    @Override
    public WmCustomerRelTableDbusEnum getTable() {
        return WmCustomerRelTableDbusEnum.TABLE_WM_CUSTOMER_POI_REL;
    }

    @Override
    public String handleUpdate(Map<String, String> metaJsonData, String dataMapJson, String diffJson) {
        DbusUtils utils = ThriftV2Utils.newThriftV2InstanceForUpdate(metaJsonData, dataMapJson, diffJson);
        //updateCustomerPoiAttribute(utils);
        WmCustomerPoiDB wmCustomerPoiDB = transToBo(utils.getAftMap());
        if (utils.getDiffMap().containsKey(WmCustomerPoiListESFields.VALID.getDbField()) ||
                utils.getDiffMap().containsKey(WmCustomerPoiListESFields.RELATION_STATUS.getDbField())) {
            log.info("#databus监听客户门店关系表变更# 更新数据 wmCustomerPoiDB={}", JSON.toJSONString(wmCustomerPoiDB));
            if (wmCustomerPoiDB.getValid() != null && wmCustomerPoiDB.getValid() == CustomerConstants.VALID) {
                return insertES(wmCustomerPoiDB);

            } else if (wmCustomerPoiDB.getRelationStatus() != null && wmCustomerPoiDB.getRelationStatus().intValue() == CustomerRelationStatusEnum.READY_BIND.getCode()) {
                return insertES(wmCustomerPoiDB);
            }
            //预绑定状态变更刷新es数据
            else if(wmCustomerPoiDB.getRelationStatus() != null && PRE_BIND_STATUS_SET.contains(wmCustomerPoiDB.getRelationStatus().intValue())){
                return insertES(wmCustomerPoiDB);
            }
            else {
                log.info("#databus监听客户门店关系表变更# 逻辑删除数据 wmCustomerPoiDB={}", JSON.toJSONString(wmCustomerPoiDB));
                UpdateResponse response = esService.update(wmCustomerPoiDB.getId().toString(), esService.makeMap(
                        new String[]{WmCustomerPoiListESFields.SWITCH_TASK_ID.getField(), WmCustomerPoiListESFields.RELATION_STATUS.getField(), WmCustomerPoiListESFields.VALID.getField(),
                                WmCustomerPoiListESFields.CTIME.getField(), WmCustomerPoiListESFields.UTIME.getField()},
                        new Object[]{wmCustomerPoiDB.getSwitchTaskId(), wmCustomerPoiDB.getRelationStatus(), wmCustomerPoiDB.getValid(),
                                wmCustomerPoiDB.getCtime(), wmCustomerPoiDB.getUtime()}));
                if (response == null) {
                    return "逻辑删除客户门店关系失败";
                }
            }
        } else {
            log.info("#databus监听客户门店关系表变更# 更新数据 wmCustomerPoiDB={}", JSON.toJSONString(wmCustomerPoiDB));
            UpdateResponse response = esService.update(wmCustomerPoiDB.getId().toString(), esService.makeMap(
                    new String[]{WmCustomerPoiListESFields.SWITCH_TASK_ID.getField(), WmCustomerPoiListESFields.RELATION_STATUS.getField(), WmCustomerPoiListESFields.VALID.getField(),
                            WmCustomerPoiListESFields.CTIME.getField(), WmCustomerPoiListESFields.UTIME.getField()},
                    new Object[]{wmCustomerPoiDB.getSwitchTaskId(), wmCustomerPoiDB.getRelationStatus(), wmCustomerPoiDB.getValid(),
                            wmCustomerPoiDB.getCtime(), wmCustomerPoiDB.getUtime()}));
            if (response == null) {
                return "逻辑删除客户门店关系失败";
            }
        }
        return null;
    }

    @Override
    public String handleInsert(Map<String, String> metaJsonData, String dataMapJson) {
        DbusUtils utils = ThriftV2Utils.newThriftV2InstanceForInsert(metaJsonData, dataMapJson);
        WmCustomerPoiDB wmCustomerPoiDB = transToBo(utils.getAftMap());
        //insertCustomerPoiAttribute(wmCustomerPoiDB);
        return insertES(wmCustomerPoiDB);
    }

    @Override
    public String handleDelete(Map<String, String> metaJsonData, String dataMapJson) {
        DbusUtils utils = ThriftV2Utils.newThriftV2InstanceForDelete(metaJsonData, dataMapJson);
        WmCustomerPoiDB wmCustomerPoiDB = transToBo(utils.getPreMap());
        //deleteCustomerPoiAttribute(wmCustomerPoiDB);
        esService.delete(wmCustomerPoiDB.getId().toString());
        return null;
    }


    private WmCustomerPoiDB transToBo(Map<String, Object> aftMap) {
        String jsonString = JSON.toJSONString(aftMap);
        return JSON.parseObject(jsonString, WmCustomerPoiDB.class);
    }

    private String insertES(WmCustomerPoiDB wmCustomerPoiDB) {
        log.info("#databus监听客户门店关系表变更# 新插入数据 wmCustomerPoiDB={}", JSON.toJSONString(wmCustomerPoiDB));
        Long wmPoiId = wmCustomerPoiDB.getWmPoiId();
        WmPoiAggre wmPoiAggre = wmPoiQueryAdapter.getWmPoiAggre(wmPoiId, WM_POI_FIELDS_AGGRE_BASE);
        if (wmPoiAggre != null) {
            WmCustomerDB wmCustomerDB = wmCustomerDBMapper.selectCustomerById(wmCustomerPoiDB.getCustomerId());
            if (wmCustomerDB == null || wmCustomerDB.getMtCustomerId() == null || wmCustomerDB.getMtCustomerId().longValue() <= 0L) {
                wmCustomerDB = wmCustomerDBMapper.selectCustomerByIdRT(wmCustomerPoiDB.getCustomerId());
            }
            if (wmCustomerDB != null && wmCustomerDB.getMtCustomerId() != null && wmCustomerDB.getMtCustomerId().longValue() > 0L) {
                wmPoiAggre.setMt_customer_id(wmCustomerDB.getMtCustomerId());
            }
        }

        ModuleStateResVo vo = beaconQueryThriftServiceAdapter.queryPoiModuleState(wmPoiId);
        List<WmCustomerKpPoi> wmCustomerKpPoiList = wmCustomerKpPoiService.getKpRelPoiInfoByWmPoiList(Lists.newArrayList(wmPoiId));
        Map<Long, Pair<Integer, String>> wmCustomerKpMaps = wmCustomerKpService.kpInfoHandler(wmCustomerKpPoiList);

        Map<String, Object> map = WmCustomerPoiListInfoTransUtil.transforMap(wmCustomerPoiDB, wmPoiAggre, vo, wmCustomerKpMaps);
        if (map != null) {
            IndexResponse response = esService.insert(wmCustomerPoiDB.getId().toString(), map);
            if (response == null) {
                return "客户门店关系新增失败";
            }
        }
        return null;
    }

    /**
     * wm_customer_poi_rel表新增数据的时候同步更新客户门店属性表wm_customer_poi_attribute
     *
     * @param wmCustomerPoiDB
     */
    private void insertCustomerPoiAttribute(WmCustomerPoiDB wmCustomerPoiDB) {
        if (wmCustomerPoiDB == null || wmCustomerPoiDB.getValid() == null) {
            return;
        }
        if (wmCustomerPoiDB.getValid() != CustomerConstants.VALID) {
            //如果存在数据则删除
            wmCustomerPoiAttributeService.deleteCustomerPoiAttribute(wmCustomerPoiDB.getCustomerId(), wmCustomerPoiDB.getWmPoiId());
            return;
        }
        wmCustomerPoiAttributeService.upsertCustomerPoiAttribute(wmCustomerPoiDB.getCustomerId(), wmCustomerPoiDB.getWmPoiId());
    }

    /**
     * wm_customer_poi_rel表删除数据的时候同步删除客户门店属性表wm_customer_poi_attribute
     *
     * @param wmCustomerPoiDB
     */
    private void deleteCustomerPoiAttribute(WmCustomerPoiDB wmCustomerPoiDB) {
        if (wmCustomerPoiDB == null) {
            return;
        }
        wmCustomerPoiAttributeService.deleteCustomerPoiAttribute(wmCustomerPoiDB.getCustomerId(), wmCustomerPoiDB.getWmPoiId());
    }

    /**
     * wm_customer_poi_rel表修改数据的时候同步更新客户门店属性表wm_customer_poi_attribute
     *
     * @param utils
     */
    private void updateCustomerPoiAttribute(DbusUtils utils) {
        try {
            if (utils == null || MapUtil.isEmpty(utils.getAftMap())) {
                return;
            }
            if (!utils.getDiffMap().containsKey(VALID_FIELD)) {
                return;
            }
            WmCustomerPoiDB wmCustomerPoiDB = transToBo(utils.getAftMap());
            if (wmCustomerPoiDB == null || wmCustomerPoiDB.getValid() == null) {
                return;
            }
            Object valid = utils.getAftMap().get(VALID_FIELD);
            if (valid != null && (Integer) valid == CustomerConstants.VALID) {
                wmCustomerPoiAttributeService.upsertCustomerPoiAttribute(wmCustomerPoiDB.getCustomerId(), wmCustomerPoiDB.getWmPoiId());
            } else {
                if (ConfigUtilAdapter.getBoolean("customerPoiAttributeUnValidCoverBottomSwitch", false)) {
                    wmCustomerPoiAttributeService.deleteCustomerPoiAttribute(wmCustomerPoiDB.getCustomerId(), wmCustomerPoiDB.getWmPoiId());
                }
            }
        } catch (Exception e) {
            log.error("updateCustomerPoiAttribute utils={}", JSON.toJSONString(utils), e);
        }
    }

}

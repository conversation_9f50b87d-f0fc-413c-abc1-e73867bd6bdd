package com.sankuai.meituan.waimai.customer.service.kp.statemachine.action.signer;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.service.kp.CustomerKpBusinessService;
import com.sankuai.meituan.waimai.customer.service.kp.DifferentCustomerKpService;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpLogService;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.action.KpSignerAbstractAction;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.context.KpSignerStatusMachineContext;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.constant.event.KpSignerEventEnum;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.machine.KpSignerBaseSM;
import com.sankuai.meituan.waimai.thrift.bo.WmCustomerDiffCellBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.KpConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.KpLegalStateMachine;
import com.sankuai.meituan.waimai.thrift.customer.constant.KpSignerStateMachine;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.KpVersionEnum;
import com.sankuai.meituan.waimai.thrift.exception.StatusMachineException;
import com.sankuai.meituan.waimai.thrift.util.DiffUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 20230821
 * @desc KP签约人实名认证失败事件
 */
@Service
@Slf4j
public class KpSignerRealNameFailAction extends KpSignerAbstractAction {


    @Autowired
    private CustomerKpBusinessService customerKpBusinessService;

    @Autowired
    private WmCustomerKpLogService wmCustomerKpLogService;

    @Autowired
    private DifferentCustomerKpService differentCustomerKpService;

    /**
     * KP签约人实名认证失败操作
     *
     * @param from
     * @param to
     * @param eventEnum
     * @param context
     * @param kpSignerBaseSM
     */
    @Override
    public void execute(KpSignerStateMachine from, KpSignerStateMachine to, KpSignerEventEnum eventEnum, KpSignerStatusMachineContext context, KpSignerBaseSM kpSignerBaseSM) {

        log.info("KpSignerRealNameFailAction,实名认证失败action开始执行,from={},to={},context={}", from, to, JSON.toJSONString(context));
        //是否生效标识
        boolean haveEffectFlag = context.getExistEffectiveFlag();
        try {
            //KP未生效过
            if (!haveEffectFlag) {
                context.getWmCustomerKp().setState(KpLegalStateMachine.PREAUTH_FAIL.getState());
                context.getWmCustomerKp().setEffective(KpConstants.UN_EFFECTIVE);
                context.getWmCustomerKp().setVersion(KpVersionEnum.V3.getCode());
                //更新KP实名认证失败
                customerKpBusinessService.updateKp2RealNameFailOnUnEffectKp(context.getWmCustomerKp());
                List<WmCustomerDiffCellBo> diffCellBos = DiffUtil.compare(context.getOldCustomerKp(), context.getWmCustomerKp(), differentCustomerKpService.getKpDiffFieldsMap());
                //添加签约人状态变更操作日志
                wmCustomerKpLogService.updateKpLog(context.getWmCustomerKp(), diffCellBos, context.getOpUid(), context.getOpUName());
            } else {
                //已生效签约人KP更新为认证失败&添加操作日志
                customerKpBusinessService.updateKp2RealNameFailOnEffectKp(context);
            }
        } catch (Exception e) {
            log.error("KpSignerRealNameFailAction.execute,Kp签约人实名认证失败异常,context={}", JSON.toJSONString(context), e);
            throw new StatusMachineException("Kp签约人实名认证失败异常异常");
        }


    }
}

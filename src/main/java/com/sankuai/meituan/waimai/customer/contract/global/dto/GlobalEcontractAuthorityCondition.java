package com.sankuai.meituan.waimai.customer.contract.global.dto;

import com.sankuai.meituan.uac.sdk.entity.UacRoleEntity;
import lombok.*;

import java.util.List;

/**
 * @description: 全局合同权限相关
 * @author: liuyunjie05
 * @create: 2024/1/12 15:04
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GlobalEcontractAuthorityCondition {

    /**
     * 超级管理员权限
     */
    private boolean superAdmin;

    /**
     * 拥有的UAC权限
     */
    private List<UacRoleEntity> userUacRoleList;

    /**
     * 业务线, 14010: 外卖, 14060: 闪购, 14090: 医药
     */
    private List<Integer> bizLineList;

    /**
     * 非三方接入的合同类型权限
     */
    private List<Integer> contractTypeCodeList;

    /**
     * 查询人uid
     */
    private long queryUid;

}

package com.sankuai.meituan.waimai.customer.service.sc.crane;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.read.builder.ExcelReaderBuilder;
import com.cip.crane.client.spring.annotation.Crane;
import com.cip.crane.client.spring.annotation.CraneConfiguration;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.service.customer.clean.WmCustomerDataCleanService;
import com.sankuai.meituan.waimai.customer.util.S3Helper;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 单店美食城客户类型清洗任务
 */
@Slf4j
@Component
@CraneConfiguration
public class WmCustomerSinglePoiMscCleanJob {

    private static final String BUCKET_NAME = "common-bucket";

    @Resource
    private S3Helper s3Helper;

    @Resource
    private WmCustomerDataCleanService wmCustomerDataCleanService;

    @Crane("wm.customer.single.poi.msc.clean.job")
    public void execute(String param) {
        log.info("WmCustomerSinglePoiMscCleanJob execute start, param:{}", param);
        byte[] object = s3Helper.getObject(BUCKET_NAME, param);
        if (object == null) {
            log.info("WmCustomerSinglePoiMscCleanJob execute end, param:{}, result:{}", param, "文件不存在");
            return;
        }
        // 处理Excel表
        InputStream inputStream = new ByteArrayInputStream(object);
        CustomerDataListener dataListener = new CustomerDataListener();
        EasyExcelFactory.read(inputStream, CustomerExcelData.class, dataListener)
                .sheet() // 读取第一个sheet
                .doRead();

        // 获取解析结果
        List<String> customerIdList = dataListener.getCustomerIds();
        //分批处理
        List<List<String>> partition = Lists.partition(customerIdList, 100);
        List<Integer> notUpdatedCustomerList = new ArrayList<>();
        for (List<String> customerIds : partition) {
            try {
                List<Integer> notUpdatedCustomers = wmCustomerDataCleanService.batchRefreshSinglePoiFoodCityCustomer(customerIds);
                notUpdatedCustomerList.addAll(notUpdatedCustomers);
            } catch (Exception e) {
                log.error("WmCustomerSinglePoiMscCleanJob execute error, param:{}, result:{}", customerIds, e.getMessage());
            }
        }
        if (CollectionUtils.isNotEmpty(notUpdatedCustomerList)) {
            log.info("WmCustomerSinglePoiMscCleanJob execute end, param:{}, not update result:{}", param, notUpdatedCustomerList);
        }
    }

    @Data
    public static class CustomerExcelData {
        // 客户ID - 对应Excel第一列
        private String customerId;

        // 执照名称/姓名 - 对应Excel第二列
        private String licenseName;

        // 客户名称 - 对应Excel第三列
        private String customerName;

        // 门店数 - 对应Excel第四列
        private String poiCount;
    }

    public static class CustomerDataListener extends AnalysisEventListener<CustomerExcelData> {

        private List<String> customerIds = new ArrayList<>();
        private int successCount = 0;
        private int errorCount = 0;

        @Override
        public void invoke(CustomerExcelData data, AnalysisContext context) {
            log.debug("解析到一行数据: {}", data);

            try {
                // 提取并验证客户ID
                String customerId = extractCustomerId(data);
                if (StringUtils.isNotBlank(customerId)) {
                    customerIds.add(customerId);
                    successCount++;
                } else {
                    log.warn("第{}行客户ID为空或无效: {}", context.readRowHolder().getRowIndex() + 1, data);
                    errorCount++;
                }
            } catch (Exception e) {
                log.error("处理第{}行数据失败: {}", context.readRowHolder().getRowIndex() + 1, data, e);
                errorCount++;
            }
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            log.info("Excel解析完成，成功处理{}行数据，失败{}行数据", successCount, errorCount);
        }

        private String extractCustomerId(CustomerExcelData data) {
            if (data == null) {
                return null;
            }

            String customerId = data.getCustomerId();
            if (StringUtils.isBlank(customerId)) {
                return null;
            }

            // 清理客户ID（去除空格、特殊字符等）
            String cleanedId = customerId.trim().replaceAll("[^0-9]", "");

            return cleanedId;
        }

        public List<String> getCustomerIds() {
            // 去重并返回
            return customerIds.stream()
                    .distinct()
                    .collect(Collectors.toList());
        }
    }
}

package com.sankuai.meituan.waimai.customer.service.sign.pack;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskStateEnum;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 门店状态模块单节点判断逻辑
 * 门店状态分为结算、门店、合同
 * 每一个模块的变更视为一个节点
 */
@Service
public class WmEcontractPoiStateNodeService {

    public static Map<String, List<String>> stateMap = Maps.newHashMap();

    public static Map<String, List<String>> toMap = Maps.newHashMap();

    public static Map<String, List<String>> backMap = Maps.newHashMap();

    static {
        stateMap.put(EcontractTaskStateEnum.TO_COMMIT.getName(), Lists.newArrayList(EcontractTaskStateEnum.HOLDING.getName(),
                                                                                    EcontractTaskStateEnum.NOT_SIGN.getName()));//无需确认
        //纸质改电子场景
        stateMap.put(EcontractTaskStateEnum.NOT_SIGN.getName(), Lists.newArrayList(EcontractTaskStateEnum.HOLDING.getName()));

        stateMap.put(EcontractTaskStateEnum.HOLDING.getName(), Lists.newArrayList(EcontractTaskStateEnum.IN_PROCESSING.getName(),
                                                                                  EcontractTaskStateEnum.TO_COMMIT.getName()));

        stateMap.put(EcontractTaskStateEnum.IN_PROCESSING.getName(), Lists.newArrayList(EcontractTaskStateEnum.SUCCESS.getName(),
                                                                                        EcontractTaskStateEnum.FAIL.getName(),
                                                                                        EcontractTaskStateEnum.TO_COMMIT.getName(),
                                                                                        EcontractTaskStateEnum.HOLDING.getName()));

        stateMap.put(EcontractTaskStateEnum.FAIL.getName(), Lists.newArrayList(EcontractTaskStateEnum.HOLDING.getName()));


        toMap.put(EcontractTaskStateEnum.TO_COMMIT.getName(), Lists.newArrayList(EcontractTaskStateEnum.HOLDING.getName(),
                                                                                 EcontractTaskStateEnum.NOT_SIGN.getName()));

        toMap.put(EcontractTaskStateEnum.NOT_SIGN.getName(), Lists.newArrayList(EcontractTaskStateEnum.HOLDING.getName()));

        toMap.put(EcontractTaskStateEnum.HOLDING.getName(), Lists.newArrayList(EcontractTaskStateEnum.IN_PROCESSING.getName()));

        toMap.put(EcontractTaskStateEnum.IN_PROCESSING.getName(), Lists.newArrayList(EcontractTaskStateEnum.SUCCESS.getName(),
                                                                                     EcontractTaskStateEnum.FAIL.getName()));

        toMap.put(EcontractTaskStateEnum.FAIL.getName(), Lists.newArrayList(EcontractTaskStateEnum.HOLDING.getName()));

        backMap.put(EcontractTaskStateEnum.IN_PROCESSING.getName(), Lists.newArrayList(EcontractTaskStateEnum.TO_COMMIT.getName(),
                                                                                       EcontractTaskStateEnum.HOLDING.getName()));
    }

    /**
     * 校验当前门店状态模块节点是否能到下一节点
     * @param currentState 当前节点
     * @param nextState 下一节点
     * @return 是否能到下一节点
     */
    public boolean canToNextState(String currentState, String nextState) {
        return CollectionUtils.isNotEmpty(stateMap.get(currentState)) && stateMap.get(currentState).contains(nextState);
    }

    /**
     * 校验当前门店状态模块节点是否能到下一节点
     * @param currentState 当前节点
     * @param nextState 下一节点
     * @return 是否能到下一节点
     */
    public boolean canToNext(String currentState, String nextState) {
        if (nextState.equals(currentState)) {
            return Boolean.TRUE;
        }
        return CollectionUtils.isNotEmpty(toMap.get(currentState)) && toMap.get(currentState).contains(nextState);
    }


}

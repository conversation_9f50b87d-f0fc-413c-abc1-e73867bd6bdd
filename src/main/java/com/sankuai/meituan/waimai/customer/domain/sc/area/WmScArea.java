package com.sankuai.meituan.waimai.customer.domain.sc.area;

import com.sankuai.meituan.waimai.customer.service.sc.area.AreaCalculatorUtils;
import com.sankuai.meituan.waimai.customer.service.sc.area.PoiUtil;
import com.vividsolutions.jts.geom.Polygon;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigInteger;
import java.util.List;


public class WmScArea {

    /**
     * 业务主键
     */
    private int businessId;

    /**
     * 最小纬度
     */
    private int minLat;

    /**
     * 最小经度
     */
    private int minLng;

    /**
     * 最大纬度
     */
    private int maxLat;

    /**
     * 最大经度
     */
    private int maxLng;

    /**
     * 顶点集合
     */
    private List<WmScPoint> pointList;
    /**
     * 多边形
     */
    private Polygon polygon;

    private WmScAreaPolygon wmScAreaPolygon;

    public WmScArea() {

    }

    public WmScArea(int businessId, int minLat, int minLng, int maxLat, int maxLng, List<WmScPoint> pointList) {
        this.businessId = businessId;
        this.minLat = minLat;
        this.minLng = minLng;
        this.maxLat = maxLat;
        this.maxLng = maxLng;
        this.pointList = pointList;
        this.polygon = AreaCalculatorUtils.createPolygon(pointList);
        this.wmScAreaPolygon = PoiUtil.getPolygonForPoints(pointList);
        if (this.wmScAreaPolygon != null) {
            this.wmScAreaPolygon.calcGridStatus(minLat, minLng, maxLat, maxLng);
        }
    }

    public int getBusinessId() {
        return businessId;
    }

    public void setBusinessId(int businessId) {
        this.businessId = businessId;
    }

    public int getMinLat() {
        return minLat;
    }

    public void setMinLat(int minLat) {
        this.minLat = minLat;
    }

    public int getMinLng() {
        return minLng;
    }

    public void setMinLng(int minLng) {
        this.minLng = minLng;
    }

    public int getMaxLat() {
        return maxLat;
    }

    public void setMaxLat(int maxLat) {
        this.maxLat = maxLat;
    }

    public int getMaxLng() {
        return maxLng;
    }

    public void setMaxLng(int maxLng) {
        this.maxLng = maxLng;
    }

    public List<WmScPoint> getPointList() {
        return pointList;
    }

    public void setPointList(List<WmScPoint> pointList) {
        this.pointList = pointList;
    }

    public Polygon getPolygon() {
        return polygon;
    }

    public void setPolygon(Polygon polygon) {
        this.polygon = polygon;
    }

    public WmScAreaPolygon getWmScAreaPolygon() {
        return wmScAreaPolygon;
    }

    public void setWmScAreaPolygon(WmScAreaPolygon wmScAreaPolygon) {
        this.wmScAreaPolygon = wmScAreaPolygon;
    }
}

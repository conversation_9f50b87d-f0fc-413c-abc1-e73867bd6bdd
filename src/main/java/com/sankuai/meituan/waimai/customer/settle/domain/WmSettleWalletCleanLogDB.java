package com.sankuai.meituan.waimai.customer.settle.domain;

/**
 * <AUTHOR>
 * @date 2018-09-25 下午7:24
 */
public class WmSettleWalletCleanLogDB {
    private Integer id;
    private String cityCode;
    private String bdMisId;
    private Long wmPoiId;
    private Integer wmPoiVersion;
    private String customerId;
    private Integer opUid;
    private Integer result;
    private Integer exception;
    private String reason;
    private Integer valid;
    private Integer ctime;
    private Integer utime;
    private Integer wmSettleId;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public String getBdMisId() {
        return bdMisId;
    }

    public void setBdMisId(String bdMisId) {
        this.bdMisId = bdMisId;
    }

    public Long getWmPoiId() {
        return wmPoiId;
    }

    public void setWmPoiId(Long wmPoiId) {
        this.wmPoiId = wmPoiId;
    }

    public Integer getWmPoiVersion() {
        return wmPoiVersion;
    }

    public void setWmPoiVersion(Integer wmPoiVersion) {
        this.wmPoiVersion = wmPoiVersion;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public Integer getOpUid() {
        return opUid;
    }

    public void setOpUid(Integer opUid) {
        this.opUid = opUid;
    }

    public Integer getResult() {
        return result;
    }

    public void setResult(Integer result) {
        this.result = result;
    }

    public Integer getException() {
        return exception;
    }

    public void setException(Integer exception) {
        this.exception = exception;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public Integer getValid() {
        return valid;
    }

    public void setValid(Integer valid) {
        this.valid = valid;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    public Integer getUtime() {
        return utime;
    }

    public void setUtime(Integer utime) {
        this.utime = utime;
    }

    public Integer getWmSettleId() {
        return wmSettleId;
    }

    public void setWmSettleId(Integer wmSettleId) {
        this.wmSettleId = wmSettleId;
    }
}
package com.sankuai.meituan.waimai.customer.ddd.contract.domain.aggre;

import static com.sankuai.meituan.waimai.customer.ddd.contract.domain.atom.service.TempletAtomServiceRouter.getService;

import java.util.List;
import java.util.Map;
import java.util.Set;

import com.google.common.base.Function;
import com.google.common.collect.*;
import com.sankuai.meituan.waimai.customer.adapter.WmEmployClient;
import com.sankuai.meituan.waimai.customer.adapter.WmPoiClient;
import com.sankuai.meituan.waimai.customer.bo.WmPoiDomain;
import com.sankuai.meituan.waimai.customer.contract.domain.WmContractVersionDB;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignManualTaskDB;
import com.sankuai.meituan.waimai.customer.service.common.EmpServiceAdaptor;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractManualTaskBizService;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import com.sankuai.meituan.waimai.thrift.customer.constant.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.OpResultBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.LongResult;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.CustomerBlackWhiteParam;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.util.DateUtil;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.sankuai.meituan.common.time.TimeUtil;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.contract.thrift.service.WmAgentContractThriftService;
import com.sankuai.meituan.waimai.customer.constant.ContractFtlTagConstant;
import com.sankuai.meituan.waimai.customer.contract.dao.*;
import com.sankuai.meituan.waimai.customer.contract.domain.WmContractFtlTagDB;
import com.sankuai.meituan.waimai.customer.contract.domain.WmTempletContractDB;
import com.sankuai.meituan.waimai.customer.contract.domain.WmTempletContractSignDB;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmCustomerOplogService;
import com.sankuai.meituan.waimai.customer.ddd.base.Entity;
import com.sankuai.meituan.waimai.customer.ddd.contract.domain.atom.service.impl.WmContractPoiProduceAtomService;
import com.sankuai.meituan.waimai.customer.ddd.contract.domain.atom.service.impl.WmContractVersionAtomService;
import com.sankuai.meituan.waimai.customer.ddd.contract.domain.atom.service.impl.check.ContractCheckFilter;
import com.sankuai.meituan.waimai.customer.ddd.domain.customer.aggre.WmCustomerPoiAggre;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.service.AutoWireBase;
import com.sankuai.meituan.waimai.customer.service.common.WmEmployeeService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerBlackWhiteListService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpService;
import com.sankuai.meituan.waimai.customer.service.sign.WmEcontractSignBzService;
import com.sankuai.meituan.waimai.customer.settle.service.WmSettleService;
import com.sankuai.meituan.waimai.customer.util.PageUtil;
import com.sankuai.meituan.waimai.customer.util.base.AssertUtil;
import com.sankuai.meituan.waimai.customer.util.trans.WmTempletContractTransUtil;
import com.sankuai.meituan.waimai.domain.ContractTagConstant;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractTypeBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.PageData;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.BooleanResult;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.CustomerBlackWhiteBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.service.WmNoticePublishThriftService;

import javax.annotation.Nullable;
import javax.annotation.Resource;

@Deprecated
public class WmContractAggre extends Entity<WmContractAggre.Context> {

    public WmContractAggre(Context _ctx) {
        super(_ctx);
    }

    private static Logger logger = LoggerFactory.getLogger(WmContractAggre.class);

    /**
     * 客户ID
     */
    long customerId;

    /**
     * 合同ID
     */
    long contractId;

    /**
     * 合同对象
     */
    WmCustomerContractBo contractBo;

    static Set<Integer> autoSaveSignContractTypeSet = Sets.newHashSet(
            WmTempletContractTypeEnum.POI_PROMOTION_SERVICE_E.getCode()
    );

    public WmTempletContractBasicBo getBasicById(boolean isEffective, int opUid, String opName)
            throws WmCustomerException, TException {
        logger.info("##getBasicById  contractId : {} isEffective : {}  opUid : {}  opUname : {}", contractId, isEffective, opUid, opName);
        WmTempletContractDB wmTempletContractDB;
        if (isEffective) {
            wmTempletContractDB = ctx().wmTempletContractAuditedDBMapper.selectByPrimaryKey(contractId);
        } else {
            wmTempletContractDB = ctx().wmTempletContractDBMapper.selectByPrimaryKey(contractId);
        }
        WmTempletContractBasicBo wmTempletContractBasicBo = WmTempletContractTransUtil.templetContractBasicDbToBo(wmTempletContractDB);
        logger.info("WmContractAggre#getBasicById, wmTempletContractBasicBo: {}", JSON.toJSONString(wmTempletContractBasicBo));
        return wmTempletContractBasicBo;
    }

    public WmCustomerContractBo getWmCustomerContractBoById(boolean isEffective, int opUid, String opName)
            throws WmCustomerException, TException {
        logger.info("##getWmCustomerContractBoById  contractId : {} isEffective : {}  opUid : {}  opUname : {}", contractId, isEffective, opUid, opName);
        WmCustomerContractBo customerContractBo = new WmCustomerContractBo();
        if (contractId <= 0) {
            return customerContractBo;
        }
        WmTempletContractDB wmTempletContractDB;
        List<WmTempletContractSignDB> templetContractSignDBS;
        if (isEffective) {
            wmTempletContractDB = ctx().wmTempletContractAuditedDBMapper.selectByPrimaryKey(contractId);
            templetContractSignDBS = ctx().wmTempletContractSignAuditedDBMapper.selectByWmTempletContractId(contractId);
        } else {
            wmTempletContractDB = ctx().wmTempletContractDBMapper.selectByPrimaryKey(contractId);
            templetContractSignDBS = ctx().wmTempletContractSignDBMapper.selectByWmTempletContractId(contractId);
        }
        customerContractBo.setBasicBo(WmTempletContractTransUtil.templetContractBasicDbToBo(wmTempletContractDB));
        customerContractBo.setSignBoList(WmTempletContractTransUtil.templetSignDbToBoList(templetContractSignDBS));
        return customerContractBo;
    }

    public List<WmCustomerContractBo> getWmCustomerContractBoListByIdList(List<Long> templetIdList, boolean isEffective, int opUid, String opName) {
        logger.info("##getWmCustomerContractBoListByIdList  contractIdList : {} isEffective : {}  opUid : {}  opUname : {}", JSON.toJSONString(templetIdList), isEffective, opUid, opName);
        List<WmCustomerContractBo> wmCustomerContractBoList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(templetIdList)) {
            return wmCustomerContractBoList;
        }

        List<WmTempletContractDB> wmTempletContractDBList = Lists.newArrayList();
        List<WmTempletContractSignDB> wmTempletContractSignDBList = Lists.newArrayList();
        if (isEffective) {
            wmTempletContractDBList = ctx().wmTempletContractAuditedDBMapper.selectByPrimaryKeyList(templetIdList);
            wmTempletContractSignDBList = ctx().wmTempletContractSignAuditedDBMapper.selectByWmTempletContractIdList(templetIdList);
        } else {
            wmTempletContractDBList = ctx().wmTempletContractDBMapper.selectByPrimaryKeyList(templetIdList);
            wmTempletContractSignDBList = ctx().wmTempletContractSignDBMapper.selectByWmTempletContractIdList(templetIdList);
        }

        Multimap<Long, WmTempletContractSignDB> contractSignDBMap = Multimaps.index(wmTempletContractSignDBList, new Function<WmTempletContractSignDB, Long>() {
            @Nullable
            @Override
            public Long apply(@Nullable WmTempletContractSignDB input) {
                return Long.valueOf(input.getWmTempletContractId());
            }
        });
        for (WmTempletContractDB wmTempletContractDB : wmTempletContractDBList) {
            WmCustomerContractBo wmCustomerContractBo = new WmCustomerContractBo();
            wmCustomerContractBo.setBasicBo(WmTempletContractTransUtil.templetContractBasicDbToBo(wmTempletContractDB));
            wmCustomerContractBo.setSignBoList(WmTempletContractTransUtil.templetSignDbToBoList(Lists.newArrayList(contractSignDBMap.get(wmTempletContractDB.getId()))));

            wmCustomerContractBoList.add(wmCustomerContractBo);
        }
        return wmCustomerContractBoList;
    }

    public List<WmTempletContractBasicBo> getWmCustomerContractBasicListByPoiIdIdAndTypes(long wmPoiId, List<Integer> types, int opUid, String opName)
            throws WmCustomerException, TException {
        logger.info("查询合同基本信息列表  wmPoiId:{}, types：{}， opUid:{}, opUname:{}", wmPoiId, types, opUid, opName);
        WmCustomerDB customerDB = ctx().wmCustomerService.selectCustomerByWmPoiId(wmPoiId);
        if (customerDB != null) {
            return WmContractAggre.Factory.makeWithCustomerId(customerDB.getId()).getContractBasicBoListByCusIdAndType(types, opUid, opName);
        }
        return Lists.newArrayList();
    }

    /**
     * 根据客户id获取合同列表
     */
    public ContractBoPageData getWmCustomerContractBoListByCusId(int pageNo, int pageSize, int opUid, String opName)
            throws WmCustomerException, TException {
        logger.info("查询合同列表  customerId:{}, pageNo:{}, pageSize:{} opUid:{}, opUname:{}", customerId, pageNo, pageSize, opUid, opName);
        PageData<WmTempletContractDB> basicPage = pageGetBasicByCusId(customerId, pageNo, pageSize);
        List<WmCustomerContractBo> showResult = Lists.newArrayList();
        for (WmTempletContractDB o : basicPage.getList()) {
            WmCustomerContractBo one = new WmCustomerContractBo();
            one.setBasicBo(WmTempletContractTransUtil.templetContractBasicDbToBo(o));
            List<WmTempletContractSignDB> contractSignDBList = ctx().wmTempletContractSignDBMapper
                    .selectByWmTempletContractId(o.getId());
            List<WmTempletContractSignBo> contractSignBoList = WmTempletContractTransUtil.templetSignDbToBoList(contractSignDBList);
            one.setSignBoList(contractSignBoList);
            showResult.add(one);
        }
        PageData<WmCustomerContractBo> boPageData = PageUtil.page(basicPage.getList(), showResult);

        return new ContractBoPageData(boPageData.getPageInfo(), boPageData.getList());
    }

    private PageData pageGetBasicByCusId(long customerId, int pageNo, int pageSize)
            throws WmCustomerException, TException {
        PageHelper.startPage(pageNo, pageSize);
        List<WmTempletContractDB> wmTempletContractDBList = ctx().wmTempletContractDBMapper
                .selectByParentIdAndTypes(customerId, Lists.newArrayList(
                        WmTempletContractTypeEnum.C1_E.getCode(),
                        WmTempletContractTypeEnum.C1_PAPER.getCode(),
                        WmTempletContractTypeEnum.C2_E.getCode(),
                        WmTempletContractTypeEnum.C2_PAPER.getCode(),
                        WmTempletContractTypeEnum.QUA_REAL_LETTER_E.getCode(),
                        WmTempletContractTypeEnum.POI_PROMOTION_SERVICE_E.getCode(),
                        WmTempletContractTypeEnum.GROUP_MEAL_E.getCode(),
                        WmTempletContractTypeEnum.BAG_SERVICE_E.getCode(),
                        WmTempletContractTypeEnum.FOODCITY_STATEMENT_E.getCode(),
                        WmTempletContractTypeEnum.INTERIM_SELF_E.getCode(),
                        WmTempletContractTypeEnum.SUBJECT_CHANGE_SUPPLEMENT_E.getCode(),
                        WmTempletContractTypeEnum.FOUR_WHEEL_PERFORMANCE_SUPPLEMENT_AGREEMENT_E.getCode(),
                        WmTempletContractTypeEnum.SPEEDY_DELIVERY_COOPERATION_AGREEMENT_E.getCode()
                ));
        PageHelper.clearPage();
        return PageUtil.page(wmTempletContractDBList);
    }


    public Integer getTypeByTempletId()
            throws WmCustomerException, TException {
        return ctx().wmTempletContractDBMapper.selectTypeByPrimaryKey(contractId);
    }

    public List<WmTempletContractBasicBo> selectAuditedContractBasicByParentIdAndTypes(List<Integer> types) {
        List<WmTempletContractDB> basicKist = ctx().wmTempletContractAuditedDBMapper
                .getBasicListByParentIdAndTypes((int) customerId, types);
        return WmTempletContractTransUtil.templetContractBasicListDbToBo(basicKist);
    }

    public List<WmTempletContractDB> getEffectContractByCustomerIdAndType(List<Integer> typeList) {
        return ctx().wmTempletContractAuditedDBMapper.getBasicListByParentIdAndTypes((int) customerId, typeList);
    }

    private List<WmTempletContractDB> getContractByCustomerIdAndTypes(List<Integer> typeList) {
        return ctx().wmTempletContractDBMapper.selectByParentIdAndTypes(customerId, typeList);
    }

    private List<WmTempletContractDB> getContractByCustomerIdAndType(int type) {
        return ctx().wmTempletContractDBMapper.selectByParentIdAndType(customerId, type);
    }

    public void saveWmContractFtlTagDB(int opUid, String opUname) {
        WmTempletContractDB wmTempletContractDB = ctx().wmTempletContractDBMapper.getByIdMaster(contractId);
        if (wmTempletContractDB != null) {
            WmContractFtlTagDB wmContractPdfDB = ctx().wmContractFtlTagMapper
                    .queryByContractIdAndType(wmTempletContractDB.getParentId().intValue(), ContractFtlTagConstant.C1_CANCEL_3000);
            if (wmContractPdfDB == null) {
                logger.info("不存在3000返还 插入3000返还记录  templetId:{}, opUid:{} opUname:{}", contractId, opUid, opUname);
                WmContractFtlTagDB wmContractFtlTagDB = new WmContractFtlTagDB();
                wmContractFtlTagDB.setContract_id(wmTempletContractDB.getParentId().intValue());
                wmContractFtlTagDB.setFtl_tag_type(ContractFtlTagConstant.C1_CANCEL_3000);
                wmContractFtlTagDB.setCtime(TimeUtil.unixtime());
                wmContractFtlTagDB.setUtime(TimeUtil.unixtime());
                wmContractFtlTagDB.setValid(ContractFtlTagConstant.VALID);
                ctx().wmContractFtlTagMapper.insert(wmContractFtlTagDB);
            } else if (null != wmContractPdfDB && ContractTagConstant.INVALID == wmContractPdfDB.getValid()) {
                wmContractPdfDB.setValid(ContractTagConstant.VALID);
                wmContractPdfDB.setUtime(TimeUtil.unixtime());
                ctx().wmContractFtlTagMapper.updateByContractIdAndType(wmContractPdfDB);
            }
        }
    }

    @Transactional
    public Boolean invalidContract(List<Long> poiIds, int opUid, String opName)
            throws WmCustomerException, TException {
        List<WmTempletContractDB> wmTempletContractDBList = getContractByCustomerIdAndTypes(Lists.newArrayList(
                WmTempletContractTypeEnum.C1_E.getCode(),
                WmTempletContractTypeEnum.C1_PAPER.getCode(),
                WmTempletContractTypeEnum.C2_E.getCode(),
                WmTempletContractTypeEnum.C2_PAPER.getCode(),
                WmTempletContractTypeEnum.QUA_REAL_LETTER_E.getCode(),
                WmTempletContractTypeEnum.POI_PROMOTION_SERVICE_E.getCode(),
                WmTempletContractTypeEnum.GROUP_MEAL_E.getCode(),
                WmTempletContractTypeEnum.BAG_SERVICE_E.getCode(),
                WmTempletContractTypeEnum.FOODCITY_STATEMENT_E.getCode(),
                WmTempletContractTypeEnum.SUBJECT_CHANGE_SUPPLEMENT_E.getCode(),
                WmTempletContractTypeEnum.FOUR_WHEEL_PERFORMANCE_SUPPLEMENT_AGREEMENT_E.getCode(),
                WmTempletContractTypeEnum.SPEEDY_DELIVERY_COOPERATION_AGREEMENT_E.getCode()
        ));
        boolean hasNotifyPoiProduce = false;
        ContractLogAggre contractLogAggre = ContractLogAggre.Factory.make();
        for (WmTempletContractDB db : wmTempletContractDBList) {
            ctx().wmTempletContractDBMapper.invalidContract(db.getId(), opUid);
            ctx().wmTempletContractSignDBMapper.invalid(db.getId(), opUid);
            contractLogAggre.logDelete((int) customerId, db.getId().intValue(), opUid, opName, "客户删除，导致合同删除");
            if (new WmTempletContractTypeBo(db.getType()).getType() == WmTempletContractTypeBo.TYPE_C1
                    && !hasNotifyPoiProduce && db.getStatus() != CustomerContractStatus.STAGE.getCode()) {
                hasNotifyPoiProduce = true;
                ctx().wmContractPoiProduceService.logPoiProduceForContractDEL((int) customerId, poiIds, opUid, opName);
            }
        }
        wmTempletContractDBList = ctx().wmTempletContractAuditedDBMapper.getBasicListByParentIdAndTypes((int) customerId, Lists.newArrayList(
                WmTempletContractTypeEnum.C1_E.getCode(),
                WmTempletContractTypeEnum.C1_PAPER.getCode(),
                WmTempletContractTypeEnum.C2_E.getCode(),
                WmTempletContractTypeEnum.C2_PAPER.getCode(),
                WmTempletContractTypeEnum.QUA_REAL_LETTER_E.getCode(),
                WmTempletContractTypeEnum.POI_PROMOTION_SERVICE_E.getCode(),
                WmTempletContractTypeEnum.GROUP_MEAL_E.getCode(),
                WmTempletContractTypeEnum.BAG_SERVICE_E.getCode(),
                WmTempletContractTypeEnum.FOODCITY_STATEMENT_E.getCode(),
                WmTempletContractTypeEnum.SUBJECT_CHANGE_SUPPLEMENT_E.getCode(),
                WmTempletContractTypeEnum.FOUR_WHEEL_PERFORMANCE_SUPPLEMENT_AGREEMENT_E.getCode(),
                WmTempletContractTypeEnum.SPEEDY_DELIVERY_COOPERATION_AGREEMENT_E.getCode()
        ));
        for (WmTempletContractDB db : wmTempletContractDBList) {
            ctx().wmTempletContractAuditedDBMapper.invalidContract(db.getId(), opUid);
            ctx().wmTempletContractSignAuditedDBMapper.invalid(db.getId(), opUid);
        }

        invalidQuaRealLetterWhiteList(customerId, opUid);
        return true;
    }

    private void invalidQuaRealLetterWhiteList(long customerId, int opUid) throws WmCustomerException {
        CustomerBlackWhiteParam param = new CustomerBlackWhiteParam();
        param.setBizId(customerId).setBizType(CustomerConstants.WHITE_LIST_BIZTYPE_QUA_REAL_LETTER).setType(CustomerConstants.TYPE_WHITE).setOpUid(opUid);
        ctx().wmCustomerBlackWhiteListService.deleteCustomerBlackWhiteList(param);
    }

    public Integer saveAndStartSign(int opUid, String opName)
            throws WmCustomerException, TException {
        logger.info("startSign contractBo:{}", JSON.toJSONString(contractBo));
        return getService(contractBo.getBasicBo().getType())
                .startSign(contractBo, opUid, opName);
    }

    public Integer saveAndCommitAudit(int opUid, String opName)
            throws WmCustomerException, TException {
        logger.info("commitAudit contractBo:{}", JSON.toJSONString(contractBo));
        return getService(contractBo.getBasicBo().getType())
                .commitAudit(contractBo, opUid, opName);
    }

    public Integer update(int opUid, String opName)
            throws WmCustomerException, TException {
        logger.info("update contractBo type:{}", contractBo.getBasicBo().getType());
        return getService(contractBo.getBasicBo().getType())
                .update(contractBo, opUid, opName);
    }

    public Integer save(int opUid, String opName)
            throws WmCustomerException, TException {
        logger.info("save contractBo type:{}", contractBo.getBasicBo().getType());
        return getService(contractBo.getBasicBo().getType())
                .save(contractBo, opUid, opName);
    }

    public List<WmCustomerContractBo> getAuditedContractBoListByCusIdAndTypeRT(List<Integer> types, int opUid, String opName)
            throws WmCustomerException, TException {
        logger.info("#getContractBoListByCusIdAndType 查询合同基本信息  customerId:{}, types：{}， opUid:{}, opUname:{}", customerId, types, opUid, opName);
        List<WmTempletContractDB> wmTempletContractDBList = ctx().wmTempletContractAuditedDBMapper
                .getBasicListByParentIdAndTypesMaster(customerId, types);
        if (wmTempletContractDBList == null) {
            return null;
        }
        List<WmCustomerContractBo> res = Lists.newArrayList();
        logger.info("#getAuditedContractBoListByCusIdAndTypeRT#wmTempletContractDBList#size:{},customerId:{}", wmTempletContractDBList.size(), customerId);
        wmTempletContractDBList.forEach(contractDd -> {
            logger.info("#getAuditedContractBoListByCusIdAndTypeRT#selectByWmTempletContractId customerId:{}, contracId:{}", customerId, contractDd.getId());
            List<WmTempletContractSignDB> wmTempletContractSignDBS =  ctx().wmTempletContractSignAuditedDBMapper.selectByWmTempletContractId(contractDd.getId());
            WmCustomerContractBo wmCustomerContractBo = WmTempletContractTransUtil.templetContractAndSignbToBo(contractDd, wmTempletContractSignDBS);
            res.add(wmCustomerContractBo);
        });
        return res;
    }

    public List<WmCustomerContractBo> getAuditedContractBoListByCusIdAndType(List<Integer> types, int opUid, String opName)
            throws WmCustomerException, TException {
        logger.info("#getContractBoListByCusIdAndType 查询合同基本信息  customerId:{}, types：{}， opUid:{}, opUname:{}", customerId, types, opUid, opName);
        List<WmTempletContractDB> wmTempletContractDBList = ctx().wmTempletContractAuditedDBMapper.getBasicListByParentIdAndTypes((int) customerId,
                types);
        if (wmTempletContractDBList == null) {
            return null;
        }
        List<WmCustomerContractBo> res = Lists.newArrayList();
//        logger.info("#getAuditedContractBoListByCusIdAndType#wmTempletContractDBList#size:{},customerId:{}", wmTempletContractDBList.size(), customerId);
        wmTempletContractDBList.forEach(contractDd -> {
//            logger.info("#getAuditedContractBoListByCusIdAndType#selectByWmTempletContractId customerId:{}, contracId:{}", customerId, contractDd.getId());
            List<WmTempletContractSignDB> wmTempletContractSignDBS = ctx().wmTempletContractSignAuditedDBMapper
                    .selectByWmTempletContractId(contractDd.getId());
            WmCustomerContractBo wmCustomerContractBo = WmTempletContractTransUtil.templetContractAndSignbToBo(contractDd, wmTempletContractSignDBS);
            res.add(wmCustomerContractBo);
        });
        return res;
    }

    public List<WmCustomerContractBo> getContractBoListByCusIdAndType(List<Integer> types, int opUid, String opName)
            throws WmCustomerException, TException {
        logger.info("#getContractBoListByCusIdAndType 查询合同基本信息  customerId:{}, types：{}， opUid:{}, opUname:{}", customerId, types, opUid, opName);
        List<WmTempletContractDB> wmTempletContractDBList = ctx().wmTempletContractDBMapper.selectValidByParentIdAndTypes(customerId, types);
        if (wmTempletContractDBList == null) {
            return null;
        }
        List<WmCustomerContractBo> res = Lists.newArrayList();
        wmTempletContractDBList.forEach(contractDd -> {
            List<WmTempletContractSignDB> wmTempletContractSignDBS = ctx().wmTempletContractSignDBMapper
                    .selectByWmTempletContractId(contractDd.getId());
            WmCustomerContractBo wmCustomerContractBo = WmTempletContractTransUtil.templetContractAndSignbToBo(contractDd, wmTempletContractSignDBS);
            res.add(wmCustomerContractBo);
        });
        return res;
    }

    public List<WmTempletContractBasicBo> getContractBasicBoListByCusIdAndType(List<Integer> types, int opUid, String opName)
            throws WmCustomerException, TException {
        logger.info("#getContractBoListByCusIdAndType 查询合同基本信息  customerId:{}, types：{}， opUid:{}, opUname:{}", customerId, types, opUid, opName);
        List<WmTempletContractDB> wmTempletContractDBList = ctx().wmTempletContractDBMapper
                .selectByParentIdAndTypes(customerId, types);
        return WmTempletContractTransUtil.templetContractBasicListDbToBo(wmTempletContractDBList);
    }

    public List<WmTempletContractBasicBo> selectByParentIdAndTypes(List<Integer> type) {
        logger.info("selectByParentIdAndTypes customerId={},type={}", customerId, type);
        List<WmTempletContractDB> wmTempletContractDBList = ctx().wmTempletContractAuditedDBMapper.selectByParentIdAndTypes
                (customerId, type);
        return WmTempletContractTransUtil.templetContractBasicListDbToBo(wmTempletContractDBList);
    }

    /**
     * 尝试是否可发起签约
     *
     * @param opUid
     * @param opUname
     * @throws WmCustomerException
     * @throws TException
     */
    public void startC1SignPreCheck(int opUid, String opUname) throws WmCustomerException, TException {
        WmCustomerContractBo wmCustomerContractBo = buildC1ContractBoForSign();
        if (wmCustomerContractBo == null) {
            return;
        }
        ContractCheckFilter.contractUpdateValidFilter(WmTempletContractTypeEnum.C1_E).filter(wmCustomerContractBo, opUid, opUname);

        if (wmCustomerContractBo.getBasicBo().getStatus() == CustomerContractStatus.SIGNING.getCode()) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "当前合同状态为"
                    + CustomerContractStatus.getByCode(wmCustomerContractBo.getBasicBo().getStatus()).getDesc() + "，不可修改。");
        }
    }

    /**
     * 直接发起签约（对于合同处于待发起签约状态）
     *
     * @param opUid
     * @param opUname
     * @throws WmCustomerException
     * @throws TException
     */
    public void startC1SignByWaitingSign(int opUid, String opUname, long manualBatchId) throws WmCustomerException, TException {
        logger.info("合同处于待发起签约，直接发起签约  customerId:{}  opUid:{}  opUname:{} manualBatchId:{}", customerId, opUid, opUname, manualBatchId);
        WmCustomerContractBo wmCustomerContractBo = buildC1ContractBoForSign();
        if (wmCustomerContractBo == null) {
            return;
        }
        wmCustomerContractBo.setManualBatchId(manualBatchId);
        WmContractAggre.Factory.makeWithWmCustomerContractBo(wmCustomerContractBo).saveAndStartSign(opUid, opUname);
    }

    /**
     * 取消待发起签约（对于合同处于待发起签约状态）
     *
     * @param opUid
     * @param opUname
     * @throws WmCustomerException
     * @throws TException
     */
    public void cancelC1SignByWaitingSign(String failReason, int opUid, String opUname) throws WmCustomerException, TException {
        logger.info("合同处于待发起签约，取消待发起签约  customerId:{}  failReason:{} opUid:{} opUname:{}", customerId, failReason, opUid, opUname);
        List<WmTempletContractDB> contractDBList = getContractByCustomerIdAndType(WmTempletContractTypeEnum.C1_E.getCode());
        if (CollectionUtils.isEmpty(contractDBList)) {
            logger.warn("合同不存在，或者被删除  customerId:{}", customerId);
            return;
        }
        ctx().wmEcontractSignBzService.cancelManualTask((int) customerId, EcontractTaskApplyTypeEnum.C1CONTRACT);
        Long contractId = contractDBList.get(0).getId();
        Integer type = WmContractAggre.Factory.make(contractId).getTypeByTempletId();
        getService(type)
                .signFail(contractId, failReason, opUid, opUname);
    }

    private WmCustomerContractBo buildC1ContractBoForSign() throws WmCustomerException {
        List<WmTempletContractDB> contractDBList = getContractByCustomerIdAndType(WmTempletContractTypeEnum.C1_E.getCode());
        if (CollectionUtils.isEmpty(contractDBList)) {
            logger.warn("合同不存在，或者被删除  customerId:{}", customerId);
            return null;
        }
        List<WmTempletContractSignDB> wmTempletContractSignDBS = ctx().wmTempletContractSignDBMapper.selectByWmTempletContractId(contractDBList.get(0).getId());
        WmCustomerContractBo wmCustomerContractBo = WmTempletContractTransUtil.templetContractAndSignbToBo(contractDBList.get(0), wmTempletContractSignDBS);
        wmCustomerContractBo.setPackWay(SignPackWay.DO_SIGN.getCode());
        wmCustomerContractBo.setIgnoreExistAnotherSignTypeContract(true);
        return wmCustomerContractBo;
    }

    public List<WmCustomerContractBo> getCusContractBoListByCusIdAndType(List<Integer> types, int opUid, String opName)
            throws WmCustomerException, TException {
        logger.info("#getContractBoListByCusIdAndType 查询合同基本信息  customerId:{}, types：{}， opUid:{}, opUname:{}", customerId, types, opUid, opName);
        List<WmTempletContractDB> wmTempletContractDBList = ctx().wmTempletContractDBMapper
                .selectByParentIdAndTypes(customerId, types);
        if (wmTempletContractDBList == null) {
            return null;
        }
        List<WmCustomerContractBo> res = Lists.newArrayList();
        wmTempletContractDBList.forEach(contractDd -> {
            List<WmTempletContractSignDB> wmTempletContractSignDBS =  ctx().wmTempletContractSignDBMapper.selectByWmTempletContractId(contractDd.getId());
            WmCustomerContractBo wmCustomerContractBo = WmTempletContractTransUtil.templetContractAndSignbToBo(contractDd, wmTempletContractSignDBS);
            res.add(wmCustomerContractBo);
        });
        return res;
    }

    public Map<Long, Boolean> hasAuditedC2ContractForAgentAndWmPoiBatch(Map<Long, Integer> wmPoiIdAndAgentIdMap) throws WmCustomerException, TException {
        logger.info("批量查询门店是否有生效C2合同  map:{}", JSON.toJSONString(wmPoiIdAndAgentIdMap));
        if (CollectionUtils.isEmpty(wmPoiIdAndAgentIdMap)) {
            return Maps.newHashMap();
        }
        int auditedContractStatusSize = ConfigUtilAdapter.getInt("batch_get_c2_audited_contract_status_size", 200);
        if (wmPoiIdAndAgentIdMap.size() > auditedContractStatusSize) {
            logger.warn("入参长度超限，限制(<=)："  + auditedContractStatusSize);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "入参长度超限，限制(<=)："  + auditedContractStatusSize);
        }
        Map<Long, Boolean> result = Maps.newHashMap();
        for (Map.Entry<Long, Integer> wmPoiIdAndAgentIdEntry : wmPoiIdAndAgentIdMap.entrySet()) {
            result.put(wmPoiIdAndAgentIdEntry.getKey(),
                    hasAuditedC2ContractForAgentAndWmPoi(wmPoiIdAndAgentIdEntry.getKey(), wmPoiIdAndAgentIdEntry.getValue()));
        }
        return result;
    }

    public Boolean hasAuditedC2ContractForAgentAndWmPoi(long wmPoiId, int agentId) throws WmCustomerException, TException {
        logger.info("查询门店是否有生效C2合同  wmPoiId:{} agentId:{}", wmPoiId, agentId);
        WmContractOnlineAggre onlineAggre = WmContractOnlineAggre.Factory.make();
        Boolean needC2ForPoiOnline;
        try {
            needC2ForPoiOnline = onlineAggre.needC2ForPoiSwitchAgent(wmPoiId);
        } catch (WmCustomerException | TException e) {
            logger.warn("查询门店信息异常", e);
            return false;
        }
        boolean res = !needC2ForPoiOnline
                || getC2ContractId(wmPoiId, agentId, true).size() > 0;
        logger.info("查询门店是否有生效C2合同  wmPoiId:{} agentId:{}  res:{}", wmPoiId, agentId, res);
        return res;
    }

    private List<Long> getC2ContractId(long wmPoiId, int agentId, boolean isEffective) throws WmCustomerException, TException {
        List<Long> result = Lists.newArrayList();
        Set<Integer> customerIdSet = WmCustomerPoiAggre.Factory.make().selectCustomerIdByPoiId(Sets.newHashSet(wmPoiId));
        if (CollectionUtils.isEmpty(customerIdSet)) {
            return result;
        }
        if (customerIdSet.size() > 1) {
            logger.error("门店：{} 绑定多个客户，客户ID：{}", wmPoiId, customerIdSet);
        }
        try {
            for (int cusId : customerIdSet) {
                List<WmCustomerContractBo> contractList;
                if (isEffective) {
                    contractList = Factory.makeWithCustomerId(cusId).getAuditedContractBoListByCusIdAndType(
                            Lists.newArrayList(WmTempletContractTypeEnum.C2_E.getCode(), WmTempletContractTypeEnum.C2_PAPER.getCode()), 0, "合作商切换");
                } else {
                    contractList = Factory.makeWithCustomerId(cusId).getContractBoListByCusIdAndType(
                            Lists.newArrayList(WmTempletContractTypeEnum.C2_E.getCode(), WmTempletContractTypeEnum.C2_PAPER.getCode()), 0, "合作商切换");
                }
                if (CollectionUtils.isEmpty(contractList)) {
                    continue;
                }
                for (WmCustomerContractBo customerContractBo : contractList) {
                    WmContractSignAggre wmContractSignAggre = WmContractSignAggre.Factory.makeWithSignBo(customerContractBo.getSignBoList());
                    WmTempletContractSignBo bSignerBo = wmContractSignAggre.getPartyBSignerBo();
                    if (bSignerBo != null && bSignerBo.getSignId() == agentId) {
                        logger.info("门店:{} 存在生效的合作商:{} 对应的C2合同", wmPoiId, agentId);
                        long contractId = customerContractBo.getBasicBo().getTempletContractId();
                        result.add(contractId);
                    }
                }

            }
        } catch (WmCustomerException e) {
            logger.warn(e.getMessage(), e);
        } catch (TException e) {
            logger.error(e.getMessage(), e);
        }
        return result;
    }

    public void invalidC2ContractForAgentAndWmPoi(long wmPoiId, int agentId, int opUid, String opUname) throws WmCustomerException, TException {
        logger.info("根据门店id废除C2合同 wmPoiId:{} agentId:{} opUid:{} opUname:{}", wmPoiId, agentId, opUid, opUname);
        List<Long> c2ContractIdList = getC2ContractId(wmPoiId, agentId, false);
        if (CollectionUtils.isEmpty(c2ContractIdList)) {
            logger.info("无生效的C2合同 wmPoiId:{} agentId:{}", wmPoiId, agentId);
            return;
        }
        for (Long c2ContractId : c2ContractIdList) {
            WmContractValidCallbackAggre.Factory.make(c2ContractId).invalid(opUid, opUname);
        }
    }

    public List<Integer> getC2PoiIdListByWmAgentId(int agentId) throws WmCustomerException, TException {
        logger.info("根据合作商id查询与该合作商签署C2合同的门店列表  agentId:{} ", agentId);
        List<WmTempletContractDB> contractDBList = ctx().wmTempletContractAuditedDBMapper.selectC2ContractListByAgentId(agentId);
        if (CollectionUtils.isEmpty(contractDBList)) {
            logger.info("根据合作商id查询与该合作商签署C2合同的门店列表  res:empty agentId:{}", agentId);
            return Lists.newArrayList();
        }
        List<Integer> result = Lists.newArrayList();
        for(WmTempletContractDB contractDB : contractDBList) {
            List<Long> wmPoiList = ctx().wmCustomerPoiService.selectWmPoiIdsByCustomerId(contractDB.getParentId().intValue());
            if (CollectionUtils.isEmpty(wmPoiList)) {
                continue;
            }
            List<WmPoiDomain> wmPoiDomainList = ctx().wmPoiClient.getWmPoiByIds(wmPoiList);
            for (WmPoiDomain domain : wmPoiDomainList) {
                if (domain.getAgentId() > 0) {
                    result.add(domain.getWmPoiId());
                }
            }
        }
        logger.info("根据合作商id查询与该合作商签署C2合同的门店列表   agentId:{} res:{}", agentId, JSON.toJSONString(result));
        return result;
    }

    public BooleanResult saveCustomerBlackWhiteList(CustomerBlackWhiteBo bo)
            throws WmCustomerException {
        AssertUtil.assertObjectNotNull(bo);
        return ctx().wmCustomerBlackWhiteListService.saveCustomerBlackWhiteList(bo);
    }

    public LongResult getC1ContractStartSignTime(long wmPoiId) {
        Set<Integer> customerIdSet = ctx().wmCustomerPoiService.selectCustomerIdByPoiId(Sets.newHashSet(wmPoiId));
        if (CollectionUtils.isEmpty(customerIdSet)) {
            return new LongResult(0);
        }

        Integer customerId = Lists.newArrayList(customerIdSet).get(0);
        List<WmEcontractSignManualTaskDB> wmEcontractSignManualTaskDBList = ctx().wmEcontractManualTaskBizService.getManualTaskByCustomerIdAndModule(customerId, EcontractTaskApplyTypeEnum.C1CONTRACT.getName());
        if (!CollectionUtils.isEmpty(wmEcontractSignManualTaskDBList)) {
            WmEcontractSignManualTaskDB wmEcontractSignManualTaskDB = wmEcontractSignManualTaskDBList.get(0);
            return new LongResult(wmEcontractSignManualTaskDB.getCtime());
        }

        List<WmTempletContractDB> wmTempletContractDBList = ctx().wmTempletContractDBMapper.selectByParentIdAndType((long) customerId, WmTempletContractTypeEnum.C1_E.getCode());
        if (CollectionUtils.isEmpty(wmTempletContractDBList)) {
            return new LongResult(0);
        }

        WmContractVersionDB wmContractVersionDB = ctx().wmContractVersionService.getLastByWmContractIdAndTypeAndStatusList(
                wmTempletContractDBList.get(0).getId().intValue(),
                CustomerContractConstant.TEMPLET_CONTRACT_VERSION_TYPE,
                Lists.newArrayList((byte) CustomerContractStatus.SIGNING.getCode(), (byte) CustomerContractStatus.SIGN_FAIL.getCode(), (byte) CustomerContractStatus.EFFECT.getCode()));
        if (wmContractVersionDB == null) {
            return new LongResult(0);
        }
        return new LongResult(wmContractVersionDB.getCtime());
    }

    public OpResultBo saveAndSignContract(long wmPoiId, int type, int signSubjectCode, int opUid, String opUname) throws WmCustomerException, TException {
        logger.info("saveAndSignContract wmPoiId = {}, type = {}, opUid = {}, opUname = {}", wmPoiId, type, opUid, opUname);
        if (!autoSaveSignContractTypeSet.contains(type)) {
            throw new WmCustomerException(CustomerContractErrCodeEnum.NOT_SUPPORT_TYPE.getCode(), CustomerContractErrCodeEnum.NOT_SUPPORT_TYPE.getDesc());
        }

        WmCustomerDB wmCustomerDB = ctx().wmCustomerService.selectCustomerByWmPoiId(wmPoiId);
        if (wmCustomerDB == null) {
            return new OpResultBo(CustomerContractRetCodeEnum.BIZ_ERROR.getCode(), "查无对应客户");
        }
        if (SignType.PAPER.getCode() == wmCustomerDB.getSignMode()) {
            return new OpResultBo(CustomerContractRetCodeEnum.BIZ_ERROR.getCode(), "纸质签约,不可走电子签约流程");
        }

        List<WmTempletContractDB> wmTempletContractDBList = ctx().wmTempletContractAuditedDBMapper.selectByParentIdAndTypes(Long.valueOf(wmCustomerDB.getId()), Lists.newArrayList(type));
        if (!CollectionUtils.isEmpty(wmTempletContractDBList)) {
            return new OpResultBo(CustomerContractRetCodeEnum.EFFECTED.getCode(),  CustomerContractRetCodeEnum.EFFECTED.getDesc());
        }

        wmTempletContractDBList = ctx().wmTempletContractDBMapper.selectByParentIdAndTypes(Long.valueOf(wmCustomerDB.getId()), Lists.newArrayList(type));
        if (CollectionUtils.isEmpty(wmTempletContractDBList)) {
            return doSaveAndSign(wmCustomerDB, type, signSubjectCode, opUid, opUname);
        }
        return doSaveOrResendMsg(wmTempletContractDBList.get(0), opUid, opUname);
    }

    private OpResultBo doSaveOrResendMsg(WmTempletContractDB wmTempletContractDB, int opUid, String opUname) {
        if (CustomerContractStatus.SIGNING.getCode() == wmTempletContractDB.getStatus()) {
            return handleSigningContract(wmTempletContractDB);
        } else if (CustomerContractStatus.SIGN_FAIL.getCode() == wmTempletContractDB.getStatus()) {
            return handleSignFailContract(wmTempletContractDB, opUid, opUname);
        } else {
            return new OpResultBo(CustomerContractRetCodeEnum.BIZ_ERROR.getCode(), "暂不支持该合同状态操作");
        }
    }

    private OpResultBo handleSignFailContract(WmTempletContractDB wmTempletContractDB, int opUid, String opUname) {
        WmCustomerKp wmCustomerKp = ctx().wmCustomerKpService.getCustomerKpOfEffectiveSigner(wmTempletContractDB.getParentId().intValue());
        if (wmCustomerKp == null) {
            return new OpResultBo(CustomerContractRetCodeEnum.BIZ_ERROR.getCode(), "查无客户KP");
        }

        List<WmTempletContractSignDB> wmTempletContractSignDBList = ctx().wmTempletContractSignDBMapper.selectByWmTempletContractId(wmTempletContractDB.getId());
        OpResultBo opResultBo;
        try {
            Integer contractId = Factory.makeWithWmCustomerContractBo(
                            WmTempletContractTransUtil.templetContractAndSignbToBo(wmTempletContractDB, wmTempletContractSignDBList))
                    .saveAndStartSign(opUid, opUname);
            String signPhoneNum = wmCustomerKp.getPhoneNum().length() < 4 ? wmCustomerKp.getPhoneNum() : wmCustomerKp.getPhoneNum().substring(wmCustomerKp.getPhoneNum().length() - 4);
            opResultBo = new OpResultBo(Long.valueOf(contractId), CustomerContractRetCodeEnum.SAVE_SIGN_SUCCESS.getCode(), "已发起签约", signPhoneNum);
        } catch (WmCustomerException e) {
            opResultBo = new OpResultBo(CustomerContractRetCodeEnum.BIZ_ERROR.getCode(), e.getMsg());
        } catch (Exception e) {
            opResultBo = new OpResultBo(CustomerContractRetCodeEnum.SYSTEM_ERROR.getCode(), "系统异常");
        }
        return opResultBo;
    }

    private OpResultBo handleSigningContract(WmTempletContractDB wmTempletContractDB) {
        WmContractVersionDB wmContractVersionDB = ctx().wmContractVersionService.getByIdAndTypeMaster(wmTempletContractDB.getId().intValue(), CustomerContractConstant.TEMPLET_CONTRACT_VERSION_TYPE);
        if (wmContractVersionDB == null) {
            return new OpResultBo(CustomerContractRetCodeEnum.BIZ_ERROR.getCode(),  "合同尚未签约,不可重发短信");
        }
        WmCustomerKp wmCustomerKp = ctx().wmCustomerKpService.getCustomerKpOfEffectiveSigner(wmTempletContractDB.getParentId().intValue());
        if (wmCustomerKp == null) {
            return new OpResultBo(CustomerContractRetCodeEnum.BIZ_ERROR.getCode(), "查无客户KP");
        }

        OpResultBo opResultBo;
        try {
            ctx().wmEcontractSignBzService.resendMsg(Long.valueOf(wmContractVersionDB.getTransaction_id()));
            String signPhoneNum = wmCustomerKp.getPhoneNum().substring(wmCustomerKp.getPhoneNum().length() - 4);
            opResultBo = new OpResultBo(wmTempletContractDB.getId(), CustomerContractRetCodeEnum.SEND_MSG_SUCCESS.getCode(), "已发起签约", signPhoneNum);
        }  catch (WmCustomerException e) {
            opResultBo = new OpResultBo(CustomerContractRetCodeEnum.BIZ_ERROR.getCode(), e.getMsg());
        } catch (Exception e) {
            opResultBo = new OpResultBo(CustomerContractRetCodeEnum.SYSTEM_ERROR.getCode(), "系统异常");
        }
        return opResultBo;
    }

    private OpResultBo doSaveAndSign(WmCustomerDB wmCustomerDB, int type, int signSubjectCode, int opUid, String opUname) throws TException, WmCustomerException {
        WmTempletContractBasicBo wmTempletContractBasicBo = new WmTempletContractBasicBo();
        wmTempletContractBasicBo.setParentId(wmCustomerDB.getId());
        wmTempletContractBasicBo.setTempletContractId(0);
        wmTempletContractBasicBo.setType(type);
        wmTempletContractBasicBo.setDueDate(0L);
        wmTempletContractBasicBo.setContractNum("电子合同保存后自动生成编号");

        CustomerPaperContractRemarkBo paperContractRemarkBo = new CustomerPaperContractRemarkBo();
        paperContractRemarkBo.setContractScan(new MultiFileJsonBo());
        paperContractRemarkBo.setOtherContractScan(new MultiFileJsonBo());
        wmTempletContractBasicBo.setExtStr(JSON.toJSONString(paperContractRemarkBo));

        WmTempletContractSignBo wmTempletContractSignBoA = new WmTempletContractSignBo();
        wmTempletContractSignBoA.setTempletContractId(0);
        wmTempletContractSignBoA.setSignType("A");
        wmTempletContractSignBoA.setSignId(wmCustomerDB.getId());
        wmTempletContractSignBoA.setSignName(wmCustomerDB.getCustomerName());
        WmCustomerKp wmCustomerKp = ctx().wmCustomerKpService.getCustomerKpOfEffectiveSigner(wmCustomerDB.getId());
        if (wmCustomerKp == null) {
            return new OpResultBo(CustomerContractRetCodeEnum.BIZ_ERROR.getCode(), "查无客户KP");
        }
        wmTempletContractSignBoA.setSignPeople(wmCustomerKp.getCompellation());
        wmTempletContractSignBoA.setSignPhone(wmCustomerKp.getPhoneNum());

        WmTempletContractSignBo wmTempletContractSignBoB = new WmTempletContractSignBo();
        wmTempletContractSignBoB.setTempletContractId(0);
        wmTempletContractSignBoB.setSignType("B");
        wmTempletContractSignBoB.setSignId(0);
        SignSubjectEnum signSubjectEnum = SignSubjectEnum.getByCode(signSubjectCode);
        if (signSubjectEnum == null) {
            return new OpResultBo(CustomerContractRetCodeEnum.BIZ_ERROR.getCode(), "该签约主体暂不支持");
        }
        wmTempletContractSignBoB.setSignName(signSubjectEnum.getDesc());
        WmEmploy wmEmploy = ctx().wmEmployClient.getById(wmCustomerDB.getOwnerUid());
        if (wmEmploy == null) {
            return new OpResultBo(CustomerContractRetCodeEnum.BIZ_ERROR.getCode(), "签约人乙方信息不存在");
        }
        wmTempletContractSignBoB.setSignPeople(wmEmploy.getName());
        wmTempletContractSignBoB.setSignPhone(ctx().empServiceAdaptor.getPhone(wmCustomerDB.getOwnerUid()));

        String today = DateUtil.secondsToString(DateUtil.unixTime());
        wmTempletContractSignBoA.setSignTime(today);
        wmTempletContractSignBoB.setSignTime(today);

        List<WmTempletContractSignBo> wmTempletContractSignBoList = Lists.newArrayList(wmTempletContractSignBoA, wmTempletContractSignBoB);
        WmCustomerContractBo wmCustomerContractBo = new WmCustomerContractBo();
        wmCustomerContractBo.setBasicBo(wmTempletContractBasicBo);
        wmCustomerContractBo.setSignBoList(wmTempletContractSignBoList);
        wmCustomerContractBo.setPackWay(SignPackWay.DO_SIGN.getCode());
        wmCustomerContractBo.setManualBatchId(0L);
        wmCustomerContractBo.setIgnoreExistAnotherSignTypeContract(false);

        OpResultBo opResultBo;
        try {
            Integer contractId = Factory.makeWithWmCustomerContractBo(wmCustomerContractBo).saveAndStartSign(opUid, opUname);
            String signPhoneNum = wmCustomerKp.getPhoneNum().length() < 4 ? wmCustomerKp.getPhoneNum() : wmCustomerKp.getPhoneNum().substring(wmCustomerKp.getPhoneNum().length() - 4);
            opResultBo = new OpResultBo(Long.valueOf(contractId), CustomerContractRetCodeEnum.SEND_MSG_SUCCESS.getCode(), "已发起签约", signPhoneNum);
        } catch (WmCustomerException e) {
            opResultBo = new OpResultBo(CustomerContractRetCodeEnum.BIZ_ERROR.getCode(), e.getMsg());
        } catch (Exception e) {
            opResultBo = new OpResultBo(CustomerContractRetCodeEnum.SYSTEM_ERROR.getCode(), "系统异常");
        }
        return opResultBo;
    }

    public static class Factory implements Entity.Factory {

        private static WmContractAggre.Context context = new WmContractAggre.Context();

        public static WmContractAggre make() {
            return new WmContractAggre(context);
        }

        public static WmContractAggre make(long contractId) {
            WmContractAggre wmContractAggre = new WmContractAggre(context);
            wmContractAggre.contractId = contractId;
            return wmContractAggre;
        }

        public static WmContractAggre makeWithCustomerId(long customerId) {
            WmContractAggre wmContractAggre = new WmContractAggre(context);
            wmContractAggre.customerId = customerId;
            return wmContractAggre;
        }

        public static WmContractAggre makeWithWmCustomerContractBo(WmCustomerContractBo contractBo) {
            WmContractAggre wmContractAggre = new WmContractAggre(context);
            wmContractAggre.contractBo = contractBo;
            return wmContractAggre;
        }

    }

    public static class Context extends AutoWireBase implements Entity.Context {
        @Autowired
        WmTempletContractDBMapper wmTempletContractDBMapper;

        @Autowired
        WmTempletContractAuditedDBMapper wmTempletContractAuditedDBMapper;

        @Autowired
        WmTempletContractSignDBMapper wmTempletContractSignDBMapper;

        @Autowired
        WmTempletContractSignAuditedDBMapper wmTempletContractSignAuditedDBMapper;

        @Autowired
        WmContractVersionAtomService wmContractVersionService;

        @Autowired
        WmContractFtlTagMapper wmContractFtlTagMapper;

        @Autowired
        WmCustomerService wmCustomerService;

        @Autowired
        WmCustomerPoiService wmCustomerPoiService;

        @Autowired
        WmCustomerKpService wmCustomerKpService;

        @Autowired
        WmSettleService wmSettleService;

        @Autowired
        WmAgentContractThriftService.Iface wmAgentContractThriftService;

        @Autowired
        WmContractPoiProduceAtomService wmContractPoiProduceService;

        @Autowired
        WmEmployeeService wmEmployeeService;
        @Autowired
        WmNoticePublishThriftService.Iface wmNoticePublishThriftService;

        @Autowired
        WmCustomerOplogService wmCustomerOplogService;

        @Autowired
        WmEcontractSignBzService wmEcontractSignBzService;

        @Autowired
        WmPoiClient wmPoiClient;
        @Autowired
        WmCustomerBlackWhiteListService wmCustomerBlackWhiteListService;

        @Autowired
        private WmEcontractManualTaskBizService wmEcontractManualTaskBizService;

        @Resource
        WmEmployClient wmEmployClient;

        @Autowired
        EmpServiceAdaptor empServiceAdaptor;

    }
}

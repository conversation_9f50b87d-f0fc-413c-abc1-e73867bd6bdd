package com.sankuai.meituan.waimai.customer.domain.sc;

import lombok.Data;

/**
 * 学校平台合作信息日志BO对象V2
 * <AUTHOR>
 * @date 2024/06/20
 * @email <EMAIL>
 */
@Data
public class WmScSchoolCoPlatformLogBO {
    /**
     * 变更前DO对象
     */
    private WmScSchoolCoPlatformDO wmScSchoolCoPlatformDOBefore;
    /**
     * 变更后DO对象
     */
    private WmScSchoolCoPlatformDO wmScSchoolCoPlatformDOAfter;
    /**
     * 新增DO对象
     */
    private WmScSchoolCoPlatformDO wmScSchoolCoPlatformDOInsert;
    /**
     * 用户ID
     */
    private Integer userId;
    /**
     * 用户名称
     */
    private String userName;


    public static final class Builder {

        private WmScSchoolCoPlatformDO wmScSchoolCoPlatformDOBefore;

        private WmScSchoolCoPlatformDO wmScSchoolCoPlatformDOAfter;

        private WmScSchoolCoPlatformDO wmScSchoolCoPlatformDOInsert;

        private Integer userId;

        private String userName;

        public Builder() {
        }

        public WmScSchoolCoPlatformLogBO.Builder wmScSchoolCoPlatformDOBefore(WmScSchoolCoPlatformDO val) {
            this.wmScSchoolCoPlatformDOBefore = val;
            return this;
        }

        public WmScSchoolCoPlatformLogBO.Builder wmScSchoolCoPlatformDOAfter(WmScSchoolCoPlatformDO val) {
            this.wmScSchoolCoPlatformDOAfter = val;
            return this;
        }

        public WmScSchoolCoPlatformLogBO.Builder wmScSchoolCoPlatformDOInsert(WmScSchoolCoPlatformDO val) {
            this.wmScSchoolCoPlatformDOInsert = val;
            return this;
        }

        public WmScSchoolCoPlatformLogBO.Builder userId(Integer val) {
            this.userId = val;
            return this;
        }

        public WmScSchoolCoPlatformLogBO.Builder userName(String val) {
            this.userName = val;
            return this;
        }

        public WmScSchoolCoPlatformLogBO build() {
            WmScSchoolCoPlatformLogBO wmScSchoolCoPlatformLogBO = new WmScSchoolCoPlatformLogBO();
            wmScSchoolCoPlatformLogBO.setUserId(this.userId);
            wmScSchoolCoPlatformLogBO.setUserName(this.userName);
            wmScSchoolCoPlatformLogBO.setWmScSchoolCoPlatformDOInsert(this.wmScSchoolCoPlatformDOInsert);
            wmScSchoolCoPlatformLogBO.setWmScSchoolCoPlatformDOBefore(this.wmScSchoolCoPlatformDOBefore);
            wmScSchoolCoPlatformLogBO.setWmScSchoolCoPlatformDOAfter(this.wmScSchoolCoPlatformDOAfter);
            return wmScSchoolCoPlatformLogBO;
        }
    }

}

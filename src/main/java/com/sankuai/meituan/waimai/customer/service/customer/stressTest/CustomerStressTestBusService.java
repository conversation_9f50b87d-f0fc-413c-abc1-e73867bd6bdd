package com.sankuai.meituan.waimai.customer.service.customer.stressTest;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.meituan.service.mobile.mtthrift.util.ClientInfoUtil;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.constant.customer.CustomerPoiBindTypeEnum;
import com.sankuai.meituan.waimai.customer.constant.customer.WmCustomerPoiOplogSourceTypeEnum;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerPoiDBMapper;
import com.sankuai.meituan.waimai.customer.ddd.adapter.MtCustomerThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.domain.poi.WmCustomerPoiBindBo;
import com.sankuai.meituan.waimai.customer.domain.task.CustomerOperateBO;
import com.sankuai.meituan.waimai.customer.service.customer.Impl.WmLeafCustomerServiceImpl;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.customer.service.customer.poi.CustomerPoiBindStressTestService;
import com.sankuai.meituan.waimai.customer.service.customer.task.CustomerTaskService;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerRealTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.task.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.WmCustomerOplogBo;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiControl;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import com.sankuai.meituan.waimai.thrift.service.WmPoiFlowlineQueryThriftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 20231211
 * @desc 客户压测专用服务
 */
@Service
@Slf4j
public class CustomerStressTestBusService {

    @Autowired
    private WmCustomerService wmCustomerService;

    @Autowired
    private WmPoiFlowlineQueryThriftService.Iface wmPoiFlowlineQueryThriftService;

    @Autowired
    private WmCustomerPoiDBMapper wmCustomerPoiDBMapper;

    @Autowired
    private CustomerTaskService customerTaskService;

    @Autowired
    private CustomerPoiBindStressTestService customerPoiBindStressTestService;

    @Autowired
    private WmLeafCustomerServiceImpl wmLeafCustomerService;

    @Autowired
    private MtCustomerThriftServiceAdapter mtCustomerThriftServiceAdapter;

    /**
     * 客户门店绑定压测场景
     */
    private static final String customerBusinessOnStressTestType = "customerBusinessOnStressTest";

    /**
     * 客户门店绑定
     */
    private static final String customerBindPoi = "customerBindPoi";

    /**
     * 修改客户类型
     */
    private static final String changeCustomerRealType = "changeCustomerRealType";

    /**
     * 客户门店绑定异常
     */
    private static final String customerBindPoiError = "customerBindPoiError";

    /**
     * 修改客户类型异常
     */
    private static final String changeCustomerRealTypeError = "changeCustomerRealTypeError";

    /**
     * 直接创建客户与门店关系
     *
     * @param customerId
     * @param wmPoiId
     * @throws WmCustomerException
     */
    public void customerDirectBindPoi(Integer customerId, Long wmPoiId) throws WmCustomerException {

        try {
            //校验客户是否有效存在
            WmCustomerDB wmCustomerDB = wmCustomerService.selectCustomerByIdRT(customerId);
            if (wmCustomerDB == null) {
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "客户不存在");
            }
            //校验当前客户类型是否允许压测场景直接绑定
            List<Integer> limitBindCustomerRealTypes = MccCustomerConfig.getStressTestLimitBindCustomerRealType();
            if (limitBindCustomerRealTypes.contains(wmCustomerDB.getCustomerRealType())) {
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "客户类型不支持压测场景直接绑定门店");
            }
            //上级客户不允许绑定门店
            if (wmCustomerDB.getIsLeaf() == CustomerConstants.CUSTOMER_IS_LEAF_NO) {
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "不允许绑定到上级客户");
            }

            //校验当前请求的appKey是否允许调用
            if (!checkRequestAppKey(ClientInfoUtil.getClientAppKey())) {
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "当前appKey不在允许调用范围内，请先申请权限");
            }

            //校验门店存在
            Set wmPoiIdSet = Sets.newHashSet(Lists.newArrayList(wmPoiId));
            checkNotExistWmPoiId(wmPoiIdSet);

            // 校验门店是否已绑定客户
            Set<Long> existWmPoiIds = wmCustomerPoiDBMapper.selectExistPoiByWmPoiId(wmPoiIdSet);
            if (!existWmPoiIds.isEmpty()) {
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR,
                        String.format("%s已有客户无法绑定", StringUtils.join(existWmPoiIds, CustomerConstants.SPLIT_SYMBOL)));
            }
            CustomerOperateBO customerOperateBO = new CustomerOperateBO.Builder()
                    .opUserId(0).opUserName("压测系统").remark("")
                    .opSource(CustomerTaskSourceEnum.STRESS_TEST.getCode())
                    .taskType(CustomerTaskTypeEnum.CUSTOMER_BIND_POI.getCode())
                    .opDetailSource(CustomerTaskDetailSourceEnum.STRESS_TEST.getDesc())
                    .taskSceneType(CustomerTaskSceneType.CUSTOMER_BIND_OR_UNBIND.getCode())
                    .opSystem(CustomerTaskOpSystemEnum.WAIMAI_SETTLE_SYS.getDesc())
                    .build();
            //参数非空则创建客户任务
            Map<Long, Integer> poiAndTaskMaps = customerTaskService.batchAddCustomerBindTask(wmCustomerDB.getId(), wmPoiIdSet, customerOperateBO);

            //门店直接绑定客户
            customerPoiBindStressTestService.bindCustomerPoiOnStressTest(new WmCustomerPoiBindBo(
                    wmCustomerDB, wmPoiIdSet, "", 0, "压测系统",
                    null, poiAndTaskMaps, CustomerPoiBindTypeEnum.DIRECT_BIND,
                    WmCustomerPoiOplogSourceTypeEnum.STRESS_TEST
            ));
            Cat.logEvent(customerBusinessOnStressTestType, customerBindPoi);
        } catch (WmCustomerException e) {
            log.error("CustomerStressTestBusService.customerDirectBindPoi,压测场景客户直接绑定门店发生业务异常,customerId={},wmPoiId={}", customerId, wmPoiId, e);
            Cat.logEvent(customerBusinessOnStressTestType, customerBindPoiError);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("CustomerStressTestBusService.customerDirectBindPoi,压测场景客户直接绑定门店发生系统异常,customerId={},wmPoiId={}", customerId, wmPoiId, e);
            Cat.logEvent(customerBusinessOnStressTestType, customerBindPoiError);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "系统异常");
        }
    }

    /**
     * 直接修改客户类型
     *
     * @param customerId
     * @param newCustomerRealType
     * @throws WmCustomerException
     */
    public void changeCustomerRealType(Integer customerId, Integer newCustomerRealType) throws WmCustomerException {

        try {
            //校验客户是否有效存在
            WmCustomerDB wmCustomerDB = wmCustomerService.selectCustomerById(customerId);
            if (wmCustomerDB == null) {
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "客户不存在");
            }
            //校验当前客户类型是否允许压测场景修改为指定客户类型
            List<Integer> limitChangeCustomerRealTypes = MccCustomerConfig.getStressTestLimitChangeCustomerRealType();
            if (limitChangeCustomerRealTypes.contains(newCustomerRealType)) {
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "压测场景不支持直接修改为当前客户类型,newCustomerRealType=" + CustomerRealTypeEnum.getByValue(newCustomerRealType));
            }
            //要修改的客户类型与当前一致，则直接返回
            if (newCustomerRealType.equals(wmCustomerDB.getCustomerRealType())) {
                return;
            }

            //校验当前请求的appKey是否允许调用
            if (!checkRequestAppKey(ClientInfoUtil.getClientAppKey())) {
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "当前appKey不在允许调用范围内，请先申请权限");
            }

            //1、直接修改客户类型
            wmCustomerService.updateCustomerRealType(customerId, newCustomerRealType);
            //添加操作记录
            addCustomerRealTypeChangeLog(wmCustomerDB, newCustomerRealType);

            //2、判断客户类型修改后业务线是否变更，如果变更需要同步客户平台
            CustomerRealTypeEnum newCustomerRealTypeEnum = CustomerRealTypeEnum.getByValue(newCustomerRealType);
            CustomerRealTypeEnum oldCustomerRealTypeEnum = CustomerRealTypeEnum.getByValue(wmCustomerDB.getCustomerRealType());
            if (!newCustomerRealTypeEnum.getBizOrgCode().equals(oldCustomerRealTypeEnum.getBizOrgCode())) {
                log.info("CustomerStressTestBusService.changeCustomerRealType,客户类型修改前后业务线不一致,更新业务线到客户平台,customerId={}", customerId);
                //调用客户平台接口修改业务线属性
                mtCustomerThriftServiceAdapter.saveMtCustomerBizOrgCode(wmCustomerDB.getMtCustomerId(), newCustomerRealTypeEnum.getBizOrgCode());
            }
            Cat.logEvent(customerBusinessOnStressTestType, changeCustomerRealType);
        } catch (WmCustomerException e) {
            log.error("CustomerStressTestBusService.changeCustomerRealType,压测场景直接修改客户类型发生业务异常,customerId={},newCustomerRealType={}", customerId, newCustomerRealType, e);
            Cat.logEvent(customerBusinessOnStressTestType, changeCustomerRealTypeError);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, e.getMsg());
        } catch (Exception e) {
            log.error("CustomerStressTestBusService.changeCustomerRealType,压测场景直接修改客户类型发生系统异常,customerId={},newCustomerRealType={}", customerId, newCustomerRealType, e);
            Cat.logEvent(customerBusinessOnStressTestType, changeCustomerRealTypeError);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "系统异常");
        }
    }


    /**
     * 校验门店是否存在
     *
     * @param wmPoiIdSet
     * @throws WmCustomerException
     * @throws TException
     */
    private void checkNotExistWmPoiId(Set<Long> wmPoiIdSet) throws WmCustomerException, TException {
        List<Long> notExistList = Lists.newArrayList();
        for (Long wmPoiId : wmPoiIdSet) {
            try {
                WmPoiControl wmPoiControl = wmPoiFlowlineQueryThriftService.getWmPoiControlByWmPoiIdRT(wmPoiId);
                if (wmPoiControl == null) {
                    log.info("客户绑定门店,校验门店ID{}不存在", wmPoiId);
                    notExistList.add(wmPoiId);
                } else {
                    if (wmPoiControl.getIs_delete() == 1) {
                        log.info("客户绑定门店,校验门店ID{}已删除", wmPoiId);
                        notExistList.add(wmPoiId);
                    }
                }
            } catch (WmServerException e) {
                log.error("查询门店是否删除异常");
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "查询门店是否删除异常");
            }
        }
        if (!CollectionUtils.isEmpty(notExistList)) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR,
                    String.format("门店ID:%s不存在", StringUtils.join(notExistList, CustomerConstants.SPLIT_SYMBOL)));
        }
    }

    /**
     * 请求AppKey是否在允许请求范围内
     *
     * @param appKey
     * @return
     */
    private boolean checkRequestAppKey(String appKey) {
        String stressTestAllowAppKeysStr = MccCustomerConfig.getStressTestAllowRequestAppKeys();
        List<String> allowAppKeys = Arrays.asList(stressTestAllowAppKeysStr.split(","));
        if (allowAppKeys.contains(appKey)) {
            return true;
        }
        return false;
    }


    /**
     * 客户类型修改添加操作日志
     *
     * @param wmCustomerDB
     * @param newCustomerRealType
     */
    private void addCustomerRealTypeChangeLog(WmCustomerDB wmCustomerDB, Integer newCustomerRealType) {
        try {
            String opLog = String.format("客户类型修改: %s -> %s", CustomerRealTypeEnum.getByValue(wmCustomerDB.getCustomerRealType()).getName(), CustomerRealTypeEnum.getByValue(newCustomerRealType).getName());
            wmLeafCustomerService.insertCustomerOpLog(wmCustomerDB.getId(), 0, "压测系统", WmCustomerOplogBo.OpType.UPDATE, opLog);
        } catch (Exception e) {
            log.error("addCustomerRealTypeChangeLog,客户类型修改添加操作日志发生异常,customerId={},newCustomerRealType={}", wmCustomerDB.getId(), newCustomerRealType, e);
        }
    }

}

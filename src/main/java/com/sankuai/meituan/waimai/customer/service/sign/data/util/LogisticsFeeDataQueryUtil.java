package com.sankuai.meituan.waimai.customer.service.sign.data.util;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDataPoiBizBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDataSourceBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description: 签约信息查询工具类
 * @author: zhangyuanhao02
 * @create: 2025/2/11 14:50
 */
@Slf4j
public class LogisticsFeeDataQueryUtil {

    /**
     * 提取门店业务对象分组列表
     * @param wmPoiIdGroupList
     * @param allWmPoiBizBoList
     * @return
     * @throws WmCustomerException
     */
    public static List<List<EcontractDataPoiBizBo>> extractPoiBizBoGroupList(List<List<Long>> wmPoiIdGroupList, List<EcontractDataPoiBizBo> allWmPoiBizBoList) throws WmCustomerException {
        List<List<EcontractDataPoiBizBo>> poiBizBoGroupList = new ArrayList<>();

        Map<Long, EcontractDataPoiBizBo> poiId2PoiBizBoMap = allWmPoiBizBoList.stream()
                .collect(Collectors.toMap(EcontractDataPoiBizBo::getWmPoiId, Function.identity()));
        for (List<Long> wmPoiIdList : wmPoiIdGroupList) {
            List<EcontractDataPoiBizBo> poiBizBoList = new ArrayList<>();
            if (CollectionUtils.isEmpty(wmPoiIdList)) {
                throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "门店分组数据为空");
            }

            for (Long wmPoiId : wmPoiIdList) {
                EcontractDataPoiBizBo poiBizBo = poiId2PoiBizBoMap.get(wmPoiId);
                if (Objects.isNull(poiBizBo)) {
                    log.error("extractPoiBizBoGroupList 未找到门店: {}, 对应的EcontractDataPoiBizBo", wmPoiId);
                    throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "门店分组数据不合法");
                }
                poiBizBoList.add(poiBizBo);
            }
            poiBizBoGroupList.add(poiBizBoList);
        }
        log.info("extractPoiBizBoGroupList poiBizBoGroupList:{}", JSONObject.toJSONString(poiBizBoGroupList));
        return poiBizBoGroupList;
    }

    /**
     * 提取门店分组关系 key为数据来源，value为门店分组
     * @param dataSourceBoList
     * @return
     */
    public static Map<Integer, List<List<Long>>> extractGroupMap(List<EcontractDataSourceBo> dataSourceBoList) {
        Map<Integer, List<List<Long>>> wmPoiIdGroupMap = new HashMap<>();
        for (EcontractDataSourceBo dataSourceBo : dataSourceBoList) {
            wmPoiIdGroupMap.put(dataSourceBo.getSorceEnum().getType(), dataSourceBo.getWmPoiIdGroupList());
        }

        log.info("LogisticsFeeDataQueryUtil#extractGroupMap wmPoiIdGroupMap:{}",JSONObject.toJSONString(wmPoiIdGroupMap));
        return wmPoiIdGroupMap;
    }

    /**
     * 如果是单门店可能没有传分组信息
     * @param dataSourceBo
     */
    public static void assembleWmPoiIdGroupList(EcontractDataSourceBo dataSourceBo) {
        log.info("assembleWmPoiIdGroupList start, dataSourceBo:{}", JSONObject.toJSONString(dataSourceBo));
        List<EcontractDataPoiBizBo> wmPoiIdAndBizIdList = dataSourceBo.getWmPoiIdAndBizIdList();
        EcontractDataPoiBizBo econtractDataPoiBizBo = wmPoiIdAndBizIdList.get(0);
        Long wmPoiId = econtractDataPoiBizBo.getWmPoiId();

        List<List<Long>> wmPoiIdGroupList = new ArrayList<>();
        wmPoiIdGroupList.add(Lists.newArrayList(wmPoiId));

        dataSourceBo.setWmPoiIdGroupList(wmPoiIdGroupList);
        log.info("assembleWmPoiIdGroupList end, dataSourceBo:{}", JSONObject.toJSONString(dataSourceBo));
    }
}

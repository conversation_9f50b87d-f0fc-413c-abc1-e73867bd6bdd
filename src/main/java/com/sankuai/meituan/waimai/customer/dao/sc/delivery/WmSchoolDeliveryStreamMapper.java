package com.sankuai.meituan.waimai.customer.dao.sc.delivery;

import com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmSchoolDeliveryStreamDO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 学校交付流程主表信息Mapper
 * <AUTHOR>
 * @date 2024/02/15
 * @email <EMAIL>
 **/
@Component
public interface WmSchoolDeliveryStreamMapper {

    /**
     * 根据主键ID查询学校交付流信息
     * @param id 主键ID(交付编号ID)
     * @return 学校交付流信息
     */
    WmSchoolDeliveryStreamDO selectByPrimaryKey(@Param("id") Long id);

    /**
     * 根据学校主键ID查询学校交付流信息
     * @param schoolPrimaryId 学校主键ID
     * @return 学校交付流信息
     */
    List<WmSchoolDeliveryStreamDO> selectBySchoolPrimaryId(@Param("schoolPrimaryId") Integer schoolPrimaryId);

    /**
     * 根据学校主键ID查询正在流程中(尚未结束)的学校交付流信息
     * @param schoolPrimaryId 学校主键ID
     * @return 学校交付流信息
     */
    WmSchoolDeliveryStreamDO selectBySchoolPrimaryIdOnStram(@Param("schoolPrimaryId") Integer schoolPrimaryId);

    /**
     * 根据主键ID(交付编号ID)列表查询正在流程中的交付流列表
     * @param deliveryIdList 主键ID(交付编号ID)列表
     * @return 正在流程中的交付流列表
     */
    List<WmSchoolDeliveryStreamDO> selectOnStreamDeliveryByDeliveryIdList(@Param("deliveryIdList") List<Integer> deliveryIdList);

    /**
     * 新增学校交付流信息
     * @param wmSchoolDeliveryStreamDO 交付流信息
     * @return 更新行数
     */
    int insertSelective(WmSchoolDeliveryStreamDO wmSchoolDeliveryStreamDO);

    /**
     * 根据主键ID更新学校交付流信息
     * @param wmSchoolDeliveryStreamDO 学校流信息
     * @return 更新行数
     */
    int updateByPrimaryKeySelective(WmSchoolDeliveryStreamDO wmSchoolDeliveryStreamDO);

}

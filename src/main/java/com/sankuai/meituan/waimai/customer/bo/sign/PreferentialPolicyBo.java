package com.sankuai.meituan.waimai.customer.bo.sign;

import org.springframework.beans.BeanUtils;

import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryInfoBo;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class PreferentialPolicyBo {
    private String poiInfo;
    private String poiName;
    //是否支持优惠政策(support/unsupport)
    private String supportExclusive;
    //优惠费率和保底
    private String exclusiveFee;
    //服务支持类
    private String serverSupport;
    //其他类
    private String other;
    //保证金
    private String deposit;
    //保证金-只有金额
    private String depositSimple;
    //有效期
    private String validate;
    //有效期-只有日期
    private String validateSimple;
    //是否支持新费率模式(support/unsupport)
    private String supportNewModle;
    //新模式版本 "1","2".....
    private String newModelVersion;
    //技术服务费（佣金）优惠-优惠折扣系数 格式：20%
    private String exclusiveFeeDiscountFactor1;
    //履约服务费优惠-优惠折扣系数 格式：20%
    private String exclusiveFeeDiscountFactor2;
    //是否支持美团配送(support/unsupport)
    private String supportMTDelivery;
    // 费率模式
    private String feeMode;
    //优惠合作商服务费率
    private String agentNewFeeInfo;
    //优惠距离收费
    private String agentNewDistance;
    //价格收费增量
    private String agentNewPrice;

    /**
     * 是否支持自配
     */
    private String supportSelfDelivery;

    /**
     * 聚合配送-履约服务费优惠-优惠折扣系数 格式：20%
     */
    private String aggregationExclusiveFeeDiscountFactor2;

    public static PreferentialPolicyBo extract(EcontractDeliveryInfoBo econtractDeliveryInfoBo) {
        if (econtractDeliveryInfoBo == null) {
            return null;
        }
        PreferentialPolicyBo result = new PreferentialPolicyBo();
        BeanUtils.copyProperties(econtractDeliveryInfoBo, result);
        return result;
    }

}

package com.sankuai.meituan.waimai.customer.adapter;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import com.sankuai.waimai.cd.crm.waimai.dto.CommonResponse;
import com.sankuai.waimai.cd.crm.waimai.dto.LeadSystemAssignRequest;
import com.sankuai.waimai.cd.crm.waimai.thrift.WaimaiLeadRotateThriftService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 外卖公海线索分配责任人适配器
 * <AUTHOR>
 * @date 2024/05/29
 * @email <EMAIL>
 */
@Service
@Slf4j
public class WmLeadRemoteThriftServiceAdapter {

    @Autowired
    private WaimaiLeadRotateThriftService waimaiLeadRotateThriftService;

    /**
     * 系统线索分配调用场景code
     */
    public final int OPERATION_SCENE_CODE = -9;

    /**
     * 分配线索跟进人
     * @param uid 人员ID
     * @param wdcClueId 线索ID
     */
    public CommonResponse systemAssign(Integer uid, Long wdcClueId) throws WmSchCantException {
        try {
            LeadSystemAssignRequest request = new LeadSystemAssignRequest();
            request.setUid(uid);
            request.setPoiId(wdcClueId);
            request.setOperationSceneCode(OPERATION_SCENE_CODE);

            log.info("[WmLeadRemoteThriftServiceAdapter.systemAssign] request = {}", JSONObject.toJSONString(request));
            CommonResponse response = waimaiLeadRotateThriftService.systemAssign(request);
            log.info("[WmLeadRemoteThriftServiceAdapter.systemAssign] response = {}", JSONObject.toJSONString(response));

            return response;
        } catch (Exception e) {
            log.error("[WmLeadRemoteThriftServiceAdapter.systemAssign] Exception. uid = {}, wdcClueId = {}", uid, wdcClueId, e);
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "分配线索跟进人失败");
        }
    }

}

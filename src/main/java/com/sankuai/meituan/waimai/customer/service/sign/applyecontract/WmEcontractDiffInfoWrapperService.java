package com.sankuai.meituan.waimai.customer.service.sign.applyecontract;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.customer.aspect.DiffDataWrapper;
import com.sankuai.meituan.waimai.customer.aspect.SlaDataWrapper;
import com.sankuai.meituan.waimai.customer.constant.WmEcontractConstant;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper.IWmEcontractDiffInfoDataWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper.IWmEcontractSlaDataWrapperService;
import com.sankuai.meituan.waimai.econtrct.client.constants.EcontractContentTypeEnum;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageBatchInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.biz.EcontractContentBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataWrapperEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import java.util.List;
import java.util.Map;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.aop.support.AopUtils;
import org.springframework.stereotype.Service;

@Service
public class WmEcontractDiffInfoWrapperService {
    private static final Logger LOGGER = LoggerFactory.getLogger(WmEcontractDiffInfoWrapperService.class);

    @Resource
    private List<IWmEcontractDiffInfoDataWrapperService> diffInfoWrapperServiceList;

    static Map<EcontractDataWrapperEnum, IWmEcontractDiffInfoDataWrapperService> enumAndWrapperMap = Maps.newHashMap();

    @PostConstruct
    public void init() {
        for (IWmEcontractDiffInfoDataWrapperService wrapperService : diffInfoWrapperServiceList) {
            DiffDataWrapper annotation = AopUtils.getTargetClass(wrapperService).getAnnotation(DiffDataWrapper.class);
            if (annotation == null || annotation.wrapperEnum() == null) {
                continue;
            }
            enumAndWrapperMap.put(annotation.wrapperEnum(), wrapperService);
        }
    }

    public void addContext(List<StageBatchInfoBo> batchInfoBoList,
            EcontractBatchContextBo contextBo, EcontractDataWrapperEnum dataWrapperEnum)
            throws WmCustomerException, IllegalAccessException {
        List<EcontractContentBo> diffDataList = null;
        if (enumAndWrapperMap.get(dataWrapperEnum) != null) {
            diffDataList = enumAndWrapperMap.get(dataWrapperEnum).wrap(contextBo);
        }
        if (CollectionUtils.isNotEmpty(diffDataList)) {
            Map<String, String> viewMap = Maps.newHashMap();
            viewMap.put("changeInfo",
                    JSONObject.toJSONString(diffDataList));
            batchInfoBoList.add(new StageBatchInfoBo.Builder()
                    .stageName(WmEcontractConstant.JUMP_CONTENT)
                    .viewContentMap(viewMap)
                    .build());
        }
    }
}

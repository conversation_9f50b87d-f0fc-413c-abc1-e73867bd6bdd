package com.sankuai.meituan.waimai.customer.ddd.contract.domain.aggre;

import java.util.List;
import java.util.Map;

import com.meituan.waimai.agent.yak.contract.client.exception.ContractException;
import com.meituan.waimai.agent.yak.contract.client.request.common.AgentIdRequest;
import com.meituan.waimai.agent.yak.contract.client.response.contract.ContractContextResponse;
import com.meituan.waimai.agent.yak.contract.client.service.ContractThrift;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.heron.agentcontract.dto.contract.WmAgentFwContractQueryByAgentIdRequestDTO;
import com.sankuai.meituan.waimai.heron.agentcontract.dto.contract.WmAgentFwContractQueryResponseDTO;
import com.sankuai.meituan.waimai.heron.agentcontract.exception.WmAgentFwException;
import com.sankuai.meituan.waimai.heron.agentcontract.service.WmAgentFwContractThriftService;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.util.DateUtil;
import com.sankuai.meituan.waimai.contract.thrift.domain.WmAgentContract;
import com.sankuai.meituan.waimai.contract.thrift.service.WmAgentContractThriftService;
import com.sankuai.meituan.waimai.customer.adapter.WmPoiClient;
import com.sankuai.meituan.waimai.customer.bo.WmPoiDomain;
import com.sankuai.meituan.waimai.customer.contract.dao.WmTempletContractAuditedDBMapper;
import com.sankuai.meituan.waimai.customer.contract.domain.WmTempletContractDB;
import com.sankuai.meituan.waimai.customer.ddd.base.Entity;
import com.sankuai.meituan.waimai.customer.service.AutoWireBase;
import com.sankuai.meituan.waimai.thrift.customer.constant.WmTempletContractTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractTypeBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractSignBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.fullstatus.CustomerModuleStatus;
import com.sankuai.meituan.waimai.thrift.customer.domain.fullstatus.StatusEnum;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;

@Deprecated
public class WmContractOnlineAggre extends Entity<WmContractOnlineAggre.Context> {

    @Autowired
    private WmAgentFwContractThriftService wmAgentFwContractThriftService;

    @Autowired
    private ContractThrift contractThrift;

    private static Logger logger = LoggerFactory.getLogger(WmContractOnlineAggre.class);

    //客户id
    private int customerId;

    public WmContractOnlineAggre(WmContractOnlineAggre.Context _ctx) {
        super(_ctx);
    }

    public Boolean needC2ForPoiOnline(Long wmPoiId) throws WmCustomerException, TException {
        WmPoiDomain poiDomain = getWmPoiDomain(wmPoiId);
        return poiDomain.getAgentId() > 0 && !ctx().wmPoiClient.isLianSuoPoi(poiDomain);
    }

    public Boolean needC2ForPoiSwitchAgent(Long wmPoiId) throws WmCustomerException, TException {
        WmPoiDomain poiDomain = getWmPoiDomain(wmPoiId);
        return !ctx().wmPoiClient.isLianSuoPoi(poiDomain);
    }

    public Map<Long, Boolean> needC2ForPoisOnline(List<Long> wmPoiId) throws WmCustomerException, TException {
        List<WmPoiDomain> poiDomains = getWmPoiDomains(wmPoiId);
        Map<Long, Boolean> res = Maps.newHashMap();
        poiDomains.forEach(poiDomain -> {
            if (poiDomain.getAgentId() > 0) {
                res.put((long) poiDomain.getWmPoiId(), !ctx().wmPoiClient.isLianSuoPoi(poiDomain));
            } else {
                res.put((long) poiDomain.getWmPoiId(), false);
            }
        });
        return res;
    }

    /**
     * 上线检查点
     * <p>
     * <p>
     * 一、直营门店	对应客户C1合同生效	  若不满足则提示：合同未生效，不可上线
     * <p>
     * 二、代理门店
     * <p>
     * 1、如下类型商家：对应客户C1合同生效：
     * 大连锁
     * 总部商超连锁
     * 总部生鲜连锁
     * 总部鲜花连锁
     * 总部药品连锁
     * 总部母婴连锁
     * 总部美妆连锁
     * 总部服饰鞋帽连锁
     * 总部日用品连锁
     * <p>
     * 2、非上述类型的商家：对应客户C1C2合同生效
     * <p>
     * 3、代理门店，所归属的合作商的C3合同生效
     * 若以上条件不满足则提示：合同未生效，不可上线
     * <p>
     * 三、C1合同有效期校验
     * 1、C1合同有效期到期后，若门店为下线状态，则不可再上线；
     * 2、若门店为上线状态，则继续保持上线状态，不会触发下线
     * 若满足条件1则提示：合同已到期，不可上线
     */
    public CustomerModuleStatus checkContractForPoiSetup(long wmPoiId) throws WmCustomerException {
        logger.info("门店上线检查点【合同】  cusId:{}, wmPoiId:{}", customerId, wmPoiId);
        WmPoiDomain poiDomain = getWmPoiDomain(wmPoiId);
        logger.info("门店上线检查点【合同】  cusId:{}, poiDomain:{}", customerId, JSON.toJSON(poiDomain));
        if (poiDomain.getAgentId() <= 0) {
            //直营门店
            return getC1PoiModuleStatus(customerId, poiDomain);
        }
        //合作商门店
        return getAgentPoiStatus(customerId, poiDomain);
    }

    private CustomerModuleStatus getAgentPoiStatus(int customerId, WmPoiDomain poiDomain) throws WmCustomerException {
        List<WmTempletContractDB> allContract = ctx().wmTempletContractAuditedDBMapper.getBasicListByParentIdAndTypes(customerId, Lists.newArrayList(
                WmTempletContractTypeEnum.C1_E.getCode(),
                WmTempletContractTypeEnum.C1_PAPER.getCode(),
                WmTempletContractTypeEnum.C2_E.getCode(),
                WmTempletContractTypeEnum.C2_PAPER.getCode())
        );
        if (CollectionUtils.isEmpty(allContract)) {
            logger.info("合作商门店，对应客户不存在C1和C2合同，不可上线  customerId：{}  poiId:{}", customerId, poiDomain.getWmPoiId());
            return new CustomerModuleStatus(StatusEnum.IN_EFFECTIVE, "合同未生效,不可上线");
        }
        boolean hasC1 = false;
        boolean hasC2 = false;
        boolean hasC3 = hasC3(poiDomain);
        if (!hasC3) {
            logger.info(" hasC3：{}  customerId：{}  poiId:{}", hasC3, customerId, poiDomain.getWmPoiId());
            return new CustomerModuleStatus(StatusEnum.IN_EFFECTIVE, "合同未生效,不可上线");
        }
        for (WmTempletContractDB db : allContract) {
            int typeInDb = new WmTempletContractTypeBo(db.getType()).getType();
            if (typeInDb == WmTempletContractTypeBo.TYPE_C1) {
                Integer dueDate = db.getDueDate();
                if (DateUtil.unixTime() >= dueDate && poiDomain.getValid() == 0) {
                    logger.info("合作商门店，对应客户C1合同到期且门店已为下线状态，不可上线  customerId：{}  poiId:{}", customerId, poiDomain.getWmPoiId());
                    return new CustomerModuleStatus(StatusEnum.IN_EFFECTIVE, "合同已到期,不可上线");
                }
                hasC1 = true;
            }
            if (typeInDb == WmTempletContractTypeBo.TYPE_C2) {
                WmTempletContractSignBo partyBSigner = WmContractSignAggre.Factory.make(db.getId()).selectAuditedPartyBSignerBo();
                if (partyBSigner.getSignId() == poiDomain.getAgentId()) {
                    hasC2 = true;
                }
            }
        }
        logger.info("hasC1：{}  hasC2：{}  hasC3：{}  customerId：{}  poiId:{}", hasC1, hasC2, hasC3, customerId, poiDomain.getWmPoiId());
        //是否是连锁门店
        boolean lianSuoPoi = ctx().wmPoiClient.isLianSuoPoi(poiDomain);
        logger.info("是否大连锁或品牌连锁门店：{} customerId：{}  poiId:{}", lianSuoPoi, customerId, poiDomain.getWmPoiId());
        if (lianSuoPoi && hasC1 || !lianSuoPoi && hasC1 && hasC2) {
            return new CustomerModuleStatus(StatusEnum.EFFECTIVE, "");
        }
        return new CustomerModuleStatus(StatusEnum.IN_EFFECTIVE, "合同未生效,不可上线");
    }

    private boolean hasC3(WmPoiDomain poiDomain) throws WmCustomerException {
        try {
            if (ConfigUtilAdapter.getBoolean("getWmAgentContractByAgentId_replace", false)) {
                //接口迁移，迁移无异常getByWmAgentId_migrate使用默认值，false为降级值
                if(ConfigUtilAdapter.getBoolean("getByWmAgentId_migrate", true) && MccConfig.agentTransferProp()){
                    AgentIdRequest agentIdRequest = new AgentIdRequest();
                    agentIdRequest.setWmAgentId((int)poiDomain.getAgentId());
                    agentIdRequest.setEffective(1);
                    ContractContextResponse response = contractThrift.getByWmAgentId(agentIdRequest);
                    logger.info("WmContractOnlineAggre getByWmAgentId resp:{}", JSON.toJSONString(response));
                    return response != null;
                }else{
                    WmAgentFwContractQueryByAgentIdRequestDTO requestDTO = new WmAgentFwContractQueryByAgentIdRequestDTO();
                    requestDTO.setWmAgentId((int)poiDomain.getAgentId());
                    requestDTO.setEffective(1);
                    WmAgentFwContractQueryResponseDTO responseDTO = wmAgentFwContractThriftService.getByWmAgentId(requestDTO);
                    return responseDTO != null;
                }
            }
            else {
                WmAgentContract wmAgentContract = ctx().wmAgentContractThriftService
                        .getWmAgentContractByAgentId(poiDomain.getAgentId());
                return wmAgentContract != null && wmAgentContract.getValid() == 1;
            }
        } catch (WmServerException e) {
            logger.info(e.getMsg(), e);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, e.getMsg());
        } catch (TException e) {
            logger.error(e.getMessage(), e);
        } catch (WmAgentFwException e) {
            logger.info(e.getMsg(), e);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, e.getMsg());
        } catch (ContractException e) {
            logger.info(e.getMsg(), e);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, e.getMsg());
        }
        return false;
    }

    private CustomerModuleStatus getC1PoiModuleStatus(int customerId, WmPoiDomain poiDomain) {
        List<WmTempletContractDB> c1Contract = ctx().wmTempletContractAuditedDBMapper.getBasicListByParentIdAndTypes(customerId, Lists.newArrayList(
                WmTempletContractTypeEnum.C1_E.getCode(),
                WmTempletContractTypeEnum.C1_PAPER.getCode()
                )
        );
        if (!CollectionUtils.isEmpty(c1Contract)) {
            Integer dueDate = c1Contract.get(0).getDueDate();
            if (DateUtil.unixTime() >= dueDate && poiDomain.getValid() == 0) {
                logger.info("直营门店，对应客户C1合同到期且门店已为下线状态，不可上线  customerId：{}  poiId:{}", customerId, poiDomain.getWmPoiId());
                return new CustomerModuleStatus(StatusEnum.IN_EFFECTIVE, "合同已到期,不可上线");
            }
            return new CustomerModuleStatus(StatusEnum.EFFECTIVE, "");
        }
        logger.info("直营门店，对应客户不存在C1合同，不可上线  customerId：{}  poiId:{}", customerId, poiDomain.getWmPoiId());
        return new CustomerModuleStatus(StatusEnum.IN_EFFECTIVE, "合同未生效,不可上线");
    }

    private WmPoiDomain getWmPoiDomain(long wmPoiId) throws WmCustomerException {
        WmPoiDomain poiDomain = ctx().wmPoiClient.getWmPoiById(wmPoiId);
        if (poiDomain == null) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "查询门店信息为空");
        }
        return poiDomain;
    }

    private List<WmPoiDomain> getWmPoiDomains(List<Long> wmPoiIds) throws WmCustomerException {
        List<WmPoiDomain> poiDomains = ctx().wmPoiClient.getWmPoiByIds(wmPoiIds);
        if (poiDomains == null) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "查询门店信息为空");
        }
        return poiDomains;
    }

    public static class Factory implements Entity.Factory {

        private static WmContractOnlineAggre.Context context = new WmContractOnlineAggre.Context();

        public static WmContractOnlineAggre make() {
            return new WmContractOnlineAggre(context);
        }

        public static WmContractOnlineAggre makeWithCustomerId(int customerId) {
            WmContractOnlineAggre aggre = new WmContractOnlineAggre(context);
            aggre.customerId = customerId;
            return aggre;
        }

    }

    public static class Context extends AutoWireBase implements Entity.Context {

        @Autowired
        WmTempletContractAuditedDBMapper wmTempletContractAuditedDBMapper;

        @Autowired
        WmPoiClient wmPoiClient;


        @Autowired
        WmAgentContractThriftService.Iface wmAgentContractThriftService;
    }
}

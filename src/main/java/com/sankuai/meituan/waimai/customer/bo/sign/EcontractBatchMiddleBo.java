package com.sankuai.meituan.waimai.customer.bo.sign;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractSignDataFactor;

import lombok.Data;

/**
 * 签约对象组装中间数据
 */
@Data
public class EcontractBatchMiddleBo {

    private EcontractSignDataFactor signDataFactor = new EcontractSignDataFactor();
    // 模块集合 SignTemplateConstant.MODEL_XXX
    private List<String> moduleList = Lists.newArrayList();
    private List<String> tabList = Lists.newArrayList();
    // pdf模板集合
    private List<SignTemplateEnum> pdfEnumList = Lists.newArrayList();
    // 模块-pdf模板映射关系
    private Map<String, Collection<SignTemplateEnum>> tabPdfMap = Maps.newHashMap();
    // 模板-填充数据映射关系
    private Map<String, List<String>> pdfDataMap = Maps.newHashMap();

}

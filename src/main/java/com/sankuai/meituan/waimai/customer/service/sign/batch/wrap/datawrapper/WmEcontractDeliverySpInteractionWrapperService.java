package com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper;

import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.google.common.base.MoreObjects;
import com.sankuai.meituan.waimai.customer.aspect.SpInteractionWrapper;
import com.sankuai.meituan.waimai.customer.constant.WmEcontractConstant;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.ExtendDataBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataWrapperEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.H5ActionConstants;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
@SpInteractionWrapper(wrapperEnum = EcontractDataWrapperEnum.DELIVERY)
public class WmEcontractDeliverySpInteractionWrapperService implements IWmEcontractSpInteractionWrapperService {

    public static final String SUPPORT_MARK = "support";

    @Override
    public ExtendDataBo wrap(EcontractBatchContextBo contextBo) throws IllegalAccessException, WmCustomerException {
        ExtendDataBo result = null;

        EcontractTaskBo taskBo = WmEcontractContextUtil.selectByApplyType(contextBo, EcontractTaskApplyTypeEnum.POIFEE);
        EcontractDeliveryInfoBo deliveryInfoBo = null;
        try {
            deliveryInfoBo = JSON.parseObject(taskBo.getApplyContext(), EcontractDeliveryInfoBo.class);
        } catch (Exception e) {
            log.warn("数据解析异常", e);
            return result;
        }

        if (deliveryInfoBo == null || !SUPPORT_MARK.equals(deliveryInfoBo.getSupportSLA())) {
            return result;
        }

        List<String> actionLimit = deliveryInfoBo.getActionLimit();
        if (CollectionUtils.isNotEmpty(actionLimit) && actionLimit.contains(H5ActionConstants.MERCHANT_CANCEL_PERMISSION_DENY)) {
            result = new ExtendDataBo();
            result.setSignForce(true);
            result.setCancleTipText(ConfigUtilAdapter.getString("delivery_cancel_deny_tips", WmEcontractConstant.DEFAULT_DELIVERY_CANCEL_DENY_TIPS)
                    .replace("{date}", MoreObjects.firstNonNull(deliveryInfoBo.getSlaValidate(), "")));
        }
        return result;
    }
}

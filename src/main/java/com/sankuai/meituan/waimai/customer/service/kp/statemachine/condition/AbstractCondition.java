package com.sankuai.meituan.waimai.customer.service.kp.statemachine.condition;

import com.sankuai.meituan.waimai.customer.service.kp.statemachine.context.KpSignerStatusMachineContext;
import org.springframework.stereotype.Component;
import org.squirrelframework.foundation.fsm.Condition;

/**
 * <AUTHOR>
 * @date 20230821
 * @desc 签约人KP状态校验
 */
@Component
public abstract class AbstractCondition implements Condition<KpSignerStatusMachineContext> {

}

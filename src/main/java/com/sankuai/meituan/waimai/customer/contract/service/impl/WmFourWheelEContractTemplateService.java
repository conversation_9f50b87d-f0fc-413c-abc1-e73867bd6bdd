package com.sankuai.meituan.waimai.customer.contract.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.base.MoreObjects;
import com.sankuai.meituan.waimai.customer.contract.annotations.ServiceTag;
import com.sankuai.meituan.waimai.customer.contract.domain.WmContractVersionDB;
import com.sankuai.meituan.waimai.customer.contract.service.impl.check.ContractCheckFilter;
import com.sankuai.meituan.waimai.customer.ddd.contract.domain.aggre.WmContractSignAggre;
import com.sankuai.meituan.waimai.customer.util.ContractNumberUtil;
import com.sankuai.meituan.waimai.thrift.customer.constant.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractBasicBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractSignBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.*;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.UUID;

/**
 * @description: 四轮履约补充协议
 * @author: liuyunjie05
 * @create: 2023/10/24 15:04
 */
@Slf4j
@Service
@ServiceTag(templetTypes = {WmTempletContractTypeEnum.FOUR_WHEEL_PERFORMANCE_SUPPLEMENT_AGREEMENT_E})
public class WmFourWheelEContractTemplateService extends AbstractWmEContractTempletService {

    @Override
    public Integer startSign(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException, TException {
        log.info("WmFourWheelEContractTemplateService#startSign, contractBo: {}", JSON.toJSONString(contractBo));
        // 如果是发起待打包
        if (contractBo.getPackWay() == SignPackWay.WAIT_HAND_PACK.getCode()) {
            try {
                // 前置校验
                ContractCheckFilter.contractSignValidFilter().filter(contractBo, opUid, opName);
                // 获取当前数据
                WmCustomerContractBo oldBo = wmContractService
                        .getWmCustomerContractBoById(contractBo.getBasicBo().getTempletContractId(), false, opUid, opName);
                // 更新or插入
                Integer contractId = insertOrUpdate(contractBo, opUid, opName);
                // 记录变更状态
                int status = MoreObjects.firstNonNull(oldBo.getBasicBo(), new WmTempletContractBasicBo()).getStatus();
                if (status != contractBo.getBasicBo().getStatus()) {
                    contractLogService.logStatusChange(
                            MoreObjects.firstNonNull(oldBo.getBasicBo(), new WmTempletContractBasicBo()).getStatus(),
                            contractBo.getBasicBo(), opUid, opName);
                }
                contractLogService.logUpdate(oldBo, contractBo, opUid, opName);
                // 正式发起待签约任务
                toNextContractStatus(contractId, CustomerContractStatus.WAITING_SIGN.getCode(), opName);
                contractBo.getBasicBo().setStatus(CustomerContractStatus.WAITING_SIGN.getCode());
                contractLogService.logStatusChange(MoreObjects.firstNonNull(oldBo.getBasicBo(), new WmTempletContractBasicBo()).getStatus(),
                        contractBo.getBasicBo(), opUid, opName);
                ManualTaskApplyBo manualTaskApplyBo = buildManualTaskApplyBo(contractBo.getBasicBo().getParentId(), contractId, opUid);
                wmEcontractSignBzService.applyManualTask(manualTaskApplyBo);
                return contractId;
            } catch (WmCustomerException ec) {
                log.error("WmFourWheelEContractTemplateService#startSign, WmCustomerException, 发起待打包合同任务失败 contractBo: {}, msg: {}", JSON.toJSONString(contractBo), ec.getMsg());
                throw ec;
            } catch (Exception e) {
                log.error("WmFourWheelEContractTemplateService#startSign, Exception, contractBo: {}", JSON.toJSONString(contractBo), e);
                throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "发起待签约合同异常");
            }
        } else {
            return super.startSign(contractBo, opUid, opName);
        }
    }

    private ManualTaskApplyBo buildManualTaskApplyBo(int wmCustomerId, long contractId, int opUid) {
        return new ManualTaskApplyBo.Builder()
                .commitUid(opUid)
                .customerId(wmCustomerId)
                .applyTypeEnum(EcontractTaskApplyTypeEnum.FOUR_WHEEL_PERFORMANCE_SUPPLEMENT_AGREEMENT)
                .bizId(contractId)
                .build();
    }

    @Override
    EcontractTaskApplyBo buildEcontractTaskApplyBo(WmCustomerContractBo contractBo, WmContractVersionDB versionDB) throws WmCustomerException {
        EcontractTaskApplyBo applyBo = new EcontractTaskApplyBo();
        applyBo.setRecordId(versionDB.getVersion_number());
        applyBo.setBizId(String.valueOf(contractBo.getBasicBo().getParentId()));
        applyBo.setBizTypeEnum(EcontractTaskApplyBizTypeEnum.CUSTOMER_ID);
        applyBo.setApplyTypeEnum(EcontractTaskApplyTypeEnum.FOUR_WHEEL_PERFORMANCE_SUPPLEMENT_AGREEMENT);

        EcontractFourWheelInfoBo econtractFourWheelInfoBo = new EcontractFourWheelInfoBo();
        WmTempletContractSignBo partyASignerBo = WmContractSignAggre.Factory.makeWithSignBo(contractBo.getSignBoList()).getPartyASignerBo();
        econtractFourWheelInfoBo.setPartAStampName(partyASignerBo.getSignName());
        econtractFourWheelInfoBo.setContractNum(contractBo.getBasicBo().getContractNum());
        applyBo.setConfigBo(new EcontractTaskConfigBo());
        applyBo.setApplyInfoBo(JSON.toJSONString(econtractFourWheelInfoBo));
        log.info("WmFourWheelEContractTemplateService#buildEcontractTaskApplyBo: {}", JSON.toJSONString(applyBo));
        return applyBo;
    }

    @Override
    public boolean invalid(int contractId, int opUid, String opUname) throws WmCustomerException, TException {
        log.info("WmFourWheelEContractTemplateService#invalid, contractId: {}, opUid: {}, opUname: {}", contractId, opUid, opUname);
        WmCustomerContractBo wmCustomerContractBo = new WmCustomerContractBo();
        wmCustomerContractBo.setBasicBo(new WmTempletContractBasicBo());
        wmCustomerContractBo.getBasicBo().setTempletContractId(contractId);
        // 合同废除校验
        ContractCheckFilter.fourWheelPerfInvalidFilter().filter(wmCustomerContractBo, opUid, opUname);
        return super.invalid(contractId, opUid, opUname);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer save(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException, TException {
        contractBo.getBasicBo().setContractNum(UUID.randomUUID().toString());
        Integer insertId = super.save(contractBo, opUid, opName);
        String fourWheelPerformanceAgreementENum = ContractNumberUtil.genFourWheelPerformanceAgreementENum(insertId);
        contractBo.getBasicBo().setContractNum(fourWheelPerformanceAgreementENum);
        wmTempletContractDBMapper.updateNumberById(insertId.longValue(), fourWheelPerformanceAgreementENum);
        log.info("WmFourWheelEContractTemplateService#save, contractId: {}, contractNumber: {}", insertId, fourWheelPerformanceAgreementENum);
        return insertId;
    }

}

package com.sankuai.meituan.waimai.customer.service.sign.batchOp;

import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Executors;
import javax.annotation.Nullable;

import com.alibaba.fastjson.JSON;

import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.google.common.base.Preconditions;
import com.google.common.util.concurrent.*;
import org.springframework.util.Assert;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.InitializingBean;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchOpRequest;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchOpResponse;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchOpFailTaskDTO;


/**
 * @description: 批量操作相关逻辑
 * @author: lixuepeng
 * @create: 2021-05-24
 **/
@Service
public class WmEcontractSignBatchOpInvoker implements InitializingBean {
    private static final Logger LOGGER = LoggerFactory.getLogger(WmEcontractSignBatchOpInvoker.class);

    private static ListeningExecutorService taskQueryExecutorService;

    private static final ListeningExecutorService batchResendMsgexecutorService;

    private static final ListeningExecutorService batchCancelexecutorService;

    private static final ListeningExecutorService batchManualPackexecutorService;

    static {
        taskQueryExecutorService = MoreExecutors.listeningDecorator(Executors.newFixedThreadPool(MccConfig.taskQueryThreadPoolSize()));
        batchResendMsgexecutorService = MoreExecutors.listeningDecorator(Executors.newFixedThreadPool(MccConfig.taskExecutorThreadPoolSize()));
        batchCancelexecutorService = MoreExecutors.listeningDecorator(Executors.newFixedThreadPool(MccConfig.taskExecutorThreadPoolSize()));
        batchManualPackexecutorService = MoreExecutors.listeningDecorator(Executors.newFixedThreadPool(MccConfig.taskExecutorThreadPoolSize()));
    }

    Map<String, WmEcontractSignBatchOpHandler> opHandlerMap = new HashMap<>();

    @Autowired
    List<WmEcontractSignBatchOpHandler> wmEcontractSignBatchOpHandlerList;

    @Override
    public void afterPropertiesSet() throws Exception {
        Preconditions.checkNotNull(wmEcontractSignBatchOpHandlerList);
        for (WmEcontractSignBatchOpHandler opHandler : wmEcontractSignBatchOpHandlerList) {
            String batchIdType = opHandler.getBatchIdType();
            Assert.hasText(batchIdType, "WmEcontractSignBatchOpInvoker: " + opHandler + " batchIdType is empty!");
            LOGGER.info("WmEcontractSignBatchOpInvoker opHandler:{}  batchIdType:{}", opHandler, batchIdType);
            opHandlerMap.put(batchIdType, opHandler);
        }
    }

    public Map<String, List<Long>> queryItemMap(EcontractBatchOpRequest request) throws InterruptedException {
        LOGGER.info("WmEcontractSignBatchOpInvoker.queryItemMap request:{}", JSON.toJSONString(request));
        Map<String, List<Long>> resultMap = new HashMap<>();
        CountDownLatch latch = new CountDownLatch(opHandlerMap.keySet().size());
        for (Map.Entry<String, WmEcontractSignBatchOpHandler> entry : opHandlerMap.entrySet()) {
            ListenableFuture batchItemFuture = taskQueryExecutorService.submit(new Callable() {
                @Override
                public Object call() throws Exception {
                    return entry.getValue().getItemListByOpRequest(request);
                }
            });
            Futures.addCallback(batchItemFuture, new FutureCallback() {
                @Override
                public void onSuccess(@Nullable Object result) {
                    List<Long> ids = (List<Long>) result;
                    resultMap.put(entry.getKey(), ids);
                    latch.countDown();
                }

                @Override
                public void onFailure(Throwable t) {
                    LOGGER.error("queryItemList error request:{}, batchIdType:{} ", JSON.toJSONString(request), entry.getKey(), t);
                    resultMap.put(entry.getKey(), new ArrayList<>());
                    latch.countDown();
                }
            });
        }
        latch.await();
        LOGGER.info("WmEcontractSignBatchOpInvoker.queryItemMap resultMap:{}", JSON.toJSONString(resultMap));
        return resultMap;
    }

    public EcontractBatchOpResponse doHandleResendMsg(EcontractBatchOpRequest request, Map<String, List<Long>> resultMap) throws InterruptedException {
        LOGGER.info("doHandleResendMsg request:{}, resultMap:{}", JSON.toJSONString(request), JSON.toJSONString(resultMap));
        EcontractBatchOpResponse response = new EcontractBatchOpResponse();
        int totalCnt = 0;
        int successCnt = 0;
        int failCnt = 0;
        List<EcontractBatchOpFailTaskDTO> finalTaskDTOList = new ArrayList<>();
        for (Map.Entry<String, List<Long>> entry : resultMap.entrySet()) {
            totalCnt += entry.getValue().size();
            List<EcontractBatchOpFailTaskDTO> perFailTaskDTO = opHandlerMap.get(entry.getKey())
                    .handleResendMsg(request, batchResendMsgexecutorService, entry.getValue());
            failCnt += perFailTaskDTO.size();
            finalTaskDTOList.addAll(perFailTaskDTO);
        }
        successCnt = totalCnt - failCnt;
        response.setTotalTaskNum(totalCnt);
        response.setSuccessTaskNum(successCnt);
        response.setFailTaskNum(failCnt);
        response.setFailTaskDTOs(finalTaskDTOList);
        response.setCode(failCnt > 0 ? 1 : 0);
        return response;
    }

    public EcontractBatchOpResponse doHandleCancel(EcontractBatchOpRequest request, Map<String, List<Long>> resultMap) throws InterruptedException {
        LOGGER.info("doHandleCancel request:{}, resultMap:{}", JSON.toJSONString(request), JSON.toJSONString(resultMap));
        EcontractBatchOpResponse response = new EcontractBatchOpResponse();
        int totalCnt = 0;
        int successCnt = 0;
        int failCnt = 0;
        List<EcontractBatchOpFailTaskDTO> finalTaskDTOList = new ArrayList<>();
        for (Map.Entry<String, List<Long>> entry : resultMap.entrySet()) {
            totalCnt += entry.getValue().size();
            List<EcontractBatchOpFailTaskDTO> perFailTaskDTO = opHandlerMap.get(entry.getKey())
                    .handleCancel(request, batchCancelexecutorService, entry.getValue());
            failCnt += perFailTaskDTO.size();
            finalTaskDTOList.addAll(perFailTaskDTO);
        }
        successCnt = totalCnt - failCnt;
        response.setTotalTaskNum(totalCnt);
        response.setSuccessTaskNum(successCnt);
        response.setFailTaskNum(failCnt);
        response.setFailTaskDTOs(finalTaskDTOList);
        response.setCode(failCnt > 0 ? 1 : 0);
        return response;
    }

    public EcontractBatchOpResponse doHandleManualPack(EcontractBatchOpRequest request, Map<String, List<Long>> resultMap) throws WmCustomerException, TException {
        LOGGER.info("doHandleManualBatch request:{}, resultMap:{}", JSON.toJSONString(request), JSON.toJSONString(resultMap));
        EcontractBatchOpResponse response = new EcontractBatchOpResponse();
        int totalCnt = 0;
        int successCnt = 0;
        int failCnt = 0;
        List<EcontractBatchOpFailTaskDTO> finalTaskDTOList = new ArrayList<>();
        for (Map.Entry<String, List<Long>> entry : resultMap.entrySet()) {
            totalCnt += entry.getValue().size();
            EcontractBatchOpResponse subResponse = opHandlerMap.get(entry.getKey())
                    .handleManualPack(request, batchManualPackexecutorService, entry.getValue());
            failCnt += subResponse.getFailTaskDTOs().size();
            finalTaskDTOList.addAll(subResponse.getFailTaskDTOs());
            if (subResponse.getManualPackId() != null) {
                response.setManualPackId(subResponse.getManualPackId());
            }
        }
        successCnt = totalCnt - failCnt;
        response.setTotalTaskNum(totalCnt);
        response.setSuccessTaskNum(successCnt);
        response.setFailTaskNum(failCnt);
        response.setFailTaskDTOs(finalTaskDTOList);
        assemblyManualPackFailCode(response);
        return response;
    }

    public EcontractBatchOpResponse doHandlePreManualTask(EcontractBatchOpRequest request) throws WmCustomerException, TException {
        LOGGER.info("doHandlePreManualTask request:{}", JSON.toJSONString(request));
        return opHandlerMap.get(WmEcontractSignBatchOpNoContractTaskHandler.NO_CONTRACT_TASK_TYPE).handleCreatePreManualTask(request);
    }

    private void assemblyManualPackFailCode(EcontractBatchOpResponse response) {
        if (response.getTotalTaskNum() == 0) {//查询到数量为空
            response.setCode(WmContractErrorCodeConstant.BUSINESS_ERROR);
            response.setFailMsg("根据查询条件未在该客户下查询到待签约的任务。");
        } else {
            response.setCode(response.getFailTaskNum() > 0 ? 1 : 0);
        }
    }

    public EcontractBatchOpResponse doHandleStartSignTask(EcontractBatchOpRequest request) throws WmCustomerException, TException {
        LOGGER.info("doHandleStartSignTask request:{}", JSON.toJSONString(request));
        return opHandlerMap.get(WmEcontractSignBatchOpNoContractTaskHandler.NO_CONTRACT_TASK_TYPE).handleCreateStartSignTask(request);
    }

}

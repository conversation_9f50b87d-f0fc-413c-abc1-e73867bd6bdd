package com.sankuai.meituan.waimai.customer.service.fullstatus;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.domain.fullstatus.CustomerFullStatus;
import com.sankuai.meituan.waimai.thrift.customer.domain.fullstatus.CustomerModule;
import com.sankuai.meituan.waimai.thrift.customer.domain.fullstatus.CustomerModuleStatus;
import com.sankuai.meituan.waimai.thrift.customer.domain.fullstatus.StatusEnum;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 获取客户所有子模块全状态
 * <AUTHOR>
 */
@Service
public class WmCustomerFullStatusService {

    private static final Logger LOGGER = LoggerFactory.getLogger(WmCustomerFullStatusService.class);

    @Autowired
    private List<WmCustomerModuleStatusSevice> wmCustomerMudelStatusSeviceList;

    private final Map<CustomerModule, WmCustomerModuleStatusSevice> wmCustomerModuleStatusServiceMap = Maps.newHashMap();

    @Autowired
    private WmCustomerService wmCustomerService;

    @PostConstruct
    public void init() {
        for (WmCustomerModuleStatusSevice wmCustomerModuleStatusSevice : wmCustomerMudelStatusSeviceList) {
            CustomerModule module = wmCustomerModuleStatusSevice.getModule();
            if (wmCustomerModuleStatusServiceMap.containsKey(module)) {
                throw new RuntimeException("defined duplicate module " + module.name() + " in class:" + wmCustomerModuleStatusSevice.getClass());
            }
            wmCustomerModuleStatusServiceMap.put(module, wmCustomerModuleStatusSevice);
        }
        LOGGER.info("WmCustomerFullStatusService init finish wmCustomerMudelStatusServiceMap={}", wmCustomerModuleStatusServiceMap);
    }

    public CustomerFullStatus getCustomerOnlineCheckList(long wmPoiId, CustomerModule CustomerModulenum) throws WmCustomerException {
        WmCustomerDB wmCustomerDB = wmCustomerService.selectCustomerByWmPoiId(wmPoiId);
        Set<CustomerModule> modules =getModulesNew(CustomerModulenum);

        CustomerFullStatus fullStatus = new CustomerFullStatus();
        Map<CustomerModule, CustomerModuleStatus> fullStatusMap = new HashMap<>();
        fullStatus.setModuleStatus(fullStatusMap);
        StatusEnum effective = StatusEnum.EFFECTIVE;
        for (CustomerModule module : modules) {
            if(wmCustomerDB==null){
                effective = StatusEnum.IN_EFFECTIVE;
                fullStatusMap.put(module, new CustomerModuleStatus(StatusEnum.IN_EFFECTIVE,"门店未绑定客户"));
                break;
            }
            WmCustomerModuleStatusSevice moduleStatusService = wmCustomerModuleStatusServiceMap.get(module);
            if (moduleStatusService == null) {
                LOGGER.warn("未找到module为" + module.name() + "的实现类");
                continue;
            }
            if (!moduleStatusService.on()) {
                LOGGER.info(moduleStatusService.getClass() + "开关为关闭状态");
                continue;
            }
            CustomerModuleStatus status = moduleStatusService.getStatus(wmCustomerDB.getId(), wmPoiId);
            fullStatusMap.put(module, status);
            if (status.getStatusEnum().getStatus()==StatusEnum.IN_EFFECTIVE.getStatus()) {
                //有任意子模块未生效，客户模块总状态即为未生效
                effective = StatusEnum.IN_EFFECTIVE;
            }
        }
        fullStatus.setFullStatus(effective);
        LOGGER.info("客户上线检查点fullStatus={}", JSONObject.toJSONString(fullStatus));
        return fullStatus;
    }

    private Set<CustomerModule> getModulesNew(CustomerModule customerModulenum)  throws WmCustomerException{
        if (CustomerModule.SETTLE.equals(customerModulenum)) {
            //客户不在提供结算信息检查点
            LOGGER.warn("结算模块校验：客户上线检查点不在提供结算模块检查");
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "客户上线检查点不在提供结算模块检查");
        }
        else if (CustomerModule.ALL.equals(customerModulenum)) {
            //客户不在提供结算信息检查点
            LOGGER.info("全模块校验：客户上线检查点不在提供结算模块检查");
            return Sets.newHashSet(CustomerModule.CUSTOMER_BASE, CustomerModule.CONTRACT);
        }else {
            return Sets.newHashSet(customerModulenum);
        }
    }
}

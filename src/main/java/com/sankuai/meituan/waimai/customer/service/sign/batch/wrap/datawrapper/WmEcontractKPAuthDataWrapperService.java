package com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper;

import com.google.common.collect.Maps;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.ParamInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractKPAuthInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * kp换绑数据拼装
 */

@Service
public class WmEcontractKPAuthDataWrapperService implements IWmEcontractDataParamWrapperService {

    private static final String TEMPLET_NAME = "replace_kp_auth";

    @Override
    public ParamInfoBo wrap(EcontractBatchContextBo contextBo) throws WmCustomerException {
        EcontractTaskBo taskBo = WmEcontractContextUtil.selectByApplyType(contextBo, EcontractTaskApplyTypeEnum.KP);

        return new ParamInfoBo.Builder()
            .metaContent(generate(contextBo, taskBo))
            .templateName(TEMPLET_NAME)
            .build();
    }

    private Map<String, String> generate(EcontractBatchContextBo contextBo, EcontractTaskBo taskBo) {
        EcontractKPAuthInfoBo kpAuthInfoBo = JSON.parseObject(taskBo.getApplyContext(), EcontractKPAuthInfoBo.class);
        Map<String, String> paramMap = Maps.newHashMap();
        paramMap.put("signerName", StringUtils.defaultIfEmpty(kpAuthInfoBo.getSignerName(), StringUtils.EMPTY));
        paramMap.put("signerPhone", StringUtils.defaultIfEmpty(kpAuthInfoBo.getSignerPhone(), StringUtils.EMPTY));
        paramMap.put("poiNames", StringUtils.defaultIfEmpty(kpAuthInfoBo.getPoiNames(), StringUtils.EMPTY));
        return paramMap;
    }


}

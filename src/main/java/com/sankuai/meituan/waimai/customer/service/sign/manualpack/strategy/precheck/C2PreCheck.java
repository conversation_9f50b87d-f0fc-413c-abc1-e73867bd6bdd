package com.sankuai.meituan.waimai.customer.service.sign.manualpack.strategy.precheck;

import com.sankuai.meituan.waimai.customer.contract.service.impl.WmContractService;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignManualTaskDB;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Author: 窦旭蒙
 * MIS: douxumeng
 * Date: 2022-03-11 11:58
 * Email: <EMAIL>
 * Desc:
 */
@Slf4j
@Service
public class C2PreCheck implements PreCheck {

    @Autowired
    private WmContractService wmContractService;

    @Override
    public void preCheck(String module, List<WmEcontractSignManualTaskDB> taskInfos, int commitUid) throws WmCustomerException, TException {
        List<Long> bizIdList = taskInfos.stream().map(WmEcontractSignManualTaskDB::getWmPoiId).collect(Collectors.toList());
        for (Long bizId : bizIdList) {
            wmContractService.startSignPreCheck(taskInfos.get(0).getCustomerId(), bizId, commitUid, "");
        }
    }
}

package com.sankuai.meituan.waimai.customer.dao.sc.canteenstall;

import com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallAuditTaskNodeDO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 食堂档口审批任务子表Mapper
 * <AUTHOR>
 * @date 2024/05/13
 * @email <EMAIL>
 **/
@Component
public interface WmCanteenStallAuditTaskNodeMapper {

    /**
     * 根据主键ID查询食堂档口审批任务子表信息
     * @param id 主键ID
     * @return 食堂档口审批任务主表信息
     */
    WmCanteenStallAuditTaskNodeDO selectByPrimaryKey(@Param("id") Integer id);

    /**
     * 根据审批任务ID查询食堂档口审批任务子任务信息
     * @param auditTaskId 审批任务ID
     * @return 食堂档口审批任务子任务信息
     */
    List<WmCanteenStallAuditTaskNodeDO> selectByAuditTaskId(@Param("auditTaskId") Integer auditTaskId);

    /**
     * 根据审批任务ID查询最新的子任务信息
     * @param auditTaskId 审批任务ID
     * @return 食堂档口审批任务子任务信息
     */
    WmCanteenStallAuditTaskNodeDO selectLatestTaskNodeByAuditTaskId(@Param("auditTaskId") Integer auditTaskId);

    /**
     * 新增食堂档口审批任务子表信息
     * @param wmCanteenStallAuditTaskNodeDO 食堂档口审批任务子表信息
     * @return 更新行数
     */
    int insertSelective(WmCanteenStallAuditTaskNodeDO wmCanteenStallAuditTaskNodeDO);

    /**
     * 根据主键ID更新食堂档口审批任务子表信息
     * @param wmCanteenStallAuditTaskNodeDO 食堂档口审批任务子表信息
     * @return 更新行数
     */
    int updateByPrimaryKeySelective(WmCanteenStallAuditTaskNodeDO wmCanteenStallAuditTaskNodeDO);

    /**
     * 通过任务系统ID查询交付审批子表
     * @param auditSystemId 任务系统ID
     * @return 学校交付审批子任务信息
     */
    WmCanteenStallAuditTaskNodeDO selectByAuditSystemIdAndAuditSystemType(@Param("auditSystemId") String auditSystemId,
                                                                          @Param("auditSystemType") Integer auditSystemType);



}

package com.sankuai.meituan.waimai.customer.domain;

import com.meituan.servicecatalog.api.annotations.FieldDoc;

/**
 * <AUTHOR>
 */
public class CustomerRealTypeSpInfoBoDiff {
    /**
     * 美食城名称
     */
    private String foodCityName;

    /**
     * 美食城图片(一张)
     */
    private String foodCityPic;

    /**
     * 蜂窝业务线 0 未选择 1 直营 2 代理
     */
    private int foodCityAorBiz;

    /**
     * 蜂窝ID
     */
    private int foodCityAorId;

    /**
     * 美食城蜂窝名称
     */
    private String foodCityAorName;

    /**
     * 美食城蜂窝所在城市
     */
    private String foodCityLocation;

    /**
     * 美食城视频
     */
    private String foodCityVideo;

    /**
     * 美食城档口数
     */
    private Integer foodCityPoiCount;

    public String getFoodCityName() {
        return foodCityName;
    }

    public void setFoodCityName(String foodCityName) {
        this.foodCityName = foodCityName;
    }

    public String getFoodCityPic() {
        return foodCityPic;
    }

    public void setFoodCityPic(String foodCityPic) {
        this.foodCityPic = foodCityPic;
    }

    public int getFoodCityAorBiz() {
        return foodCityAorBiz;
    }

    public void setFoodCityAorBiz(int foodCityAorBiz) {
        this.foodCityAorBiz = foodCityAorBiz;
    }

    public int getFoodCityAorId() {
        return foodCityAorId;
    }

    public void setFoodCityAorId(int foodCityAorId) {
        this.foodCityAorId = foodCityAorId;
    }

    public String getFoodCityAorName() {
        return foodCityAorName;
    }

    public void setFoodCityAorName(String foodCityAorName) {
        this.foodCityAorName = foodCityAorName;
    }

    public String getFoodCityLocation() {
        return foodCityLocation;
    }

    public void setFoodCityLocation(String foodCityLocation) {
        this.foodCityLocation = foodCityLocation;
    }

    public String getFoodCityVideo() {
        return foodCityVideo;
    }

    public void setFoodCityVideo(String foodCityVideo) {
        this.foodCityVideo = foodCityVideo;
    }

    public Integer getFoodCityPoiCount() {
        return foodCityPoiCount;
    }

    public void setFoodCityPoiCount(Integer foodCityPoiCount) {
        this.foodCityPoiCount = foodCityPoiCount;
    }
}

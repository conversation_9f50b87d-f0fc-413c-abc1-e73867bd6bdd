package com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import com.sankuai.meituan.waimai.customer.aspect.DataWrapper;
import com.sankuai.meituan.waimai.customer.constant.WmEcontractConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.ParamInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageBatchInfoBo;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataWrapperEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.aop.support.AopUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;

@Service
public class WmEcontractDateWrapperService {

    private static Logger logger = LoggerFactory.getLogger(WmEcontractDateWrapperService.class);

    @Resource
    private List<IWmEcontractDataWrapperService> dataWrapperServiceList;

    @Resource
    private WmCommonConfigContractWrapperService wmCommonConfigContractWrapperService;

    static Map<EcontractDataWrapperEnum, IWmEcontractDataWrapperService> enumAndWrapperMap = Maps.newHashMap();

    static Map<EcontractDataWrapperEnum, IWmEcontractDataParamWrapperService> enumAndParamWrapperMap = Maps.newHashMap();

    @PostConstruct
    public void init() {
        for (IWmEcontractDataWrapperService wrapperService : dataWrapperServiceList) {
            DataWrapper annotation = AopUtils.getTargetClass(wrapperService).getAnnotation(DataWrapper.class);
            if (annotation == null || annotation.wrapperEnum() == null) {
                continue;
            }
            enumAndWrapperMap.put(annotation.wrapperEnum(), wrapperService);
        }
    }

    /**
     * 生成pdf或参数列表数据
     * @param contextBo batch上下文
     * @param enumMap 生成数据
     * @return StageBatchInfoBo
     */
    public StageBatchInfoBo wrap(EcontractBatchContextBo contextBo, Map<String, EcontractDataWrapperEnum> enumMap)
        throws WmCustomerException, IllegalAccessException {
        Map<String, List<PdfContentInfoBo>> pdfContentInfoBoMap = Maps.newHashMap();
        Map<String, ParamInfoBo> paramInfoBoMap = Maps.newHashMap();

        for (Map.Entry<String, EcontractDataWrapperEnum> entry:enumMap.entrySet()) {
            if (enumAndWrapperMap.get(entry.getValue()) != null) {
                List<PdfContentInfoBo> pdfInfoList = enumAndWrapperMap.get(entry.getValue()).wrap(contextBo);
                pdfContentInfoBoMap.put(entry.getKey(), pdfInfoList);
            }
        }

        return new StageBatchInfoBo.Builder()
            .stageName(WmEcontractConstant.CREATE_PDF)
            .pdfContentInfoBoMap(pdfContentInfoBoMap)
            .paramInfoBoMap(paramInfoBoMap)
            .metaFlowList(Lists.newArrayList(enumMap.keySet()))
            .build();
    }

    public StageBatchInfoBo wrapConfigContract(EcontractBatchContextBo contextBo, String metaFlow) throws WmCustomerException, IllegalAccessException {
        Map<String, List<PdfContentInfoBo>> pdfContentInfoBoMap = Maps.newHashMap();
        List<PdfContentInfoBo> pdfInfoList = wmCommonConfigContractWrapperService.wrap(contextBo);
        pdfContentInfoBoMap.put(metaFlow, pdfInfoList);

        return new StageBatchInfoBo.Builder()
                .stageName(WmEcontractConstant.CREATE_PDF)
                .pdfContentInfoBoMap(pdfContentInfoBoMap)
                .paramInfoBoMap(Maps.newHashMap())
                .metaFlowList(Lists.newArrayList(metaFlow))
                .build();
    }

    /**
     * 生成pdf或参数列表数据（使用一种模版，生成多个合同）
     * @param contextBo
     * @param flowList
     * @param wrapperEnum
     * @return
     * @throws WmCustomerException
     * @throws IllegalAccessException
     */
    public StageBatchInfoBo wrapMultiContractWithSingleTemplate(EcontractBatchContextBo contextBo, List<String> flowList,
                                                                EcontractDataWrapperEnum wrapperEnum)
            throws WmCustomerException, IllegalAccessException {
        logger.info("wrapMultiContractWithSingleTemplate, flowList:{}", JSONObject.toJSONString(flowList));
        if (enumAndWrapperMap.get(wrapperEnum) == null) {
            logger.error("未找到包装类: wrapperEnum{}", JSONObject.toJSONString(wrapperEnum));
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "未找到包装类");
        }
        Map<String, ParamInfoBo> paramInfoBoMap = Maps.newHashMap();
        Map<String, List<PdfContentInfoBo>> pdfContentInfoBoMap = Maps.newHashMap();
        List<PdfContentInfoBo> pdfInfoList = enumAndWrapperMap.get(wrapperEnum).wrap(contextBo);

        if (flowList.size() != pdfInfoList.size()) {
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "流程列表和pdf列表数量不一致");
        }

        for (int i = 0; i < pdfInfoList.size(); i++) {
             String key = flowList.get(i);
             pdfContentInfoBoMap.put(key, Lists.newArrayList(pdfInfoList.get(i)));
        }

        return new StageBatchInfoBo.Builder()
                .stageName(WmEcontractConstant.CREATE_PDF)
                .pdfContentInfoBoMap(pdfContentInfoBoMap)
                .paramInfoBoMap(paramInfoBoMap)
                .metaFlowList(flowList)
                .build();
    }

    /**
     * 多模版创建合同（一种模版多个合同）
     * @param contextBo
     * @param enum2flowList
     * @return
     * @throws WmCustomerException
     * @throws IllegalAccessException
     */
    public StageBatchInfoBo wrapMultiContractWithGroup(EcontractBatchContextBo contextBo, Map<EcontractDataWrapperEnum, List<String>> enum2flowList)
            throws WmCustomerException, IllegalAccessException {
        logger.info("wrapMultiContractWithGroup, enum2flowList:{}", JSONObject.toJSONString(enum2flowList));
        Map<String, List<PdfContentInfoBo>> pdfContentInfoBoMap = Maps.newHashMap();
        Map<String, ParamInfoBo> paramInfoBoMap = Maps.newHashMap();
        List<String> flowList = Lists.newArrayList();

        Set<Map.Entry<EcontractDataWrapperEnum, List<String>>> entries = enum2flowList.entrySet();
        for (Map.Entry<EcontractDataWrapperEnum, List<String>> entry : entries) {
            EcontractDataWrapperEnum wrapperEnum = entry.getKey();
            List<String> currentFlowList = entry.getValue();
            if (enumAndWrapperMap.get(wrapperEnum) != null) {
                // List中一个pdf一个对象
                List<PdfContentInfoBo> pdfInfoList = enumAndWrapperMap.get(wrapperEnum).wrap(contextBo);
                fillPdfContentInfoBoMap(pdfContentInfoBoMap, currentFlowList, pdfInfoList);
                flowList.addAll(currentFlowList);
            }
        }

        return new StageBatchInfoBo.Builder()
                .stageName(WmEcontractConstant.CREATE_PDF)
                .pdfContentInfoBoMap(pdfContentInfoBoMap)
                .paramInfoBoMap(paramInfoBoMap)
                .metaFlowList(flowList)
                .build();
    }

    /**
     * flowlist需要与pdfInfoList一一对应
     * @param pdfContentInfoBoMap
     * @param flowList
     * @param pdfInfoList
     * @throws WmCustomerException
     */
    private void fillPdfContentInfoBoMap(Map<String, List<PdfContentInfoBo>> pdfContentInfoBoMap, List<String> flowList, List<PdfContentInfoBo> pdfInfoList)
            throws WmCustomerException {
        if (flowList.size() != pdfInfoList.size()) {
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR,"流程列表和pdf列表数量不一致");
        }

        for (int i = 0; i < flowList.size(); i++) {
            String key = flowList.get(i);
            pdfContentInfoBoMap.put(key, Lists.newArrayList(pdfInfoList.get(i)));
        }
    }
}

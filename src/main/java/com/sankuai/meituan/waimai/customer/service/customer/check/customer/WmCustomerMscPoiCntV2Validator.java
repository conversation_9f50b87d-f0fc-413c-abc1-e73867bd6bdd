package com.sankuai.meituan.waimai.customer.service.customer.check.customer;

import com.sankuai.meituan.waimai.customer.adapter.WmAorServiceAdapter;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerMSCLabelService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.customer.service.customer.check.BaseWmCustomerValidator;
import com.sankuai.meituan.waimai.customer.service.customer.check.IWmCustomerValidator;
import com.sankuai.meituan.waimai.customer.service.customer.dto.CustomerMscUsedPoiDetailDTO;
import com.sankuai.meituan.waimai.customer.service.gray.WmCustomerGrayService;
import com.sankuai.meituan.waimai.infra.domain.WmUniAor;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerRealTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerSource;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.CustomerRealTypeSpInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.ValidateResultBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerBasicBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * MSC客户下门店数量校验V2版本，全量之后可以弃用WmCustomerMscPoiCntValidator
 */
@Service
public class WmCustomerMscPoiCntV2Validator extends BaseWmCustomerValidator implements IWmCustomerValidator {

    @Autowired
    private WmCustomerGrayService wmCustomerGrayService;

    @Autowired
    private WmCustomerService wmCustomerService;

    @Autowired
    private WmCustomerMSCLabelService wmCustomerMSCLabelService;

    @Autowired
    private WmAorServiceAdapter wmAorServiceAdapter;


    @Override
    public ValidateResultBo valid(ValidateResultBo validateResultBo, WmCustomerBasicBo wmCustomerBasicBo, Boolean force, Boolean isAudit, Integer opUid) throws WmCustomerException {
        //判断是否命中客户责任人组织架构灰度
        //判断是否命中客户责任人组织架构灰度
        Integer ownerUid = opUid;
        if(wmCustomerBasicBo.getId() > 0){
            WmCustomerDB wmCustomerDB = getExistWmCustomer(wmCustomerBasicBo.getId());
            ownerUid = wmCustomerDB.getOwnerUid();
        }
        if (!wmCustomerGrayService.isGrayMscPoiCntCheckNew(ownerUid)) {
            return checkPass(validateResultBo);
        }
        if (wmCustomerBasicBo.getCustomerRealType() != CustomerRealTypeEnum.MEISHICHENG.getValue()) {
            return checkPass(validateResultBo);
        }
        if (wmCustomerBasicBo.getId() == 0){
            // 新增客户的场景下，不需要校验已占用档口数
            return checkPass(validateResultBo);
        }

        WmCustomerDB wmCustomerDB =  getExistWmCustomer(wmCustomerBasicBo.getId());
        if (wmCustomerDB == null) {
            return checkFail(validateResultBo, CustomerConstants.RESULT_CODE_ERROR, "未查到有效客户信息");
        }
        // 非BD修改未生效客户的场景下，不需要校验档口数
        if(wmCustomerBasicBo.getCustomerSource() != CustomerSource.WAIMAI_BD && wmCustomerDB.isUnEffectived()){
            return checkPass(validateResultBo);
        }
        CustomerRealTypeSpInfoBo customerRealTypeSpInfoBo = wmCustomerBasicBo.getCustomerRealTypeSpInfoBo();
        boolean hasQuaComCusTag = wmCustomerMSCLabelService.checkHasQuaComCustomerLabel(wmCustomerDB.getMtCustomerId());
        CustomerMscUsedPoiDetailDTO customerMscUsedPoiDTO = wmCustomerService.getMscUsedPoiDetailDTOV2(wmCustomerDB.getId());
        boolean usedPoiCntIsPass = customerMscUsedPoiDTO == null || customerRealTypeSpInfoBo.getFoodCityPoiCount() >= customerMscUsedPoiDTO.getUsedPoiCnt();

        if (hasQuaComCusTag){
            boolean overSpecialCityCntLimit = false;
            if (customerRealTypeSpInfoBo != null){
                // 如果有资质共用标签，根据蜂窝判断物理城市
                WmUniAor wmUniAor = wmAorServiceAdapter.getAorInfoById(customerRealTypeSpInfoBo.getFoodCityAorId());
                if (wmUniAor == null || wmUniAor.getLocationInfos() == null){
                    return checkFail(validateResultBo, CustomerConstants.RESULT_CODE_ERROR, "未查到蜂窝信息");
                }
                overSpecialCityCntLimit = isOverSpecialCityCntLimit(customerRealTypeSpInfoBo,wmUniAor);

                return buildValidateResultBo4HasQuaTag(overSpecialCityCntLimit,usedPoiCntIsPass,customerMscUsedPoiDTO,validateResultBo,wmUniAor.getLocationInfos().getLevel2CityIds().get(0));

            }
        }
        if (!usedPoiCntIsPass){
            String failMsg = String.format("档口数量不可小于已占用档口数量（已占用%s个），请核实后再提交"
                    , customerMscUsedPoiDTO.getUsedPoiCnt());
            return checkFail(validateResultBo, CustomerConstants.RESULT_CODE_ERROR, failMsg);
        }
        return checkPass(validateResultBo);

    }

    private boolean isOverSpecialCityCntLimit(CustomerRealTypeSpInfoBo customerRealTypeSpInfoBo,WmUniAor wmUniAor){
        // 取蜂窝的二级物理城市
        List<Integer> level2CityIds =  wmUniAor.getLocationInfos().getLevel2CityIds();
        // 前端页面展示也是直接get(0)
        if (CollectionUtils.isNotEmpty(level2CityIds)){
            Map<Integer,Integer> cityMap = MccCustomerConfig.getSpecialCityPoiCntLimit();
            if (cityMap.containsKey(level2CityIds.get(0))){
                return customerRealTypeSpInfoBo.getFoodCityPoiCount() > cityMap.get(level2CityIds.get(0));
            }

        }
        return false;
    }

    private ValidateResultBo buildValidateResultBo4HasQuaTag(boolean overSpecialCityCntLimit,boolean usedPoiCntIsPass,CustomerMscUsedPoiDetailDTO customerMscUsedPoiDTO,ValidateResultBo validateResultBo,Integer specialCityId){
        if (overSpecialCityCntLimit && !usedPoiCntIsPass){
            String failMsg = String.format("该美食城为“资质共用特殊场景”美食城客户，且客户的物理城市为“北京市”，档口数不可超过%s。该美食城已占用档口数为%s家，已超过%s家，请核实门店情况后进行维护"
                    , MccCustomerConfig.getSpecialCityPoiCntLimit().get(specialCityId)
                    , customerMscUsedPoiDTO.getUsedPoiCnt()
                    , MccCustomerConfig.getSpecialCityPoiCntLimit().get(specialCityId));
            return checkFail(validateResultBo, CustomerConstants.RESULT_CODE_ERROR, failMsg);
        }
        if (usedPoiCntIsPass && overSpecialCityCntLimit){
            String failMsg = String.format("该美食城为“资质共用特殊场景”美食城客户，且客户的物理城市为“北京市”，档口数不可超过%s。"
                    , MccCustomerConfig.getSpecialCityPoiCntLimit().get(specialCityId));
            return checkFail(validateResultBo, CustomerConstants.RESULT_CODE_ERROR, failMsg);
        }
        if (!usedPoiCntIsPass){
            String failMsg = String.format("档口数量不可小于已占用档口数量（已占用%s个），请核实后再提交"
                    , customerMscUsedPoiDTO.getUsedPoiCnt());
            return checkFail(validateResultBo, CustomerConstants.RESULT_CODE_ERROR, failMsg);
        }
        return checkPass(validateResultBo);

    }

}

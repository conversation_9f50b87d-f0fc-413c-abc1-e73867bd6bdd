package com.sankuai.meituan.waimai.customer.service.sc.thrift;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;
import com.alibaba.fastjson.JSONObject;
import com.meituan.gecko.boot.domain.PageVo;
import com.meituan.gecko.boot.util.JacksonUtils;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.waimai.customer.config.MccScConfig;
import com.sankuai.meituan.waimai.customer.dao.sc.WmScEnumDictMapper;
import com.sankuai.meituan.waimai.customer.dao.sc.WmSchoolMapper;
import com.sankuai.meituan.waimai.customer.domain.sc.WmScEnumDictDO;
import com.sankuai.meituan.waimai.customer.domain.sc.WmSchoolDB;
import com.sankuai.meituan.waimai.customer.dao.sc.delivery.WmScDisplayFieldsMapper;
import com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmScDisplayFieldsDO;
import com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmSchoolDeliveryPlanAggr;
import com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmSchoolDeliverySearchModel;
import com.sankuai.meituan.waimai.customer.service.common.EmpServiceAdaptor;
import com.sankuai.meituan.waimai.customer.service.sc.delivery.WmSchoolDeliveryNewService;
import com.sankuai.meituan.waimai.customer.service.sc.school.auth.WmScAuthService;
import com.sankuai.meituan.waimai.customer.service.sc.school.sensitive.WmScSchoolSensitiveWordsService;
import com.sankuai.meituan.waimai.customer.util.ScBuildUtil;
import com.sankuai.meituan.waimai.customer.util.WmScTransUtil;
import com.sankuai.meituan.waimai.infra.constants.WmEmployHqTypeEnum;
import com.sankuai.meituan.waimai.infra.constants.WmOrgConstant;
import com.sankuai.meituan.waimai.infra.constants.WmVirtualOrgRecursiveTypeEnum;
import com.sankuai.meituan.waimai.infra.constants.WmVirtualOrgSourceEnum;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import com.sankuai.meituan.waimai.infra.domain.WmOrgResult;
import com.sankuai.meituan.waimai.infra.domain.WmVirtualOrg;
import com.sankuai.meituan.waimai.infra.domain.builder.WmOrgSearchParamBuilder;
import com.sankuai.meituan.waimai.infra.service.WmEmployService;
import com.sankuai.meituan.waimai.infra.service.WmOrgService;
import com.sankuai.meituan.waimai.infra.service.WmVirtualOrgService;
import com.sankuai.meituan.waimai.infra.util.WmVirtualOrgUtil;
import com.sankuai.meituan.waimai.mtauth.util.WmEmployUtils;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.SchoolGradeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.SchoolTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScEnumTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.*;
import com.sankuai.meituan.waimai.thrift.customer.service.sc.WmScCommonThriftService;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * @program: scm
 * @description: 校园食堂项目公共方法类
 * @author: jianghuimin02
 * @create: 2020-04-24 20:50
 **/
@Slf4j
@Service
public class WmScCommonThriftServiceImpl implements WmScCommonThriftService {

   @Autowired
   private WmScAuthService wmScAuthService;

   @Autowired
   private WmScEnumDictMapper wmScEnumDictMapper;

   @Autowired
   private WmEmployService.Iface wmEmployThriftService;

   @Autowired
   private WmOrgService.Iface wmOrgThriftService;

   @Autowired
   private WmVirtualOrgService.Iface wmVirtualOrgThriftService;

   @Autowired
   private EmpServiceAdaptor empServiceAdaptor;

   /**
    * 查询学校供给分级
    * @param type 供给分级id
    * @return 供给分级desc
    */
   @Override
   public String getSchoolGrade(int type) {
      int grade = 0;
      if (type == SchoolTypeEnum.THELEVEN.getType()
              || type == SchoolTypeEnum.NHFIVE.getType()
              || type == SchoolTypeEnum.THELEVEN_NHFIVE.getType()
              || type == SchoolTypeEnum.PROVINCE_FOCUS.getType()) {
         grade = SchoolGradeEnum.SKR_DIRECT.getType();
      }

      if (type == SchoolTypeEnum.COMMON_PUBLIC.getType()
              || type == SchoolTypeEnum.TRAINING.getType()
              || type == SchoolTypeEnum.OTHER.getType()) {
         grade = SchoolGradeEnum.KR_DIRECT.getType();
      }

      if (type == SchoolTypeEnum.PRIVATE.getType()) {
         grade = SchoolGradeEnum.CANTEEN_DIRECT.getType();
      }
      return SchoolGradeEnum.getByType(grade).getName();
   }

   @Override
   public boolean isHasRole(int userId, String roleCode) {
      return wmScAuthService.isHasRole(userId,roleCode);
   }

   @Override
   public Boolean isScAndCanSuperAdmin(int userId) {
      return wmScAuthService.isScAndCanSuperAdmin(userId);
   }

   /**
    * 获取校园食堂常用枚举字段表Map
    * @return Map<Integer, Map<Integer, String>> key: enumType  val:map(enumCode, enumDesc)
    */
   @Override
   public Map<Integer, Map<Integer, String>> getScEnumDictMap() {
      // 查询学校食堂枚举值字典表的全量数据
      List<WmScEnumDictDO> wmScEnumDictDOList = wmScEnumDictMapper.selectAllEnums();
      if (CollectionUtils.isEmpty(wmScEnumDictDOList)) {
         return new HashMap<>();
      }

      Map<Integer, Map<Integer, String>> scEnumDictMap = new HashMap<>();
      Map<Integer, String> platformMap = new HashMap<>();
      Map<Integer, String> platformV2Map = new HashMap<>();
      // 组装为Map: key: enumType  val:map(enumCode, enumDesc)
      for (WmScEnumDictDO wmScEnumDictDO : wmScEnumDictDOList) {
         if (wmScEnumDictDO.getEnumType().equals((int) WmScEnumTypeEnum.COOPERATION_PLATFORM.getType())) {
            platformMap.put(wmScEnumDictDO.getEnumCode(), wmScEnumDictDO.getEnumDesc());
         }
      }

      for (WmScEnumDictDO wmScEnumDictDO : wmScEnumDictDOList) {
         if (wmScEnumDictDO.getEnumType().equals((int) WmScEnumTypeEnum.COOPERATION_PLATFORM_V2.getType())) {
            platformV2Map.put(wmScEnumDictDO.getEnumCode(), wmScEnumDictDO.getEnumDesc());
         }
      }

      scEnumDictMap.put((int) WmScEnumTypeEnum.COOPERATION_PLATFORM.getType(), platformMap);
      scEnumDictMap.put((int) WmScEnumTypeEnum.COOPERATION_PLATFORM_V2.getType(), platformV2Map);
      return scEnumDictMap;
   }

   @Override
   public GetWmStaffResp getWmStaff(String content, Integer type, Boolean isRestrict, Integer isFilterJZ) {
      GetWmStaffResp resp = new GetWmStaffResp();
      try {
         List<WmStaffDTO> wmStaffDTOList = new ArrayList<>();
         if (isRestrict != null && isRestrict) {
            boolean isHQ = WmEmployUtils.isHQ(UserUtils.getUser().getId());
            isRestrict = !isHQ;
         }
         List<WmOrgResult> orgCities = null;
         if (isRestrict != null && isRestrict) {
            orgCities = WmEmployUtils.getOrgs(WmOrgConstant.OrgType.WM_ORG_CITY);
            if (CollectionUtils.isEmpty(orgCities))
               resp.setCode(0);
               resp.setData(Collections.emptyList());
               return resp;
         }

         List<WmEmploy> employeeInfos = null;
         if (content != null && content.trim().length() > 0) {
            content = content.trim();
            //包含"_"代表代理商，包含汉字走名字查询
            if (containsChinese(content) && !content.contains("_")) {
               try {
                  employeeInfos = wmEmployThriftService.getLikeName(content);
               } catch (WmServerException | TException e) {
                  log.error("调用结构服务化wmEmployThriftService.getLikeName异常", e);
               }
               if (employeeInfos != null && employeeInfos.size() > 5 && (isRestrict == null || !isRestrict)) {
                  employeeInfos = employeeInfos.subList(0, 5);
               }
            } else if (content.length() > 2) {    //默认按拼音搜索,最低要求输入3个字符
               try {
                  employeeInfos = wmEmployThriftService.getLikeMisId(content.toLowerCase());
               } catch (WmServerException | TException e) {
                  log.error("调用结构服务化wmEmployThriftService.getLikeMisId异常", e);
               }
               if (employeeInfos != null && employeeInfos.size() > 5 && (isRestrict == null || !isRestrict)) {
                  employeeInfos = employeeInfos.subList(0, 5);
               }
            }
         }
         if (employeeInfos != null && !employeeInfos.isEmpty()) {
            int i = 0;
            for (WmEmploy employeeInfo : employeeInfos) {
               if (i == 5) break;
               if (isRestrict != null && isRestrict) {
                  boolean hasAuthority = false;
                  if (!CollectionUtils.isEmpty(orgCities)) {
                     for (WmOrgResult orgResult : orgCities) {
                        if (inOrg(employeeInfo.getUid(), orgResult.getVirtualOrg().getId())) {
                           hasAuthority = true;
                           break;
                        }
                     }
                  }
                  if (!hasAuthority) {
                     continue;
                  }
               }
               //过滤兼职类型、代理商类型人员（代理人员修改为不过滤)
               if (isFilterJZ != null && isFilterJZ == 1) {
                  if (employeeInfo.getMisId().startsWith("jz_wm") ) {
                     continue;
                  }
               }

               WmStaffDTO wmStaffDTO = new WmStaffDTO();
               wmStaffDTO.setId(employeeInfo.getUid());
               wmStaffDTO.setLogin(employeeInfo.getMisId());
               wmStaffDTO.setEmail(employeeInfo.getMisId() + "@meituan.com");
               wmStaffDTO.setName(employeeInfo.getName());
               wmStaffDTO.setOrgName("产品部外卖组");
               wmStaffDTO.setWmOrgCityId(0);
               wmStaffDTO.setStructString(assembleString(employeeInfo));
               WmEmploy wmEmploy = wmEmployThriftService.getByMisId(employeeInfo.getMisId());
               wmStaffDTO.setPhone(wmEmploy == null ? "" : empServiceAdaptor.getPhone(wmEmploy.getUid()));
               String orgSuffix = getOrgSuffix(employeeInfo);
               wmStaffDTO.setOrgSuffix(orgSuffix); //组织架构的最后一层

               wmStaffDTOList.add(wmStaffDTO);

               i++;
            }
         }
         resp.setCode(0);
         resp.setData(wmStaffDTOList);
         return resp;
      } catch (Exception e) {
         log.error("查询人员信息异常", e);
         resp.setCode(200);
         resp.setMsg("查询人员信息异常");
         return resp;
      }
   }
   
   private boolean containsChinese(String str) {
      if (StringUtils.isBlank(str)) {
         return false;
      }
      return str.matches(".*[\u4e00-\u9fa5].*");
   }

   public boolean inOrg(int uid, int orgId) {
      Object orgIds = Lists.newArrayList();

      try {
         orgIds = this.wmOrgThriftService.search((new WmOrgSearchParamBuilder()).source(WmVirtualOrgSourceEnum.WAIMAI.getSource()).currentUid(uid).recursive(WmVirtualOrgRecursiveTypeEnum.BOTTOM_UP.getType()).justNeedOrgIds().build()).getOrgIds();
      } catch (TException | WmServerException var5) {
         log.warn("inOrg searchIds failed", var5);
      }

      return ((List) orgIds).contains(Integer.valueOf(orgId));
   }

   private String getOrgSuffix(WmEmploy wmEmploy) {
      log.info("getOrgSuffix(), wmEmploy={}", wmEmploy);
      String retStr = "总部";
      try {

         List<WmVirtualOrg> listRet = wmVirtualOrgThriftService.getOrgsByUid(wmEmploy.getUid(), WmOrgConstant.QUERY_ALL_SOURCE, WmVirtualOrgRecursiveTypeEnum.NONE.getType());
         if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(listRet)) {
            retStr = listRet.get(0).getName();
         }
      } catch (WmServerException e) {
         log.error("查询用户组织架构异常, WmServerException={}", e);
         retStr = "";
      } catch (TException e) {
         log.error("查询用户组织架构异常, TException={}", e);
         retStr = "";
      }
      return retStr;
   }

   private List<String> assembleString(WmEmploy wmEmploy) throws TException, WmServerException {
      int uid = wmEmploy.getUid();
      String separator = "--";

      ArrayList result = Lists.newArrayList();
      if (isHQ(wmEmploy)) {
         result.add("总部");
      }

      try {
         List e = this.wmVirtualOrgThriftService.getUserSources(uid);
         Iterator i$ = e.iterator();

         while (i$.hasNext()) {
            Byte source = (Byte) i$.next();
            List orgList = this.wmVirtualOrgThriftService.getOrgsByUid(uid, source.byteValue(), WmVirtualOrgRecursiveTypeEnum.BOTTOM_UP.getType());
            List orgPathSplit = WmVirtualOrgUtil.orgPathList(orgList);
            Iterator i$1 = orgPathSplit.iterator();

            while (i$1.hasNext()) {
               List orgPath = (List) i$1.next();
               ArrayList orgNamePath = Lists.newArrayList();
               Iterator i$2 = orgPath.iterator();

               while (i$2.hasNext()) {
                  WmVirtualOrg org = (WmVirtualOrg) i$2.next();
                  orgNamePath.add(org.getName());
               }

               result.add(StringUtils.join(orgNamePath, separator));
            }
         }
      } catch (Exception var14) {
         log.warn("getUserOrgPathString failed, uid=" + uid + ", separator=" + separator, var14);
      }

      return result;
   }

   public boolean isHQ(WmEmploy wmEmploy) {
      if (wmEmploy == null) {
         return false;
      } else {
         List hqTypeList = wmEmploy.getHqTypeList();
         return hqTypeList != null && hqTypeList.size() != 0 ? hqTypeList.contains(Integer.valueOf(WmEmployHqTypeEnum.HQ.getType())) : false;
      }
   }

}

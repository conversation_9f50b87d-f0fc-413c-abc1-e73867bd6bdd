package com.sankuai.meituan.waimai.customer.contract.service.impl.check.config;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.contract.config.WmFrameContractConfigService;
import com.sankuai.meituan.waimai.customer.contract.dao.WmTempletContractDBMapper;
import com.sankuai.meituan.waimai.customer.contract.domain.WmTempletContractDB;
import com.sankuai.meituan.waimai.customer.contract.service.IContractValidator;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.config.ContractSourceEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.config.ContractConfigInfo;
import com.sankuai.meituan.waimai.thrift.customer.domain.config.OperationSupportInfo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @description: 校验配置化合同是否具有废除合同的能力
 * @author: liuyunjie05
 * @create: 2024/5/27 17:47
 */
@Slf4j
@Service
public class ConfigContractAbilityInvalidValidator implements IContractValidator {

    @Resource
    private WmFrameContractConfigService wmFrameContractConfigService;

    @Resource
    private WmTempletContractDBMapper wmTempletContractDBMapper;

    @Override
    public boolean valid(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException {

        if (ContractSourceEnum.isCodeSource(contractBo.getBasicBo().getContractSource())) {
            return true;
        }

        long templateContractId = contractBo.getBasicBo().getTempletContractId();
        WmTempletContractDB wmTemplateContractPo = wmTempletContractDBMapper.selectByPrimaryKey(templateContractId);
        ContractConfigInfo configInfo = wmFrameContractConfigService.queryContractConfigInfo(wmTemplateContractPo.getType());
        if (configInfo == null) {
            log.warn("ConfigContractAbilityInvalidValidator#valid, 配置化信息不存在，templateContractId: {}", templateContractId);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "配置化信息不存在，不可废弃");
        }
        OperationSupportInfo supportOpInfo = configInfo.getSupportOpInfo();
        log.info("ConfigContractAbilityInvalidValidator#valid, supportOpInfo: {}", JSON.toJSONString(supportOpInfo));
        Boolean canAbolishContract = supportOpInfo.getCanAbolishContract();
        if (canAbolishContract == null || !canAbolishContract) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "该合同不支持废弃合同");
        }
        return true;
    }
}

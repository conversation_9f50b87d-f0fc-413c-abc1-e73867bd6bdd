package com.sankuai.meituan.waimai.customer.util.trans;

import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractTemplet;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractTempletWithBLOBs;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.SignVersionDetailBo;
import java.util.List;
import org.apache.commons.collections.CollectionUtils;

public class SignTempletTransUtil {

    public static List<SignVersionDetailBo> transTempletListToSignVersionDetailBoList(
            List<WmEcontractTemplet> wmEcontractTemplets) {
        List<SignVersionDetailBo> result = Lists.newArrayList();
        if(CollectionUtils.isEmpty(wmEcontractTemplets)){
            return result;
        }
        for(WmEcontractTemplet temp : wmEcontractTemplets){
            result.add(transToSignVersionDetailBo(temp));
        }
        return result;
    }

    public static List<SignVersionDetailBo> transTempletWithBLOBsListToSignVersionDetailBoList(
            List<WmEcontractTempletWithBLOBs> wmEcontractTempletWithBLOBs) {
        List<SignVersionDetailBo> result = Lists.newArrayList();
        if(CollectionUtils.isEmpty(wmEcontractTempletWithBLOBs)){
            return result;
        }
        for(WmEcontractTempletWithBLOBs temp : wmEcontractTempletWithBLOBs){
            result.add(transToSignVersionDetailBo(temp));
        }
        return result;
    }

    private static SignVersionDetailBo transToSignVersionDetailBo(WmEcontractTemplet temp) {
        SignVersionDetailBo result = new SignVersionDetailBo();
        result.setBizType(temp.getBizType());
        result.setVersionNum(temp.getVersionNum());
        result.setCtime(temp.getCtime());
        result.setUtime(temp.getUtime());
        result.setStatus(temp.getStatus());
        result.setValid(temp.getValid());
        if(temp instanceof WmEcontractTempletWithBLOBs){
            WmEcontractTempletWithBLOBs templetWithBLOBs = (WmEcontractTempletWithBLOBs)temp;
            result.setDocument(templetWithBLOBs.getDocument());
            result.setPdfTemplet(templetWithBLOBs.getPdfTemplet());
        }
        return result;
    }

}

package com.sankuai.meituan.waimai.customer.settle.ddd.domain.input.dservice;

import com.sankuai.meituan.waimai.thrift.customer.domain.base.BooleanResult;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;

public interface WmSettleInputAuditDomainService {

    BooleanResult paperContractSettleApprove(int wmCustomerId, int opUid, String opUname)
            throws WmCustomerException;

    BooleanResult paperContractSettleReject(int wmCustomerId, String rejectReason, int opUid,
            String opUname) throws WmCustomerException;

}

package com.sankuai.meituan.waimai.customer.domain.sc.metadata;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 校园食堂元数据表DO
 * <AUTHOR>
 * @date 2024/02/22
 * @email <EMAIL>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WmScMetadataDO {
    /**
     * 主键ID
     */
    private Integer id;
    /**
     * 业务场景
     */
    private Integer scene;
    /**
     * 业务ID
     */
    private Integer businessId;
    /**
     * 业务类型
     */
    private Integer businessType;
    /**
     * 字段值(JSON形式)
     */
    private String dataJson;
    /**
     * 数据版本号
     */
    private Integer dataVersion;
    /**
     * 表单版本号
     */
    private Integer templateVersion;
    /**
     * 操作类型
     * {@link com.sankuai.meituan.waimai.thrift.customer.constant.sc.delivery.SchoolDeliveryOpTypeEnum}
     */
    private Integer opType;
    /**
     * 是否有效
     */
    private Integer valid;
    /**
     * 创建人ID
     */
    private Long cuid;
    /**
     * 修改人ID
     */
    private Long muid;
    /**
     * 创建时间
     */
    private Integer ctime;
    /**
     * 修改时间
     */
    private Integer utime;

}

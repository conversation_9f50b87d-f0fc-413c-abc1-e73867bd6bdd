package com.sankuai.meituan.waimai.customer.contract.service.impl.check;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.contract.dao.WmTempletContractDBMapper;
import com.sankuai.meituan.waimai.customer.contract.domain.WmTempletContractDB;
import com.sankuai.meituan.waimai.customer.contract.service.IContractValidator;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerContractStatus;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * -@author: huangji<PERSON>lou
 * -@description:
 * -@date: 2023/1/13 11:38 AM
 */

@Service
@Slf4j
public class MedicOrderSplitInvalidator implements IContractValidator {

    @Resource
    private WmTempletContractDBMapper wmTempletContractDBMapper;

    /**
     * @param contractBo 合同信息
     * @param opUid 操作人id
     * @param opName 操作人名
     * @return 返回
     * @throws WmCustomerException
     * 签约失败、待生效、已生效的情况才能废除合同
     */
    @Override
    public boolean valid(WmCustomerContractBo contractBo, int opUid, String opName) throws WmCustomerException {
        Long contractId = contractBo.getBasicBo().getTempletContractId();
        WmTempletContractDB wmTempletContractPo = wmTempletContractDBMapper.selectByPrimaryKey(contractId);
        log.info("合同信息:wmTempletContractPo = {}", JSON.toJSONString(wmTempletContractPo));
        if (wmTempletContractPo == null) {
            log.info("valid#废除合同校验器，contractId:{}，不存在合同", contractId);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "不存在合同，不可废除");
        }
        if (wmTempletContractPo.getStatus() == CustomerContractStatus.SIGN_FAIL.getCode()
                || wmTempletContractPo.getStatus() == CustomerContractStatus.TO_EFFECT.getCode()
                || wmTempletContractPo.getStatus() == CustomerContractStatus.EFFECT.getCode()) {
            return true;
        }
        log.info("valid#废除合同校验器，contractId 合同状态为:{}，不允许操作废除", JSON.toJSONString(wmTempletContractPo));
        throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "当前合同状态不允许操作废除");
    }

}

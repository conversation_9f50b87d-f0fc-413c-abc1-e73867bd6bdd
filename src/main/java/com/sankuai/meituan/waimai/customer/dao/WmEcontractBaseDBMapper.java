package com.sankuai.meituan.waimai.customer.dao;

import com.sankuai.meituan.waimai.customer.domain.WmEContractSignBaseDB;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Component;
import java.util.List;

/**
 * 电子合同签约base信息
 * 目前包括：customer信息、签约人信息
 */
@Component
public interface WmEcontractBaseDBMapper {

    public static final String TABLE_NAME = "wm_econtract_sign_base";
    public static final String SELECT_KEYS = "id, customer_id AS customerId, base_context AS baseContext, valid, ctime, utime, version";

    /**
     * 新增base信息
     */
    int insert(WmEContractSignBaseDB baseDB);

    /**
     * 修改base信息
     */
    int updateByCustomerId(WmEContractSignBaseDB baseDB);

    /**
     * 删除base信息
     */
    @Update("UPDATE " + TABLE_NAME + " SET valid = 0 WHERE customer_id = #{customerId} AND valid = 1")
    int deleteByCustomerId(@Param("customerId")Integer customerId);

    /**
     * 根据customerId查询base信息
     */
    @Select("SELECT " + SELECT_KEYS + " FROM " + TABLE_NAME + " WHERE customer_id = #{customerId} AND valid = 1 order by id desc limit 1")
    WmEContractSignBaseDB queryByCustomerId(@Param("customerId")Integer customerId);

    Integer queryLastIndex();

    List<WmEContractSignBaseDB> queryEntityListWithLabel4Encryption(@Param("lastId") long lastId, @Param("size") int size);

    int updateOriginalRecordById(Long id);

    void batchUpdateOriginalRecordByIds(@Param("ids") List<Long> ids);

}

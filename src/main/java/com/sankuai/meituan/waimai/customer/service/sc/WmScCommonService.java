package com.sankuai.meituan.waimai.customer.service.sc;

import com.sankuai.meituan.org.opensdk.model.domain.Emp;
import com.sankuai.meituan.waimai.customer.adapter.WmEmployClient;
import com.sankuai.meituan.waimai.customer.domain.sc.WmScCommonDB;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.CanteenBo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @program: scm
 * @description: 校园食堂公共逻辑处理
 * @author: jianghuimin02
 * @create: 2020-04-24 10:57
 **/
@Slf4j
@Service
public abstract class WmScCommonService {

    @Autowired
    private WmEmployClient wmEmployClient;

    /**
     * 设置公共属性
     */
    public <T extends WmScCommonDB> void setCommon(T common, String userName, int userId, boolean insert){
        long timeLong = System.currentTimeMillis() / 1000L;
        int time = (int)timeLong;
        if (insert) {
            common.setUtime(time);
            common.setCtime(time);
            common.setUserName(userName);
            common.setUserId(userId);
        } else {
            common.setUtime(time);
            common.setUserName(userName);
            common.setUserId(userId);
        }
    }

    /**
     * 设置枚举值的描述，列表入口
     */
    public void setCanteenListDesc(List<CanteenBo> canteenBos) {
        for (CanteenBo canteenBo : canteenBos) {
            setCanteenBeanDesc(canteenBo);
        }
    }

    /**
     * 设置枚举值的描述，单个对象入口
     */
    public void setCanteenBeanDesc(CanteenBo canteenBo) {
        // 食堂类型
        CanteenTypeEnum canteenTypeEnum = CanteenTypeEnum.getByType(canteenBo.getCanteenType());
        canteenBo.setTypeDesc(canteenTypeEnum == null ? null : canteenTypeEnum.getName());
        // 食堂属性
        CanteenAttributeEnum canteenAttributeEnum = CanteenAttributeEnum.getByType(canteenBo.getCanteenAttribute());
        canteenBo.setAttributeDesc(canteenAttributeEnum == null ? null : canteenAttributeEnum.getName());
        // 食堂供给分级
        CanteenGradeEnum canteenGradeEnum = CanteenGradeEnum.getByType(canteenBo.getGrade());
        canteenBo.setGradeDesc(canteenGradeEnum == null ? null : canteenGradeEnum.getName());
        // 食堂合作状态
        if (canteenBo.getCanteenStatus() != null) {
            CanteenStatusEnum canteenStatusEnum = CanteenStatusEnum.getByType(canteenBo.getCanteenStatus());
            canteenBo.setStatusDesc(canteenStatusEnum == null ? null : canteenStatusEnum.getName());
            canteenBo.setCanteenStatusDesc(canteenStatusEnum == null ? null : canteenStatusEnum.getName());
        }
        // 证件类型
        if (canteenBo.getCardType() != null && canteenBo.getCardType() > 0) {
            canteenBo.setCardTypeDesc(CanteenManagerCardTypeEnum.getName(canteenBo.getCardType().byteValue()));
        }
        // 审核明细状态
        if (canteenBo.getAuditDetailStatus() != null) {
            CanteenDetailStatusEnum canteenDetailStatusEnum = CanteenDetailStatusEnum.getByType(canteenBo.getAuditDetailStatus().byteValue());
            canteenBo.setAuditDetailStatusDesc(canteenDetailStatusEnum == null ? null : canteenDetailStatusEnum.getName());
        }
        // 审核状态
        if (canteenBo.getAuditStatus() != null) {
            CanteenAuditStatusEnum canteenAuditStatusEnum = CanteenAuditStatusEnum.getByType(canteenBo.getAuditStatus().byteValue());
            canteenBo.setAuditStatusDesc(canteenAuditStatusEnum == null ? null : canteenAuditStatusEnum.getName());
        }
        // 生效状态
        if (canteenBo.getEffective() != null) {
            EffectiveStatusEnum effectiveStatusEnum = EffectiveStatusEnum.getByType(canteenBo.getEffective().byteValue());
            canteenBo.setEffectiveDesc(effectiveStatusEnum == null ? null : effectiveStatusEnum.getName());
        }
        // 食堂品类
        if (canteenBo.getCategory() != null) {
            CanteenCategoryEnum canteenCategoryEnum = CanteenCategoryEnum.getByType(canteenBo.getCategory().byteValue());
            canteenBo.setCategoryDesc(canteenCategoryEnum == null ? null : canteenCategoryEnum.getName());
        }
    }

    /**
     * 设置食堂责任人的显示格式
     * @param list
     * @return
     */
    public List<CanteenBo> setCanteenResponsiblePerson(List<CanteenBo> list){
        if(list == null || list.size() == 0){
            return list;
        }
        List<String> responsiblePersonList = new ArrayList<>();
        for(CanteenBo canteenBo : list){
            responsiblePersonList.add(canteenBo.getResponsiblePerson());
        }
        Map<String, Emp> wmEmployMap = new HashMap<>();
        List<Emp> empList = wmEmployClient.getEmp(responsiblePersonList);
        wmEmployMap = empList.stream().collect(Collectors.toMap(Emp::getMis, emp -> emp));
        for (CanteenBo canteenBo : list) {
            StringBuilder responsiblePerson = new StringBuilder();
            Emp emp = wmEmployMap.get(canteenBo.getResponsiblePerson());
            if (emp != null) {
                responsiblePerson.append(emp.getName())
                        .append("(").append(canteenBo.getResponsiblePerson()).append(")");
            } else {
                responsiblePerson.append(canteenBo.getResponsiblePerson());
            }
            canteenBo.setResponsiblePerson(String.valueOf(responsiblePerson));
        }
        return list;
    }

}

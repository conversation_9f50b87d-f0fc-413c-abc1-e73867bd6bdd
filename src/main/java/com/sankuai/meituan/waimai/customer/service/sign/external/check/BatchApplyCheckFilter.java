package com.sankuai.meituan.waimai.customer.service.sign.external.check;

import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.bo.sign.BatchApplyManualPackContext;
import com.sankuai.meituan.waimai.customer.util.base.SpringBeanUtil;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

public class BatchApplyCheckFilter {

    private static final Logger LOGGER = LoggerFactory.getLogger(BatchApplyCheckFilter.class);

    private List<IBatchApplyManualPackValidator> validators = Lists.newArrayList();

    public void filter(BatchApplyManualPackContext context) throws WmCustomerException {
        for (IBatchApplyManualPackValidator validator : validators) {
            try {
                validator.valid(context);
            } catch (WmCustomerException e) {
                LOGGER.info(e.getMsg(), e);
                throw e;
            }
        }
    }

    private void registry(IBatchApplyManualPackValidator validator) {
        validators.add(validator);
    }

    private static BatchApplyCheckFilter batchApplyManualPackCheckFilter = new BatchApplyCheckFilter();

    static {
        batchApplyManualPackCheckFilter.registry((IBatchApplyManualPackValidator) SpringBeanUtil.getBean("batchApplyCustomerNumValidator"));
        batchApplyManualPackCheckFilter.registry((IBatchApplyManualPackValidator) SpringBeanUtil.getBean("batchApplyExistCustomerValidator"));
        batchApplyManualPackCheckFilter.registry((IBatchApplyManualPackValidator) SpringBeanUtil.getBean("batchApplyDuplicateCustomerValidator"));
    }

    public static BatchApplyCheckFilter getBatchApplyManualPackCheckFilter() {
        return batchApplyManualPackCheckFilter;
    }
}

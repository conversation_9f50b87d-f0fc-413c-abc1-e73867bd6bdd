package com.sankuai.meituan.waimai.customer.service.customer.monitor.customerPoiList;

import com.dianping.frog.sdk.alarm.DXUtil;
import com.dianping.frog.sdk.data.BinlogRawData;
import com.dianping.frog.sdk.data.ColumnInfo;
import com.dianping.frog.sdk.data.DmlType;
import com.dianping.frog.sdk.data.RawData;
import com.dianping.frog.sdk.rpc.RpcService;
import com.dianping.frog.sdk.rpc.RpcServiceFactory;
import com.dianping.frog.sdk.runner.DefaultRuleRunner;
import com.dianping.lion.client.util.JsonUtils;
import com.dianping.lion.client.util.StringUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.Map;

public class WmCustomerPoiListRuleRunner extends DefaultRuleRunner {


    @Override
    public String check(RawData rawData, RawData... rawData2) throws Exception {
        BinlogRawData binlogRawData = (BinlogRawData) rawData;
        String tableName = binlogRawData.getRealTableName();
        DmlType operateType = binlogRawData.getDmlType();
        Map<String, ColumnInfo> columnInfoMap = binlogRawData.getColumnInfoMap();

        RpcService rpcService = RpcServiceFactory.getThriftInstance("com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerPoiThriftService",
                "com.sankuai.waimai.e.customer", 10000, null, "8435");
        Map<String, Object> params = Maps.newHashMap();
        params.put("tableName", tableName);
        params.put("operateType", operateType.getCode());
        Map<String, Map<String, Object>> map = Maps.newHashMap();

        for (Map.Entry<String, ColumnInfo> data : columnInfoMap.entrySet()) {
            Map<String, Object> tmp = Maps.newHashMap();
            ColumnInfo columnInfo = data.getValue();
            tmp.put("oldValue", columnInfo.getOldValue() == null ? "" : columnInfo.getOldValue().toString());
            tmp.put("newValue", columnInfo.getNewValue() == null ? "" : columnInfo.getNewValue().toString());
            map.put(data.getKey(), tmp);
        }
        params.put("columnInfoMap", map);

        String result = rpcService.invoke("monitorCustomerPoiListEs",
                Lists.newArrayList("com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerPoiListUpdateInfo"),
                Lists.newArrayList(JsonUtils.toJson(params)));

        if (!StringUtils.isBlank(result) && !"\"\"".equals(result)) {
            return result;
        }
        return null;
    }


    @Override
    public void alarm(String s, RawData rawData, RawData... rawData2) throws Exception {
        //通过大象公众号发送告警消息, 收件人为告警配置中的告警接收人。
        DXUtil.sendAlarm(s);
    }
}

package com.sankuai.meituan.waimai.customer.service.sign.manualpack.strategy.noticetask;

import java.util.List;

import com.sankuai.meituan.waimai.customer.adapter.WmLogisticsContractThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignManualTaskDB;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractManualTaskBizService;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmContractService;
import com.sankuai.meituan.waimai.customer.service.sign.applytask.context.ManualPackNoticeContext;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class AgentSqsStandardNoticeTask implements NoticeTask {

    @Autowired
    private WmContractService wmContractService;
    @Autowired
    private WmLogisticsContractThriftServiceAdapter wmLogisticsContractThriftServiceAdapter;
    @Autowired
    private WmEcontractManualTaskBizService wmEcontractManualTaskBizService;

    @Override
    public void notice(String module, List<Long> bizIdList, ManualPackNoticeContext context, List<Long> taskIds) throws WmCustomerException, TException {
        log.info("notice#module:{}, taskInfo:{}", module, JSON.toJSONString(context.getAllTaskInfo()));
        if (MapUtils.isEmpty(context.getTaskInfo()) || CollectionUtils.isEmpty(taskIds)) {
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "参数不合法");
        }
        // 代理神枪手任务列表
        List<WmEcontractSignManualTaskDB> taskDBList = wmEcontractManualTaskBizService.batchGetByManualTaskIdsIgnoreValid(taskIds);
        wmLogisticsContractThriftServiceAdapter.manualBatchApplySignByChannel(taskDBList, context.getManualBatchId(), module, context.getCommitUid());
    }
}

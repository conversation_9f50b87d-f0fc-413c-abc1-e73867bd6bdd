package com.sankuai.meituan.waimai.customer.service.sign.applyecontract;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.ca.WmEcontractCAPoiWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper.WmEcontractDateWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.sms.WmEcontractSmsWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.stamp.WmEcontractStampHNLXWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.stamp.WmEcontractStampMTWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.stamp.WmEcontractStampPoiWrapperService;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.EcontractBatchBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageBatchInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataWrapperEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @description: 极速达合作模式协议构建流程信息
 * @author: liuyunjie05
 * @create: 2023/10/30 19:57
 */
@Slf4j
@Service
public class WmEcontractSpeedyDeliveryCooperationApplyService extends AbstractWmEcontractApplyAdapterService{

    private static final String SPEEDY_DELIVERY_COOPERATION_AGREEMENT = EcontractTaskApplyTypeEnum.SPEEDY_DELIVERY_COOPERATION_AGREEMENT.getName();

    private static final List<String> flowList = Lists.newArrayList();

    private static final List<String> poiStampList = Lists.newArrayList();

    private static final List<String> mtStampList = Lists.newArrayList();


    private static final Map<String, EcontractDataWrapperEnum> dataWrapperMap = Maps.newHashMap();

    static {
        flowList.add(SPEEDY_DELIVERY_COOPERATION_AGREEMENT);

        poiStampList.add(SPEEDY_DELIVERY_COOPERATION_AGREEMENT);

        mtStampList.add(SPEEDY_DELIVERY_COOPERATION_AGREEMENT);

        dataWrapperMap.put(SPEEDY_DELIVERY_COOPERATION_AGREEMENT, EcontractDataWrapperEnum.SPEEDY_DELIVERY_COOPERATION_AGREEMENT);
    }

    @Resource
    private WmEcontractDateWrapperService wmEcontractDateWrapperService;

    @Resource
    private WmEcontractCAPoiWrapperService wmEcontractCAPoiWrapperService;

    @Resource
    private WmEcontractSmsWrapperService wmEcontractSmsWrapperService;

    @Resource
    private WmEcontractStampPoiWrapperService wmEcontractStampPoiWrapperService;

    @Resource
    private WmEcontractStampMTWrapperService wmEcontractStampMTWrapperService;


    private static final String speedy_delivery_cooperation_agreement = "speedy_delivery_cooperation_agreement";

    @Override
    public EcontractBatchBo wrapEcontractBo(EcontractBatchContextBo batchContextBo)
            throws TException, IllegalAccessException, WmCustomerException {
        log.info("WmEcontractSpeedyDeliveryCooperationApplyService#wrapEcontractBo");
        List<StageBatchInfoBo> batchInfoBoList = Lists.newArrayList();
        batchInfoBoList.add(wmEcontractDateWrapperService.wrap(batchContextBo, dataWrapperMap));
        batchInfoBoList.add(wmEcontractCAPoiWrapperService.wrap(batchContextBo));
        batchInfoBoList.add(wmEcontractSmsWrapperService.wrap(batchContextBo));
        batchInfoBoList.add(wmEcontractStampPoiWrapperService.wrap(batchContextBo, poiStampList));
        batchInfoBoList.add(wmEcontractStampMTWrapperService.wrap(batchContextBo, mtStampList));


        return new EcontractBatchBo.Builder()
                .token(getToken())
                .econtractBizId(getBizId(batchContextBo))
                .econtractType(speedy_delivery_cooperation_agreement)
                .stageInfoBoList(batchInfoBoList)
                .flowList(flowList)
                .callBackUrl(getCallbackUrl()).econtractBatchSource(getSource(batchContextBo))
                .build();
    }

}

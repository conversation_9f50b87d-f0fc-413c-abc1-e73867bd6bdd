package com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.util.DateUtil;
import com.sankuai.meituan.waimai.customer.aspect.DataWrapper;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.constants.PdfConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.ContractSignSubjectEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataWrapperEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractMedDepositInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 医药保证金协议数据打包
 */
@Service
@DataWrapper(wrapperEnum = EcontractDataWrapperEnum.MED_DEPOSIT)
public class WmEcontractMedDepositDataWrapperService implements IWmEcontractDataWrapperService {

    private static final Logger LOG = LoggerFactory.getLogger(WmEcontractMedDepositDataWrapperService.class);

    private static final String TEMPLET_NAME = "med_deposit";

    @Override
    public List<PdfContentInfoBo> wrap(EcontractBatchContextBo contextBo) throws IllegalAccessException, WmCustomerException {
        EcontractTaskBo taskBo = WmEcontractContextUtil.selectByApplyType(contextBo, EcontractTaskApplyTypeEnum.MED_DEPOSIT);
        PdfContentInfoBo pdfInfoBo = new PdfContentInfoBo();
        // 电子合同平台配置化改造迁移
        if (ConfigUtilAdapter.getBoolean("is_new_econtract_platform", true)) {
            // 新模式
            pdfInfoBo.setPdfTemplateId(ConfigUtilAdapter.getInt("MED_DEPOSIT_TEMPLATE_ID", 46)); // 指定模版
            pdfInfoBo.setPdfTemplateVersion(ConfigUtilAdapter.getInt("MED_DEPOSIT_TEMPLATE_VERSION")); // (可选)指定版本，不指定或传null、传0的话则默认为当前已发布的版本
        } else {
            // 旧模式
            pdfInfoBo.setPdfTemplateName(TEMPLET_NAME);
        }
        pdfInfoBo.setPdfMetaContent(generatePdfObject(contextBo, taskBo));
        pdfInfoBo.setPdfBizContent(Lists.<Map<String, String>>newArrayList());
        return Lists.newArrayList(pdfInfoBo);
    }

    private Map<String, String> generatePdfObject(EcontractBatchContextBo contextBo, EcontractTaskBo taskBo) {
        LOG.info("generatePdfObject taskBo = {}", JSON.toJSONString(taskBo));
        Map<String, String> pdfMap = Maps.newHashMap();
        String signTime = DateUtil.secondsToString(DateUtil.unixTime());
        pdfMap.put("partA", StringUtils.defaultIfEmpty(contextBo.getCustomerInfoBo().getCustomerName(), StringUtils.EMPTY));
        pdfMap.put("partAEstamp", PdfConstant.POI_SIGNKEY);
        pdfMap.put("partAsignerName", StringUtils.defaultIfEmpty(contextBo.getKpBo().getSignerName(), StringUtils.EMPTY));
        pdfMap.put("partASignTime", StringUtils.defaultIfEmpty(signTime, StringUtils.EMPTY));
        //深圳百寿健康信息技术有限公司
        pdfMap.put("partB", ContractSignSubjectEnum.SHENZHEN_SHOUKANG.getDesc());
        pdfMap.put("partBEstamp", PdfConstant.SZ_BS_JK_SIGNKEY);

        EcontractMedDepositInfoBo medDepositInfoBo = JSON.parseObject(taskBo.getApplyContext(), EcontractMedDepositInfoBo.class);
        pdfMap.put("wmPoiId",medDepositInfoBo.getWmPoiId());
        pdfMap.put("wmPoiName",medDepositInfoBo.getWmPoiName());
        LOG.info("generatePdfObject pdfMap = {}", JSON.toJSONString(pdfMap));
        return pdfMap;
    }
}

package com.sankuai.meituan.waimai.customer.settle.message;

import com.google.common.collect.Lists;
import com.meituan.mtrace.thread.pool.ScheduledExecutorServiceTraceWrapper;
import com.sankuai.meituan.waimai.settle.WmSettleMsgBo;
import java.util.List;
import java.util.concurrent.*;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.concurrent.BasicThreadFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.customer.settle.ddd.domain.effect.aggre.WmSettleEffectContext;
import com.sankuai.meituan.waimai.customer.settle.ddd.domain.effect.aggre.factory.WmSettleEffectContextFactory;
import com.sankuai.meituan.waimai.customer.settle.ddd.domain.effect.atom.service.impl.WmSettleEffectAtomServiceImpl;
import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettle;
import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettleAudited;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter;

@Service
public class WmSettleApproveModifyMsgToSettleService {

    private static Logger                         log                 = LoggerFactory.getLogger(WmSettleApproveModifyMsgToSettleService.class);

    private static final ScheduledExecutorService scheduledthreadpool = new ScheduledExecutorServiceTraceWrapper(new ScheduledThreadPoolExecutor(2,
            new BasicThreadFactory.Builder().namingPattern("example-schedule-pool-%d").daemon(true).build()));
    @Autowired
    private WmSettleEffectContextFactory          wmSettleEffectContextFactory;
    @Autowired
    private WmSettleEffectAtomServiceImpl         wmSettleEffectAtomService;

    public boolean publish(int wmCustomerId, List<WmSettle> wmSettleList, List<WmSettleAudited> wmSettleAuditedList) throws WmCustomerException{
        return publish(wmCustomerId,wmSettleList,wmSettleAuditedList, Lists.newArrayList());
    }

    public boolean publish(int wmCustomerId, List<WmSettle> wmSettleList, List<WmSettleAudited> wmSettleAuditedList,List<Integer> switchWmPoiRelWmSettleIdList) throws WmCustomerException {
        log.info("approveWmContract wmCustomerId = {} 3.结算消息", wmCustomerId);
        WmSettleMsgSender wmSettleMsgSender = new WmSettleMsgSender();
        wmSettleMsgSender.addWmSettleMsg(wmCustomerId, wmSettleList, wmSettleAuditedList,switchWmPoiRelWmSettleIdList);
        wmSettleMsgSender.send();
        return true;
    }

    //WmCustomerId
    //NewWmSettleList
    //OldWmSettleAuditedList
    //SwitchWmPoiIdList
    public void delayNotifyAndProcessSettlePoi(WmSettleEffectContext context) throws WmCustomerException {
        log.info("#delayNotifyAndProcessSettlePoi,context={}", JSONObject.toJSONString(context));
        int wmCustomerId = context.getWmCustomerId();
        List<WmSettle> newWmSettleList = context.getNewWmSettleList();
        List<WmSettleAudited> oldWmSettleAuditedList = context.getOldWmSettleAuditedList();
        int targetWmCustomerId = context.getWmCustomerId();
        List<Long> switchedWmPoiIdList = context.getSwitchWmPoiIdList();
        List<Integer> switchWmPoiRelWmSettleIdList = context.getSwitchWmPoiRelWmSettleIdList();
        scheduledthreadpool.schedule(new Runnable() {
            @Override
            public void run() {
                try {
//                    wmSettleEffectAtomService.handleWallet(context);
//                    publishForOldCustomer(targetWmCustomerId,switchedWmPoiIdList);
                    publish(wmCustomerId, newWmSettleList, oldWmSettleAuditedList,switchWmPoiRelWmSettleIdList);
                    delayProcessSettlePoi(targetWmCustomerId, switchedWmPoiIdList);
                } catch (WmCustomerException e) {
                    log.error("MsgToSettle_publish异常", e);
                }
            }
        }, ConfigUtilAdapter.getInt("config_delayNotify_time", 3), TimeUnit.SECONDS);
    }

    private void publishForOldCustomer(int targetWmCustomerId, List<Long> switchedWmPoiIdList) throws WmCustomerException{
        log.info("#publishForOldCustomer,targetWmCustomerId={},switchedWmPoiIdList={}",targetWmCustomerId,switchedWmPoiIdList);
//        WmSettleEffectContext context = new WmSettleEffectContext();
//        context.setWmCustomerId(targetWmCustomerId);
//        context.setSwitchWmPoiIdList(switchedWmPoiIdList);
//        List<WmSettleMsgBo> wmSettleMsgBos = wmSettleEffectAtomService
//                .publishForOldCustomer(context);
//        log.info("publishForOldCustomer targetWmCustomerId = {},wmSettleMsgBos={}", targetWmCustomerId,JSONObject.toJSONString(wmSettleMsgBos));
//        if(CollectionUtils.isEmpty(wmSettleMsgBos)){
//            return;
//        }
//        WmSettleMsgSender wmSettleMsgSender = new WmSettleMsgSender();
//        wmSettleMsgSender.sendDeleteWmCustomerPoi(wmSettleMsgBos);
    }

    public void delayProcessSettlePoi(int targetWmCustomerId, List<Long> switchedWmPoiIdList) throws WmCustomerException {
        log.info("#delayProcessSettlePoi,targetWmCustomerId={},switchedWmPoiIdList={}", JSONObject.toJSONString(targetWmCustomerId),
                JSONObject.toJSONString(switchedWmPoiIdList));
        scheduledthreadpool.schedule(new Runnable() {
            @Override
            public void run() {
                try {
                    WmSettleEffectContext context = new WmSettleEffectContext();
                    context.setWmCustomerId(targetWmCustomerId);
                    context.setSwitchWmPoiIdList(switchedWmPoiIdList);
                    wmSettleEffectAtomService.handleWmSettlePoiAfterSwitchCustomer(context);
                } catch (WmCustomerException e) {
                    log.error("delayProcessSettlePoi异常", e);
                }
            }
        }, ConfigUtilAdapter.getInt("config_delayProcessSettlePoi_time", 3), TimeUnit.SECONDS);
    }

    public void notifyOldSettleDelete(List<WmSettleMsgBo> notifyOldSettleDeleteList) {
        WmSettleMsgSender wmSettleMsgSender = new WmSettleMsgSender();
        wmSettleMsgSender.sendDeleteWmSettle(notifyOldSettleDeleteList);
    }
}

package com.sankuai.meituan.waimai.customer.bo.brand;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * -@author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * -@description:
 *
 * @see com.sankuai.meituan.waimai.thrift.customer.constant.customer.CustomerBizOrgEnum
 * -@date: 2023/4/25 8:27 PM
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BizOrgCodeBrandTypeRelBO {

    private Integer bizOrgCode;

    private String bizOrgCodeDesc;

    private List<Integer> brandTypeList;

}

package com.sankuai.meituan.waimai.customer.adapter;

import com.sankuai.meituan.waimai.kv.groupmbase.client.WmKvTairClient;
import com.sankuai.meituan.waimai.kv.groupmbase.exception.WmKvException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

/**
 * @description: kv缓存适配器
 * @author: zhangyuanhao02
 * @create: 2024/12/30 17:10
 */
@Slf4j
@Service
public class WmKvTairClientAdapter {

    @Autowired(required = false)
    @Qualifier("m_WmKvTairClient")
    private WmKvTairClient wmKvTairClient = null;

    public void set(String key, String value, int expire) {
        try {
            wmKvTairClient.set(key, value, expire);
        } catch (WmKvException e) {
            log.error("WmKvTairClientAdapter#set 异常", e);
        }
    }

    public String get(String key) {
        try {
            return wmKvTairClient.get(key);
        } catch (WmKvException e) {
            log.error("WmKvTairClientAdapter#get 异常", e);
        }

        return null;
    }
}

package com.sankuai.meituan.waimai.customer.mq.service;

import com.alibaba.fastjson.JSONObject;
import com.meituan.mafka.client.bean.MafkaProducer;
import com.sankuai.meituan.waimai.customer.mq.domain.WmCustomerQiKeDelayBo;
import com.sankuai.meituan.waimai.customer.mq.domain.WmCustomerQikePoiBo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 企客延时处理消息发送
 */
@Slf4j
@Service
public class CustomerQikeSendService {

    @Resource(name = "customerQikeDelayProducer")
    private MafkaProducer customerQikeDelayProducer;

    @Resource(name = "customerQikePoiProducer")
    private MafkaProducer customerQikePoiProducer;

    /**
     * 企客延时处理
     *
     * @param delayBo
     */
    public void sendQikeDelayMq(WmCustomerQiKeDelayBo delayBo) {
        try {
            log.info("sendQikeDelayMq delayBo={}", JSONObject.toJSONString(delayBo));
            customerQikeDelayProducer.sendMessage(JSONObject.toJSONString(delayBo));
        } catch (Exception e) {
            log.error("sendQikeDelayMq 异常：delayBo={}", delayBo, e);
        }
    }

    /**
     * 企客门店绑定处理
     *
     * @param poiBo
     */
    public void sendQikePoiCreate(WmCustomerQikePoiBo poiBo) {
        try {
            log.info("sendQikePoiCreate poiBo={}", JSONObject.toJSONString(poiBo));
            customerQikePoiProducer.sendMessage(JSONObject.toJSONString(poiBo));
        } catch (Exception e) {
            log.error("sendQikePoiCreate 异常：poiBo={}", poiBo, e);
        }
    }
}

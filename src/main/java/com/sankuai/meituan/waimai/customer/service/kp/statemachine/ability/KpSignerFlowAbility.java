package com.sankuai.meituan.waimai.customer.service.kp.statemachine.ability;

import com.sankuai.meituan.waimai.customer.service.kp.statemachine.context.KpSignerStatusMachineContext;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;


/**
 * <AUTHOR>
 * @date 20240416
 * KP签约人流程能力定义
 */
public interface KpSignerFlowAbility {

    /**
     * 新增签约人KP
     *
     * @param context
     * @return
     */
    Object insertKpSignerWithSM(KpSignerStatusMachineContext context) throws WmCustomerException;

    /**
     * 更新签约人KP
     *
     * @param context
     * @return
     */
    Object updateKpSignerWithSM(KpSignerStatusMachineContext context) throws WmCustomerException;

    /**
     * 是否匹配当前能力
     *
     * @param certType
     * @return
     */
    Boolean matchKpSignerAbility(byte certType) throws WmCustomerException;

    /**
     * 处理审核结果
     *
     * @param context
     * @return
     * @throws WmCustomerException
     */
    Boolean dealSignerKpAuditResult(KpSignerStatusMachineContext context) throws WmCustomerException;


    /**
     * 处理短信授权结果
     *
     * @param context
     * @return
     * @throws WmCustomerException
     */
    Boolean dealMsgAuthResult(KpSignerStatusMachineContext context) throws WmCustomerException;
}

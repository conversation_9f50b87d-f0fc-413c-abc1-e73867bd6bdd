package com.sankuai.meituan.waimai.customer.service.sc.thrift;

import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.meituan.waimai.common.utils.JacksonUtils;
import com.sankuai.meituan.waimai.customer.dao.sc.WmSchoolContractMapper;
import com.sankuai.meituan.waimai.customer.domain.sc.WmSchoolContractDB;
import com.sankuai.meituan.waimai.customer.domain.sc.WmSchoolContractSearchCondition;
import com.sankuai.meituan.waimai.customer.util.RedisKvUtil;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.delivery.*;
import com.sankuai.meituan.waimai.thrift.customer.service.sc.WmSchoolContractThriftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class WmSchoolContractThriftServiceImpl implements WmSchoolContractThriftService {

    @Autowired
    private WmSchoolContractAssemble wmSchoolContractAssemble;

    @Autowired
    private WmSchoolContractMapper wmSchoolContractMapper;

    @Autowired(required = false)
    @Qualifier("schoolRedisClientRead")
    private RedisStoreClient schoolRedisClientRead;

    @Autowired(required = false)
    @Qualifier("schoolRedisClientWrite")
    private RedisStoreClient schoolRedisClientWrite;

    private static final String SCHOOL_REDIS_CATEGORY = "campus_school_contract";

    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    private static final DateTimeFormatter time_formatter = DateTimeFormatter.ofPattern("HH:mm");

    private static final String DEFAULT_VAL = "-";

    private static final Integer EXPIRED_TIME = 1 * 24 * 60 * 60;

    @Override
    public QuerySchoolFeeRateResponse querySchoolFeeRate(QuerySchoolFeeRateReq querySchoolFeeRateReq) {
        return ThriftUtils.exec(() -> {
            log.info("查询学校管理费请求 req:{}", JacksonUtils.toJson(querySchoolFeeRateReq));

            Long schoolId = querySchoolFeeRateReq.getSchoolId();
            Long timestamp = querySchoolFeeRateReq.getTimestamp();

            LocalDate date = Instant.ofEpochMilli(timestamp)
                    .atZone(ZoneId.systemDefault())
                    .toLocalDate();
            String formattedDate = date.format(formatter);

            LocalTime time = Instant.ofEpochMilli(timestamp)
                    .atZone(ZoneId.systemDefault())
                    .toLocalTime();
            String formattedTime = time.format(time_formatter);

            //先查缓存
            //拼接key：学校id+日期
            StoreKey storeKey = new StoreKey(SCHOOL_REDIS_CATEGORY, schoolId, formattedDate);
            String val = schoolRedisClientRead.hget(storeKey, formattedTime);
            log.info("[查询学校管理费缓存]schoolId:{}, timestamp:{}, formattedTime:{}, val:{}", schoolId, timestamp, formattedTime, val);

            if (Objects.isNull(val)) {
                //如果缓存中没有这个key，走DB
                log.info("[学校管理费缓存不存在，查询DB]schoolId:{}, timestamp:{}, time:{}", schoolId, timestamp, formattedTime);
                WmSchoolContractSearchCondition condition = new WmSchoolContractSearchCondition();
                condition.setSchoolId(querySchoolFeeRateReq.getSchoolId().intValue());
                condition.setTimestamp(querySchoolFeeRateReq.getTimestamp());
                condition.setValid(1);
                condition.setStatus(3);

                List<WmSchoolContractDB> wmSchoolContractDBList = wmSchoolContractMapper.selectByCondition(condition);
                WmSchoolContractDB effectiveContract = CollectionUtils.isNotEmpty(wmSchoolContractDBList) ? wmSchoolContractDBList.get(0) : null;
                //将值set进缓存中
                String contractStr = Objects.nonNull(effectiveContract) ? JacksonUtils.toJson(effectiveContract) : DEFAULT_VAL;
                schoolRedisClientWrite.hset(storeKey, formattedTime, contractStr, EXPIRED_TIME);

                SchoolFeeRateDTO schoolFeeRateDTO = wmSchoolContractAssemble.transManageFee(effectiveContract);
                log.info("[查询学校管理费DB返回]schoolId:{}, timestamp:{}, val:{}", schoolId, timestamp, JacksonUtils.toJson(schoolFeeRateDTO));
                return schoolFeeRateDTO;
            }

            if (Objects.equals(val, DEFAULT_VAL)) {
                log.info("[查询学校管理费缓存有值但为空]schoolId:{}, timestamp:{}", schoolId, timestamp);
                return null;
            }

            WmSchoolContractDB wmSchoolContractDB = JacksonUtils.fromJson(val, WmSchoolContractDB.class);
            return wmSchoolContractAssemble.transManageFee(wmSchoolContractDB);

        }, new QuerySchoolFeeRateResponse());
    };


    @Override
    public QuerySchoolContractResponse querySchoolContract(QuerySchoolContractReq querySchoolContractReq) {
        return ThriftUtils.exec(() -> {
            log.info("学校合同查询请求 req:{}", JacksonUtils.toJson(querySchoolContractReq));
            WmSchoolContractSearchCondition condition = new WmSchoolContractSearchCondition();
            condition.setSchoolId(querySchoolContractReq.getSchoolId().intValue());
            condition.setValid(1);
            int total = wmSchoolContractMapper.selectCountByCondition(condition);

            condition.setPageInfo(querySchoolContractReq.getPageNo(), querySchoolContractReq.getPageSize());
            List<WmSchoolContractDB> wmSchoolContractDBList = wmSchoolContractMapper.selectByCondition(condition);

            SchoolContractPageDTO pageDTO = new SchoolContractPageDTO();
            pageDTO.setPageNo(querySchoolContractReq.getPageNo());
            pageDTO.setPageSize(querySchoolContractReq.getPageSize());
            pageDTO.setData(wmSchoolContractAssemble.trans(wmSchoolContractDBList));
            pageDTO.setTotal(total);

            return pageDTO;
        }, new QuerySchoolContractResponse());
    }
}

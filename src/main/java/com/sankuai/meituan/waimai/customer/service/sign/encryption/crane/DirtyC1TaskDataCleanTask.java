package com.sankuai.meituan.waimai.customer.service.sign.encryption.crane;

import com.alibaba.fastjson.JSON;
import com.cip.crane.client.spring.annotation.Crane;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.customer.dao.WmEcontractTaskDBMapper;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignTaskDB;
import com.sankuai.meituan.waimai.customer.service.sign.WmEcontractSignBzService;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractBigTaskParseService;
import com.sankuai.meituan.waimai.thrift.customer.constant.WmEcontractBatchConstant;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractContractInfoBo;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStreamReader;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * Created by lixuepeng on 2021/12/01
 */

@Component
public class DirtyC1TaskDataCleanTask {

    private static final Logger LOGGER = LoggerFactory.getLogger(DirtyC1TaskDataCleanTask.class);

    @Autowired
    private WmEcontractSignBzService       wmEcontractSignBzService;
    @Autowired
    private WmEcontractTaskDBMapper        wmEcontractTaskDBMapper;
    @Autowired
    private WmEcontractBigTaskParseService wmEcontractBigTaskParseService;

    /**
     *
     * 情况C1脏数据
     */
    @Crane("dirty.c1.task.data.clean.task")
    public void handleClean(String path) {
        try {
            if (StringUtils.isBlank(path)) {
                LOGGER.error("#DirtyC1TaskDataCleanTask.handleClean# 参数错误, path:{}", path);
                return;
            }
            /* 读入TXT文件 */
            File filename = new File(path); // 要读取以上路径的input。txt文件
            InputStreamReader reader = new InputStreamReader(new FileInputStream(filename)); // 建立一个输入流对象reader
            BufferedReader br = new BufferedReader(reader); // 建立一个对象，它把文件内容转成计算机能读懂的语言
            String line = "";
            line = br.readLine();
            int taskNum = 0;
            while (line != null) {
                taskNum++;
                long taskId = Long.valueOf(line);
                if (isCanClean(taskId)) {
                    LOGGER.info("#DirtyC1TaskDataCleanTask.handleClean# taskId:{} 需要清理", taskId);
                    if (ConfigUtilAdapter.getBoolean("is_real_clean", false)) {
                        try {
                            wmEcontractSignBzService.cancelSign(taskId, WmEcontractBatchConstant.TASK_MANAGE_TASK_LIST);
                        } catch (Exception e) {
                            LOGGER.error("#DirtyC1TaskDataCleanTask.handleClean# cancelSign error taskId:{}", taskId, e);
                        }
                    }
                }
                line = br.readLine(); // 一次读入一行数据
            }
            LOGGER.info("#DirtyC1TaskDataCleanTask.handleClean# taskNum:{}", taskNum);
        } catch (Exception e) {
            LOGGER.error("#DirtyC1TaskDataCleanTask.handleClean# error path:{}", path, e);
        }
    }

    private boolean isCanClean(long taskId) {
        try {
            WmEcontractSignTaskDB taskDB = wmEcontractBigTaskParseService.getById(taskId);
            //task为空或者类型不是C1或者不是holding状态
            if (taskDB == null || !"c1contract".equals(taskDB.getApplyType()) || !"holding".equals(taskDB.getApplyState())) {
                return false;
            }
            EcontractContractInfoBo contractInfoBo = JSON.parseObject(taskDB.getApplyContext(), EcontractContractInfoBo.class);
            //如果履约服务费为空，即需要清理的数据
            if (StringUtils.isEmpty(contractInfoBo.getPerformanceServiceFeeName())) {
                return true;
            }
        } catch (Exception e) {
            LOGGER.error("#DirtyC1TaskDataCleanTask.isCanClean# error taskId:{}", taskId, e);
        }
        return false;
    }
}

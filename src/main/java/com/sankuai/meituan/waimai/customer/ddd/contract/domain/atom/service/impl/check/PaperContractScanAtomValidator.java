package com.sankuai.meituan.waimai.customer.ddd.contract.domain.atom.service.impl.check;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.ddd.contract.domain.atom.service.IContractValidator;
import com.sankuai.meituan.waimai.thrift.customer.constant.WmTempletContractTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.CustomerPaperContractRemarkBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
public class PaperContractScanAtomValidator implements IContractValidator {

    @Override
    public boolean valid(WmCustomerContractBo contractBo, int opUid, String opName)
            throws WmCustomerException {
        if (WmTempletContractTypeEnum.isEContract(contractBo.getBasicBo().getType())) {
            return true;
        }
        CustomerPaperContractRemarkBo remarkBo = JSON.parseObject(contractBo.getBasicBo().getExtStr(), CustomerPaperContractRemarkBo.class);
        if (remarkBo == null || remarkBo.getContractScan() == null || CollectionUtils.isEmpty(remarkBo.getContractScan().getList())) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "纸质合同必须上传合同扫描件");
        }
        if (remarkBo.getContractScan().getList().size() > 1) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "纸质合同扫描件只能上传一张");
        }
        return true;
    }

}

package com.sankuai.meituan.waimai.customer.domain.cuspoirel.unbind;

import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiSmsRecordDB;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 解绑签约回调请求参数
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UnBindSignNoticeDTO {

    /**
     * 解绑关联的签约任务ID
     */
    private Long signUnBindRelTaskId;

    /**
     * 签约结果
     * com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskStateEnum
     */
    private Integer signResult;

    /**
     * 解绑绑定短信记录
     */
    private WmCustomerPoiSmsRecordDB wmCustomerPoiSmsRecordDB;
}

package com.sankuai.meituan.waimai.customer.service.sc;

import com.sankuai.meituan.waimai.thrift.customer.domain.sc.*;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import org.apache.thrift.TException;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2020/11/26 17:58
 */
public interface WmCanteenService {


    /**
     * 根据食堂主键ID查询食堂信息
     * @param id 食堂主键ID
     * @return CanteenBo
     * @throws TException org.apache.thrift.TException
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */

    CanteenBo getCanteen(int id) throws TException, WmSchCantException;

    /**
     * 食堂详情页面-获取食堂和提审修改的diff内容
     * @param canteenPrimaryid 食堂主键id
     * @return CanteenAuditDetailBo
     * @throws TException org.apache.thrift.TException
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    CanteenAuditDetailBo getCanteenAndAuditDiffForPage(int canteenPrimaryid) throws TException, WmSchCantException;

    /**
     * 获取任务系统中的食堂审核信息
     * @param taskId 任务id
     * @return Canteen4AuditPageBo
     * @throws TException org.apache.thrift.TException
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    Canteen4AuditPageBo getCanteen4AuditPage(Integer taskId) throws TException, WmSchCantException;

    /**
     * 查询食堂审批信息
     * @param auditSystemId 审批任务ID
     * @param auditSystemType 审批系统类型
     * @param userId 用户ID
     * @return Canteen4AuditPageBo
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    Canteen4AuditPageBo getCanteenAuditInfo(String auditSystemId, Integer auditSystemType, Integer userId) throws WmSchCantException, TException;

    /**
     * 获取食堂列表
     * @param canteenQueryBo 查询条件
     * @return WmCanteenListPageData
     * @throws TException org.apache.thrift.TException
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    WmCanteenListPageData getCanteenList(CanteenQueryBo canteenQueryBo) throws TException, WmSchCantException;

    /**
     * 新建食堂提审
     * @param canteenBO canteenBO
     * @param userId 用户ID
     * @param userName 用户名称
     * @return 食堂主键ID
     * @throws TException org.apache.thrift.TException
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    int saveCanteenTicket(CanteenBo canteenBO, int userId, String userName) throws TException, WmSchCantException;

    /**
     * 修改食堂信息审核
     * @param canteenBO canteenBO
     * @param userId 用户ID
     * @param userName 用户名称
     * @return 食堂主键ID
     * @throws TException org.apache.thrift.TException
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    int editCanteenTicket(CanteenBo canteenBO, int userId, String userName) throws TException, WmSchCantException;

    /**
     * 新增/修改食堂审核回调
     * @param wmScTicketNoticeBo wmScTicketNoticeBo
     * @throws TException org.apache.thrift.TException
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    void auditCallback(WmScTicketNoticeBo wmScTicketNoticeBo) throws TException, WmSchCantException;


    /**
     * 通过学校Id查询学校内的食堂信息列表
     */
    List<CanteenBo> getCanteensBySchoolId(int schoolId) throws TException, WmSchCantException;

    /**
     * 食堂信息审批通过(蜂鸟系统)
     * @param auditDTO auditDTO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    void passCanteenInfoAuditByHummingBird(WmCanteenAuditDTO auditDTO) throws WmSchCantException, TException;

    /**
     * 食堂信息审批驳回(蜂鸟系统)
     * @param auditDTO auditDTO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    void rejectCanteenInfoAuditByHummingBird(WmCanteenAuditDTO auditDTO) throws WmSchCantException, TException;


    /**
     * 食堂信息审批通过(任务系统)
     * @param auditSystemId 任务ID
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    void passCanteenInfoAuditByTicket(String auditSystemId) throws WmSchCantException, TException;


    /**
     * 食堂信息审批驳回(任务系统)
     * @param auditSystemId 任务ID
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    void rejectCanteenInfoAuditByTicket(String auditSystemId) throws WmSchCantException, TException;

    /**
     * 食堂信息审批终止(任务系统)
     * @param auditSystemId 任务ID
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    void stopCanteenInfoAuditByTicket(String auditSystemId) throws WmSchCantException, TException;

    void canteenBoExcelUpLoad(List<CanteenBo> canteenBoList) throws WmSchCantException;
}
package com.sankuai.meituan.waimai.customer.contract.frame.impl;

import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.customer.contract.frame.CooperateModeAuthority;
import com.sankuai.meituan.waimai.customer.contract.frame.WmCustomerFrameContractAuthService;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmSubjectChangeSupplementEContractTempletService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.thrift.customer.constant.BusinessGroupLineEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.WmCustomerAuthTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractTypeBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.framecontract.AuthDcRequestDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerBasicBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import static com.sankuai.meituan.waimai.customer.contract.frame.CooperateModeAuthority.COOPERATE_MODE_AUTHORITY_FOR_APP;


/**
 * @description: App端合同类型校验
 * @author: zhangyuanhao02
 * @create: 2024/8/5 19:20
 */
@Service(value = COOPERATE_MODE_AUTHORITY_FOR_APP)
public class CooperateModeAuthorityForApp extends CooperateModeAuthority {

    @Resource
    private WmCustomerService wmCustomerService;

    @Resource
    private WmSubjectChangeSupplementEContractTempletService wmSubjectChangeSupplementEContractTempletService;

    @Resource
    private WmCustomerFrameContractAuthService wmCustomerFrameContractAuthService;

    /**
     * 默认校验到家业务线
     * @param cooperateMode
     * @param uid
     * @param wmCustomerId
     * @param businessGroupLine
     * @return
     * @throws TException
     * @throws WmCustomerException
     */
    @Override
    public Boolean isCooperateModeAuthority(int cooperateMode, Long uid, Integer wmCustomerId, Integer businessGroupLine) throws TException, WmCustomerException {
        //主体变更补充协议客户类型判断
        if (businessGroupLine == BusinessGroupLineEnum.DAOCAN_SERVICE.getGroupLine()) {
            return isCooperateModeAuthorityForDc(uid, wmCustomerId);
        } else {
            return isCooperateModeAuthorityForDj(wmCustomerId, cooperateMode);
        }
    }

    /**
     * 到餐合同类型校验
     * @return
     * @throws WmCustomerException
     */
    private Boolean isCooperateModeAuthorityForDc(Long uid, Integer wmCustomerId) throws WmCustomerException {
        return wmCustomerFrameContractAuthService.authDcRead(uid.intValue(), wmCustomerId);
    }

    /**
     * 到家合同类型校验
     * @param wmCustomerId
     * @param cooperateMode
     * @return
     * @throws TException
     * @throws WmCustomerException
     */
    private Boolean isCooperateModeAuthorityForDj(Integer wmCustomerId, int cooperateMode) throws TException, WmCustomerException {
        if (cooperateMode == WmTempletContractTypeBo.COOPERATEMODE_SUBJECT_CHANGE_SUPPLEMENT) {
           return authSubjectChangeSupplement(wmCustomerId);
        }

        return true;
    }
}

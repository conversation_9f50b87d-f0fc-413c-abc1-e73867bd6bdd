package com.sankuai.meituan.waimai.customer.adapter;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.scmbrand.thrift.domain.metadataplatform.MetaTemplateRequestDTO;
import com.sankuai.meituan.scmbrand.thrift.domain.metadataplatform.MetaTemplateResultDTO;
import com.sankuai.meituan.scmbrand.thrift.exception.BrandServiceException;
import com.sankuai.meituan.scmbrand.thrift.service.metadataplatform.BrandMetadataOuterService;
import com.sankuai.meituan.waimai.customer.domain.sc.metadata.WmScMetadataBusinessParamBO;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.metadata.WmScMetadataModuleTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.metadata.WmScMetadataSceneTypeEnum;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 品牌元数据服务适配器
 * <AUTHOR>
 * @date 2024/02/27
 * @email <EMAIL>
 */
@Service
@Slf4j
public class WmBrandMetadataServiceAdapter {

    @Autowired
    private BrandMetadataOuterService brandMetadataOuterService;

    /**
     * 元数据平台使用业务方. 1-校园食堂
     */
    public static final int SCHOOL_METADATA_BUSINESS_ID = 1;

    /**
     * 根据业务类型查询学校交付表单最新版本号
     * @param businessType 业务类型
     * @return 学校交付表单最新版本号
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public Integer getNewestSchoolDeliveryTemplateVersionByBusinessType(Integer businessType) throws WmSchCantException {
        WmScMetadataBusinessParamBO businessParamBO = WmScMetadataBusinessParamBO.builder()
                .moduleType(String.valueOf(WmScMetadataModuleTypeEnum.SCHOOL.getCode()))
                .sceneType(String.valueOf(WmScMetadataSceneTypeEnum.SCHOOL_DELIVERY.getCode()))
                .businessType(String.valueOf(businessType))
                .build();

        MetaTemplateRequestDTO requestDTO = new MetaTemplateRequestDTO();
        requestDTO.setFactor(JSONObject.toJSONString(businessParamBO));
        requestDTO.setBusiness(SCHOOL_METADATA_BUSINESS_ID);
        try {
            log.info("[WmBrandMetadataServiceAdapter.getNewestSchoolDeliveryTemplateVersionByBusinessType] requestDTO = {}", JSONObject.toJSONString(requestDTO));
            List<MetaTemplateResultDTO> templateResultDTOList = brandMetadataOuterService.getMetaTemplateByBusIndicator(requestDTO);
            log.info("[WmBrandMetadataServiceAdapter.getNewestSchoolDeliveryTemplateVersionByBusinessType] templateResultDTOList = {}", JSONObject.toJSONString(templateResultDTOList));
            List<Integer> versionIdList = templateResultDTOList.stream()
                    .map(MetaTemplateResultDTO::getMetadataTemplateVersion)
                    .collect(Collectors.toList());
            // 最新表单版本号为最大版本
            return Collections.max(versionIdList);
        } catch (Exception e) {
            log.error("[WmBrandMetadataServiceAdapter.getNewestSchoolDeliveryTemplateVersionByBusinessType] BrandServiceException. businessType = {}", businessType);
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "查询元数据最新表单版本异常");
        }
    }

}

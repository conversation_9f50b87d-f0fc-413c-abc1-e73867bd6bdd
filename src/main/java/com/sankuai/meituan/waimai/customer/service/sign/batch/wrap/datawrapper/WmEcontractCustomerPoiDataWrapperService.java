package com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.customer.aspect.DataWrapper;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.constants.PdfConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.ContractSignSubjectEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataWrapperEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchPoiInfoExtBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 客户批量门店生成数据组装
 */
@Service
@DataWrapper(wrapperEnum = EcontractDataWrapperEnum.BATCH_POI)
public class WmEcontractCustomerPoiDataWrapperService implements IWmEcontractDataWrapperService {

    private static final Logger LOG = LoggerFactory.getLogger(WmEcontractCustomerPoiDataWrapperService.class);

    @Autowired
    private WmEcontractBatchPoiDataWrapperService wmEcontractBatchPoiDataWrapperService;
    @Autowired
    private WmEcontractPoiSupportDataWrapperService wmEcontractPoiSupportDataWrapperService;
    @Autowired
    private WmEcontractPoiSLASupportDataWrapperService wmEcontractPoiSLASupportDataWrapperService;

    static final Map<String, String> subjectEstampMap = Maps.newHashMap();
    static {
        subjectEstampMap.put(ContractSignSubjectEnum.BEIJING_SANKUAI.getDesc(), PdfConstant.MT_SIGNKEY);
        subjectEstampMap.put(ContractSignSubjectEnum.SHANGHAI_ZHISONG.getDesc(), PdfConstant.MT_SH_SIGNKEY);
        subjectEstampMap.put(ContractSignSubjectEnum.SHENZHEN_SHOUKANG.getDesc(), PdfConstant.SZ_BS_JK_SIGNKEY);
    }

    @Override
    public List<PdfContentInfoBo> wrap(EcontractBatchContextBo contextBo)
            throws IllegalAccessException, WmCustomerException {

        LOG.info("客户批量门店生成数据 contextBo = {}", JSON.toJSONString(contextBo));

        EcontractTaskBo taskBo =
                WmEcontractContextUtil.selectByApplyType(contextBo, EcontractTaskApplyTypeEnum.BATCH_POI_GENERATE_PDF);
        EcontractBatchPoiInfoExtBo econtractBatchPoiInfoExtBo =
                JSON.parseObject(taskBo.getApplyContext(), EcontractBatchPoiInfoExtBo.class);
        if (econtractBatchPoiInfoExtBo == null) {
            return Lists.newArrayList();
        }

        // 组装数据
        List<PdfContentInfoBo> pdfInfoBoList = Lists.newArrayList();

        // 门店列表数据
        pdfInfoBoList.addAll(wmEcontractBatchPoiDataWrapperService.wrap(contextBo, taskBo, subjectEstampMap));

        // 有优惠申请书
        if (econtractBatchPoiInfoExtBo.getHasSupport()) {
            pdfInfoBoList.addAll(wmEcontractPoiSupportDataWrapperService.wrap(contextBo, taskBo, subjectEstampMap));
        }

        // 有sla
        if (econtractBatchPoiInfoExtBo.getHasSLASupport()) {
            pdfInfoBoList.addAll(wmEcontractPoiSLASupportDataWrapperService.wrap(contextBo, taskBo, subjectEstampMap));
        }

        return pdfInfoBoList;

    }

}

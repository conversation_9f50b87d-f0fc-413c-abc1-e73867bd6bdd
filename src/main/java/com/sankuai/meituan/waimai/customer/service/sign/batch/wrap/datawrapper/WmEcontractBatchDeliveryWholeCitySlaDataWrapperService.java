package com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper;

import com.sankuai.meituan.waimai.customer.constant.WmEcontractConstant;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryWholeCityInfoBo;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.aspect.SlaDataWrapper;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.customer.util.trans.WmPoiSpAreaBoUtil;
import com.sankuai.meituan.waimai.econtrct.client.domain.biz.EcontractContentBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataWrapperEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchDeliveryInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractDeliveryInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractWmPoiSpAreaBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;

@Service
@SlaDataWrapper(wrapperEnum = EcontractDataWrapperEnum.BATCH_DELIVERY_WHOLE_CITY)
public class WmEcontractBatchDeliveryWholeCitySlaDataWrapperService implements IWmEcontractSlaDataWrapperService{

    private static final Logger LOGGER = LoggerFactory.getLogger(WmEcontractBatchDeliveryWholeCitySlaDataWrapperService.class);

    public static final String SUPPORT_MARK = "support";

    @Override
    public List<EcontractContentBo> wrap(EcontractBatchContextBo contextBo)
            throws WmCustomerException {

        List<EcontractContentBo> result = Lists.newArrayList();

        EcontractTaskBo taskBo = WmEcontractContextUtil
                .selectByApplyType(contextBo, EcontractTaskApplyTypeEnum.BATCHPOIFEE);

        EcontractBatchDeliveryInfoBo econtractBatchDeliveryInfoBo = null;

        try{
            econtractBatchDeliveryInfoBo = JSON
                    .parseObject(taskBo.getApplyContext(), EcontractBatchDeliveryInfoBo.class);
        }catch(Exception e){
            LOGGER.warn("数据解析异常",e);
            return result;
        }

        if(econtractBatchDeliveryInfoBo == null){
            return result;
        }

        List<EcontractDeliveryInfoBo> econtractDeliveryInfoBoList = econtractBatchDeliveryInfoBo
                .getEcontractDeliveryInfoBoList();

        if(CollectionUtils.isEmpty(econtractDeliveryInfoBoList)){
            return result;
        }

        EcontractWmPoiSpAreaBo econtractWmPoiSpAreaBoTemp = null;
        EcontractDeliveryWholeCityInfoBo econtractDeliveryWholeCityInfoBoTemp = null;
        for(EcontractDeliveryInfoBo temp : econtractDeliveryInfoBoList) {
            econtractDeliveryWholeCityInfoBoTemp = temp.getEcontractDeliveryWholeCityInfoBo();
            if(econtractDeliveryWholeCityInfoBoTemp == null){
                continue;
            }
            if (SUPPORT_MARK.equals(econtractDeliveryWholeCityInfoBoTemp.getSupportSLA()) && StringUtils
                    .isNotEmpty(econtractDeliveryWholeCityInfoBoTemp.getDeliveryArea())) {
                econtractWmPoiSpAreaBoTemp =
                        JSONArray.parseObject(econtractDeliveryWholeCityInfoBoTemp.getDeliveryArea(), EcontractWmPoiSpAreaBo.class);
                if (econtractWmPoiSpAreaBoTemp == null) {
                    continue;
                }
                econtractWmPoiSpAreaBoTemp.setSlaText(WmEcontractConstant.DEFAULT_DELIVERY_SLA_WHOLE_CITY_DOCUMENT);
                result.add(WmPoiSpAreaBoUtil.transEcontractWmPoiSelfDeliveryAreaBo2EcontractContentBo(econtractWmPoiSpAreaBoTemp));
            }
        }
        return result;
    }
}

package com.sankuai.meituan.waimai.customer.service.sc.canteenstall.poiflow;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.customer.constant.sc.WmScLableEnum;
import com.sankuai.meituan.waimai.customer.dao.sc.WmCanteenMapper;
import com.sankuai.meituan.waimai.customer.dao.sc.WmScCanteenPoiHistoryMapper;
import com.sankuai.meituan.waimai.customer.dao.sc.WmScCanteenPoiLabelMapper;
import com.sankuai.meituan.waimai.customer.dao.sc.canteenstall.WmCanteenStallBindMapper;
import com.sankuai.meituan.waimai.customer.domain.sc.WmCanteenDB;
import com.sankuai.meituan.waimai.customer.domain.sc.WmScCanteenPoiHistoryDO;
import com.sankuai.meituan.waimai.customer.domain.sc.WmScCanteenPoiLabelDB;
import com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallBindDO;
import com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallWmPoiBindBO;
import com.sankuai.meituan.waimai.customer.service.sc.poi.WmScCanteenPoiAttributeService;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.CanteenPoiOperateSourceEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.CanteenPoiOperateTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.canteenstall.CanteenStallWmPoiBindStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 食堂档口门店绑定食堂相关服务
 * <AUTHOR>
 * @date 2024/05/31
 * @email <EMAIL>
 */
@Slf4j
@Service
public class WmCanteenStallPoiCanteenSerivice {

    @Autowired
    private WmScCanteenPoiLabelMapper wmScCanteenPoiLabelMapper;

    @Autowired
    private WmScCanteenPoiAttributeService wmScCanteenPoiAttributeService;

    @Autowired
    private WmCanteenMapper wmCanteenMapper;

    @Autowired
    private WmCanteenStallBindMapper wmCanteenStallBindMapper;

    @Autowired
    private WmScCanteenPoiHistoryMapper wmScCanteenPoiHistoryMapper;

    /**
     * 根据食堂ID和门店ID查询
     * @param canteenPrimaryId 食堂主键ID
     * @param wmPoiId 门店ID
     * @return 食堂门店标签关系列表
     */
    public List<WmScCanteenPoiLabelDB> getLabelListByCanteenPrimaryIdAndWmPoiId(Integer canteenPrimaryId, Long wmPoiId) {
        List<WmScCanteenPoiLabelDB> poiLabelDBList = wmScCanteenPoiLabelMapper.selectByCanteenPrimaryIdAndWmPoiId(
                canteenPrimaryId,
                wmPoiId
        );

        log.info("[WmCanteenStallPoiCanteenSerivice.getLabelListByCanteenPrimaryIdAndWmPoiId] canteenPrimaryId = {}, wmPoiId = {}, poiLabelDBList = {}",
                canteenPrimaryId, wmPoiId, JSONObject.toJSONString(poiLabelDBList));
        return poiLabelDBList;
    }

    /**
     * 删除本地记录的门店标签关系
     * @param poiLabelDBList 门店标签关联关系记录
     */
    public void deleteCanteenPoiLabel(List<WmScCanteenPoiLabelDB> poiLabelDBList) {
        log.info("[WmCanteenStallPoiCanteenSerivice.deleteCanteenPoiLabel] input param: poiLabelDBList = {}",
                JSONObject.toJSONString(poiLabelDBList));
        if (CollectionUtils.isEmpty(poiLabelDBList)) {
            return;
        }

        for (WmScCanteenPoiLabelDB labelDB : poiLabelDBList) {
            wmScCanteenPoiLabelMapper.deleteByPrimaryKey(labelDB.getId());
        }
    }

    /**
     * 本地记录门店A类和B类标签关系
     * @param canteenPrimaryId 食堂主键ID
     * @param wmPoiId 门店ID
     * @param labelIdList 标签ID列表
     */
    public void addCanteenPoiLabel(Integer canteenPrimaryId, Long wmPoiId, List<Long> labelIdList) {
        log.info("[WmCanteenStallPoiCanteenSerivice.addCanteenPoiLabel] input param: canteenPrimaryId = {}, wmPoiId = {}, labelIdList = {}",
                canteenPrimaryId, wmPoiId, JSONObject.toJSONString(labelIdList));

        for (Long labelId : labelIdList) {
            WmScCanteenPoiLabelDB labelDB = new WmScCanteenPoiLabelDB();
            labelDB.setCanteenId(canteenPrimaryId);
            labelDB.setWmPoiId(wmPoiId);
            labelDB.setLabelId(labelId);
            labelDB.setLabelType(WmScLableEnum.getLabelType(labelId));
            wmScCanteenPoiLabelMapper.insertSelective(labelDB);
        }
    }



    public void insertCanteenPoiHistory(Integer canteenPrimaryId, Long wmPoiId, Integer userId, String userName, Integer operateType) {
        log.info("[WmCanteenStallPoiCanteenSerivice.insertCanteenPoiHistory] input param: canteenPrimaryId = {}, wmPoiId = {}, userId = {}, userName = {}",
                canteenPrimaryId, wmPoiId, userId, userName);
        WmScCanteenPoiHistoryDO historyDO = new WmScCanteenPoiHistoryDO();
        historyDO.setCanteenIdTo(canteenPrimaryId);
        historyDO.setWmPoiId(wmPoiId);
        historyDO.setUserId(userId);
        historyDO.setUserName(userName);
        historyDO.setOperateType(operateType);
        historyDO.setOperateSource(CanteenPoiOperateSourceEnum.CANTEEN_POI_TASK.getCode());
        wmScCanteenPoiHistoryMapper.insertSelective(historyDO);
        log.info("[WmCanteenStallPoiCanteenSerivice.insertCanteenPoiHistory] historyDO = {}", JSONObject.toJSONString(historyDO));
    }


    /**
     * 删除食堂门店关联属性
     * @param canteenPrimaryId 食堂主键ID
     * @param wmPoiId 门店ID
     */
    public void deleteCanteenPoiAttribute(Integer canteenPrimaryId, Long wmPoiId) {
        log.info("[WmCanteenStallPoiCanteenSerivice.deleteCanteenPoiAttribute] input param: canteenPrimaryId = {} wmPoiId = {}",
                canteenPrimaryId, wmPoiId);
        wmScCanteenPoiAttributeService.deleteCanteenPoiAttribute(canteenPrimaryId, wmPoiId);
    }

    /**
     * 添加/更新食堂门店关联属性
     * @param canteenPrimaryId 食堂主键ID
     * @param wmPoiId 门店ID
     */
    public void upsertCanteenPoiAttribute(Integer canteenPrimaryId, Long wmPoiId) {
        log.info("[WmCanteenStallPoiCanteenSerivice.deleteCanteenPoiAttribute] input param: canteenPrimaryId = {} wmPoiId = {}",
                canteenPrimaryId, wmPoiId);
        WmCanteenDB wmCanteenDB = wmCanteenMapper.selectCanteenById(canteenPrimaryId);
        List<Long> wmPoiIdList = new ArrayList<>();
        wmPoiIdList.add(wmPoiId);
        wmScCanteenPoiAttributeService.saveCanteenPoiAttribute(wmCanteenDB, wmPoiIdList);
    }


    /**
     * 更新食堂合作档口数量
     * @param canteenPrimaryId 食堂主键ID
     */
    public void updateCanteenStoreNum(Integer canteenPrimaryId) {
        log.info("[WmCanteenStallPoiCanteenSerivice.updateCanteenStoreNum] canteenPrimaryId = {}", canteenPrimaryId);
        // 1. 获取食堂关联的门店ID列表
        List<Long> wmPoiIdList = wmScCanteenPoiAttributeService.getWmPoiIdListByCanteenPrimaryIdByMaster(canteenPrimaryId);
        // 2. 查询食堂信息
        WmCanteenDB wmCanteenDB = wmCanteenMapper.selectCanteenById(canteenPrimaryId);
        // 3. 检查食堂门店数量是否需要更新
        if (wmCanteenDB.getStoreNum().equals(wmPoiIdList.size())) {
            // 3.1 如果门店数量未变，记录日志并返回
            log.info("[WmCanteenStallPoiCanteenSerivice.updateCanteenStoreNum] store num is the same, return. wmCanteenDB = {}",
                    JSONObject.toJSONString(wmCanteenDB));
            return;
        }
        // 4. 更新食堂门店数量
        wmCanteenDB.setStoreNum(wmPoiIdList.size());
        log.info("[WmCanteenStallPoiCanteenSerivice.updateCanteenStoreNum] wmCanteenDB = {}", JSONObject.toJSONString(wmCanteenDB));
        // 4.1 执行数据库更新操作
        wmCanteenMapper.updateCanteen(wmCanteenDB);
    }

    /**
     * 更新外卖门店ID和外卖门店绑定状态为"绑定成功"
     * @param wmPoiBindBO wmPoiBindBO
     */
    public void updateWmPoiIdAndWmPoiBindStatusByBindSuccess(WmCanteenStallWmPoiBindBO wmPoiBindBO) {
        WmCanteenStallBindDO bindDO = wmPoiBindBO.getBindDO();
        bindDO.setWmPoiId(wmPoiBindBO.getWmPoiId());
        bindDO.setWmPoiBindStatus((int) CanteenStallWmPoiBindStatusEnum.BIND_SUCCESS.getType());
        wmCanteenStallBindMapper.updateByPrimaryKeySelective(bindDO);
        log.info("[WmCanteenStallPoiCanteenSerivice.updateWmPoiIdAndWmPoiBindStatusByBindSuccess] bindDO = {}", JSONObject.toJSONString(bindDO));
    }

    /**
     * 更新外卖门店ID和外卖门店绑定状态为"未绑定"
     * @param bindDO bindDO
     */
    public void updateWmPoiBindStatusByUnBind(WmCanteenStallBindDO bindDO) {
        bindDO.setWmPoiBindStatus((int) CanteenStallWmPoiBindStatusEnum.UNBIND.getType());
        wmCanteenStallBindMapper.updateByPrimaryKeySelective(bindDO);
        log.info("[WmCanteenStallPoiCanteenSerivice.updateWmPoiIdAndWmPoiBindStatusByUnBind] bindDO = {}", JSONObject.toJSONString(bindDO));
    }



}

package com.sankuai.meituan.waimai.customer.settle.ddd.domain.businessdataquery.atom.service;

import com.sankuai.meituan.waimai.customer.settle.ddd.domain.businessdataquery.aggre.WmSettleBusinessQueryContext;
import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettleStatusBo;
import java.util.List;
import java.util.Map;

/**
 * 业务数据查询-原子服务
 */
public interface WmSettleBusinessQueryAtomService {

    Map<Long, WmSettleStatusBo> batchGetWmSettleStatus(WmSettleBusinessQueryContext context);

}

package com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.util.DateUtil;
import com.sankuai.meituan.waimai.customer.aspect.DataWrapper;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.util.business.WmEcontractContextUtil;
import com.sankuai.meituan.waimai.econtrct.client.constants.PdfConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataWrapperEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * @description: 四轮履约补充协议creat_pdf
 * @author: liuyunjie05
 * @create: 2023/11/1 14:37
 */
@Slf4j
@Service
@DataWrapper(wrapperEnum = EcontractDataWrapperEnum.FOUR_WHEEL_PERFORMANCE_SUPPLEMENT_AGREEMENT)
public class WmEcontractFourWheelPerfSupplementWrapperService implements IWmEcontractDataWrapperService{
    @Override
    public List<PdfContentInfoBo> wrap(EcontractBatchContextBo contextBo) throws IllegalAccessException, WmCustomerException {
        log.info("WmEcontractFourWheelPerfSupplementWrapperService#wrap");
        EcontractTaskBo taskBo = WmEcontractContextUtil.selectByApplyType(contextBo, EcontractTaskApplyTypeEnum.FOUR_WHEEL_PERFORMANCE_SUPPLEMENT_AGREEMENT);
        PdfContentInfoBo pdfInfoBo = new PdfContentInfoBo();
        pdfInfoBo.setPdfTemplateId(MccConfig.getFourWheelPerfSupplementTemplateId());
        pdfInfoBo.setPdfTemplateVersion(MccConfig.getFourWheelPerfSupplementTemplateVersion());
        pdfInfoBo.setPdfMetaContent(generatePdfObject(taskBo));
        pdfInfoBo.setPdfBizContent(Lists.newArrayList());
        return Lists.newArrayList(pdfInfoBo);
    }

    private Map<String, String> generatePdfObject(EcontractTaskBo taskBo) {
        log.info("WmEcontractFourWheelPerfSupplementWrapperService#generatePdfObject, taskBo = {}", JSON.toJSONString(taskBo));
        String signTime = DateUtil.secondsToString(DateUtil.unixTime());
        JSONObject jsonObject = JSON.parseObject(taskBo.getApplyContext());
        Map<String, String> pdfMap = Maps.newHashMap();
        pdfMap.put("partAStampName", jsonObject.getString("partAStampName"));
        pdfMap.put("partASignTime", StringUtils.defaultIfEmpty(signTime, StringUtils.EMPTY));
        pdfMap.put("partAEstamp", PdfConstant.POI_SIGNKEY);
        pdfMap.put("partBEstamp", PdfConstant.CDZH_SIGN_KET);
        log.info("WmEcontractFourWheelPerfSupplementWrapperService#generatePdfObject, pdfMap = {}", JSON.toJSONString(pdfMap));
        return pdfMap;
    }

}

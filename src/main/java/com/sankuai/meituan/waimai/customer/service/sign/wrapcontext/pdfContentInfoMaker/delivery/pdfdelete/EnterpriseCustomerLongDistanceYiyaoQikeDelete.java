package com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.pdfContentInfoMaker.delivery.pdfdelete;

import com.sankuai.meituan.waimai.customer.constant.sign.DeliveryPdfDataTypeEnum;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.PdfDeleter;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import static com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignTemplateConstant.TAB_DELIVERY_COMPANY_CUSTOMER_LONG_DISTANCE;

/**
 * 判断医药企客远距离合同参数为空，移除该合同
 *
 * @Author: limingxuan
 * @Date: 2024-02-18
 */
@Slf4j
@Service
@PdfDeleter(deliveryPdfDataType = DeliveryPdfDataTypeEnum.ENTERPRISE_CUSTOMER_LONG_DISTANCE_YIYAO_QIKE_FEEMODE)
public class EnterpriseCustomerLongDistanceYiyaoQikeDelete implements DeliveryPdfDelete {
    @Override
    public void delete(Map<String, Collection<SignTemplateEnum>> tabPdfMap, Map<String, List<String>> pdfDataMap) {
        if (CollectionUtils.isEmpty(pdfDataMap.get(DeliveryPdfDataTypeEnum.ENTERPRISE_CUSTOMER_LONG_DISTANCE_YIYAO_QIKE_FEEMODE.getName()))) {
            Collection<SignTemplateEnum> tabList = tabPdfMap.get(TAB_DELIVERY_COMPANY_CUSTOMER_LONG_DISTANCE);
            if (CollectionUtils.isNotEmpty(tabList)) {
                log.info("companyCustomerLongDistanceYiyaoQikeDelete remove ENTERPRISE_CUSTOMER_LONG_DISTANCE_YIYAO_QIKE_FEEMODE");
                tabList.removeIf(value -> value.equals(SignTemplateEnum.ENTERPRISE_CUSTOMER_LONG_DISTANCE_YIYAO_QIKE_FEEMODE));
            }
        }
    }
}

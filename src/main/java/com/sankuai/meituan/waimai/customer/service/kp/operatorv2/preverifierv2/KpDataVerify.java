package com.sankuai.meituan.waimai.customer.service.kp.operatorv2.preverifierv2;

import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.service.gray.WmCustomerGrayService;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpService;
import com.sankuai.meituan.waimai.customer.service.kp.preverifier.KpDataPreVerify;
import com.sankuai.meituan.waimai.customer.util.ThrowUtil;
import com.sankuai.meituan.waimai.thrift.customer.constant.*;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.KpVersionEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * Author: 窦旭蒙
 * MIS: douxumeng
 * Date: 2020-07-01 20:15
 * Email: <EMAIL>
 * Desc:
 */
@Service
public class KpDataVerify extends KpPreverify {

    @Autowired
    private KpDataPreVerify kpDataPreVerify;

    @Autowired
    private WmCustomerGrayService wmCustomerGrayService;

    @Autowired
    private WmCustomerKpService wmCustomerKpService;

    @Override
    public Object verify(WmCustomerDB wmCustomer, List<WmCustomerKp> oldCustomerKpList, WmCustomerKp insertKp, WmCustomerKp updateKp, WmCustomerKp deleteKp, int uid, String uname) throws WmCustomerException {
        if (insertKp != null) {
            //客户是否处于特批流程校验
            checkCustomerSpecialAuditFlow(wmCustomer, uid);
            kpDataVerifyCommonLogic(insertKp, wmCustomer);
        } else if (updateKp != null) {
            kpDataVerifyCommonLogic(updateKp, wmCustomer);
        } else if (deleteKp != null) {
            kpDataVerifyCommonLogic(deleteKp, wmCustomer);
        } else {
            ThrowUtil.throwClientError("KP信息为空");
        }
        return new Object();// 暂且返回object，方便后续扩展

    }

    /**
     * 客户是否处于特批流程校验
     *
     * @param wmCustomer
     * @param uid
     * @throws WmCustomerException
     */
    private void checkCustomerSpecialAuditFlow(WmCustomerDB wmCustomer, Integer uid) throws WmCustomerException {
        //非外卖单店客户类型或非个人资质则直接过滤
        if ((wmCustomer.getCustomerRealType() != null
                && wmCustomer.getCustomerRealType() != CustomerRealTypeEnum.DANDIAN.getValue())
                || (wmCustomer.getCustomerType() != null &&
                wmCustomer.getCustomerType() != CustomerType.CUSTOMER_TYPE_IDCARD.getCode())) {
            return;
        }
        //命中灰度需要添加客户状态卡控：待发起特批、特批中、特批驳回不允许绑定门店
        Integer auditStatus = wmCustomer.getAuditStatus();
        if (CustomerConstants.notAllowBindCustomerAuditStatusList.contains(auditStatus)) {
            String errTips = String.format("客户处于“%s”状态，不支持新增KP", CustomerAuditStatus.getByCode(auditStatus).getDesc());
            ThrowUtil.throwClientError(errTips);
        }
    }

    private void kpDataVerifyCommonLogic(WmCustomerKp kp, WmCustomerDB wmCustomerDB) throws WmCustomerException {
        // v1版本的老逻辑复用之前的校验逻辑，新增的kp类型校验逻辑补充至此
        kpDataPreVerify.verifyData(kp, wmCustomerDB);
        // 新增：运营经理kp校验
        KpTypeEnum kpTypeEnum = KpTypeEnum.getByType(kp.getKpType());
        if (KpTypeEnum.OPMANAGER.equals(kpTypeEnum)) {
            CertTypeEnum certTypeEnum = CertTypeEnum.getByType(kp.getCertType());
            if (certTypeEnum == null) {
                ThrowUtil.throwClientError("证件类型不能为空");
            }
            if (!CertTypeEnum.ID_CARD.equals(certTypeEnum)) {
                ThrowUtil.throwClientError("证件类型需选择身份证");
            }
            if (StringUtil.isBlank(kp.getCertNumber())) {
                ThrowUtil.throwClientError("证件编号不能为空");
            }
            // 运营经理签约类型
            if (StringUtil.isBlank(kp.getSignTaskType())) {
                ThrowUtil.throwClientError("可签约任务不能为空");
            }
            List<String> signTaskType = Arrays.asList(kp.getSignTaskType().split(","));
            for (String type : signTaskType) {
                if (!(type.equals(KpSignTaskTypeEnum.DELIVERY.getType())
                        || type.equals(KpSignTaskTypeEnum.CUSTOMER_POI_UNBIND.getType()))) {
                    ThrowUtil.throwClientError("非法的可签约任务类型");
                }
            }
        }

        //新流程版本，KP类型法人只能选择身份证
        if (KpTypeEnum.LEGAL.equals(kpTypeEnum) && kp.getVersion() != null
                && kp.getVersion() == KpVersionEnum.V3.getCode()
                && CertTypeEnum.ID_CARD.getType() != kp.getCertType()) {
            ThrowUtil.throwClientError("证件类型只支持身份证");
        }
    }
}

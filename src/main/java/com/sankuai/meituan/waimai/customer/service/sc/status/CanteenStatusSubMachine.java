package com.sankuai.meituan.waimai.customer.service.sc.status;

import com.sankuai.meituan.waimai.customer.domain.sc.WmCanteenDB;
import com.sankuai.meituan.waimai.customer.domain.sc.WmScCanteenAuditDO;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.CanteenAuditDiffBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.DiffField;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/11/24 16:21
 */
public interface CanteenStatusSubMachine {

    /**
     * 流转状态
     * @param canteenDB 食堂主表对象
     * @param wmScCanteenAuditDO 食堂审核表对象
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    void changeStatus(WmCanteenDB canteenDB, WmScCanteenAuditDO wmScCanteenAuditDO) throws WmSchCantException;

    /**
     * 获取审核变更内容
     * @param canteenDB 食堂主表对象
     * @return CanteenAuditDiffBo
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    CanteenAuditDiffBo getCanteenAuditDiffBo(WmCanteenDB canteenDB) throws WmSchCantException;

    /**
     * 获取食堂审核diff field list
     * @param canteenDB canteenDB
     * @return List<DiffField>
     */
    List<DiffField> getCanteenAuditDiffList(WmCanteenDB canteenDB);

    /**
     * 判断食堂是否是审核中
     * @param canteenDB 食堂
     */
    boolean isAuditing(WmCanteenDB canteenDB);

    /**
     * 判断是否是驳回
     * @param canteenDB 食堂
     * @return
     */
    boolean isReject(WmCanteenDB canteenDB);

    /**
     * 判断是否是审核通过
     * @param canteenDB
     * @return
     */
    boolean isAudited(WmCanteenDB canteenDB);

    /**
     * 判断是否是新增审核中
     * @param canteenDB 食堂
     * @return
     */
    boolean isInsertAuditing(WmCanteenDB canteenDB);

    /**
     * 判断是否是新增驳回
     * @param canteenDB 食堂
     * @return
     */
    boolean isInsertReject(WmCanteenDB canteenDB);

}

package com.sankuai.meituan.waimai.customer.service.sign.data;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.meituan.mtrace.thread.pool.TraceExecutors;
import com.sankuai.meituan.waimai.customer.adapter.DeliveryContractAdapter;
import com.sankuai.meituan.waimai.customer.adapter.WmLogisticsContractThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.service.sign.data.util.LogisticsFeeDataQueryUtil;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.delivery.EcontractDeliveryBaseInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.delivery.EcontractDeliveryFeeInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.delivery.batch.EcontractBatchDeliveryBaseAndFeeInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.delivery.single.EcontractSingleDeliveryBaseAndFeeInfoBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description: 查询拼好饭签约数据（技术）
 * @author: zhangyuanhao02
 * @create: 2024/12/10 09:57
 */
@Slf4j
@Service
public class PhfLogisticsFeeDataQueryHandler implements EcontractDataQueryHandler<EcontractDeliveryInfoBo> {

    private static final String SUPPORT_MARK = "support";

    @Autowired
    private WmLogisticsContractThriftServiceAdapter wmLogisticsContractThriftServiceAdapter;

    @Autowired
    private DeliveryContractAdapter deliveryContractAdapter;

    // todo zyh 线程池参数
    private static final ExecutorService executorService = TraceExecutors.getTraceExecutorService(new ThreadPoolExecutor(
            10, 32,
            5L, TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<>(1000),
            new ThreadFactoryBuilder().setNameFormat("phf-fee-data-query-pool-%d").build(),
            new ThreadPoolExecutor.AbortPolicy()
    ));

    @Override
    public EcontractDataSourceEnum sorceEnum() {
        return EcontractDataSourceEnum.PHF_LOGISTICS_FEE;
    }

    @Override
    public Map<Long, EcontractDeliveryInfoBo> queryData(Map<EcontractDataSourceEnum, List<EcontractDataPoiBizBo>> map, long manualBatchId) throws WmCustomerException {
        throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "方法未实现");
    }

    @Override
    public void queryAndAssembleData(Map<EcontractDataSourceEnum, EcontractDataSourceBo> map, long manualBatchId, Map<Long, EcontractDeliveryInfoBo> resultMap) throws WmCustomerException {
        log.info("#PhfLogusticsFeeDataQueryHandler queryData request map:{}, manualBatchId:{}", JSON.toJSONString(map), manualBatchId);
        // 校验对应来源的门店数据是否正确
        EcontractDataSourceBo dataSourceBo = map.get(EcontractDataSourceEnum.PHF_LOGISTICS_FEE);
        if (Objects.isNull(dataSourceBo) || CollectionUtils.isEmpty(dataSourceBo.getWmPoiIdAndBizIdList())) {
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "技术服务费数据来源无对应门店数据");
        }

        List<EcontractDataPoiBizBo> wmPoiIdAndBizIdList = dataSourceBo.getWmPoiIdAndBizIdList();
        boolean needAssembleWmPoiGroup = wmPoiIdAndBizIdList.size() == 1 && CollectionUtils.isEmpty(dataSourceBo.getWmPoiIdGroupList());
        if (needAssembleWmPoiGroup) {
            LogisticsFeeDataQueryUtil.assembleWmPoiIdGroupList(dataSourceBo);
        }

        if (CollectionUtils.isEmpty(dataSourceBo.getWmPoiIdGroupList())) {
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "技术服务费数据来源无对应门店分组数据");
        }

        // 组装返回数据
        EcontractBatchDeliveryBaseAndFeeInfoBo batchBaseAndFeeInfoBo = queryByGroup(dataSourceBo);
        Map<Long, EcontractSingleDeliveryBaseAndFeeInfoBo> batchBaseAndFeeInfoMap = batchBaseAndFeeInfoBo.getBatchBaseAndFeeInfoMap();

        // key为门店ID
        for (Map.Entry<Long, EcontractSingleDeliveryBaseAndFeeInfoBo> entry : batchBaseAndFeeInfoMap.entrySet()) {
            EcontractDeliveryInfoBo deliveryInfoBo = new EcontractDeliveryInfoBo();
            if (resultMap.containsKey(entry.getKey())) {
                deliveryInfoBo = resultMap.get(entry.getKey());
            }

            // 组装基础信息和技术服务费信息
            assemblyBaseAndTechInfoBo(entry.getValue(), deliveryInfoBo);
            log.info("queryData deliveryInfoBo:{}",JSON.toJSONString(deliveryInfoBo));
            resultMap.put(entry.getKey(), deliveryInfoBo);
        }

        log.info("#PhfLogusticsFeeDataQueryHandler queryData resultMap:{}",JSON.toJSONString(resultMap));
    }

    /**
     * 分组查询技术服务费和基础信息
     * @param dataSourceBo
     * @return
     */
    public EcontractBatchDeliveryBaseAndFeeInfoBo queryByGroup(EcontractDataSourceBo dataSourceBo) throws WmCustomerException {
        log.info("queryByGroup dataSourceBo:{}",JSON.toJSONString(dataSourceBo));
        // 获取分组的poibizBo
        List<List<EcontractDataPoiBizBo>> poiBizBoGroupList = LogisticsFeeDataQueryUtil.extractPoiBizBoGroupList(
                dataSourceBo.getWmPoiIdGroupList(), dataSourceBo.getWmPoiIdAndBizIdList());

        Map<Long, EcontractSingleDeliveryBaseAndFeeInfoBo> allBatchBaseAndFeeInfoMap = new HashMap<>();

        // 分组查询
        Transaction transaction = Cat.newTransaction("phf.sign", "queryLogisticsFeeData");
        try {
            List<Future> futureList = new ArrayList<>();
            for (List<EcontractDataPoiBizBo> poiBizBoList : poiBizBoGroupList) {
                futureList.add(executorService.submit(() -> wmLogisticsContractThriftServiceAdapter.getTechFeeSignDataMultiPoiWithRetry(
                        poiBizBoList, EcontractTaskApplyTypeEnum.PHF_DELIVERY)));
            }

            int maxDelayTime = MccConfig.getQueryPhfLogisticsFeeDataMaxDelayTime();
            List<String> queryResultList = new ArrayList<>();
            for (Future future : futureList) {
                queryResultList.add((String) future.get(maxDelayTime, TimeUnit.SECONDS));
            }

            transaction.setSuccessStatus();
            for (String data : queryResultList) {
                // 判断签约数据是否合法
                EcontractBatchDeliveryBaseAndFeeInfoBo batchBaseAndFeeInfoBo =  JSON.parseObject(data, EcontractBatchDeliveryBaseAndFeeInfoBo.class);
                if (batchBaseAndFeeInfoBo == null || MapUtils.isEmpty(batchBaseAndFeeInfoBo.getBatchBaseAndFeeInfoMap())) {
                    throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "获取到的技术服务费签约数据不合法");
                }

                Map<Long, EcontractSingleDeliveryBaseAndFeeInfoBo> batchBaseAndFeeInfoMap = batchBaseAndFeeInfoBo.getBatchBaseAndFeeInfoMap();
                allBatchBaseAndFeeInfoMap.putAll(batchBaseAndFeeInfoMap);
            }
        } catch (Exception e) {
            transaction.setStatus(e);
            log.error("PhfLogisticsFeeDataQueryHandler#queryByGroup 查询拼好饭技术服务费数据异常", e);
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "查询拼好饭技术服务费数据异常");
        } finally {
            transaction.complete();
        }

        // 数据合并
        EcontractBatchDeliveryBaseAndFeeInfoBo allBatchBaseAndFeeInfoBo = new EcontractBatchDeliveryBaseAndFeeInfoBo();
        allBatchBaseAndFeeInfoBo.setBatchBaseAndFeeInfoMap(allBatchBaseAndFeeInfoMap);

        log.info("queryByGroup allBatchBaseAndFeeInfoMap:{}", JSONObject.toJSONString(allBatchBaseAndFeeInfoBo));
        return allBatchBaseAndFeeInfoBo;
    }



    /**
     * 组装基础信息和技术服务费信息
     * @param baseAndFeeInfoBo
     * @param econtractDeliveryInfoBo
     * @return
     * @throws WmCustomerException
     */
    private void assemblyBaseAndTechInfoBo(EcontractSingleDeliveryBaseAndFeeInfoBo baseAndFeeInfoBo, EcontractDeliveryInfoBo econtractDeliveryInfoBo) throws WmCustomerException {
        EcontractDeliveryBaseInfoBo baseInfo = baseAndFeeInfoBo.getBaseInfo();

        // 构建基础信息
        BeanUtils.copyProperties(baseInfo, econtractDeliveryInfoBo);
        EcontractDeliveryPhfInfoBo phfInfoBo = new EcontractDeliveryPhfInfoBo();
        if (Objects.nonNull(econtractDeliveryInfoBo.getEcontractDeliveryPhfInfoBo())) {
            phfInfoBo = econtractDeliveryInfoBo.getEcontractDeliveryPhfInfoBo();
        }

        buildPhfBaseInfo(phfInfoBo, baseInfo);

        // 构建技术服务费信息
        buildPhfTechInfo(phfInfoBo, baseAndFeeInfoBo);

        econtractDeliveryInfoBo.setEcontractDeliveryPhfInfoBo(phfInfoBo);
    }

    /**
     * 构建拼好饭技术服务费信息
     * @param phfInfoBo
     * @param baseAndFeeInfoBo
     */
    private void buildPhfTechInfo(EcontractDeliveryPhfInfoBo phfInfoBo, EcontractSingleDeliveryBaseAndFeeInfoBo baseAndFeeInfoBo) {
        Map<Integer, EcontractDeliveryFeeInfoBo> feeInfoMap = baseAndFeeInfoBo.getFeeInfoMap();

        Map<Integer, EcontractDeliveryPhfTechInfoBo> phfTechInfoMap = new HashMap<>();

        // 设置保底金额
        EcontractDeliveryBaseInfoBo baseInfo = baseAndFeeInfoBo.getBaseInfo();
        phfInfoBo.setSpecialPrice(baseInfo.getSpecialPrice());

        Set<Integer> deliveryTypeCodes = feeInfoMap.keySet();
        for (Integer deliveryTypeCode : deliveryTypeCodes) {
            EcontractDeliveryFeeInfoBo feeInfoBo = feeInfoMap.get(deliveryTypeCode);

            EcontractDeliveryPhfTechInfoBo phfTechInfoBo = new EcontractDeliveryPhfTechInfoBo();
            // 设置基础费用
            phfTechInfoBo.setBaseFee(feeInfoBo.getBaseFee());
            // 设置基础补贴金额
            phfTechInfoBo.setBaseSubsidy(feeInfoBo.getBaseSubsidy());

            phfTechInfoMap.put(deliveryTypeCode, phfTechInfoBo);
        }

        phfInfoBo.setTechInfo(phfTechInfoMap);
    }

    /**
     * 构建拼好饭基础信息
     * @param phfInfoBo
     * @param baseInfo
     */
    private void buildPhfBaseInfo(EcontractDeliveryPhfInfoBo phfInfoBo, EcontractDeliveryBaseInfoBo baseInfo) throws WmCustomerException {
        phfInfoBo.setWmPoiId(baseInfo.getWmPoiId());
        phfInfoBo.setWmPoiName(baseInfo.getPoiName());
        Set<EcontractTaskApplySubTypeEnum> subTypeEnumSet = new HashSet<>();
        // 仅支持一种发起类型
        if (SUPPORT_MARK.equals(baseInfo.getSupportVirtualPhf())) {
            // 拼好送基础版
            subTypeEnumSet.add(EcontractTaskApplySubTypeEnum.PHF_VIRTUAL);
        }
        if (SUPPORT_MARK.equals(baseInfo.getSupportVirtualPhfSelf())) {
            // 拼好送基础版-自配
            subTypeEnumSet.add(EcontractTaskApplySubTypeEnum.PHF_VIRTUAL_SELF);
        }
        if (SUPPORT_MARK.equals(baseInfo.getSupportPhf())) {
            // 拼好送正式版
            subTypeEnumSet.add(EcontractTaskApplySubTypeEnum.PHF_FORMAL);
        }

        if (SUPPORT_MARK.equals(baseInfo.getSupportPhfSelf())) {
            // 拼好送正式版-自配
            subTypeEnumSet.add(EcontractTaskApplySubTypeEnum.PHF_FORMAL_SELF);
        }

        if (SUPPORT_MARK.equals(baseInfo.getSupportJhs())) {
            // 聚好送
            subTypeEnumSet.add(EcontractTaskApplySubTypeEnum.JHS);
        }

        if (SUPPORT_MARK.equals(baseInfo.getSupportAgentSpecialPrice())) {
            // 代理商保底金额
            subTypeEnumSet.add(EcontractTaskApplySubTypeEnum.PHF_AGENT_SPECIAL_PRICE);
        }

        if (CollectionUtils.isEmpty(subTypeEnumSet) || subTypeEnumSet.size() > 1) {
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "phf未知的合同发起类型");
        }

        EcontractTaskApplySubTypeEnum typeEnum = subTypeEnumSet.stream().findFirst().get();
        phfInfoBo.setSubTypeEnum(typeEnum);
    }

}

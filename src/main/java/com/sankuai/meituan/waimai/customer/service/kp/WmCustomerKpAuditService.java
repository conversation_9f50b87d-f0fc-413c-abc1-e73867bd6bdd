package com.sankuai.meituan.waimai.customer.service.kp;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.util.MetricHelper;
import com.dianping.cat.Cat;
import com.google.common.base.Splitter;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.customer.aspect.TairLock;
import com.sankuai.meituan.waimai.customer.bo.WmPoiDomain;
import com.sankuai.meituan.waimai.customer.constant.TairLockGroup;
import com.sankuai.meituan.waimai.customer.constant.WmCustomerConstant;
import com.sankuai.meituan.waimai.customer.constant.customer.MetricConstant;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerKpAuditMapper;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerKpDBMapper;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerKpTempDBMapper;
import com.sankuai.meituan.waimai.customer.ddd.adapter.WmPoiQueryAdapter;
import com.sankuai.meituan.waimai.customer.ddd.domain.customer.aggre.WmCustomerPoiAggre;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerKpAudit;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerKpPoiDB;
import com.sankuai.meituan.waimai.customer.mq.service.MafkaMessageSendManager;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPlatformDataParseService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerSensitiveWordsService;
import com.sankuai.meituan.waimai.customer.service.gray.WmCustomerGrayService;
import com.sankuai.meituan.waimai.customer.service.kp.dboperator.KpDBOperate;
import com.sankuai.meituan.waimai.customer.service.kp.operatorv2.dboperatorv2.SignerDBOperator;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.ability.KpSignerFlowAbility;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.context.KpSignerStatusMachineContext;
import com.sankuai.meituan.waimai.customer.service.sign.WmEcontractSignBzService;
import com.sankuai.meituan.waimai.customer.service.version.VersionCheckUtil;
import com.sankuai.meituan.waimai.customer.util.DateUtils;
import com.sankuai.meituan.waimai.customer.util.ThrowUtil;
import com.sankuai.meituan.waimai.poisearch.thrift.domain.WmPoiSearchField;
import com.sankuai.meituan.waimai.poisearch.thrift.domain.WmPoiSearchParam;
import com.sankuai.meituan.waimai.poisearch.thrift.domain.WmPoiSearchResult;
import com.sankuai.meituan.waimai.poisearch.thrift.service.WmPoiSearchThriftService;
import com.sankuai.meituan.waimai.thrift.bo.WmCustomerDiffCellBo;
import com.sankuai.meituan.waimai.thrift.constants.WmPoiFieldQueryConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.*;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.CertNumberModifyEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.HaveAgentAuthEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.KpVersionEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.LongResult;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKpTemp;
import com.sankuai.meituan.waimai.thrift.customer.domain.mq.CustomerMQBody;
import com.sankuai.meituan.waimai.thrift.customer.domain.mq.CustomerMQEventEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.*;
import com.sankuai.meituan.waimai.thrift.customer.service.WmEcontractSignThriftService;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import com.sankuai.meituan.waimai.thrift.service.WmPoiQueryThriftService;
import com.sankuai.meituan.waimai.thrift.util.DiffUtil;
import com.sankuai.meituan.waimai.thrift.util.ObjectUtil;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.sankuai.meituan.waimai.thrift.customer.constant.KpConstants.*;

/**
 * 客户KP审核相关实现
 *
 * <AUTHOR>
 */
@Service
public class WmCustomerKpAuditService {

    private static final Logger LOGGER = LoggerFactory.getLogger(WmCustomerKpAuditService.class);

    @Autowired
    private WmCustomerKpAuditMapper wmCustomerKpAuditMapper;

    @Autowired
    private WmCustomerKpDBMapper wmCustomerKpDBMapper;

    @Autowired
    private WmCustomerKpTempDBMapper wmCustomerKpTempDBMapper;

    @Autowired
    private WmCustomerKpLogService wmCustomerKpLogService;

    @Resource
    private WmEcontractSignBzService wmEcontractSignBzService;

    @Autowired
    private KpDBOperate kpDBOperate;

    @Autowired
    private DifferentCustomerKpService differentCustomerKpService;

    @Autowired
    private MafkaMessageSendManager mafkaMessageSendManager;

    @Autowired
    private WmPoiSearchThriftService.Iface wmPoiSearchThriftService;

    @Autowired
    private WmPoiQueryThriftService.Iface wmPoiQueryThriftService;

    @Autowired
    private WmCustomerKpPoiService wmCustomerKpPoiService;

    @Autowired
    private WmPoiQueryAdapter wmPoiQueryAdapter;

    @Autowired
    private WmCustomerSensitiveWordsService wmCustomerSensitiveWordsService;

    @Autowired
    private WmCustomerGrayService wmCustomerGrayService;

    @Autowired
    private WmCustomerKpRealAuthService wmCustomerKpRealAuthService;

    @Autowired
    private WmCustomerKpBuryingPointService wmCustomerKpBuryingPointService;

    @Autowired
    private WmCustomerKpService wmCustomerKpService;

    @Autowired
    private WmCustomerPlatformDataParseService wmCustomerPlatformDataParseService;

    @Autowired
    private WmEcontractSignThriftService wmEcontractSignThriftService;

    @Autowired
    private SignerDBOperator signerDBOperator;

    private static final int MAX_POI_NAME_NUM = 3;

    private static final Set<String> QUERY_FILES = ImmutableSet.of(WmPoiFieldQueryConstant.WM_POI_FIELD_NAME);

    private static Splitter SPLITTER = Splitter.on(",").omitEmptyStrings().trimResults();

    /**
     * 证件类型为身份证
     */
    private static final List<Integer> ID_CERT_TYPE = Lists.newArrayList(Integer.valueOf(CertTypeEnum.ID_CARD.getType()),
            Integer.valueOf(CertTypeEnum.ID_CARD_TEMP.getType()),
            Integer.valueOf(CertTypeEnum.ID_CARD_COPY.getType()));

    /**
     * kp蜂鸟审核回调
     *
     * @param bizId           wm_customer_kp_audit表ID
     * @param auditResultType 1通过,2驳回
     * @param reason          驳回原因
     * @param uid             操作人
     * @param uname           操作人名称
     */
    @TairLock(group = TairLockGroup.KP_AUDIT_RESULT_CALL_BACK, seedExp = "bizId")
    @Transactional(rollbackFor = Exception.class)
    public void kpAuditCallback(int bizId, int auditResultType, String reason, int uid, String uname) throws WmCustomerException, TException {
        LOGGER.info("kpAuditCallback bizId = {}, auditType = {}, reason = {}, uid = {}, uname = {}", bizId, auditResultType, reason, uid, uname);
        if (auditResultType != KpAuditConstants.AUDIT_RESULT_TYPE_APPROVED && KpAuditConstants.AUDIT_RESULT_TYPE_REJECT != auditResultType) {
            ThrowUtil.throwClientError("审核结果类型不正确");
        }
        WmCustomerKpAudit audit = wmCustomerKpAuditMapper.selectByPrimaryKey(bizId);
        if (audit == null) {
            audit = wmCustomerKpAuditMapper.selectByPrimaryKeyRT(bizId);
            if (audit == null) {
                LOGGER.warn("未找到提审记录，kpAuditCallback bizId = {}, auditType = {}, reason = {}, uid = {}, uname = {}", bizId, auditResultType, reason, uid, uname);
                return;
            }
        }
        // 校验当前审核结果是否已经被更新
        if (StringUtils.isNotEmpty(audit.getResult())) {
            LOGGER.error("当前审核结果已经被更新，kpAuditCallback bizId = {}, auditType = {}, reason = {}, uid = {}, uname = {}", bizId, auditResultType, reason, uid, uname);
            return;
        }

        if (StringUtils.isNotEmpty(reason)) {
            if (reason.length() > WmCustomerConstant.MAX_SUPPORT_REJECT_REASON_LENGTH_FOR_KP) {
                LOGGER.warn("客户信息审核回调结果太长 bizId = {}, auditType = {}, reason = {}, uid = {}, uname = {}", bizId, auditResultType, reason, uid, uname);
                reason = reason.substring(0, WmCustomerConstant.MAX_SUPPORT_REJECT_REASON_LENGTH_FOR_KP - WmCustomerConstant.TOO_LONG_MSG.length()) + WmCustomerConstant.TOO_LONG_MSG;
            }
        }

        if (KpAuditConstants.TYPE_SPECIAL == audit.getType()) {
            //特批审核
            try {
                specialAuditCallback(audit, auditResultType, reason, uid, uname);
            } catch (WmCustomerException e) {
                LOGGER.error("KP特批审核回调异常 bizId = {}, auditType = {}, reason = {}, uid = {}, uname = {}", bizId, auditResultType, reason, uid, uname);
                MetricHelper.build()
                        .name(MetricConstant.METRIC_CUS_KP_AUDIT_CALLBACK_FAIL)
                        .tag("auditType", String.valueOf(KpAuditConstants.TYPE_SPECIAL))
                        .count();
                throw e;
            } catch (Exception e) {
                LOGGER.error("KP特批审核回调异常 bizId = {}, auditType = {}, reason = {}, uid = {}, uname = {}", bizId, auditResultType, reason, uid, uname);
                MetricHelper.build()
                        .name(MetricConstant.METRIC_CUS_KP_AUDIT_CALLBACK_FAIL)
                        .tag("auditType", String.valueOf(KpAuditConstants.TYPE_SPECIAL))
                        .count();
                throw new TException(e);
            }
            audit.setResult(auditResultType == KpAuditConstants.AUDIT_RESULT_TYPE_APPROVED ? "特批审核通过" : "特批审核驳回");
        } else if (KpAuditConstants.TYPE_AGENT == audit.getType()) {
            //代理人审核
            try {
                agentAuditCallBack(audit, auditResultType, reason, uid, uname);
            } catch (WmCustomerException e) {
                LOGGER.error("KP代理人审核回调异常 bizId = {}, auditType = {}, reason = {}, uid = {}, uname = {}", bizId, auditResultType, reason, uid, uname);
                MetricHelper.build()
                        .name(MetricConstant.METRIC_CUS_KP_AUDIT_CALLBACK_FAIL)
                        .tag("auditType", String.valueOf(KpAuditConstants.TYPE_AGENT))
                        .count();
                throw e;
            } catch (Exception e) {
                LOGGER.error("KP代理人审核回调异常 bizId = {}, auditType = {}, reason = {}, uid = {}, uname = {}", bizId, auditResultType, reason, uid, uname);
                MetricHelper.build()
                        .name(MetricConstant.METRIC_CUS_KP_AUDIT_CALLBACK_FAIL)
                        .tag("auditType", String.valueOf(KpAuditConstants.TYPE_AGENT))
                        .count();
                throw new TException(e);
            }
            audit.setResult(auditResultType == KpAuditConstants.AUDIT_RESULT_TYPE_APPROVED ? "代理人审核通过" : "代理人审核驳回");
        } else {
            throw new WmCustomerException(500, "审核类型不正确");
        }
        audit.setValid(UN_VALID);
        wmCustomerKpAuditMapper.updateByPrimaryKey(audit);
    }

    private void agentAuditCallBack(WmCustomerKpAudit audit, int auditResultType, String reason, int uid, String uname) throws WmCustomerException {
        Integer kpId = audit.getKpId();
        WmCustomerKp singerKp = wmCustomerKpDBMapper.selectByPrimaryKey(kpId);
        LOGGER.info("agentAuditCallBack singerKp={}", JSON.toJSONString(singerKp));
        if (singerKp == null) {
            LOGGER.warn("kp已删除，kpId={}", kpId);
            return;
        }
        if (KpAuditConstants.AUDIT_RESULT_TYPE_REJECT == auditResultType) {
            //特批证件驳回
            if (KpSignerStateMachine.EFFECT.getState() == singerKp.getState()) {
                WmCustomerKpTemp kpTemp = wmCustomerKpTempDBMapper.selectByKpId(kpId);
                kpTemp.setState(KpSignerStateMachine.CHANGE_AGENT_AUDIT_REJECT.getState());
                kpTemp.setFailReason(reason);
                //更新临时变更记录为驳回失败
                wmCustomerKpTempDBMapper.updateByPrimaryKey(kpTemp);
                wmCustomerKpBuryingPointService.afterSingKp(null, audit.getAcctId(), kpTemp == null ? null : transformWmCustomerKpTemp(kpTemp), singerKp);
            } else {
                singerKp.setState(KpSignerStateMachine.AGENT_AUDIT_REJECT.getState());
                singerKp.setFailReason(reason);
                wmCustomerKpDBMapper.updateByPrimaryKey(singerKp);
            }
            wmCustomerSensitiveWordsService.readKpWhenSelect(singerKp);
            wmCustomerKpLogService.changeState(singerKp.getCustomerId(), singerKp, "代理人审核驳回，驳回原因：" + reason, uid, uname);
        } else if (KpAuditConstants.AUDIT_RESULT_TYPE_APPROVED == auditResultType) {
            wmCustomerSensitiveWordsService.readKpWhenSelect(singerKp);
            //通过
            signerKpAuditApproved(audit, uid, uname, kpId, singerKp, KpAuditConstants.TYPE_AGENT);
        } else {
            LOGGER.warn("不支持的auditResultType=" + auditResultType);
            throw new WmCustomerException(400, "不支持的审核结果类型");
        }
    }

    private void specialAuditCallback(WmCustomerKpAudit audit, int auditResultType, String reason, int uid, String uname) throws WmCustomerException {
        Integer kpId = audit.getKpId();
        WmCustomerKp signerKp = wmCustomerKpDBMapper.selectByPrimaryKey(kpId);
        LOGGER.info("specialAuditCallback signerKp={}", JSON.toJSONString(signerKp));
        if (signerKp == null) {
            LOGGER.warn("kp已删除，kpId={}", kpId);
            return;
        }
        if (KpAuditConstants.AUDIT_RESULT_TYPE_REJECT == auditResultType) {
            //特批证件驳回
            if (KpSignerStateMachine.EFFECT.getState() == signerKp.getState()) {
                WmCustomerKpTemp kpTemp = wmCustomerKpTempDBMapper.selectByKpId(kpId);
                if (kpTemp.getState() != KpSignerStateMachine.CHANGE_SPECILA_AUDIT_ING.getState()) {
                    LOGGER.error("特批认证审核驳回失败，状态非变更特批审核中，auditId:{}, state:{}", audit.getId(), kpTemp.getState());
                    return;
                }
                kpTemp.setState(KpSignerStateMachine.CHANGE_SPECILA_AUDIT_REJECT.getState());
                kpTemp.setFailReason(reason);
                //更新临时变更记录为驳回失败
                wmCustomerKpTempDBMapper.updateByPrimaryKey(kpTemp);
                wmCustomerKpBuryingPointService.afterSingKp(null, audit.getAcctId(), transformWmCustomerKpTemp(kpTemp), signerKp);
            } else {
                if (signerKp.getState() != KpSignerStateMachine.SPECILA_AUDIT_ING.getState()) {
                    LOGGER.error("特批认证审核驳回失败，状态非特批认证审核中，auditId:{}, state:{}", audit.getId(), signerKp.getState());
                    return;
                }
                signerKp.setState(KpSignerStateMachine.SPECILA_AUDIT_REJECT.getState());
                signerKp.setFailReason(reason);
                //更新数据为驳回失败
                wmCustomerKpDBMapper.updateByPrimaryKey(signerKp);
                WmCustomerKpTemp kpTemp = wmCustomerKpTempDBMapper.selectByKpId(kpId);
                wmCustomerKpBuryingPointService.afterSingKp(null, audit.getAcctId(), kpTemp == null ? null : transformWmCustomerKpTemp(kpTemp), signerKp);
            }
            wmCustomerSensitiveWordsService.readKpWhenSelect(signerKp);
            wmCustomerKpLogService.changeState(signerKp.getCustomerId(), signerKp, "特批认证审核驳回，驳回原因：" + reason, uid, uname);
        } else if (KpAuditConstants.AUDIT_RESULT_TYPE_APPROVED == auditResultType) {
            wmCustomerSensitiveWordsService.readKpWhenSelect(signerKp);
            //通过
            signerKpAuditApproved(audit, uid, uname, kpId, signerKp, KpAuditConstants.TYPE_SPECIAL);
        } else {
            LOGGER.warn("不支持的auditResultType=" + auditResultType);
            throw new WmCustomerException(400, "不支持的审核结果类型");
        }
    }

    /**
     * 审核通过后的操作
     *
     * @param uid
     * @param uname
     * @param kpId
     * @param singerKp
     */
    private void signerKpAuditApproved(WmCustomerKpAudit audit, int uid, String uname, Integer kpId, WmCustomerKp singerKp, int auditType) throws WmCustomerException {
        String stateMsg;
        if (KpAuditConstants.TYPE_SPECIAL == auditType) {
            stateMsg = "特批认证审核通过";
        } else if (KpAuditConstants.TYPE_AGENT == auditType) {
            stateMsg = "代理人审核通过";
        } else {
            stateMsg = "";
        }
        boolean sendKpEffectiveMq = false;
        wmCustomerKpLogService.changeState(singerKp.getCustomerId(), singerKp, stateMsg, uid, uname);
        if (KpSignerStateMachine.EFFECT.getState() == singerKp.getState()) {
            WmCustomerKpTemp kpTemp = wmCustomerKpTempDBMapper.selectByKpId(kpId);
            wmCustomerSensitiveWordsService.readKpWhenSelect(kpTemp);
            if (kpTemp == null) {
                LOGGER.warn("没有临时KP变更数据，kpId={}", kpId);
                return;
            }
            if (KpAuditConstants.TYPE_SPECIAL == auditType && kpTemp.getState() != KpSignerStateMachine.CHANGE_SPECILA_AUDIT_ING.getState()) {
                LOGGER.error("特批认证审核通过失败，状态非变更特批审核中，kpId:{}, state:{}", kpId, kpTemp.getState());
                return;
            }
            if (KpAuditConstants.TYPE_AGENT == auditType && kpTemp.getState() != KpSignerStateMachine.CHANGE_AGENT_AUDIT_ING.getState()) {
                LOGGER.error("代理人认证审核通过失败，状态非变更代理人审核中，kpId:{}, state:{}", kpId, kpTemp.getState());
                return;
            }

            boolean isNewProcess = singerKp.getVersion() != null && (singerKp.getVersion() == KpVersionEnum.V2.getCode() || singerKp.getVersion() == KpVersionEnum.V3.getCode());

            boolean fromUnSignerToSigner = isFromUnSignerToSigner(singerKp.getSignerType(), kpTemp.getSignerType());

            boolean fromUnAgentToAgent = isFromUnAgentToAgent(singerKp.getSignerType(), kpTemp.getSignerType());
            boolean fromAgentToAgent = isFromAgentToAgent(singerKp.getSignerType(), kpTemp.getSignerType());
            boolean isProcessUpdate = fromUnAgentToAgent || (fromAgentToAgent && !singerKp.getCertNumber().equals(kpTemp.getCertNumber()));
            boolean isAgentAuthHave = isAgentAuthHave(kpTemp.getHaveAgentAuth());
            boolean isLegalMsgAuth = (kpTemp.getLegalAuthType() != null && kpTemp.getLegalAuthType() == LegalAuthTypeEnum.MESSAGE_AUTH.getCode());
            //如果KP的签约人由非代理人变更为代理人/或代理人的身份证号发生变更，无授权书则需要走原签约人授权流程
            if (isNewProcess && isProcessUpdate && !isAgentAuthHave) {
                LOGGER.info("如果KP的签约人由非代理人变更为代理人/或代理人的身份证号发生变更，无授权书则需要走原签约人授权流程，singerKp={}, kpTemp={}", JSON.toJSONString(singerKp), JSON.toJSONString(kpTemp));
                //代理人+短信授权-申请短信授权
                if (kpTemp.getSignerType() == KpSignerTypeEnum.AGENT.getType() && isLegalMsgAuth) {
                    if (!checkKpLegalEffective(singerKp.getCustomerId())) {
                        kpTemp.setFailReason("无有效KP法人授权失败");
                        kpTemp.setState(KpSignerStateMachine.CHANGE_ORIGIN_SIGNER_CANCEL.getState());
                        wmCustomerKpLogService.changeState(singerKp.getCustomerId(), singerKp, "授权失败", uid, uname);
                    } else {
                        commitLegalAuthSignInfo(singerKp, kpTemp, uid, uname);
                        //更新临时数据状态
                        kpTemp.setFailReason("");
                        kpTemp.setState(KpSignerStateMachine.LEGAL_AUTH_AUTHORIZE_ING.getState());
                    }
                } else {
                    //进行原签约人授权
                    commitOriginSignerAuth(singerKp, transformWmCustomerKpTemp(kpTemp), uid, uname);
                    //更新临时数据状态
                    kpTemp.setFailReason("");
                    kpTemp.setState(KpSignerStateMachine.CHANGE_ORIGIN_SIGNER_AUTHING.getState());
                }
            } else if (!isNewProcess &&
                    !fromUnSignerToSigner
                    && (singerKp.getCertType() != kpTemp.getCertType() || !singerKp.getCertNumber().equals(kpTemp.getCertNumber()))) {
                LOGGER.info("变动了证件类型或证件编码需要原签约人授权，singerKp={}, kpTemp={}", JSON.toJSONString(singerKp), JSON.toJSONString(kpTemp));
                if (kpTemp.getSignerType() == KpSignerTypeEnum.AGENT.getType() && isLegalMsgAuth) {
                    if (!checkKpLegalEffective(singerKp.getCustomerId())) {
                        kpTemp.setFailReason("无有效KP法人授权失败");
                        kpTemp.setState(KpSignerStateMachine.CHANGE_ORIGIN_SIGNER_CANCEL.getState());
                        wmCustomerKpLogService.changeState(singerKp.getCustomerId(), singerKp, "授权失败", uid, uname);
                    } else {
                        commitLegalAuthSignInfo(singerKp, kpTemp, uid, uname);
                        //更新临时数据状态
                        kpTemp.setFailReason("");
                        kpTemp.setState(KpSignerStateMachine.LEGAL_AUTH_AUTHORIZE_ING.getState());
                    }
                } else {
                    //进行原签约人授权
                    commitOriginSignerAuth(singerKp, transformWmCustomerKpTemp(kpTemp), uid, uname);
                    //更新临时数据状态
                    kpTemp.setFailReason("");
                    kpTemp.setState(KpSignerStateMachine.CHANGE_ORIGIN_SIGNER_AUTHING.getState());
                }
            } else {
                //代理人+短信授权-申请短信授权+走新流程
                if (kpTemp.getSignerType() == KpSignerTypeEnum.AGENT.getType() && isLegalMsgAuth && isProcessUpdate) {
                    if (!checkKpLegalEffective(singerKp.getCustomerId())) {
                        kpTemp.setFailReason("无有效KP法人授权失败");
                        kpTemp.setState(KpSignerStateMachine.CHANGE_ORIGIN_SIGNER_CANCEL.getState());
                        wmCustomerKpLogService.changeState(singerKp.getCustomerId(), singerKp, "授权失败", uid, uname);
                    } else {
                        commitLegalAuthSignInfo(singerKp, kpTemp, uid, uname);
                        //更新临时数据状态
                        kpTemp.setFailReason("");
                        kpTemp.setState(KpSignerStateMachine.LEGAL_AUTH_AUTHORIZE_ING.getState());
                    }
                } else {
                    LOGGER.info("不需要原签约人授权，直接生效，kpTemp={},singerKp={}", JSON.toJSONString(kpTemp), JSON.toJSONString(singerKp));
                    wmCustomerKpLogService.changeState(singerKp.getCustomerId(), singerKp, "变更签约人生效", uid, uname);
                    WmCustomerKp tempKp = transformWmCustomerKpTemp(kpTemp);
                    tempKp.setEffective(KpConstants.EFFECTIVE);
                    wmCustomerKpBuryingPointService.afterSingKp(null, audit.getAcctId(), tempKp, singerKp);

//                kpDBOperate.tempKpEffect(kpTemp, singerKp);
                    wmCustomerKpService.tempKpEffect(kpTemp, singerKp);
                    //签约人KP生效发送MQ通知
                    sendKpEffectiveMq = true;
                    kpTemp.setValid(UN_VALID);
                }
            }
            wmCustomerSensitiveWordsService.writeKpWhenInsertOrUpdate(kpTemp);
            wmCustomerKpTempDBMapper.updateByPrimaryKey(kpTemp);
        } else {
            if (KpAuditConstants.TYPE_SPECIAL == auditType && singerKp.getState() != KpSignerStateMachine.SPECILA_AUDIT_ING.getState()) {
                LOGGER.error("特批认证审核通过失败，状态非特批认证审核中，kpId:{}, state:{}", kpId, singerKp.getState());
                return;
            }
            if (KpAuditConstants.TYPE_AGENT == auditType && singerKp.getState() != KpSignerStateMachine.AGENT_AUDIT_ING.getState()) {
                LOGGER.error("代理人认证审核通过失败，状态非代理人认证审核中，kpId:{}, state:{}", kpId, singerKp.getState());
                return;
            }
            Integer customerId = singerKp.getCustomerId();
            //代理人+短信授权-申请短信授权(特批认证审核中+代理人人审核中)
            if (singerKp.getSignerType() == KpSignerTypeEnum.AGENT.getType()
                    && (singerKp.getLegalAuthType() != null && singerKp.getLegalAuthType() == LegalAuthTypeEnum.MESSAGE_AUTH.getCode())
                    && (singerKp.getState() == KpSignerStateMachine.SPECILA_AUDIT_ING.getState() || singerKp.getState() == KpSignerStateMachine.AGENT_AUDIT_ING.getState())) {
                if (!checkKpLegalEffective(singerKp.getCustomerId())) {
                    singerKp.setFailReason("无有效KP法人授权失败");
                    singerKp.setState(KpSignerStateMachine.CHANGE_ORIGIN_SIGNER_CANCEL.getState());
                    wmCustomerKpLogService.changeState(singerKp.getCustomerId(), singerKp, "授权失败", uid, uname);
                } else {
                    //判断是否首次代理人电销系统提审
                    if (wmCustomerGrayService.hitDxKpSignerFirstAuditSucGray(customerId)
                            && checkDxFirstAuditSucIdCardAgent(singerKp, audit.getOperateSource())) {
                        LOGGER.info("客户命中灰度且是电销创建首次提审成功，则不需要短信签约直接生效,customerId={},kpId={}", customerId, singerKp.getId());
                        auditSuc2Effect(singerKp, audit, uid, uname);
                        sendKpEffectiveMq = true;
                    } else {
                        //更新签约人KP数据状态
                        singerKp.setFailReason("");
                        singerKp.setState(KpSignerStateMachine.LEGAL_AUTH_AUTHORIZE_ING.getState());
                        //发起法人授权短信签约
                        commitLegalAuthSignInfo(singerKp, null, uid, uname);
                    }
                }
            } else {
                auditSuc2Effect(singerKp, audit, uid, uname);
                sendKpEffectiveMq = true;
            }
            wmCustomerSensitiveWordsService.writeKpWhenInsertOrUpdate(singerKp);
            wmCustomerKpDBMapper.updateByPrimaryKey(singerKp);
        }
        /**
         * 满足如下条件掉客户四要素标签
         *  1.审核通过
         *  2.生效
         *  3.签约类型为非签约人或者证件类型为非身份证
         */
        if (sendKpEffectiveMq && (singerKp.getSignerType() != KpSignerTypeEnum.SIGNER.getType()
                || !ID_CERT_TYPE.contains((int) singerKp.getCertType()))) {
            wmCustomerKpRealAuthService.deleteFourEleTag(singerKp.getCustomerId());
        }
        if (sendKpEffectiveMq) {
            mafkaMessageSendManager.send(new CustomerMQBody(singerKp.getCustomerId(), CustomerMQEventEnum.CUSTOMER_KP_EFFECTIVE, ""));
        }
    }

    /**
     * 签约人KP直接到生效流程参数设置
     *
     * @param singerKp
     * @param audit
     * @param uid
     * @param uname
     */
    private void auditSuc2Effect(WmCustomerKp singerKp, WmCustomerKpAudit audit, int uid, String uname) {
        wmCustomerKpLogService.changeState(singerKp.getCustomerId(), singerKp, "签约人生效", uid, uname);
        singerKp.setState(KpSignerStateMachine.EFFECT.getState());
        singerKp.setEffective(KpConstants.EFFECTIVE);
        singerKp.setFailReason("");
        WmCustomerKpTemp kpTemp = wmCustomerKpTempDBMapper.selectByKpId(singerKp.getId());
        wmCustomerKpBuryingPointService.afterSingKp(null, audit.getAcctId(), kpTemp == null ? null : transformWmCustomerKpTemp(kpTemp), singerKp);
    }

    /**
     * 判断是否 首次电销系统提审通过的身份证类型代理人&短信授权
     *
     * @param wmCustomerKp
     * @param auditSource
     * @return
     */
    private boolean checkDxFirstAuditSucIdCardAgent(WmCustomerKp wmCustomerKp, Integer auditSource) {
        //来源为空或者非电销则不是
        if (auditSource == null || auditSource != CustomerDeviceType.MERCHANT_SYS.getCode()) {
            return false;
        }
        //判断代理人证件类型是否为身份证
        if (!CertTypeEnum.checkIdCardSet(wmCustomerKp.getCertType())) {
            return false;
        }
        //只有一条代理人提审记录
        int cntAll = wmCustomerKpAuditMapper.cntKpAuditAgentRecord(wmCustomerKp.getId());
        if (cntAll == 1) {
            return true;
        }
        return false;
    }

    public WmCustomerKp transformWmCustomerKpTemp(WmCustomerKpTemp kpTemp) {
        WmCustomerKp kp = new WmCustomerKp();
        BeanUtils.copyProperties(kpTemp, kp);
        kp.setId(kpTemp.getKpId());
        return kp;
    }

    /**
     * 法人授权-提交签约任务请求
     *
     * @param signerKp
     * @param uid
     * @param uname
     */
    public void commitLegalAuthSignInfo(WmCustomerKp signerKp, WmCustomerKpTemp kpTemp, int uid, String uname) {
        LOGGER.info("提交法人授权短信签约，commitLegalAuthSignInfo signerKp = {}", JSON.toJSONString(signerKp));
        wmCustomerKpLogService.changeState(signerKp.getCustomerId(), signerKp, "授权中", uid, uname);

        EcontractTaskApplyBo applyBo = convertEcontractTaskApplyBo(signerKp.getCustomerId(), signerKp, kpTemp);

        //KP法人信息
        WmCustomerKp legalKp = wmCustomerKpService.getEffectiveLegalKp(signerKp.getCustomerId());
        if (legalKp != null) {
            EcontractCustomerKPBo customerKPBo = new EcontractCustomerKPBo.Builder()
                    .kpTypeEnum(KpTypeEnum.LEGAL)
                    .kpSignerTypeEnum(null)
                    .certType(legalKp.getCertType())
                    .certNumber(legalKp.getCertNumber())
                    .signerName(legalKp.getCompellation())
                    .signerIDCardNum(legalKp.getCertNumber())
                    .signerPhoneNum(legalKp.getPhoneNum())
                    .signerBankName(legalKp.getBankName())
                    .signerBankCardNum(legalKp.getCreditCard())
                    .signerEmail(legalKp.getEmail())
                    .build();
            applyBo.setKpBo(JSON.toJSONString(customerKPBo));
        }

        //构造审核对象
        WmCustomerKpAudit audit = new WmCustomerKpAudit();
        audit.setKpId(signerKp.getId());
        audit.setType(KpAuditConstants.TYPE_AUTH_FOR_SIGNER_AGENT_LEGAL);
        audit.setValid(KpConstants.VALID);
        audit.setUid(uid);
        audit.setUname(uname);
        audit.setExtra(legalKp != null ? legalKp.getCompellation() + "," + legalKp.getPhoneNum() : "");
        ObjectUtil.defaultValue(audit);
        wmCustomerKpAuditMapper.insert(audit);
        applyBo.setRecordId(String.valueOf(audit.getId()));
        applyBo.setCommitUid(uid);
        try {
            wmCustomerKpDBMapper.updateCertNumberModify(CertNumberModifyEnum.YES.getCode(), signerKp.getId());
            LOGGER.info("commitLegalAuthSignInfo.applyTask applyBo={}", JSON.toJSONString(applyBo));
            LongResult applyTaskId = wmEcontractSignThriftService.easyApplyTask(applyBo);
            LOGGER.info("commitLegalAuthSignInfo.applyTask applyTaskId={}", applyTaskId.getValue());
            audit.setCommitId((int) applyTaskId.getValue());
            wmCustomerKpAuditMapper.updateByPrimaryKey(audit);
        } catch (WmCustomerException e) {
            Cat.logEvent(MetricConstant.METRIC_CUSTOMER_KP_LEGAL_AUTH_ERROR, MetricConstant.SEND_LEGAL_AUTH_SIGN_ERROR);
            LOGGER.warn("commitLegalAuthSignInfo.applyTask,提交法人授权短信签约失败，applyBo={}", JSON.toJSONString(applyBo), e);
            throw new RuntimeException(e);
        } catch (Exception e) {
            Cat.logEvent(MetricConstant.METRIC_CUSTOMER_KP_LEGAL_AUTH_ERROR, MetricConstant.SEND_LEGAL_AUTH_SIGN_ERROR);
            LOGGER.error("commitLegalAuthSignInfo.applyTask,提交法人授权短信签约异常，applyBo={}", JSON.toJSONString(applyBo), e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 签约人原签约人授权
     *
     * @param oldKpSigner 旧签约人信息
     * @param signerKp    新签约人信息
     */
    public void commitOriginSignerAuth(WmCustomerKp oldKpSigner, WmCustomerKp signerKp, int uid, String uname) {
        LOGGER.info("提交原签约人授权，commitOriginSignerAuth oldKpSigner = {}, signerKp = {}", JSON.toJSONString(oldKpSigner), JSON.toJSONString(signerKp));
        wmCustomerKpLogService.changeState(oldKpSigner.getCustomerId(), signerKp, "变更授权", uid, uname);

        EcontractTaskApplyBo applyBo = new EcontractTaskApplyBo();
        applyBo.setBizId(String.valueOf(oldKpSigner.getCustomerId()));
        applyBo.setBizTypeEnum(EcontractTaskApplyBizTypeEnum.CUSTOMER_ID);
        applyBo.setApplyTypeEnum(EcontractTaskApplyTypeEnum.KP);

        EcontractKPAuthInfoBo kpBo = new EcontractKPAuthInfoBo.Builder()
                .signerName(signerKp.getCompellation())
                .signerPhone(signerKp.getPhoneNum())
                .poiNames(getPoiNameListByCustomerId(oldKpSigner.getCustomerId())).build();
        applyBo.setApplyInfoBo(JSON.toJSONString(kpBo));

        //构造审核对象
        WmCustomerKpAudit audit = new WmCustomerKpAudit();
        audit.setKpId(oldKpSigner.getId());
        audit.setType(KpAuditConstants.TYPE_AUTH);
        audit.setValid(KpConstants.VALID);
        audit.setUid(uid);
        audit.setUname(uname);
        audit.setExtra("");
        ObjectUtil.defaultValue(audit);
        wmCustomerKpAuditMapper.insert(audit);
        applyBo.setRecordId(String.valueOf(audit.getId()));
        applyBo.setCommitUid(uid);
        try {
            wmCustomerKpDBMapper.updateCertNumberModify(CertNumberModifyEnum.YES.getCode(), oldKpSigner.getId());
            LOGGER.info("wmEcontractSignBzService.applyTask applyBo={}", JSON.toJSONString(applyBo));
            LongResult applyTaskId = wmEcontractSignBzService.applyTask(applyBo);
            LOGGER.info("wmEcontractSignBzService.applyTask applyTaskId={}", applyTaskId.getValue());
            audit.setCommitId((int) applyTaskId.getValue());
            wmCustomerKpAuditMapper.updateByPrimaryKey(audit);
        } catch (WmCustomerException e) {
            LOGGER.warn("提交变更授权失败，applyBo={}", JSON.toJSONString(applyBo), e);
            throw new RuntimeException(e);
        } catch (Exception e) {
            LOGGER.error("提交变更授权异常，applyBo={}", JSON.toJSONString(applyBo), e);
            throw new RuntimeException(e);
        }
    }

    public String getPoiNamesByCustomerId(int customerId) {
        WmPoiSearchParam wmPoiSearchParam = new WmPoiSearchParam().setSql("select wmPoiId,name from poi.poi_index_rw where customerId=" + customerId);
        try {
            LOGGER.info("getPoiNamesByCustomerId customerId = {}", customerId);
            WmPoiSearchResult search = wmPoiSearchThriftService.search(wmPoiSearchParam);
            LOGGER.info("getPoiNamesByCustomerId customerId = {}, search={}", customerId, JSON.toJSONString(search));
            if (CollectionUtils.isEmpty(search.getFields())) {
                return "";
            }
            List<WmPoiSearchField> fields = search.getFields();
            StringBuilder poiNames = new StringBuilder();
            int count = 0;
            for (WmPoiSearchField field : fields) {
                if (count++ >= MAX_POI_NAME_NUM) {
                    break;
                }
                poiNames.append(field.getName()).append("、");
            }
            if (poiNames.length() > 0) {
                poiNames.deleteCharAt(poiNames.length() - 1);
            }
            if (count >= MAX_POI_NAME_NUM) {
                poiNames.append("等");
            }
            LOGGER.info("customerId={}, poiNames={}", customerId, poiNames.toString());
            return poiNames.toString();
        } catch (Exception e) {
            LOGGER.error("调用PoiSearch根据客户ID获取门店名称异常,customerId={}", customerId, e);
        }
        return "";
    }

    public String getPoiNameListByCustomerId(int customerId) {
        try {
            List<Long> wmPoiIds = WmCustomerPoiAggre.Factory.make().selectWmPoiIdsByCustomerId(customerId);
            LOGGER.info("selectWmPoiIdsByCustomerId customerId={},wmPoiIds={}", customerId, JSON.toJSONString(wmPoiIds));
            if (CollectionUtils.isEmpty(wmPoiIds)) {
                return "";
            }
            List<WmPoiAggre> wmPoiAggres = wmPoiQueryThriftService.mgetWmPoiAggreByWmPoiIdWithSpecificField(wmPoiIds, QUERY_FILES);

            LOGGER.info("wmPoiAggres wmPoiIdList={}", JSON.toJSONString(VersionCheckUtil.transferWmPoiAggre(wmPoiAggres)));
            if (CollectionUtils.isEmpty(wmPoiAggres)) {
                return "";
            }

            StringBuilder poiNames = new StringBuilder();
            int count = 0;
            for (WmPoiAggre aggre : wmPoiAggres) {
                if (count++ >= MAX_POI_NAME_NUM) {
                    break;
                }
                poiNames.append(aggre.getName()).append("、");
            }
            if (poiNames.length() > 0) {
                poiNames.deleteCharAt(poiNames.length() - 1);
            }
            if (count >= MAX_POI_NAME_NUM) {
                poiNames.append("等");
            }
            LOGGER.info("customerId={}, poiNames={}", customerId, poiNames.toString());
            return poiNames.toString();
        } catch (WmServerException e) {
            LOGGER.warn("调用PoiQuery批量获取店名称异常 customerId={}, msg={}", customerId, e.getMsg(), e);
        } catch (Exception e) {
            LOGGER.error("调用PoiQuery批量获取店名称异常,customerId={}", customerId, e);
        }
        return "";
    }

    /**
     * 原签约人短信授权回调接口+签约代理人短信授权回调
     *
     * @param taskId
     * @param taskStateEnum
     */
    @Transactional(rollbackFor = Exception.class)
    public void signerKpAuthCallback(int taskId, EcontractTaskStateEnum taskStateEnum) throws WmCustomerException {
        LOGGER.info("signerKpAutCallback taskId = {}, taskStateEnum = {}", taskId, taskStateEnum);
        if (!EcontractTaskStateEnum.SUCCESS.equals(taskStateEnum) && !EcontractTaskStateEnum.FAIL.equals(taskStateEnum)) {
            return;
        }
        WmCustomerKpAudit wmCustomerKpAudit = wmCustomerKpAuditMapper.selectByCommitId(taskId);
        if (wmCustomerKpAudit == null) {
            LOGGER.warn("未获取到提交授权记录，taskId={}", taskId);
            return;
        }
        Byte auditType = wmCustomerKpAudit.getType();
        Integer kpId = wmCustomerKpAudit.getKpId();

        wmCustomerKpAudit.setValid(UN_VALID);
        WmCustomerKp wmCustomerKp = wmCustomerKpDBMapper.selectByPrimaryKey(kpId);
        wmCustomerSensitiveWordsService.readKpWhenSelect(wmCustomerKp);
        if (wmCustomerKp == null) {
            LOGGER.warn("未找到KP数据，KP可能被删除（客户删除触发），kpId={}", wmCustomerKpAudit.getKpId());
            return;
        }

        WmCustomerKpTemp kpTemp = wmCustomerKpTempDBMapper.selectByKpId(wmCustomerKpAudit.getKpId());
        wmCustomerSensitiveWordsService.readKpWhenSelect(kpTemp);
        if (KpSignerStateMachine.EFFECT.getState() != wmCustomerKp.getState()) {
            LOGGER.warn("未找到KP临时变更数据，kpId={}", wmCustomerKpAudit.getKpId());
            if (auditType == KpAuditConstants.TYPE_AUTH_FOR_SIGNER_AGENT_LEGAL
                    && wmCustomerKp.getState() == KpSignerStateMachine.LEGAL_AUTH_AUTHORIZE_ING.getState()
                    && wmCustomerKp.getLegalAuthType() != null && wmCustomerKp.getLegalAuthType() == LegalAuthTypeEnum.MESSAGE_AUTH.getCode()) {
                LOGGER.info("客户KP的状态是授权中，KP临时表无数据，按照新增签约人KP来进行处理，kpId={}", wmCustomerKpAudit.getKpId());
                
                //命中KP签约人状态模型
                KpSignerFlowAbility kpSignerFlowAbility = signerDBOperator.getMatchKpSignerFlowAbility(wmCustomerKp.getCertType());
                KpSignerStatusMachineContext context = KpSignerStatusMachineContext
                        .builder()
                        .customerId(wmCustomerKp.getCustomerId())
                        .econtractTaskStateEnum(taskStateEnum)
                        .wmCustomerKpAudit(wmCustomerKpAudit)
                        .wmCustomerKp(wmCustomerKp)
                        .sendEffectiveMq(EcontractTaskStateEnum.SUCCESS.equals(taskStateEnum))
                        .existEffectiveFlag(wmCustomerKp.getState() == KpSignerStateMachine.EFFECT.getState())
                        .build();
                Boolean dealFlag = kpSignerFlowAbility.dealMsgAuthResult(context);
                if (dealFlag == null || !dealFlag) {
                    LOGGER.error("signerKpAuthCallback,KP代理人授权结果通知处理失败，请关注,taskId = {}, taskStateEnum = {}", taskId, taskStateEnum);
                }
                return;
            }
            return;
        }
        
        // KP签约人状态模型
        KpSignerFlowAbility kpSignerFlowAbility = signerDBOperator
                .getMatchKpSignerFlowAbility(wmCustomerKp.getCertType());
        KpSignerStatusMachineContext context = KpSignerStatusMachineContext.builder()
                .customerId(wmCustomerKp.getCustomerId()).econtractTaskStateEnum(taskStateEnum)
                .wmCustomerKpAudit(wmCustomerKpAudit).wmCustomerKp(wmCustomerKp)
                .sendEffectiveMq(EcontractTaskStateEnum.SUCCESS.equals(taskStateEnum))
                .existEffectiveFlag(wmCustomerKp.getState() == KpSignerStateMachine.EFFECT.getState()).build();

        Boolean dealFlag = kpSignerFlowAbility.dealMsgAuthResult(context);
        if (dealFlag == null || !dealFlag) {
            LOGGER.error("signerKpAuthCallback,KP代理人授权结果通知处理失败，请关注,taskId = {}, taskStateEnum = {}", taskId,
                    taskStateEnum);
        }

    }


    /**
     * 处理新增签约人KP情况下-短信授权处理
     *
     * @param customerKp
     */
    public void dealSignerKpLegalAuthResult(WmCustomerKp customerKp, WmCustomerKpAudit wmCustomerKpAudit,
                                             EcontractTaskStateEnum taskStateEnum) {
        int customerId = customerKp.getCustomerId();
        String stateMsg = "";
        boolean sendKpEffectiveMq = false;
        try {
            if (EcontractTaskStateEnum.SUCCESS.equals(taskStateEnum)) {
                stateMsg = "授权成功";
                wmCustomerKpAudit.setResult(stateMsg);
                customerKp.setState(KpSignerStateMachine.EFFECT.getState());
                customerKp.setFailReason(stateMsg);
                customerKp.setEffective(EFFECTIVE);
                //kp生效，发送MQ
                sendKpEffectiveMq = true;
                /**
                 * 满足如下条件掉客户四要素标签
                 *  1.授权通过
                 *  2.生效
                 *  3.签约类型为非签约人或者证件类型为非身份证
                 */
                if (customerKp.getSignerType() != KpSignerTypeEnum.SIGNER.getType()
                        || !ID_CERT_TYPE.contains((int) customerKp.getCertType())) {
                    wmCustomerKpRealAuthService.deleteFourEleTag(customerKp.getCustomerId());
                }
            } else if (EcontractTaskStateEnum.FAIL.equals(taskStateEnum)) {
                stateMsg = "取消授权";
                customerKp.setEffective(UN_EFFECTIVE);
                customerKp.setState(KpSignerStateMachine.CHANGE_ORIGIN_SIGNER_CANCEL.getState());
                customerKp.setFailReason(stateMsg);
            } else {
                LOGGER.warn("不支持的处理状态：{}", taskStateEnum.getName());
            }
            //更新审核表
            wmCustomerKpAudit.setResult(stateMsg);
            wmCustomerKpAudit.setValid(UN_VALID);
            wmCustomerKpAuditMapper.updateByPrimaryKey(wmCustomerKpAudit);
            //更新签约人KP表
            wmCustomerSensitiveWordsService.writeKpWhenInsertOrUpdate(customerKp);
            wmCustomerKpDBMapper.updateByPrimaryKey(customerKp);
            wmCustomerKpLogService.changeState(customerKp.getCustomerId(), customerKp, stateMsg, 0, "法人短信授权");
            LOGGER.info("dealSignerKpLegalAuthResult,新增签约人的短信授权结果处理完成，customerKp={},wmCustomerKpAudit={}", JSON.toJSONString(customerKp), JSON.toJSONString(wmCustomerKpAudit));
            //记录埋点信息
            WmCustomerKpTemp kpTemp = wmCustomerKpTempDBMapper.selectByKpId(customerKp.getId());
            wmCustomerKpBuryingPointService.afterSingKp(null, wmCustomerKpAudit.getAcctId(), kpTemp == null ? null : transformWmCustomerKpTemp(kpTemp), customerKp);
            if (sendKpEffectiveMq) {
                mafkaMessageSendManager.send(new CustomerMQBody(customerId, CustomerMQEventEnum.CUSTOMER_KP_EFFECTIVE, ""));
            }
        } catch (Exception e) {
            Cat.logEvent(MetricConstant.METRIC_CUSTOMER_KP_LEGAL_AUTH_ERROR, MetricConstant.LEGAL_AUTH_CALL_BACK_ERROR);
            LOGGER.error("dealSignerKpLegalAuthResult,新增签约人KP短信授权结果发生异常,customerId={},kpId={},kpAuditId={}",
                    customerKp.getCustomerId(), customerKp.getId(), wmCustomerKpAudit.getId(), e);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void operationManagerKpAuthCallback(EcontractCallbackBo callbackBo) {
        LOGGER.info("operationManagerKpAuthCallback callbackBo = {}", JSONObject.toJSONString(callbackBo));
        List<WmCustomerKpAudit> wmCustomerKpAuditList = wmCustomerKpAuditMapper.selectByCommitIdAndType(callbackBo.getTaskId().intValue(), KpAuditConstants.TYPE_AUTH_FOR_OPMANAGER);
        if (CollectionUtils.isEmpty(wmCustomerKpAuditList)) {
            LOGGER.warn("未获取到提交授权记录，taskId = {}", callbackBo.getTaskId());
            return;
        }

        List<Integer> kpIdList = wmCustomerKpAuditList.stream().map(WmCustomerKpAudit::getKpId).collect(Collectors.toList());
        Map<Integer, WmCustomerKpTemp> wmCustomerKpTempMap = getWmCustomerKpTempMap(kpIdList);
        Map<Integer, WmCustomerKp> wmCustomerKpMap = getWmCustomerKpMap(kpIdList);
        if (MapUtils.isEmpty(wmCustomerKpMap)) {
            LOGGER.warn("未找到KP数据，KP可能被删除（客户删除触发），kpIdList = {}", JSON.toJSONString(kpIdList));
            return;
        }

        int customerId = wmCustomerKpMap.values().stream().findFirst().get().getCustomerId();
        wmCustomerKpAuditList.stream().forEach(wmCustomerKpAudit -> {
            WmCustomerKpTemp wmCustomerKpTemp = wmCustomerKpTempMap.get(wmCustomerKpAudit.getKpId());
            WmCustomerKp wmCustomerKp = wmCustomerKpMap.get(wmCustomerKpAudit.getKpId());
            wmCustomerKpAudit.setValid(UN_VALID);
            if (EcontractTaskStateEnum.SUCCESS.equals(callbackBo.getState())) {
                handleSuccess(customerId, wmCustomerKp, wmCustomerKpTemp, wmCustomerKpAudit, callbackBo);
            } else if (EcontractTaskStateEnum.FAIL.equals(callbackBo.getState())) {
                handleFail(customerId, wmCustomerKp, wmCustomerKpTemp, wmCustomerKpAudit, callbackBo);
            } else if (EcontractTaskStateEnum.CANCEL.equals(callbackBo.getState())) {
                handleCancel(customerId, wmCustomerKp, wmCustomerKpTemp, wmCustomerKpAudit, callbackBo);
            } else {
                LOGGER.warn("不支持的处理状态：{}", callbackBo.getState().getName());
            }
        });
    }


    private void handleFail(int customerId, WmCustomerKp wmCustomerKp, WmCustomerKpTemp wmCustomerKpTemp, WmCustomerKpAudit wmCustomerKpAudit, EcontractCallbackBo callbackBo) {
        // 审核表
        wmCustomerKpAudit.setResult(callbackBo.getFailMsg());
        //更新审核表
        wmCustomerKpAuditMapper.updateByPrimaryKey(wmCustomerKpAudit);
        KpSignerStateMachine fromState;
        KpSignerStateMachine toState;
        if (wmCustomerKpTemp != null) {
            fromState = KpSignerStateMachine.getByState(wmCustomerKpTemp.getState());
            toState = KpSignerStateMachine.EFFECT;
            wmCustomerKpTemp.setState(toState.getState());
            wmCustomerKpTemp.setFailReason(callbackBo.getFailMsg());
            wmCustomerKpTemp.setValid(UN_VALID);
            wmCustomerSensitiveWordsService.writeKpSourceWhenUpdate(wmCustomerKpTemp);
            //更新临时表
            wmCustomerKpTempDBMapper.updateByPrimaryKey(wmCustomerKpTemp);
            wmCustomerKpPoiService.deleteTempByKpIdList(Lists.newArrayList(wmCustomerKp.getId()));
        } else {
            fromState = KpSignerStateMachine.getByState(wmCustomerKp.getState());
            toState = KpSignerStateMachine.AUTHORIZE_FAIL;
            wmCustomerKp.setState(toState.getState());
            wmCustomerKp.setFailReason(callbackBo.getFailMsg());
            //wmCustomerKp对象中原KP手机号phoneNum字段会查询赋值，所以需要对原手机号是否写入控制，防止开关关闭还继续写入
            wmCustomerSensitiveWordsService.writeKpSourceWhenUpdate(wmCustomerKp);
            wmCustomerKpDBMapper.updateByPrimaryKey(wmCustomerKp);
        }
        wmCustomerKpLogService.insertOpManagerKpCommitAuthLog(customerId, callbackBo.getTaskId(), "签约人短信确认不通过",
                fromState, toState, 0, "商家短信确认", wmCustomerKp, wmCustomerKpTemp);
    }

    private void handleCancel(int customerId, WmCustomerKp wmCustomerKp, WmCustomerKpTemp wmCustomerKpTemp, WmCustomerKpAudit wmCustomerKpAudit, EcontractCallbackBo callbackBo) {
        // 审核表
        wmCustomerKpAudit.setResult(callbackBo.getFailMsg());
        //更新审核表
        wmCustomerKpAuditMapper.updateByPrimaryKey(wmCustomerKpAudit);
        KpSignerStateMachine fromState;
        KpSignerStateMachine tomState;
        if (wmCustomerKpTemp != null) {
            fromState = KpSignerStateMachine.getByState(wmCustomerKpTemp.getState());
            tomState = KpSignerStateMachine.EFFECT;
            wmCustomerKpTemp.setState(tomState.getState());
            wmCustomerKpTemp.setFailReason(callbackBo.getFailMsg());
            wmCustomerKpTemp.setValid(UN_VALID);
            wmCustomerSensitiveWordsService.writeKpSourceWhenUpdate(wmCustomerKpTemp);
            //更新临时表
            wmCustomerKpTempDBMapper.updateByPrimaryKey(wmCustomerKpTemp);
            wmCustomerKpPoiService.deleteTempByKpIdList(Lists.newArrayList(wmCustomerKp.getId()));
        } else {
            fromState = KpSignerStateMachine.getByState(wmCustomerKp.getState());
            tomState = KpSignerStateMachine.AUTHORIZE_CANCEL;
            wmCustomerKp.setState(tomState.getState());
            wmCustomerKp.setFailReason(callbackBo.getFailMsg());
            wmCustomerSensitiveWordsService.writeKpSourceWhenUpdate(wmCustomerKp);
            wmCustomerKpDBMapper.updateByPrimaryKey(wmCustomerKp);
        }
        wmCustomerKpLogService.insertOpManagerKpCommitAuthLog(customerId, callbackBo.getTaskId(), "任务取消授权",
                fromState, tomState, 0, "系统", wmCustomerKp, wmCustomerKpTemp);
    }

    private void handleSuccess(int customerId, WmCustomerKp wmCustomerKp, WmCustomerKpTemp wmCustomerKpTemp, WmCustomerKpAudit wmCustomerKpAudit, EcontractCallbackBo callbackBo) {
        // 审核表
        wmCustomerKpAudit.setResult("授权通过");
        //更新审核表
        wmCustomerKpAuditMapper.updateByPrimaryKey(wmCustomerKpAudit);
        KpSignerStateMachine fromState;
        if (wmCustomerKpTemp != null) {
            fromState = KpSignerStateMachine.getByState(wmCustomerKpTemp.getState());
            // 临时表
            wmCustomerKpTemp.setState(KpSignerStateMachine.EFFECT.getState());
            wmCustomerKpTemp.setFailReason("授权通过");
            wmCustomerKpTemp.setValid(UN_VALID);
            wmCustomerSensitiveWordsService.writeKpSourceWhenUpdate(wmCustomerKpTemp);
            //更新临时表
            wmCustomerKpTempDBMapper.updateByPrimaryKey(wmCustomerKpTemp);
            // 生效KP
            kpDBOperate.tempKpEffect(wmCustomerKpTemp, wmCustomerKp);
        } else {
            fromState = KpSignerStateMachine.getByState(wmCustomerKp.getState());
            wmCustomerKp.setState(KpSignerStateMachine.EFFECT.getState());
            wmCustomerKp.setEffective(EFFECTIVE);
            wmCustomerKp.setFailReason("");
            wmCustomerSensitiveWordsService.writeKpSourceWhenUpdate(wmCustomerKp);
            wmCustomerKpDBMapper.updateByPrimaryKey(wmCustomerKp);
        }

        // 生效KP门店关联关系
        wmCustomerKpPoiService.effectCustomerKpPoiRel(wmCustomerKpAudit.getKpId());
        wmCustomerKpLogService.insertOpManagerKpCommitAuthLog(customerId, callbackBo.getTaskId(), "签约人短信已确认",
                fromState, KpSignerStateMachine.EFFECT, 0, "商家短信确认", wmCustomerKp, wmCustomerKpTemp);
        //企客推送生效消息
        mafkaMessageSendManager.send(new CustomerMQBody(customerId, CustomerMQEventEnum.CUSTOMER_OTHER_KP_EFFECTIVE, ""));
    }


    public void deleteKpByCustomerId(int customerId) {
        LOGGER.info("deleteKpByCustomerId customerId = {}", customerId);
        wmCustomerKpDBMapper.deleteByCustomerId(customerId);
    }

    public boolean isAgentAuthHave(Integer haveAgentAuth) {
        if (haveAgentAuth == null) {
            return true;
        }
        return HaveAgentAuthEnum.of(haveAgentAuth) == HaveAgentAuthEnum.HAVE;
    }

    public boolean isFromUnSignerToSigner(byte oldSignerType, byte newKpSignerType) {
        return oldSignerType != KpSignerTypeEnum.SIGNER.getType()
                && newKpSignerType == KpSignerTypeEnum.SIGNER.getType();
    }

    public boolean isFromUnAgentToAgent(byte oldSignerType, byte newKpSignerType) {
        return oldSignerType != KpSignerTypeEnum.AGENT.getType()
                && newKpSignerType == KpSignerTypeEnum.AGENT.getType();
    }

    public boolean isFromAgentToAgent(byte oldSignerType, byte newKpSignerType) {
        return oldSignerType == KpSignerTypeEnum.AGENT.getType()
                && newKpSignerType == KpSignerTypeEnum.AGENT.getType();
    }


    public List<WmCustomerKp> checkForOperationManagerKpCommitAuth(List<Integer> kpIdList, int uid, String uname) throws WmCustomerException {
        LOGGER.info("checkForOperationManagerKpCommitAuth kpIdList = {}, uid = {}, uname = {}", JSON.toJSONString(kpIdList), uid, uname);
        if (CollectionUtils.isEmpty(kpIdList)) {
            return Lists.newArrayList();
        }

        // 查临时表 KP-POI 关联关系
        List<WmCustomerKpPoiDB> wmCustomerKpPoiDBList = wmCustomerKpPoiService.batchGetTempRelByKpIdList(kpIdList);
        Map<Integer, Integer> kpPoiCntMap = Maps.newHashMap();
        wmCustomerKpPoiDBList.stream().forEach(wmCustomerKpPoiDB -> kpPoiCntMap.merge(wmCustomerKpPoiDB.getKpId(), 1, Integer::sum));
        LOGGER.info("checkForOperationManagerKpCommitAuth kpPoiCntMap = {}", JSON.toJSONString(kpPoiCntMap));

        // 查审核表
        List<WmCustomerKpAudit> wmCustomerKpAuditList = wmCustomerKpAuditMapper.selectByKpIdListAndType(kpIdList, KpAuditConstants.TYPE_AUTH_FOR_OPMANAGER);
        Map<Integer, WmCustomerKpAudit> wmCustomerKpAuditMap = wmCustomerKpAuditList.stream().collect(Collectors.toMap(WmCustomerKpAudit -> WmCustomerKpAudit.getKpId(), WmCustomerKpAudit -> WmCustomerKpAudit));
        // 查临时表
        Map<Integer, WmCustomerKpTemp> wmCustomerKpTempMap = getWmCustomerKpTempMap(kpIdList);
        // 查正式表
        Map<Integer, WmCustomerKp> wmCustomerKpMap = getWmCustomerKpMap(kpIdList);
        LOGGER.info("checkForOperationManagerKpCommitAuth wmCustomerKpAuditMap = {}, wmCustomerKpTempMap = {}, wmCustomerKpMap = {}", JSON.toJSONString(wmCustomerKpAuditMap), JSON.toJSONString(wmCustomerKpTempMap), JSON.toJSONString(wmCustomerKpMap));

        List<WmCustomerKp> checkFailList = Lists.newArrayList();
        for (Integer kpId : kpIdList) {
            WmCustomerKpAudit wmCustomerKpAudit = wmCustomerKpAuditMap.get(kpId);
            if (wmCustomerKpAudit != null) {
                fillCommitAuthFailList(kpId, wmCustomerKpMap.get(kpId).getCompellation(), checkFailList);
                continue;
            }

            WmCustomerKpTemp wmCustomerKpTemp = wmCustomerKpTempMap.get(kpId);
            if (wmCustomerKpTemp != null) {
                // 先判断临时表，有数据说明KP生效过，且生效后变更数据在临时表
                if (KpTypeEnum.OPMANAGER.getType() != wmCustomerKpTemp.getKpType() ||
                        KpSignerStateMachine.CHANGE_NO_AUTHORIZE.getState() != wmCustomerKpTemp.getState()) {
                    fillCommitAuthFailList(kpId, wmCustomerKpTemp.getCompellation(), checkFailList);
                }
            } else {
                // 临时表无数据，说明未生效过，判断正式表，生效前变更数据在正式表
                WmCustomerKp wmCustomerKp = wmCustomerKpMap.get(kpId);
                if (wmCustomerKp == null) {
                    throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "KP不存在");
                }
                if (KpTypeEnum.OPMANAGER.getType() != wmCustomerKp.getKpType() ||
                        KpSignerStateMachine.NO_AUTHORIZE.getState() != wmCustomerKp.getState() ||
                        kpPoiCntMap.getOrDefault(kpId, 0) <= 0) {
                    fillCommitAuthFailList(kpId, wmCustomerKp.getCompellation(), checkFailList);
                }
            }
        }

        LOGGER.info("checkForOperationManagerKpCommitAuth checkFailList = {}", JSON.toJSONString(checkFailList));
        return checkFailList;
    }

    private void fillCommitAuthFailList(Integer kpId, String kpName, List<WmCustomerKp> checkFailList) {
        WmCustomerKp tmpKp = new WmCustomerKp();
        tmpKp.setId(kpId);
        tmpKp.setCompellation(kpName);
        checkFailList.add(tmpKp);
    }

    private Map<Integer, WmCustomerKp> getWmCustomerKpMap(List<Integer> kpIdList) {
        List<WmCustomerKp> wmCustomerKpList = wmCustomerKpDBMapper.selectByIdList(kpIdList);
        wmCustomerSensitiveWordsService.readKpWhenSelect(wmCustomerKpList);
        return wmCustomerKpList.stream().collect(Collectors.toMap(wmCustomerKp -> wmCustomerKp.getId(), wmCustomerKp -> wmCustomerKp));
    }

    private Map<Integer, WmCustomerKpTemp> getWmCustomerKpTempMap(List<Integer> kpIdList) {
        List<WmCustomerKpTemp> wmCustomerKpTempList = wmCustomerKpTempDBMapper.selectByIdList(kpIdList);
        wmCustomerSensitiveWordsService.readKpTempWhenSelect(wmCustomerKpTempList);
        return wmCustomerKpTempList.stream().collect(Collectors.toMap(wmCustomerKpTemp -> wmCustomerKpTemp.getKpId(), wmCustomerKpTemp -> wmCustomerKpTemp));
    }

    @Transactional(rollbackFor = Exception.class)
    public void commitAuthForOperationManagerKp(Integer customerId, List<Integer> kpIdList, int uid, String uname) throws WmCustomerException {
        LOGGER.info("commitAuthForOperationManagerKp customerId = {}, kpIdList = {}, uid = {}, uname = {}", customerId, JSON.toJSONString(kpIdList), uid, uname);
        if (CollectionUtils.isEmpty(kpIdList) || customerId == null) {
            return;
        }

        // 提交授权校验
        List<WmCustomerKp> wmCustomerKpList = checkForOperationManagerKpCommitAuth(kpIdList, uid, uname);
        if (!CollectionUtils.isEmpty(wmCustomerKpList)) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "提交授权校验失败");
        }

        // 查临时表
        Map<Integer, WmCustomerKpTemp> wmCustomerKpTempMap = getWmCustomerKpTempMap(kpIdList);
        // 查正式表
        Map<Integer, WmCustomerKp> wmCustomerKpMap = getWmCustomerKpMap(kpIdList);
        // 查临时表 KP-POI 关联关系
        List<WmCustomerKpPoiDB> wmCustomerKpPoiDBList = wmCustomerKpPoiService.batchGetTempRelByKpIdList(kpIdList);
        Map<Integer, List<Long>> kpIdPoiMap = Maps.newHashMap();
        wmCustomerKpPoiDBList.stream().forEach(wmCustomerKpPoiDB -> {
            List<Long> wmPoiIdList = kpIdPoiMap.getOrDefault(wmCustomerKpPoiDB.getKpId(), Lists.newArrayList());
            wmPoiIdList.add(wmCustomerKpPoiDB.getWmPoiId());
            kpIdPoiMap.put(wmCustomerKpPoiDB.getKpId(), wmPoiIdList);
        });

        Map<Integer, KpSignerStateMachine> kpFromStateMap = Maps.newHashMap();
        Map<Integer, KpSignerStateMachine> kpToStateMap = Maps.newHashMap();
        List<WmCustomerKpAudit> wmCustomerKpAuditList = Lists.newArrayList();
        // 组装申请参数
        List<EcontractOperationManagerKPBo> operationManagerKPBoList = Lists.newArrayList();
        for (Integer kpId : kpIdList) {
            EcontractOperationManagerKPBo operationManagerKPBo = new EcontractOperationManagerKPBo();
            operationManagerKPBo.setKpId(String.valueOf(kpId));
            WmCustomerKpTemp wmCustomerKpTemp = wmCustomerKpTempMap.get(kpId);
            if (wmCustomerKpTemp != null) {
                // 临时表存在，说明是生效修改
                operationManagerKPBo.setName(wmCustomerKpTemp.getCompellation());
                CertTypeEnum certTypeEnum = CertTypeEnum.getByType(wmCustomerKpTemp.getCertType());
                operationManagerKPBo.setCertificateType(certTypeEnum == null ? "未知" : certTypeEnum.getName());
                operationManagerKPBo.setNumber(wmCustomerKpTemp.getCertNumber());
                operationManagerKPBo.setPhoneNumber(wmCustomerKpTemp.getPhoneNum());
                assembleAuthorizationScope(wmCustomerKpTemp.getSignTaskType(), operationManagerKPBo);
                // 记录KP状态
                kpFromStateMap.put(kpId, KpSignerStateMachine.getByState(wmCustomerKpTemp.getState()));
                kpToStateMap.put(kpId, KpSignerStateMachine.CHANGE_AUTHORIZE_ING);
                // 更新状态
                wmCustomerKpTemp.setState(KpSignerStateMachine.CHANGE_AUTHORIZE_ING.getState());
                wmCustomerSensitiveWordsService.writeKpSourceWhenUpdate(wmCustomerKpTemp);
                wmCustomerKpTempDBMapper.updateByPrimaryKeySelective(wmCustomerKpTemp);
            } else {
                // 临时表不存在，说明未生效
                WmCustomerKp wmCustomerKp = wmCustomerKpMap.get(kpId);
                operationManagerKPBo.setName(wmCustomerKp.getCompellation());
                CertTypeEnum certTypeEnum = CertTypeEnum.getByType(wmCustomerKp.getCertType());
                operationManagerKPBo.setCertificateType(certTypeEnum == null ? "未知" : certTypeEnum.getName());
                operationManagerKPBo.setNumber(wmCustomerKp.getCertNumber());
                operationManagerKPBo.setPhoneNumber(wmCustomerKp.getPhoneNum());
                assembleAuthorizationScope(wmCustomerKp.getSignTaskType(), operationManagerKPBo);
                // 记录KP状态
                kpFromStateMap.put(kpId, KpSignerStateMachine.getByState(wmCustomerKp.getState()));
                kpToStateMap.put(kpId, KpSignerStateMachine.AUTHORIZE_ING);
                // 更新状态
                wmCustomerKp.setState(KpSignerStateMachine.AUTHORIZE_ING.getState());
                wmCustomerSensitiveWordsService.writeKpSourceWhenUpdate(wmCustomerKp);
                wmCustomerKpDBMapper.updateByPrimaryKeySelective(wmCustomerKp);
            }
            assembleAuthorizationWmPoiInfo(kpId, kpIdPoiMap, operationManagerKPBo);
            operationManagerKPBoList.add(operationManagerKPBo);
            // 插入提审表
            WmCustomerKpAudit audit = new WmCustomerKpAudit();
            audit.setKpId(kpId);
            audit.setType(KpAuditConstants.TYPE_AUTH_FOR_OPMANAGER);
            audit.setValid(KpConstants.VALID);
            audit.setUid(uid);
            audit.setUname(uname);
            audit.setExtra("");
            ObjectUtil.defaultValue(audit);
            wmCustomerKpAuditMapper.insert(audit);
            wmCustomerKpAuditList.add(audit);
        }

        EcontractTaskApplyBo applyBo = new EcontractTaskApplyBo();
        applyBo.setBizId(String.valueOf(customerId));
        applyBo.setBizTypeEnum(EcontractTaskApplyBizTypeEnum.CUSTOMER_ID);
        applyBo.setApplyTypeEnum(EcontractTaskApplyTypeEnum.OPERATION_MANAGER_KP_CONFIRM);
        applyBo.setApplyInfoBo(JSON.toJSONString(operationManagerKPBoList));
        applyBo.setRecordId(UUID.randomUUID().toString().replaceAll("-", ""));
        applyBo.setCommitUid(uid);

        // 申请任务
        try {
            LongResult applyTaskId = wmEcontractSignBzService.applyTask(applyBo);
            LOGGER.info("commitAuthForOperationManagerKp doApplyTask applyTaskId = {}", applyTaskId.getValue());
            wmCustomerKpAuditList.stream().forEach(audit -> {
                // 更新 taskId
                audit.setCommitId((int) applyTaskId.getValue());
                wmCustomerKpAuditMapper.updateByPrimaryKey(audit);

                // 记录操作日志
                WmCustomerKp wmCustomerKp = wmCustomerKpMap.get(audit.getKpId());
                WmCustomerKpTemp wmCustomerKpTemp = wmCustomerKpTempMap.get(audit.getKpId());
                KpSignerStateMachine fromState = kpFromStateMap.get(audit.getKpId());
                KpSignerStateMachine toState = kpToStateMap.get(audit.getKpId());
                wmCustomerKpLogService.insertOpManagerKpCommitAuthLog(customerId, applyTaskId.getValue(), "",
                        fromState, toState, uid, uname, wmCustomerKp, wmCustomerKpTemp);
            });
        } catch (WmCustomerException e) {
            LOGGER.warn("运营经理提交授权失败", e);
            throw new RuntimeException(e);
        } catch (Exception e) {
            LOGGER.error("运营经理提交授权失败", e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 新增KP提示记录
     *
     * @param wmCustomerKp
     * @param uid
     * @param uname
     * @return
     */
    public WmCustomerKpAudit insertKpAudit(WmCustomerKp wmCustomerKp, byte auditType, Integer uid, String uname) {
        WmCustomerKpAudit audit = new WmCustomerKpAudit();
        audit.setKpId(wmCustomerKp.getId());
        audit.setType(auditType);
        audit.setValid(KpConstants.VALID);
        audit.setUid(uid);
        audit.setUname(uname);
        audit.setOperateSource(wmCustomerKp.getOperateSource() == null ? CustomerDeviceType.UNKNOWN.getCode() : wmCustomerKp.getOperateSource());
        audit.setExtra("");
        ObjectUtil.defaultValue(audit);
        wmCustomerKpAuditMapper.insert(audit);
        return audit;
    }

    private void assembleAuthorizationScope(String signTaskType, EcontractOperationManagerKPBo operationManagerKPBo) {
        List<String> signTaskTypeList = SPLITTER.splitToList(signTaskType);
        StringBuffer authorizationScope = new StringBuffer();
        signTaskTypeList.stream().forEach(item -> authorizationScope.append(KpSignTaskTypeEnum.getSignTaskNameByType(item)).append("\n"));
        operationManagerKPBo.setAuthorizationScope(authorizationScope.toString());
    }

    private void assembleAuthorizationWmPoiInfo(Integer kpId, Map<Integer, List<Long>> kpIdPoiMap, EcontractOperationManagerKPBo operationManagerKPBo) throws WmCustomerException {
        List<Long> wmPoiIdList = kpIdPoiMap.getOrDefault(kpId, Lists.newArrayList());
        List<WmPoiDomain> wmPoiDomainList = wmPoiQueryAdapter.pageGetWmPoiByWmPoiIdList(wmPoiIdList);
        StringBuffer authorizationWmPoiInfo = new StringBuffer();
        wmPoiDomainList.stream().forEach(wmPoiDomain -> {
            authorizationWmPoiInfo.append(wmPoiDomain.getWmPoiId()).append(wmPoiDomain.getName()).append("、");
        });
        if (StringUtils.isNotBlank(authorizationWmPoiInfo)) {
            // 删除最后一个 '、'
            authorizationWmPoiInfo.deleteCharAt(authorizationWmPoiInfo.length() - 1);
        }
        operationManagerKPBo.setAuthorizationWmPoiInfo(authorizationWmPoiInfo.toString());
    }

    public void deleteKpByKpIdListAndKpType(List<Integer> kpIdList, Byte kpType) {
        LOGGER.info("deleteKpByKpIdListAndKpType kpIdList = {}, kpType = {}", JSON.toJSONString(kpIdList), kpType);
        if (CollectionUtils.isEmpty(kpIdList)) {
            return;
        }

        List<WmCustomerKpAudit> wmCustomerKpAuditList = wmCustomerKpAuditMapper.selectByKpIdListAndType(kpIdList, KpAuditConstants.TYPE_AUTH_FOR_OPMANAGER);
        // 取消签约中任务
        Set<Integer> taskIdSet = wmCustomerKpAuditList.stream().map(WmCustomerKpAudit::getCommitId).collect(Collectors.toSet());
        taskIdSet.stream().forEach(taskId -> {
            try {
                wmEcontractSignBzService.cancelSign(taskId.longValue());
            } catch (Exception e) {
                LOGGER.error("删除运营经理KP, 取消任务异常, taskId = {}", taskId, e);
            }
        });
        // 删除审核表数据
        wmCustomerKpAuditMapper.deleteByKpIdList(kpIdList);

        List<WmCustomerKpTemp> wmCustomerKpTempList = wmCustomerKpTempDBMapper.selectByIdListByKpType(kpIdList, kpType);
        if (!CollectionUtils.isEmpty(wmCustomerKpTempList)) {
            List<Integer> tmpKpIdList = wmCustomerKpTempList.stream().map(WmCustomerKpTemp::getKpId).collect(Collectors.toList());
            // 删除临时表数据
            wmCustomerKpTempDBMapper.deleteByKpIdList(tmpKpIdList);
            wmCustomerKpPoiService.deleteTempByKpIdList(tmpKpIdList);
        }

        // 删除正式表数据
        wmCustomerKpDBMapper.deleteByIdList(kpIdList);
        wmCustomerKpPoiService.deleteByKpIdList(kpIdList);
    }

    /**
     * 法人短信授权-组装参数
     *
     * @param customerId
     * @return
     */
    private EcontractTaskApplyBo convertEcontractTaskApplyBo(Integer customerId, WmCustomerKp signerKp, WmCustomerKpTemp kpTemp) {
        EcontractTaskApplyBo econtractTaskApplyBo = new EcontractTaskApplyBo();
        econtractTaskApplyBo.setBizId(String.valueOf(customerId));
        econtractTaskApplyBo.setBizTypeEnum(EcontractTaskApplyBizTypeEnum.CUSTOMER_ID);
        econtractTaskApplyBo.setApplyTypeEnum(EcontractTaskApplyTypeEnum.AGENT_SIGNER_AUTH);

        try {
            //客户信息
            EcontractCustomerInfoBo econtractCustomerInfoBo = new EcontractCustomerInfoBo();
            WmCustomerDB wmCustomer = wmCustomerPlatformDataParseService.selectCustomerFromPlatformById(customerId);
            if (wmCustomer == null) {
                wmCustomer = wmCustomerPlatformDataParseService.selectCustomerFromPlatformByIdRT(customerId);
            }
            econtractCustomerInfoBo.setCustomerName(wmCustomer.getCustomerName());
            econtractCustomerInfoBo.setQuaNum(wmCustomer.getCustomerNumber());
            econtractCustomerInfoBo.setQuaTypeEnum(CustomerType.getByCode(wmCustomer.getCustomerType()));
            econtractCustomerInfoBo.setAddress(wmCustomer.getAddress());
            econtractCustomerInfoBo.setLegalPerson(wmCustomer.getLegalPerson());
            econtractCustomerInfoBo.setCustomerSecondType(wmCustomer.getCustomerSecondType());
            econtractCustomerInfoBo.setCustomerNumber(wmCustomer.getCustomerNumber());
            econtractTaskApplyBo.setCustomerInfoBo(JSON.toJSONString(econtractCustomerInfoBo));

            //代理签约人信息
            EcontractAgentSignerAuthInfoBo agentSignerAuthInfoBo = new EcontractAgentSignerAuthInfoBo();
            agentSignerAuthInfoBo.setAgentSignerName(signerKp.getCompellation());
            agentSignerAuthInfoBo.setAgentSignerIDCardNum(signerKp.getCertNumber());
            if (kpTemp != null) {
                agentSignerAuthInfoBo.setAgentSignerName(kpTemp.getCompellation());
                agentSignerAuthInfoBo.setAgentSignerIDCardNum(kpTemp.getCertNumber());
            }
            agentSignerAuthInfoBo.setBusinessLicenseName(wmCustomer.getCustomerName());
            agentSignerAuthInfoBo.setBusinessLicenseNum(wmCustomer.getCustomerNumber());
            agentSignerAuthInfoBo.setOfficialSeal(wmCustomer.getCustomerName());
            agentSignerAuthInfoBo.setSignTime(DateUtils.getNDay(0));
            econtractTaskApplyBo.setApplyInfoBo(JSON.toJSONString(agentSignerAuthInfoBo));
        } catch (Exception e) {
            LOGGER.error("convertEcontractTaskApplyBo,根据客户ID获取法人短信授权参数发生异常,customerId={}", customerId, e);
        }
        LOGGER.info("convertEcontractTaskApplyBo,econtractTaskApplyBo={}", JSON.toJSONString(econtractTaskApplyBo));
        return econtractTaskApplyBo;
    }

    /**
     * 查询是否KP法人有效数据是否存在
     *
     * @param customerId
     * @return
     */
    private boolean checkKpLegalEffective(Integer customerId) {
        WmCustomerKp legalKp = wmCustomerKpService.getEffectiveLegalKp(customerId);
        if (legalKp == null) {
            LOGGER.info("checkKpLegalEffective,查询无有效的KP法人数据,,customerId={}", customerId);
            return false;
        }
        return true;
    }
}

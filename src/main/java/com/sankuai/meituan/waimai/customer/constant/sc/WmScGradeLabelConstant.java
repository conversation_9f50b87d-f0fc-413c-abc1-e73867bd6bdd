package com.sankuai.meituan.waimai.customer.constant.sc;

import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.CanteenGradeEnum;

import java.util.Map;
import java.util.Set;

public class WmScGradeLabelConstant {

    private static Map<String, Long> lableEnumHashMap = Maps.newHashMap();

    static {
        Map<String, WmScLableEnum> labelMap = WmScLableEnum.keyMap;
        Set<Map.Entry<String, WmScLableEnum>> entry = labelMap.entrySet();
        for (Map.Entry<String, WmScLableEnum> map : entry) {
            lableEnumHashMap.put(map.getKey(), map.getValue().getLabelId());
        }
    }

    public static Long getLabelIdByGrade(int grade) {
        CanteenGradeEnum canteenGrade = CanteenGradeEnum.getByType(grade);
        return lableEnumHashMap.get(canteenGrade.toString());
    }

    public static Long getLabelA() {
        return lableEnumHashMap.get(WmScLableEnum.A);
    }
}

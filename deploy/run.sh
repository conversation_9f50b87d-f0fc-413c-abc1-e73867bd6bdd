#!/bin/bash
set -ex


# 初始化环境变量
init_env() {
    APP_KEY=waimai_e_customer
    OCTO_KEY=com.sankuai.waimai.e.customerweb
    ENVIRONMENT=production
    APP_BASE=/opt/meituan/apps
    LOG_HOME=/var/sankuai/logs
    APP_PORT=8490
    STOP_PORT=38489

    APP_HOME=$APP_BASE/$APP_KEY
    WAR_HOME=$APP_HOME/wars
    mkdir -p $APP_HOME
    mkdir -p $WAR_HOME
    APP_WORK_HOME=$APP_HOME/work
    HOST_NAME=`hostname`
    CONTEXT=/
    HOST_IP=`hostname -i | awk -F ' ' '{if(NF==1) print $1;else print $2 end}'`
    HOST_IP="localhost"

    DEFAULT_ARGS=" -server
                   -Dfile.encoding=UTF-8
                   -Djava.io.tmpdir=/tmp
                   -Djava.net.preferIPv6Addresses=false
                   -Duser.timezone=GMT+08"
    CUSTOM_JVM_ONLINE=" -Xmx10g
                        -Xms10g
                        -XX:SurvivorRatio=8
                        -XX:NewRatio=3
                        -XX:MaxMetaspaceSize=512m
                        -XX:MaxDirectMemorySize=4g"
    DEFAULT_JVM=" -XX:+HeapDumpOnOutOfMemoryError
                  -XX:+DisableExplicitGC
                  -XX:-OmitStackTraceInFastThrow
                  -XX:+PrintGCDetails
                  -XX:+PrintGCDateStamps
                  -XX:+PrintCommandLineFlags
                  -XX:+UseConcMarkSweepGC
                  -XX:+UseParNewGC
                  -XX:ParallelCMSThreads=4
                  -XX:+CMSClassUnloadingEnabled
                  -XX:+UseCMSCompactAtFullCollection
                  -XX:CMSFullGCsBeforeCompaction=1
                  -XX:CMSInitiatingOccupancyFraction=50
                  -XX:+UseCMSInitiatingOccupancyOnly"
}

# 前后端分离项目专用
# 备份
backup_static(){
    echo "把静态文件从"$APP_WORK_HOME/static/dist"备份到"$APP_HOME"中...."
    rm -rf $APP_HOME/dist
    cp -r $APP_WORK_HOME/static/dist $APP_HOME || echo 'copy文件错误'
}

# 还原
reduction_static(){
    echo "把备份静态文件copy回去...."
    cp -r  $APP_HOME/dist $APP_WORK_HOME/static/ || echo "copy文件错误"
}

# 解压war包
unpack_war(){
    rm -rf $APP_WORK_HOME/*
    unzip -o $WAR_HOME/*.war -d $APP_WORK_HOME/
}

# 前台启动程序
start_webapp() {
    echo "start webapp"
    JAVA_CMD=$JAVA_VERSION
    if [ -z "$JAVA_VERSION" ]; then
        JAVA_CMD="/usr/local/java8/bin/java" #系统默认的java命令
    else
        JAVA_CMD="/usr/local/$JAVA_VERSION/bin/java"
    fi

    JAVA_CMD=$JAVA_CMD" -cp $APP_WORK_HOME/WEB-INF/lib/mms-boot-1.2.2.jar"

    if [ -z ${STOP_PORT} ]
    then
        JAVA_CMD=$JAVA_CMD" -DSTOP.PORT=$STOP_PORT"
    fi


    if [ $ENVIRONMENT == "production" ]; then
        cp -r $APP_HOME/work/WEB-INF/classes/conf/log4j2.xml $APP_HOME/work/WEB-INF/classes/log4j2.xml

    fi

    JAVA_CMD=$JAVA_CMD$DEFAULT_ARGS
    JAVA_CMD=$JAVA_CMD$CUSTOM_JVM_ONLINE
    JAVA_CMD=$JAVA_CMD$DEFAULT_JVM
    JAVA_CMD=$JAVA_CMD" -DSTOP.KEY=stop_$APP_KEY"
    JAVA_CMD=$JAVA_CMD" -Dapp.key=$APP_KEY"
    JAVA_CMD=$JAVA_CMD" -Denvironment=$ENVIRONMENT"
    JAVA_CMD=$JAVA_CMD" -Dapp.host=$HOST_NAME"
    JAVA_CMD=$JAVA_CMD" -Dapp.ip=$HOST_IP"
    JAVA_CMD=$JAVA_CMD" -Dapp.port=$APP_PORT"
    JAVA_CMD=$JAVA_CMD" -Dapp.context=$CONTEXT"
    JAVA_CMD=$JAVA_CMD" -Dapp.workdir=$APP_WORK_HOME"
    JAVA_CMD=$JAVA_CMD" -Dapp.logdir=$LOG_HOME"
    JAVA_CMD=$JAVA_CMD" -Dfile.encoding=UTF-8"
    JAVA_CMD=$JAVA_CMD" -Dsun.jnu.encoding=UTF-8"
    JAVA_CMD=$JAVA_CMD" -Djetty.host=0.0.0.0"
    JAVA_CMD=$JAVA_CMD" -Djetty.port=$APP_PORT"
    JAVA_CMD=$JAVA_CMD" -Djetty.context=$CONTEXT"
    JAVA_CMD=$JAVA_CMD" -Djetty.logs=$LOG_HOME"
    JAVA_CMD=$JAVA_CMD" -Djetty.appkey=$APP_KEY"
    JAVA_CMD=$JAVA_CMD" -Xloggc:$LOG_HOME/$APP_KEY.gc.log.$(date +%Y%m%d%H%M)"
    JAVA_CMD=$JAVA_CMD" -XX:ErrorFile=$LOG_HOME/$APP_KEY.vmerr.log.$(date +%Y%m%d%H%M)"
    JAVA_CMD=$JAVA_CMD" -XX:HeapDumpPath=$LOG_HOME/$APP_KEY.heaperr.log.$(date +%Y%m%d%H%M)"

    JAVA_CMD=$JAVA_CMD" com.sankuai.mms.boot.Bootstrap"

    umask 000
    echo "-------------------------------------------------------------"
    echo "$JAVA_CMD"
    echo "-------------------------------------------------------------"
    echo "start command exec $JAVA_CMD"
    exec $JAVA_CMD >> /var/sankuai/logs/$APP_KEY.start.log.`date '+%Y%m%d'` 2>&1
}

# Entrypoint
init_env
backup_static
unpack_war
reduction_static
start_webapp
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE generatorConfiguration PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN" "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd" >
<generatorConfiguration>

    <!-- command: java -Djava.ext.dirs=. -jar mybatis-generator-core-1.3.2.jar -configfile mybatis-generator.xml -overwrite -->
    <classPathEntry location="commons-mybatis-extend-1.0.16.jar"/>
    <!-- 数据库驱动 -->
    <classPathEntry location="mysql-connector-java-5.1.31.jar"/>

    <!-- 一个数据库一个context -->
    <context id="waimai_service_customer_server" targetRuntime="MyBatis3">
        <!-- 引入配置文件 -->

        <!-- 注释 -->
        <commentGenerator >
            <property name="suppressAllComments" value="true"/>		<!-- 是否取消注释 -->
            <property name="suppressDate" value="true" /> 			<!-- 是否取消注释时间戳-->
        </commentGenerator>

        <!-- jdbc连接 -->
        <jdbcConnection
                driverClass="com.mysql.jdbc.Driver"
                connectionURL="**************************************"
                userId="waimai"
                password="waimai"
        />

        <!-- 类型转换 -->
        <javaTypeResolver>
            <!-- 是否使用bigDecimal， false可自动转化以下类型（Long, Integer, Short, etc.） -->
            <property name="forceBigDecimals" value="false"/>
        </javaTypeResolver>

        <!-- 生成实体类 -->
        <javaModelGenerator
                targetPackage="com.sankuai.meituan.waimai.customer.domain"
                targetProject="../../src/main/java" >
            <!-- 是否在当前路径下新加一层schema, eg：false:com.oop.eksp.user.model，true:com.oop.eksp.user.model.[schemaName] -->
            <property name="enableSubPackages" value="false"/>
            <!-- 是否针对string类型的字段在set的时候进行trim调用  -->
            <property name="trimStrings" value="true"/>
        </javaModelGenerator>

        <!-- 生成map xml文件 -->
        <sqlMapGenerator targetPackage="mybatis"
                         targetProject="../../src/main/resources" >
            <!--是否在当前路径下新加一层schema, eg：false:com.oop.eksp.user.model， true:com.oop.eksp.user.model.[schemaName]  -->
            <property name="enableSubPackages" value="false" />
        </sqlMapGenerator>

        <!-- 生成map xml文件对应的client，也就是dao接口 -->
        <javaClientGenerator targetPackage="com.sankuai.meituan.waimai.customer.dao"
                             targetProject="../../src/main/java" type="XMLMAPPER" >
            <!-- 是否在当前路径下新加一层schema,eg：fase路径com.oop.eksp.user.model， true:com.oop.eksp.user.model.[schemaName]   -->
            <property name="enableSubPackages" value="false" />
        </javaClientGenerator>

        <!--<table tableName="wm_customer_oplog"  domainObjectName="WmCustomerOplog"-->
               <!--enableCountByExample="false" enableDeleteByExample="false" enableSelectByExample="false" enableUpdateByExample="false">-->
            <!--<property name="useActualColumnNames" value="false" />-->
        <!--</table>-->

        <table tableName="wm_customer_black_white_list"  domainObjectName="WmCustomerBlackWhiteList"
          enableCountByExample="false" enableDeleteByExample="false" enableSelectByExample="false" enableUpdateByExample="false">
            <property name="useActualColumnNames" value="false" />
        </table>

    </context>
</generatorConfiguration>